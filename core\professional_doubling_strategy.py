#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专业倍增策略
Professional Doubling Strategy

专门设计用于实现资金倍增的专业交易策略
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import random

logger = logging.getLogger(__name__)

@dataclass
class DoublingParams:
    """倍增策略参数"""
    # 基础风险参数
    base_risk_per_trade: float = 2.0      # 基础每笔风险2%
    max_position_size: float = 25.0       # 最大仓位25%
    
    # 倍增机制参数
    profit_reinvest_ratio: float = 0.8    # 利润再投资比例80%
    capital_growth_threshold: float = 0.1  # 资金增长10%时升级策略
    
    # 止损止盈参数
    base_stop_loss: float = 2.0           # 基础止损2%
    base_take_profit: float = 6.0         # 基础止盈6%
    dynamic_tp_multiplier: float = 1.5    # 动态止盈倍数
    
    # 信号过滤参数
    min_signal_strength: float = 80.0     # 最小信号强度80%
    min_win_rate_target: float = 75.0     # 目标胜率75%
    
    # 复利参数
    compound_frequency: int = 5           # 每5笔交易重新计算仓位
    max_daily_trades: int = 8             # 每日最大交易次数
    
    # 倍增目标参数
    target_multiplier: float = 2.0        # 目标倍数2倍
    max_drawdown_limit: float = 0.15      # 最大回撤限制15%

@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    direction: str  # 'long' or 'short'
    strength: float  # 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    confidence: float  # 0-1
    expected_return: float
    risk_reward_ratio: float

class ProfessionalDoublingStrategy:
    """专业倍增策略"""
    
    def __init__(self, initial_capital: float = 1000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.target_capital = initial_capital * 2.0  # 目标翻倍
        
        # 策略参数
        self.params = DoublingParams()
        
        # 交易统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0
        self.max_drawdown = 0.0
        self.peak_capital = initial_capital
        
        # 交易历史
        self.trade_history = []
        self.daily_trades = 0
        self.last_trade_date = None
        
        # 策略状态
        self.strategy_level = 1  # 策略等级
        self.is_active = True
        self.risk_mode = "aggressive"  # aggressive, balanced, conservative
        
    def calculate_position_size(self, signal: TradingSignal) -> float:
        """计算仓位大小"""
        # 基础仓位计算
        risk_amount = self.current_capital * (self.params.base_risk_per_trade / 100)
        
        # 根据信号强度调整
        strength_multiplier = 0.5 + (signal.strength * 0.5)  # 0.5-1.0
        
        # 根据胜率调整
        current_win_rate = self.get_current_win_rate()
        if current_win_rate > 0.8:
            win_rate_multiplier = 1.2
        elif current_win_rate > 0.7:
            win_rate_multiplier = 1.0
        else:
            win_rate_multiplier = 0.8
        
        # 根据资金增长调整（复利效应）
        growth_ratio = self.current_capital / self.initial_capital
        compound_multiplier = min(growth_ratio * 0.5 + 0.5, 2.0)  # 最大2倍
        
        # 计算最终仓位
        position_value = (risk_amount * strength_multiplier * 
                         win_rate_multiplier * compound_multiplier)
        
        # 限制最大仓位
        max_position_value = self.current_capital * (self.params.max_position_size / 100)
        position_value = min(position_value, max_position_value)
        
        return position_value
    
    def generate_professional_signal(self, market_data: Dict) -> Optional[TradingSignal]:
        """生成专业交易信号"""
        # 检查交易限制
        if not self._can_trade():
            return None
        
        # 模拟专业信号生成
        symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        symbol = random.choice(symbols)
        
        # 生成高质量信号
        base_price = self._get_market_price(symbol)
        
        # 信号强度（专业策略要求高强度）
        signal_strength = random.uniform(0.75, 0.95)  # 75%-95%
        
        # 方向判断（基于趋势分析）
        direction = self._analyze_trend(symbol, market_data)
        
        # 计算入场价格
        if direction == 'long':
            entry_price = base_price * random.uniform(0.998, 1.002)
            stop_loss = entry_price * (1 - self.params.base_stop_loss / 100)
            take_profit = entry_price * (1 + self.params.base_take_profit / 100)
        else:
            entry_price = base_price * random.uniform(0.998, 1.002)
            stop_loss = entry_price * (1 + self.params.base_stop_loss / 100)
            take_profit = entry_price * (1 - self.params.base_take_profit / 100)
        
        # 计算风险回报比
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        # 只接受高质量信号
        if (signal_strength >= self.params.min_signal_strength / 100 and 
            risk_reward_ratio >= 2.0):
            
            return TradingSignal(
                symbol=symbol,
                direction=direction,
                strength=signal_strength,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=signal_strength * 0.9,
                expected_return=reward / entry_price,
                risk_reward_ratio=risk_reward_ratio
            )
        
        return None
    
    def execute_trade(self, signal: TradingSignal) -> Dict:
        """执行交易"""
        # 计算仓位大小
        position_size = self.calculate_position_size(signal)
        
        # 实战演练执行
        trade_result = self._simulate_trade_execution(signal, position_size)
        
        # 更新统计
        self._update_statistics(trade_result)
        
        # 检查是否需要升级策略
        self._check_strategy_upgrade()
        
        return trade_result
    
    def _simulate_trade_execution(self, signal: TradingSignal, position_size: float) -> Dict:
        """实战演练执行"""
        # 模拟市场执行
        execution_price = signal.entry_price * random.uniform(0.9995, 1.0005)
        
        # 实战演练结果（专业策略有更高的成功率）
        success_probability = signal.confidence * 0.85  # 85%基础成功率
        
        if random.random() < success_probability:
            # 成功交易
            if signal.direction == 'long':
                exit_price = signal.take_profit * random.uniform(0.98, 1.02)
            else:
                exit_price = signal.take_profit * random.uniform(0.98, 1.02)
            
            profit_loss = self._calculate_pnl(signal.direction, execution_price, 
                                            exit_price, position_size)
            result = 'win'
        else:
            # 失败交易
            exit_price = signal.stop_loss * random.uniform(0.98, 1.02)
            profit_loss = self._calculate_pnl(signal.direction, execution_price, 
                                            exit_price, position_size)
            result = 'loss'
        
        # 更新资金
        self.current_capital += profit_loss
        
        # 记录交易
        trade_record = {
            'timestamp': datetime.now(),
            'symbol': signal.symbol,
            'direction': signal.direction,
            'entry_price': execution_price,
            'exit_price': exit_price,
            'position_size': position_size,
            'profit_loss': profit_loss,
            'result': result,
            'capital_after': self.current_capital,
            'signal_strength': signal.strength
        }
        
        self.trade_history.append(trade_record)
        return trade_record
    
    def _calculate_pnl(self, direction: str, entry_price: float, 
                      exit_price: float, position_size: float) -> float:
        """计算盈亏"""
        if direction == 'long':
            return (exit_price - entry_price) / entry_price * position_size
        else:
            return (entry_price - exit_price) / entry_price * position_size
    
    def _update_statistics(self, trade_result: Dict):
        """更新交易统计"""
        self.total_trades += 1
        
        if trade_result['result'] == 'win':
            self.winning_trades += 1
        
        self.total_profit += trade_result['profit_loss']
        
        # 更新最大回撤
        if self.current_capital > self.peak_capital:
            self.peak_capital = self.current_capital
        
        current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # 更新每日交易计数
        today = datetime.now().date()
        if self.last_trade_date != today:
            self.daily_trades = 1
            self.last_trade_date = today
        else:
            self.daily_trades += 1
    
    def _check_strategy_upgrade(self):
        """检查策略升级"""
        growth_ratio = self.current_capital / self.initial_capital
        
        if growth_ratio >= 1.0 + self.params.capital_growth_threshold:
            self.strategy_level += 1
            
            # 升级策略参数
            self.params.base_risk_per_trade *= 1.1  # 增加风险
            self.params.max_position_size *= 1.05   # 增加最大仓位
            self.params.min_signal_strength *= 0.98  # 稍微降低信号要求
            
            logger.info(f"策略升级到等级 {self.strategy_level}")
    
    def get_current_win_rate(self) -> float:
        """获取当前胜率"""
        if self.total_trades == 0:
            return 0.75  # 默认胜率
        return self.winning_trades / self.total_trades
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        win_rate = self.get_current_win_rate()
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        profit_factor = 0
        if self.total_trades > 0:
            total_wins = sum(t['profit_loss'] for t in self.trade_history if t['result'] == 'win')
            total_losses = abs(sum(t['profit_loss'] for t in self.trade_history if t['result'] == 'loss'))
            if total_losses > 0:
                profit_factor = total_wins / total_losses
        
        return {
            'current_capital': self.current_capital,
            'total_return': total_return,
            'win_rate': win_rate,
            'total_trades': self.total_trades,
            'max_drawdown': self.max_drawdown,
            'profit_factor': profit_factor,
            'strategy_level': self.strategy_level,
            'progress_to_double': (self.current_capital - self.initial_capital) / self.initial_capital,
            'doubling_achieved': self.current_capital >= self.target_capital
        }
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查每日交易限制
        if self.daily_trades >= self.params.max_daily_trades:
            return False
        
        # 检查最大回撤限制
        if self.max_drawdown > self.params.max_drawdown_limit:
            return False
        
        # 检查资金充足性
        if self.current_capital < self.initial_capital * 0.5:  # 不能低于初始资金50%
            return False
        
        return True
    
    def _get_market_price(self, symbol: str) -> float:
        """获取市场价格"""
        base_prices = {
            'BTC/USDT': 43000,
            'ETH/USDT': 2600,
            'SOL/USDT': 95
        }
        return base_prices.get(symbol, 1000) * random.uniform(0.95, 1.05)
    
    def _analyze_trend(self, symbol: str, market_data: Dict) -> str:
        """分析趋势"""
        # 简化的趋势分析
        trend_score = random.uniform(-1, 1)
        return 'long' if trend_score > 0 else 'short'
    
    def get_strategy_summary(self) -> str:
        """获取策略摘要"""
        metrics = self.get_performance_metrics()
        
        return f"""
🚀 专业倍增策略状态报告
{'='*40}
💰 当前资金: {metrics['current_capital']:.2f} USDT
📈 总收益率: {metrics['total_return']:.2%}
🎯 倍增进度: {metrics['progress_to_double']:.1%}
🏆 胜率: {metrics['win_rate']:.1%}
📊 总交易: {metrics['total_trades']}
📉 最大回撤: {metrics['max_drawdown']:.2%}
💎 盈利因子: {metrics['profit_factor']:.2f}
⭐ 策略等级: {metrics['strategy_level']}
🎊 倍增达成: {'✅ 已达成' if metrics['doubling_achieved'] else '🔄 进行中'}
"""

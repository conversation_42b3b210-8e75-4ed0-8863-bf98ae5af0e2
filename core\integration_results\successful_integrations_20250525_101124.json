{"AdaptiveDualMAStrategy": {"strategy_name": "AdaptiveDualMAStrategy", "strategy_file": "../strategies\\adaptive_dual_ma_strategy.py", "validation_score": 13.88826197654943, "sharpe_ratio": 0.1256105339587444, "max_drawdown": -0.4022996711372282, "win_rate": 0.4801641586867305, "r_squared": 0.003299525804962067, "tracking_error": 0.23930326110524364, "alpha_contribution": 0.046760997589284474, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.698788", "status": "SUCCESS"}, "AdaptiveNeuralNetworkStrategy": {"strategy_name": "AdaptiveNeuralNetworkStrategy", "strategy_file": "../strategies\\adaptive_neural_network_strategy.py", "validation_score": 18.353701400883846, "sharpe_ratio": -0.5972133402602744, "max_drawdown": -0.41470138446667726, "win_rate": 0.49247606019151846, "r_squared": 0.010428763250991069, "tracking_error": 0.18962865124223865, "alpha_contribution": -0.0798915609798359, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.779896", "status": "SUCCESS"}, "adaptive_parameters": {"strategy_name": "adaptive_parameters", "strategy_file": "../strategies\\adaptive_parameters.py", "validation_score": 31.730922473516554, "sharpe_ratio": 0.2604016715130509, "max_drawdown": -0.31647994064372037, "win_rate": 0.5129958960328317, "r_squared": 0.010697499872223237, "tracking_error": 0.23991802486347671, "alpha_contribution": 0.0650343468145414, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.868066", "status": "SUCCESS"}, "AdvancedEnsembleStrategy": {"strategy_name": "AdvancedEnsembleStrategy", "strategy_file": "../strategies\\advanced_ensemble_strategy.py", "validation_score": 19.06222378063054, "sharpe_ratio": -0.55453847846412, "max_drawdown": -0.39976064064593364, "win_rate": 0.4952120383036936, "r_squared": 0.005063678189724974, "tracking_error": 0.24143810948493066, "alpha_contribution": -0.13255529016218573, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.952120", "status": "SUCCESS"}, "BollingerBandsStrategy": {"strategy_name": "BollingerBandsStrategy", "strategy_file": "../strategies\\bollinger_bands_strategy.py", "validation_score": 38.43304708368234, "sharpe_ratio": -0.05180682372433076, "max_drawdown": -0.12460425635480687, "win_rate": 0.5006839945280438, "r_squared": 0.011435591085299901, "tracking_error": 0.16068266242590892, "alpha_contribution": -0.010118265387563472, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.848893", "status": "SUCCESS"}, "AlligatorTradingStrategy": {"strategy_name": "AlligatorTradingStrategy", "strategy_file": "../strategies\\alligator_trading_strategy.py", "validation_score": 42.931601507824496, "sharpe_ratio": 0.7591154528993052, "max_drawdown": -0.24112243992109114, "win_rate": 0.5047879616963065, "r_squared": 0.0123989869529596, "tracking_error": 0.23401543825758248, "alpha_contribution": 0.1587682642323191, "recommendation": "谨慎：策略存在一定风险，需要进一步优化", "integration_time": "2025-05-25 10:11:23.104973", "status": "SUCCESS"}, "ArbitrageStrategy": {"strategy_name": "ArbitrageStrategy", "strategy_file": "../strategies\\arbitrage_strategy.py", "validation_score": 82.68013860430347, "sharpe_ratio": 1.12299578446607, "max_drawdown": -0.044722314437577876, "win_rate": 0.5526675786593708, "r_squared": 0.004069252293076864, "tracking_error": 0.04664336524096581, "alpha_contribution": 0.07257734840918198, "recommendation": "强烈推荐：策略表现优异，符合机构级标准", "integration_time": "2025-05-25 10:11:23.204656", "status": "SUCCESS"}, "Strategy": {"strategy_name": "Strategy", "strategy_file": "../strategies\\auto_trading_strategy.py", "validation_score": 12.60233088147718, "sharpe_ratio": 0.005682067885072185, "max_drawdown": -0.34523264382909563, "win_rate": 0.5006839945280438, "r_squared": 0.000394622892442853, "tracking_error": 0.22527042228314187, "alpha_contribution": 0.017927376994262525, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.282903", "status": "SUCCESS"}, "BaggingEnsembleStrategy": {"strategy_name": "BaggingEnsembleStrategy", "strategy_file": "../strategies\\bagging_ensemble_strategy.py", "validation_score": 12.062564026880988, "sharpe_ratio": -0.06222213373005013, "max_drawdown": -0.36306780861656923, "win_rate": 0.5198358413132695, "r_squared": 0.028192744633628086, "tracking_error": 0.23797800423036009, "alpha_contribution": 0.007545917197755675, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.362332", "status": "SUCCESS"}, "BaseStrategy": {"strategy_name": "BaseStrategy", "strategy_file": "../strategies\\base_strategy.py", "validation_score": 22.44846672478775, "sharpe_ratio": 0.48125248879126825, "max_drawdown": -0.2613632906383669, "win_rate": 0.506155950752394, "r_squared": 0.0057222582295713975, "tracking_error": 0.24235640976301406, "alpha_contribution": 0.15355505616136983, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.457006", "status": "SUCCESS"}, "BaseStrategyEnhanced": {"strategy_name": "BaseStrategyEnhanced", "strategy_file": "../strategies\\base_strategy_enhanced.py", "validation_score": 53.510634577440726, "sharpe_ratio": 1.388169072148579, "max_drawdown": -0.31466788111944743, "win_rate": 0.5075239398084815, "r_squared": 0.010241845984587106, "tracking_error": 0.24464808184818, "alpha_contribution": 0.2840681278138258, "recommendation": "谨慎：策略存在一定风险，需要进一步优化", "integration_time": "2025-05-25 10:11:23.535573", "status": "SUCCESS"}, "BearTrapStrategy": {"strategy_name": "BearTrapStrategy", "strategy_file": "../strategies\\bear_trap_strategy.py", "validation_score": 75.91659293205447, "sharpe_ratio": 1.3124131863268662, "max_drawdown": -0.1810994764994993, "win_rate": 0.5321477428180574, "r_squared": 0.005728631884575508, "tracking_error": 0.2331194510604193, "alpha_contribution": 0.31537484688918394, "recommendation": "推荐：策略表现良好，建议小规模试运行", "integration_time": "2025-05-25 10:11:23.619260", "status": "SUCCESS"}, "BidirectionalNetworkStrategy": {"strategy_name": "BidirectionalNetworkStrategy", "strategy_file": "../strategies\\bidirectional_network_strategy.py", "validation_score": 61.81774850889124, "sharpe_ratio": 1.5929494817853882, "max_drawdown": -0.2317288011171809, "win_rate": 0.5348837209302325, "r_squared": 0.009264630958554565, "tracking_error": 0.23400060419947613, "alpha_contribution": 0.3011573547429695, "recommendation": "推荐：策略表现良好，建议小规模试运行", "integration_time": "2025-05-25 10:11:23.697471", "status": "SUCCESS"}, "BlackSwanProtectionStrategy": {"strategy_name": "BlackSwanProtectionStrategy", "strategy_file": "../strategies\\black_swan_protection_strategy.py", "validation_score": 46.254377425819754, "sharpe_ratio": 0.8866036293985002, "max_drawdown": -0.21842665317538273, "win_rate": 0.5006839945280438, "r_squared": 0.0045600485301318106, "tracking_error": 0.22432224909498613, "alpha_contribution": 0.18865861269549905, "recommendation": "谨慎：策略存在一定风险，需要进一步优化", "integration_time": "2025-05-25 10:11:23.777252", "status": "SUCCESS"}, "BollingerStrategy": {"strategy_name": "BollingerStrategy", "strategy_file": "../strategies\\bollinger_strategy.py", "validation_score": 38.399200368247804, "sharpe_ratio": 0.0009276990277318944, "max_drawdown": -0.13800321649760464, "win_rate": 0.5034199726402189, "r_squared": 0.00858979984436814, "tracking_error": 0.15579519663629204, "alpha_contribution": 0.0112255678808841, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.934363", "status": "SUCCESS"}, "BollMA5Strategy": {"strategy_name": "BollMA5Strategy", "strategy_file": "../strategies\\boll_ma5_strategy.py", "validation_score": 23.01750032454889, "sharpe_ratio": -0.3113732113775432, "max_drawdown": -0.4082358114324273, "win_rate": 0.5075239398084815, "r_squared": 0.0032595910667808647, "tracking_error": 0.2401322006175665, "alpha_contribution": -0.05457652062729288, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:24.013931", "status": "SUCCESS"}, "BoostingEnsembleStrategy": {"strategy_name": "BoostingEnsembleStrategy", "strategy_file": "../strategies\\boosting_ensemble_strategy.py", "validation_score": 20.66457429752355, "sharpe_ratio": -0.440875165390815, "max_drawdown": -0.4038131222242217, "win_rate": 0.4911080711354309, "r_squared": 0.01672294751656267, "tracking_error": 0.2397494024876724, "alpha_contribution": -0.13806964085120627, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:24.096088", "status": "SUCCESS"}, "BreakoutStrategy": {"strategy_name": "BreakoutStrategy", "strategy_file": "../strategies\\breakout_strategy.py", "validation_score": 85.65513290494576, "sharpe_ratio": 2.725748764089831, "max_drawdown": -0.20357929917547155, "win_rate": 0.5690834473324213, "r_squared": 0.005320633314843115, "tracking_error": 0.18985380362200444, "alpha_contribution": 0.44107233053234, "recommendation": "强烈推荐：策略表现优异，符合机构级标准", "integration_time": "2025-05-25 10:11:24.249987", "status": "SUCCESS"}}
{"data_mtime": 1748490240, "dep_lines": [26, 33, 12, 16, 22, 23, 24, 25, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.asyncio.compatibility", "websockets.asyncio.messages", "collections.abc", "websockets.exceptions", "websockets.frames", "websockets.http11", "websockets.protocol", "websockets.typing", "__future__", "asyncio", "collections", "contextlib", "logging", "random", "struct", "sys", "traceback", "uuid", "types", "typing", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.transports", "enum", "typing_extensions", "websockets.asyncio.async_timeout"], "hash": "1350c4331d63fc2cd833ad5d46f66cb81ec67f1c", "id": "websockets.asyncio.connection", "ignore_all": true, "interface_hash": "68114366a9f789842c6f2202a4d66a26dbbc9624", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py", "plugin_data": null, "size": 49959, "suppressed": [], "version_id": "1.15.0"}
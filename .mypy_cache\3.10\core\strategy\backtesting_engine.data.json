{".class": "MypyFile", "_fullname": "core.strategy.backtesting_engine", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BacktestResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.strategy.backtesting_engine.BacktestResult", "name": "BacktestResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "start_date", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "end_date", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "initial_capital", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "final_capital", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "total_return", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "total_return_pct", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "max_drawdown", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "max_drawdown_pct", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "sharpe_ratio", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "win_rate", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "profit_factor", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "total_trades", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "winning_trades", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "losing_trades", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 60, "name": "avg_win", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "avg_loss", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "max_win", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "max_loss", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "total_commission", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "trades", "type": {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "equity_curve", "type": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "core.strategy.backtesting_engine", "mro": ["core.strategy.backtesting_engine.BacktestResult", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "start_date", "end_date", "initial_capital", "final_capital", "total_return", "total_return_pct", "max_drawdown", "max_drawdown_pct", "sharpe_ratio", "win_rate", "profit_factor", "total_trades", "winning_trades", "losing_trades", "avg_win", "avg_loss", "max_win", "max_loss", "total_commission", "trades", "equity_curve"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "start_date", "end_date", "initial_capital", "final_capital", "total_return", "total_return_pct", "max_drawdown", "max_drawdown_pct", "sharpe_ratio", "win_rate", "profit_factor", "total_trades", "winning_trades", "losing_trades", "avg_win", "avg_loss", "max_win", "max_loss", "total_commission", "trades", "equity_curve"], "arg_types": ["core.strategy.backtesting_engine.BacktestResult", "datetime.datetime", "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BacktestResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "core.strategy.backtesting_engine.BacktestResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "start_date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end_date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "initial_capital"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "final_capital"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_return"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_return_pct"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_drawdown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_drawdown_pct"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sharpe_ratio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "win_rate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "profit_factor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_trades"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "winning_trades"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "losing_trades"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avg_win"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avg_loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_win"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_commission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "trades"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equity_curve"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["start_date", "end_date", "initial_capital", "final_capital", "total_return", "total_return_pct", "max_drawdown", "max_drawdown_pct", "sharpe_ratio", "win_rate", "profit_factor", "total_trades", "winning_trades", "losing_trades", "avg_win", "avg_loss", "max_win", "max_loss", "total_commission", "trades", "equity_curve"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "core.strategy.backtesting_engine.BacktestResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["start_date", "end_date", "initial_capital", "final_capital", "total_return", "total_return_pct", "max_drawdown", "max_drawdown_pct", "sharpe_ratio", "win_rate", "profit_factor", "total_trades", "winning_trades", "losing_trades", "avg_win", "avg_loss", "max_win", "max_loss", "total_commission", "trades", "equity_curve"], "arg_types": ["datetime.datetime", "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BacktestResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["start_date", "end_date", "initial_capital", "final_capital", "total_return", "total_return_pct", "max_drawdown", "max_drawdown_pct", "sharpe_ratio", "win_rate", "profit_factor", "total_trades", "winning_trades", "losing_trades", "avg_win", "avg_loss", "max_win", "max_loss", "total_commission", "trades", "equity_curve"], "arg_types": ["datetime.datetime", "datetime.datetime", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BacktestResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "avg_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.avg_loss", "name": "avg_loss", "type": "builtins.float"}}, "avg_win": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.avg_win", "name": "avg_win", "type": "builtins.float"}}, "end_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.end_date", "name": "end_date", "type": "datetime.datetime"}}, "equity_curve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.equity_curve", "name": "equity_curve", "type": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}}}, "final_capital": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.final_capital", "name": "final_capital", "type": "builtins.float"}}, "initial_capital": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.initial_capital", "name": "initial_capital", "type": "builtins.float"}}, "losing_trades": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.losing_trades", "name": "losing_trades", "type": "builtins.int"}}, "max_drawdown": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.max_drawdown", "name": "max_drawdown", "type": "builtins.float"}}, "max_drawdown_pct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.max_drawdown_pct", "name": "max_drawdown_pct", "type": "builtins.float"}}, "max_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.max_loss", "name": "max_loss", "type": "builtins.float"}}, "max_win": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.max_win", "name": "max_win", "type": "builtins.float"}}, "profit_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.profit_factor", "name": "profit_factor", "type": "builtins.float"}}, "sharpe_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.sharpe_ratio", "name": "sharpe_ratio", "type": "builtins.float"}}, "start_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.start_date", "name": "start_date", "type": "datetime.datetime"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestResult.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["core.strategy.backtesting_engine.BacktestResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of BacktestResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "total_commission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.total_commission", "name": "total_commission", "type": "builtins.float"}}, "total_return": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.total_return", "name": "total_return", "type": "builtins.float"}}, "total_return_pct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.total_return_pct", "name": "total_return_pct", "type": "builtins.float"}}, "total_trades": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.total_trades", "name": "total_trades", "type": "builtins.int"}}, "trades": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.trades", "name": "trades", "type": {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "win_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.win_rate", "name": "win_rate", "type": "builtins.float"}}, "winning_trades": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestResult.winning_trades", "name": "winning_trades", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.strategy.backtesting_engine.BacktestResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.strategy.backtesting_engine.BacktestResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BacktestTrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.strategy.backtesting_engine.BacktestTrade", "name": "BacktestTrade", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestTrade", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "timestamp", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "symbol", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "trade_type", "type": "core.strategy.backtesting_engine.TradeType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "price", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 37, "name": "quantity", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "commission", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "signal_confidence", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "signal_reason", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "core.strategy.backtesting_engine", "mro": ["core.strategy.backtesting_engine.BacktestTrade", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "timestamp", "symbol", "trade_type", "price", "quantity", "commission", "signal_confidence", "signal_reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestTrade.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "timestamp", "symbol", "trade_type", "price", "quantity", "commission", "signal_confidence", "signal_reason"], "arg_types": ["core.strategy.backtesting_engine.BacktestTrade", "datetime.datetime", "builtins.str", "core.strategy.backtesting_engine.TradeType", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BacktestTrade", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "trade_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "price"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quantity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "commission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "signal_confidence"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "signal_reason"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["timestamp", "symbol", "trade_type", "price", "quantity", "commission", "signal_confidence", "signal_reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["timestamp", "symbol", "trade_type", "price", "quantity", "commission", "signal_confidence", "signal_reason"], "arg_types": ["datetime.datetime", "builtins.str", "core.strategy.backtesting_engine.TradeType", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BacktestTrade", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["timestamp", "symbol", "trade_type", "price", "quantity", "commission", "signal_confidence", "signal_reason"], "arg_types": ["datetime.datetime", "builtins.str", "core.strategy.backtesting_engine.TradeType", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BacktestTrade", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "commission": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.commission", "name": "commission", "type": "builtins.float"}}, "price": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.price", "name": "price", "type": "builtins.float"}}, "quantity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.quantity", "name": "quantity", "type": "builtins.float"}}, "signal_confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.signal_confidence", "name": "signal_confidence", "type": "builtins.float"}}, "signal_reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.signal_reason", "name": "signal_reason", "type": "builtins.str"}}, "symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.symbol", "name": "symbol", "type": "builtins.str"}}, "timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.timestamp", "name": "timestamp", "type": "datetime.datetime"}}, "trade_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "core.strategy.backtesting_engine.BacktestTrade.trade_type", "name": "trade_type", "type": "core.strategy.backtesting_engine.TradeType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.strategy.backtesting_engine.BacktestTrade.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.strategy.backtesting_engine.BacktestTrade", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BacktestingEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.strategy.backtesting_engine.BacktestingEngine", "name": "BacktestingEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.strategy.backtesting_engine", "mro": ["core.strategy.backtesting_engine.BacktestingEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_capital", "commission_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_capital", "commission_rate"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BacktestingEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_date", "end_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._calculate_results", "name": "_calculate_results", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_date", "end_date"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_results of BacktestingEngine", "ret_type": "core.strategy.backtesting_engine.BacktestResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "price", "timestamp", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._close_position", "name": "_close_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "price", "timestamp", "reason"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", "builtins.float", "datetime.datetime", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close_position of BacktestingEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_open_long_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "price", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._open_long_position", "name": "_open_long_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "price", "timestamp"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_open_long_position of BacktestingEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_open_short_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "price", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._open_short_position", "name": "_open_short_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "price", "timestamp"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_open_short_position of BacktestingEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "current_price", "current_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._process_signal", "name": "_process_signal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal", "current_price", "current_time"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_signal of BacktestingEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reset_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._reset_state", "name": "_reset_state", "type": null}}, "_update_equity_curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "timestamp", "current_price"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine._update_equity_curve", "name": "_update_equity_curve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "timestamp", "current_price"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", "datetime.datetime", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_equity_curve of BacktestingEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commission_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.commission_rate", "name": "commission_rate", "type": "builtins.float"}}, "current_capital": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.current_capital", "name": "current_capital", "type": "builtins.float"}}, "current_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.current_position", "name": "current_position", "type": "builtins.float"}}, "entry_price": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.entry_price", "name": "entry_price", "type": "builtins.float"}}, "equity_curve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.equity_curve", "name": "equity_curve", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "initial_capital": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.initial_capital", "name": "initial_capital", "type": "builtins.float"}}, "max_drawdown": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.max_drawdown", "name": "max_drawdown", "type": "builtins.float"}}, "peak_capital": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.peak_capital", "name": "peak_capital", "type": "builtins.float"}}, "position_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.position_value", "name": "position_value", "type": "builtins.float"}}, "run_backtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "market_data", "signal_generator", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.run_backtest", "name": "run_backtest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "market_data", "signal_generator", "symbol"], "arg_types": ["core.strategy.backtesting_engine.BacktestingEngine", {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_backtest of BacktestingEngine", "ret_type": "core.strategy.backtesting_engine.BacktestResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "trades": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "core.strategy.backtesting_engine.BacktestingEngine.trades", "name": "trades", "type": {".class": "Instance", "args": ["core.strategy.backtesting_engine.BacktestTrade"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.strategy.backtesting_engine.BacktestingEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.strategy.backtesting_engine.BacktestingEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TradeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.strategy.backtesting_engine.TradeType", "name": "TradeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "core.strategy.backtesting_engine.TradeType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "core.strategy.backtesting_engine", "mro": ["core.strategy.backtesting_engine.TradeType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.TradeType.BUY", "name": "BUY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "buy"}, "type_ref": "builtins.str"}}}, "SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.TradeType.SELL", "name": "SELL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sell"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.strategy.backtesting_engine.TradeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.strategy.backtesting_engine.TradeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.backtesting_engine.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "dates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.dates", "name": "dates", "type": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.strategy.backtesting_engine.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "test_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.strategy.backtesting_engine.test_data", "name": "test_data", "type": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": {".class": "AnyType", "missing_import_name": "core.strategy.backtesting_engine.pd", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\strategy\\backtesting_engine.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库管理系统
Database Management System

负责所有数据的持久化存储、查询和管理
"""

import logging
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import threading

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "database/trading.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"数据库管理器初始化完成: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 交易记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trades (
                        id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        type TEXT NOT NULL,
                        amount REAL NOT NULL,
                        price REAL,
                        filled_amount REAL DEFAULT 0,
                        average_price REAL DEFAULT 0,
                        status TEXT NOT NULL,
                        timestamp DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        strategy TEXT,
                        client_order_id TEXT,
                        exchange_order_id TEXT,
                        error_message TEXT,
                        profit_loss REAL DEFAULT 0,
                        commission REAL DEFAULT 0
                    )
                ''')
                
                # 市场数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS market_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        timestamp DATETIME NOT NULL,
                        open_price REAL NOT NULL,
                        high_price REAL NOT NULL,
                        low_price REAL NOT NULL,
                        close_price REAL NOT NULL,
                        volume REAL NOT NULL,
                        timeframe TEXT NOT NULL DEFAULT '1m'
                    )
                ''')
                
                # 账户余额历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS balance_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        total_balance REAL NOT NULL,
                        available_balance REAL NOT NULL,
                        frozen_balance REAL DEFAULT 0,
                        unrealized_pnl REAL DEFAULT 0,
                        total_equity REAL NOT NULL
                    )
                ''')
                
                # 持仓记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS positions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        size REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        current_price REAL NOT NULL,
                        unrealized_pnl REAL NOT NULL,
                        timestamp DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        strategy TEXT
                    )
                ''')
                
                # 风险事件表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS risk_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        risk_level TEXT NOT NULL,
                        event_type TEXT NOT NULL,
                        message TEXT NOT NULL,
                        metrics TEXT,
                        resolved BOOLEAN DEFAULT FALSE
                    )
                ''')
                
                # 系统日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        level TEXT NOT NULL,
                        module TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT
                    )
                ''')
                
                # 策略性能表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS strategy_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_name TEXT NOT NULL,
                        date DATE NOT NULL,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        losing_trades INTEGER DEFAULT 0,
                        total_pnl REAL DEFAULT 0,
                        max_drawdown REAL DEFAULT 0,
                        sharpe_ratio REAL DEFAULT 0,
                        win_rate REAL DEFAULT 0
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_balance_timestamp ON balance_history(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_risk_events_timestamp ON risk_events(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)')
                
                conn.commit()
                logger.info("数据库表结构初始化完成")
                
            except Exception as e:
                conn.rollback()
                logger.error(f"初始化数据库失败: {str(e)}")
                raise
            finally:
                conn.close()
    
    def save_trade(self, trade_data: Dict[str, Any]) -> bool:
        """
        保存交易记录
        
        Args:
            trade_data: 交易数据
            
        Returns:
            保存成功标志
        """
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO trades (
                        id, symbol, side, type, amount, price, filled_amount, 
                        average_price, status, timestamp, updated_at, strategy,
                        client_order_id, exchange_order_id, error_message,
                        profit_loss, commission
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    trade_data['id'],
                    trade_data['symbol'],
                    trade_data['side'],
                    trade_data['type'],
                    trade_data['amount'],
                    trade_data.get('price'),
                    trade_data.get('filled_amount', 0),
                    trade_data.get('average_price', 0),
                    trade_data['status'],
                    trade_data['timestamp'],
                    trade_data.get('updated_at', datetime.now()),
                    trade_data.get('strategy'),
                    trade_data.get('client_order_id'),
                    trade_data.get('exchange_order_id'),
                    trade_data.get('error_message'),
                    trade_data.get('profit_loss', 0),
                    trade_data.get('commission', 0)
                ))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            logger.error(f"保存交易记录失败: {str(e)}")
            return False
    
    def get_trade_history(self, symbol: Optional[str] = None, 
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取交易历史
        
        Args:
            symbol: 交易对过滤
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回数量限制
            
        Returns:
            交易记录列表
        """
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                query = "SELECT * FROM trades WHERE 1=1"
                params = []
                
                if symbol:
                    query += " AND symbol = ?"
                    params.append(symbol)
                
                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date)
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                trades = []
                for row in rows:
                    trade = dict(row)
                    # 转换时间戳
                    if trade['timestamp']:
                        trade['timestamp'] = datetime.fromisoformat(trade['timestamp'])
                    if trade['updated_at']:
                        trade['updated_at'] = datetime.fromisoformat(trade['updated_at'])
                    trades.append(trade)
                
                conn.close()
                return trades
                
        except Exception as e:
            logger.error(f"获取交易历史失败: {str(e)}")
            return []
    
    def save_market_data(self, market_data: Dict[str, Any]) -> bool:
        """
        保存市场数据
        
        Args:
            market_data: 市场数据
            
        Returns:
            保存成功标志
        """
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO market_data (
                        symbol, timestamp, open_price, high_price, low_price,
                        close_price, volume, timeframe
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    market_data['symbol'],
                    market_data['timestamp'],
                    market_data['open'],
                    market_data['high'],
                    market_data['low'],
                    market_data['close'],
                    market_data['volume'],
                    market_data.get('timeframe', '1m')
                ))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            logger.error(f"保存市场数据失败: {str(e)}")
            return False
    
    def get_market_data(self, symbol: str, timeframe: str = '1m',
                       start_date: Optional[datetime] = None,
                       end_date: Optional[datetime] = None,
                       limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取市场数据
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回数量限制
            
        Returns:
            市场数据列表
        """
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                query = "SELECT * FROM market_data WHERE symbol = ? AND timeframe = ?"
                params = [symbol, timeframe]
                
                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date)
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                data = []
                for row in rows:
                    item = dict(row)
                    if item['timestamp']:
                        item['timestamp'] = datetime.fromisoformat(item['timestamp'])
                    data.append(item)
                
                conn.close()
                return data
                
        except Exception as e:
            logger.error(f"获取市场数据失败: {str(e)}")
            return []
    
    def save_balance_history(self, balance_data: Dict[str, Any]) -> bool:
        """保存账户余额历史"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO balance_history (
                        timestamp, total_balance, available_balance,
                        frozen_balance, unrealized_pnl, total_equity
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    balance_data.get('timestamp', datetime.now()),
                    balance_data['total_balance'],
                    balance_data['available_balance'],
                    balance_data.get('frozen_balance', 0),
                    balance_data.get('unrealized_pnl', 0),
                    balance_data['total_equity']
                ))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            logger.error(f"保存余额历史失败: {str(e)}")
            return False
    
    def save_risk_event(self, event_data: Dict[str, Any]) -> bool:
        """保存风险事件"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO risk_events (
                        timestamp, risk_level, event_type, message, metrics
                    ) VALUES (?, ?, ?, ?, ?)
                ''', (
                    event_data.get('timestamp', datetime.now()),
                    event_data['risk_level'],
                    event_data['event_type'],
                    event_data['message'],
                    json.dumps(event_data.get('metrics', {}))
                ))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            logger.error(f"保存风险事件失败: {str(e)}")
            return False
    
    def get_performance_summary(self, start_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Args:
            start_date: 开始日期
            
        Returns:
            性能摘要数据
        """
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 基本查询条件
                where_clause = "WHERE status IN ('filled', 'partial')"
                params = []
                
                if start_date:
                    where_clause += " AND timestamp >= ?"
                    params.append(start_date)
                
                # 获取交易统计
                cursor.execute(f'''
                    SELECT 
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as winning_trades,
                        SUM(CASE WHEN profit_loss < 0 THEN 1 ELSE 0 END) as losing_trades,
                        SUM(profit_loss) as total_pnl,
                        AVG(profit_loss) as avg_pnl,
                        MAX(profit_loss) as max_profit,
                        MIN(profit_loss) as max_loss,
                        SUM(commission) as total_commission
                    FROM trades {where_clause}
                ''', params)
                
                trade_stats = cursor.fetchone()
                
                # 获取最新余额
                cursor.execute('''
                    SELECT total_balance, total_equity 
                    FROM balance_history 
                    ORDER BY timestamp DESC LIMIT 1
                ''')
                
                balance_data = cursor.fetchone()
                
                conn.close()
                
                # 计算性能指标
                total_trades = trade_stats[0] or 0
                winning_trades = trade_stats[1] or 0
                losing_trades = trade_stats[2] or 0
                total_pnl = trade_stats[3] or 0
                
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                
                return {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl,
                    'avg_pnl': trade_stats[4] or 0,
                    'max_profit': trade_stats[5] or 0,
                    'max_loss': trade_stats[6] or 0,
                    'total_commission': trade_stats[7] or 0,
                    'current_balance': balance_data[0] if balance_data else 0,
                    'current_equity': balance_data[1] if balance_data else 0
                }
                
        except Exception as e:
            logger.error(f"获取性能摘要失败: {str(e)}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留天数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 清理旧的市场数据
                cursor.execute('DELETE FROM market_data WHERE timestamp < ?', (cutoff_date,))
                
                # 清理旧的系统日志
                cursor.execute('DELETE FROM system_logs WHERE timestamp < ?', (cutoff_date,))
                
                # 清理已解决的风险事件
                cursor.execute('DELETE FROM risk_events WHERE timestamp < ? AND resolved = TRUE', (cutoff_date,))
                
                conn.commit()
                conn.close()
                
                logger.info(f"清理{days_to_keep}天前的旧数据完成")
                
        except Exception as e:
            logger.error(f"清理旧数据失败: {str(e)}")


# 全局数据库管理器实例
_database_manager_instance = None


def get_database_manager(db_path: str = "database/trading.db"):
    """获取数据库管理器实例（单例模式）"""
    global _database_manager_instance
    if _database_manager_instance is None:
        _database_manager_instance = DatabaseManager(db_path)
    return _database_manager_instance


if __name__ == "__main__":
    # 测试代码
    print("数据库管理系统模块加载完成")
    
    # 创建测试实例
    db = DatabaseManager("test.db")
    print("测试数据库创建成功")

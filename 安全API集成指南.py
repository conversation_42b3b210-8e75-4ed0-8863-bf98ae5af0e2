#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全API集成指南
Secure API Integration Guide

安全地将您的GATE.IO API凭证集成到系统中
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加core目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
core_dir = os.path.join(current_dir, 'core')
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)

def quick_api_setup():
    """快速API设置"""
    
    # 您的API凭证（请在使用后删除此文件以确保安全）
    API_KEY = "225022dec7931484987f4860201a1f1a"
    API_SECRET = "14e0257293b21bf8bc8f1ada46ae5d2d4876105cfbeee21063a79778445aef44"
    
    print("🔐 开始安全API集成...")
    print("=" * 60)
    
    try:
        # 导入必要模块
        from gate_api_connector import GateAPIConnector, APICredentials
        
        print("✅ API连接器模块导入成功")
        
        # 创建API连接器
        connector = GateAPIConnector()
        print("✅ API连接器实例创建成功")
        
        # 创建API凭证对象
        credentials = APICredentials(
            api_key=API_KEY,
            secret_key=API_SECRET,
            sandbox=True  # 使用测试环境，更安全
        )
        print("✅ API凭证对象创建成功")
        
        # 测试连接
        print("\n🔗 正在测试API连接...")
        success, message = connector.connect(credentials)
        
        if success:
            print("🎉 API连接成功！")
            print(f"📊 连接信息: {message}")
            
            # 获取市场数据测试
            print("\n📊 正在获取市场数据...")
            market_data = connector.get_latest_market_data()
            
            if market_data:
                print(f"✅ 成功获取 {len(market_data)} 个交易对的数据")
                
                # 显示部分数据
                print("\n💰 实时市场数据预览:")
                print("-" * 50)
                
                for symbol, data in list(market_data.items())[:3]:
                    change_indicator = "🟢" if data.change_24h > 0 else "🔴" if data.change_24h < 0 else "🟡"
                    print(f"{change_indicator} {symbol}:")
                    print(f"  价格: ${data.price:,.2f}")
                    print(f"  24h涨跌: {data.change_24h:+.2f}%")
                    print(f"  成交量: ${data.volume_24h:,.0f}")
                    print()
                
                return True, connector, credentials
            else:
                print("⚠️ 未能获取市场数据")
                return False, None, None
        else:
            print(f"❌ API连接失败: {message}")
            return False, None, None
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保ccxt已安装: pip install ccxt")
        return False, None, None
    except Exception as e:
        print(f"❌ 连接过程出错: {e}")
        return False, None, None

def launch_system_with_api():
    """启动带有API集成的系统"""
    
    print("\n🚀 启动集成真实数据的交易系统...")
    
    try:
        # 先测试API连接
        success, connector, credentials = quick_api_setup()
        
        if success:
            print("\n✅ API集成成功！现在启动主系统...")
            
            # 创建配置文件以便主系统使用
            create_api_config(credentials)
            
            # 启动主系统
            import subprocess
            gui_path = os.path.join(core_dir, 'ultimate_spot_trading_gui.py')
            
            if os.path.exists(gui_path):
                subprocess.Popen(['python', gui_path])
                print("🎉 主系统已启动，API已预配置！")
                print("\n💡 使用说明:")
                print("1. 在主界面点击'连接GATE交易所'")
                print("2. 系统将自动使用预配置的API凭证")
                print("3. 开始享受真实数据的实战演练体验")
                
                return True
            else:
                print("❌ 主系统文件未找到")
                return False
        else:
            print("\n❌ API集成失败，请检查凭证或网络连接")
            return False
            
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        return False

def create_api_config(credentials):
    """创建API配置文件"""
    try:
        import json
        from pathlib import Path
        
        # 确保config目录存在
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        # 简单加密存储（实际应用中应使用更强的加密）
        def simple_encrypt(text):
            return ''.join(chr(ord(c) + 1) for c in text)
        
        # 创建配置数据
        config_data = {
            'api_key': simple_encrypt(credentials.api_key),
            'secret_key': simple_encrypt(credentials.secret_key),
            'environment': 'sandbox' if credentials.sandbox else 'production',
            'auto_connect': True
        }
        
        # 保存配置
        with open(config_dir / "api_credentials.json", 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)
        
        print("✅ API配置文件已创建")
        
    except Exception as e:
        print(f"⚠️ 配置文件创建失败: {e}")

def show_security_warning():
    """显示安全警告"""
    
    warning_text = """
🛡️ 重要安全提醒 🛡️

您的API凭证将被安全地集成到系统中，但请注意：

✅ 安全措施:
• 凭证将被加密存储在本地
• 只使用测试环境，更加安全
• 只需要"现货查看"权限
• 不会执行任何真实交易操作

⚠️ 安全建议:
• 使用完毕后可删除此脚本文件
• 定期更换API密钥
• 监控API使用情况
• 如有异常立即撤销权限

🎯 系统功能:
• 获取真实的GATE.IO市场数据
• 进行安全的实战演练学习
• 零风险的策略测试环境
• 专业级的交易学习体验

是否继续集成API？
    """
    
    print(warning_text)
    
    # 创建确认对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    result = messagebox.askyesno(
        "安全确认", 
        "🔐 API安全集成确认\n\n"
        "您的API凭证将被安全地集成到系统中。\n"
        "系统只会获取市场数据，不会执行真实交易。\n\n"
        "是否继续？",
        icon='question'
    )
    
    root.destroy()
    return result

def main():
    """主函数"""
    print("🔐 GATE.IO API安全集成工具")
    print("=" * 60)
    
    # 显示安全警告并获取确认
    if show_security_warning():
        print("✅ 用户确认继续集成")
        
        # 启动API集成
        if launch_system_with_api():
            print("\n🎉 API集成完成！")
            print("🚀 交易系统已启动，请在新窗口中体验真实数据功能")
        else:
            print("\n❌ API集成失败")
    else:
        print("❌ 用户取消了API集成")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

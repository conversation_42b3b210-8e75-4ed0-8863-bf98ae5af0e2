#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户界面模块
User Interface Module

包含图表组件、界面组件等功能
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要类和函数
try:
    from .charts.candlestick_chart import CandlestickChart
    from .theme_manager import ThemeManager, get_theme_manager, get_color, get_font

    __all__ = [
        'CandlestickChart',
        'ThemeManager',
        'get_theme_manager',
        'get_color',
        'get_font'
    ]

except ImportError as e:
    print(f"界面系统模块导入警告: {e}")
    __all__ = []

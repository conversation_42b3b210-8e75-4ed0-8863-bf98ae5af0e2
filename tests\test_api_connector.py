#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API连接器测试
API Connector Tests

全面测试API连接器功能
"""

import unittest
import sys
import os
import time
from unittest.mock import patch, MagicMock
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.gate_api_connector import (
    GateAPIConnector, 
    APICredentials, 
    MarketData,
    get_api_connector
)

class TestAPIConnector(unittest.TestCase):
    """API连接器测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建测试凭证
        self.test_credentials = APICredentials(
            api_key="test_api_key",
            secret_key="test_secret_key",
            sandbox=True
        )
        
        # 创建测试连接器
        self.connector = GateAPIConnector()

    def tearDown(self):
        """测试后清理"""
        # 断开连接
        if hasattr(self, 'connector') and self.connector:
            self.connector.disconnect()

    def test_singleton_pattern(self):
        """测试单例模式"""
        # 获取两个实例
        connector1 = get_api_connector()
        connector2 = get_api_connector()
        
        # 验证是同一个实例
        self.assertIs(connector1, connector2, "单例模式失败，获取了不同的实例")

    @patch('ccxt.gateio')
    def test_set_credentials(self, mock_gateio):
        """测试设置API凭证"""
        # 设置模拟对象
        mock_exchange = MagicMock()
        mock_gateio.return_value = mock_exchange
        
        # 调用测试方法
        result = self.connector.set_credentials(self.test_credentials)
        
        # 验证结果
        self.assertTrue(result, "设置凭证应该返回True")
        self.assertEqual(self.connector.credentials, self.test_credentials, "凭证应该被正确设置")
        
        # 验证ccxt.gateio被正确调用
        mock_gateio.assert_called_once()
        
        # 验证参数
        args, kwargs = mock_gateio.call_args
        self.assertEqual(kwargs['apiKey'], "test_api_key")
        self.assertEqual(kwargs['secret'], "test_secret_key")
        self.assertTrue(kwargs['sandbox'])

    @patch('ccxt.gateio')
    def test_connect(self, mock_gateio):
        """测试连接方法"""
        # 设置模拟对象
        mock_exchange = MagicMock()
        mock_exchange.load_markets.return_value = {"BTC/USDT": {}, "ETH/USDT": {}}
        mock_gateio.return_value = mock_exchange
        
        # 调用测试方法
        success, message = self.connector.connect(self.test_credentials)
        
        # 验证结果
        self.assertTrue(success, f"连接应该成功，但返回: {message}")
        self.assertTrue(self.connector.is_connected, "连接状态应该为True")
        
        # 验证方法调用
        mock_exchange.load_markets.assert_called_once()

    @patch('ccxt.gateio')
    def test_connect_failure(self, mock_gateio):
        """测试连接失败情况"""
        # 设置模拟对象抛出异常
        mock_exchange = MagicMock()
        mock_exchange.load_markets.side_effect = Exception("测试连接失败")
        mock_gateio.return_value = mock_exchange
        
        # 调用测试方法
        success, message = self.connector.connect(self.test_credentials)
        
        # 验证结果
        self.assertFalse(success, "连接应该失败")
        self.assertIn("连接测试失败", message, "错误消息应该包含异常信息")
        self.assertFalse(self.connector.is_connected, "连接状态应该为False")

    @patch('ccxt.gateio')
    def test_disconnect(self, mock_gateio):
        """测试断开连接"""
        # 设置模拟对象
        mock_exchange = MagicMock()
        mock_gateio.return_value = mock_exchange
        
        # 先连接
        self.connector.set_credentials(self.test_credentials)
        self.connector.is_connected = True
        
        # 调用测试方法
        self.connector.disconnect()
        
        # 验证结果
        self.assertFalse(self.connector.is_connected, "断开连接后状态应该为False")
        mock_exchange.close.assert_called_once()

    @patch('ccxt.gateio')
    def test_get_symbol_price(self, mock_gateio):
        """测试获取交易对价格"""
        # 设置模拟数据
        self.connector.market_data_cache = {
            "BTC/USDT": MarketData(
                symbol="BTC/USDT",
                price=50000.0,
                volume_24h=1000.0,
                change_24h=0.05,
                high_24h=51000.0,
                low_24h=49000.0,
                timestamp=time.time()
            )
        }
        
        # 调用测试方法
        price = self.connector.get_symbol_price("BTC/USDT")
        
        # 验证结果
        self.assertEqual(price, 50000.0, "应该返回正确的价格")
        
        # 测试不存在的交易对
        price = self.connector.get_symbol_price("NONEXISTENT/USDT")
        self.assertIsNone(price, "不存在的交易对应该返回None")

    @patch('ccxt.gateio')
    def test_auto_connect_if_available(self, mock_gateio):
        """测试自动连接功能"""
        # 设置模拟对象
        mock_exchange = MagicMock()
        mock_exchange.load_markets.return_value = {"BTC/USDT": {}}
        mock_gateio.return_value = mock_exchange
        
        # 设置自动凭证
        self.connector.auto_credentials = self.test_credentials
        
        # 调用测试方法
        result = self.connector.auto_connect_if_available()
        
        # 验证结果
        self.assertTrue(result, "应该自动连接成功")
        self.assertTrue(self.connector.is_connected, "连接状态应该为True")
        
        # 测试无自动凭证的情况
        self.connector.is_connected = False
        self.connector.auto_credentials = None
        result = self.connector.auto_connect_if_available()
        self.assertFalse(result, "无自动凭证时应该返回False")

if __name__ == '__main__':
    unittest.main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动终极版现货交易系统
Launch Ultimate Spot Trading System

专业级现货交易学习和实战系统启动器
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印系统横幅"""
    print("🏦" + "=" * 58 + "🏦")
    print("🏦                                                        🏦")
    print("🏦     终极版现货交易系统 - 专业学习平台                🏦")
    print("🏦     Ultimate Spot Trading System                      🏦")
    print("🏦                                                        🏦")
    print("🏦     版本: 2.0.0 (重命名版)                           🏦")
    print("🏦     数据源: GATE.IO 真实现货数据                       🏦")
    print("🏦                                                        🏦")
    print("🏦" + "=" * 58 + "🏦")

def check_system_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    issues = []
    
    # 检查核心文件
    required_files = [
        'core/ultimate_spot_trading_gui.py',
        'core/gate_api_connector.py',
        'core/api_login_dialog.py'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            issues.append(f"缺少核心文件: {file_path}")
        else:
            print(f"✅ {Path(file_path).name}")
    
    # 检查Python模块
    modules = [
        ('ccxt', 'GATE.IO API连接'),
        ('tkinter', 'GUI界面'),
        ('threading', '多线程支持'),
        ('datetime', '时间处理')
    ]
    
    for module, desc in modules:
        try:
            __import__(module)
            print(f"✅ {module}: {desc}")
        except ImportError:
            issues.append(f"缺少模块: {module} ({desc})")
    
    return issues

def show_system_info():
    """显示系统信息"""
    print("\n🏦 终极版现货交易系统特点:")
    print("-" * 50)
    print("📊 数据源:")
    print("  • GATE.IO 真实现货数据")
    print("  • 实时价格更新")
    print("  • 完整的K线数据")
    print("  • 真实的市场深度")
    
    print("\n💎 专业特性:")
    print("  • 专业级学习平台")
    print("  • 实战演练环境")
    print("  • 风险控制机制")
    print("  • 策略回测功能")
    
    print("\n🛡️ 安全机制:")
    print("  • 完整的风险警告")
    print("  • 教育目的明确")
    print("  • 无真实交易风险")
    print("  • 专业指导建议")
    
    print("\n🎯 学习目标:")
    print("  • 交易策略学习")
    print("  • 风险管理理解")
    print("  • 市场分析技能")
    print("  • 专业交易思维")

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择操作:")
    print("1. 🚀 启动终极版现货交易系统")
    print("2. 🔧 测试API连接")
    print("3. 📊 查看系统状态")
    print("4. 📖 查看使用指南")
    print("5. 🔍 查看审查报告")
    print("6. ❌ 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-6): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6']:
                return choice
            else:
                print("❌ 无效选择，请输入1-6")
        except KeyboardInterrupt:
            return '6'

def launch_ultimate_system():
    """启动终极版系统"""
    print("\n🚀 启动终极版现货交易系统...")
    
    try:
        gui_file = Path('core/ultimate_spot_trading_gui.py')
        if gui_file.exists():
            subprocess.Popen([sys.executable, str(gui_file)])
            print("✅ 终极版现货交易系统已启动")
            print("\n💡 系统特点:")
            print("  • 专业级界面设计")
            print("  • 真实数据连接")
            print("  • 安全学习环境")
            print("  • 完整风险控制")
            return True
        else:
            print("❌ 系统文件不存在")
            return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🔧 测试API连接...")
    
    try:
        # 测试ccxt模块
        import ccxt
        print("✅ CCXT库可用")
        
        # 创建GATE交易所实例
        exchange = ccxt.gateio({'sandbox': True})
        print("✅ GATE交易所实例创建成功")
        
        # 测试市场数据获取
        markets = exchange.load_markets()
        print(f"✅ 成功加载 {len(markets)} 个交易对")
        
        # 测试获取BTC价格
        if 'BTC/USDT' in markets:
            ticker = exchange.fetch_ticker('BTC/USDT')
            price = ticker['last']
            print(f"✅ BTC/USDT 当前价格: ${price:,.2f}")
        
        print("\n🎉 API连接测试成功！")
        return True
        
    except ImportError:
        print("❌ CCXT库未安装，请运行: pip install ccxt")
        return False
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def show_system_status():
    """显示系统状态"""
    print("\n📊 系统状态检查:")
    print("-" * 40)
    
    # 检查文件状态
    files_status = {
        '终极版GUI': 'core/ultimate_spot_trading_gui.py',
        'API连接器': 'core/gate_api_connector.py',
        'API登录': 'core/api_login_dialog.py',
        '倍增引擎': 'core/doubling_growth_engine.py',
        '快速盈利': 'core/fast_profit_engine.py'
    }
    
    for name, path in files_status.items():
        if Path(path).exists():
            print(f"✅ {name}: 已就绪")
        else:
            print(f"❌ {name}: 缺失")
    
    # 检查依赖库
    print("\n📦 依赖库状态:")
    dependencies = ['ccxt', 'tkinter', 'threading', 'datetime']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安装")
        except ImportError:
            print(f"❌ {dep}: 未安装")
    
    # 检查配置
    print("\n⚙️ 配置状态:")
    config_files = [
        'config/api_credentials.json',
        'config/api_setup.env'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {Path(config_file).name}: 存在")
        else:
            print(f"⚠️ {Path(config_file).name}: 不存在")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 终极版现货交易系统使用指南:")
    print("=" * 50)
    
    guide = """
🎯 系统目标:
  • 提供专业级的现货交易学习平台
  • 基于真实GATE.IO数据进行实战演练
  • 帮助用户掌握交易策略和风险管理

🚀 快速开始:
  1. 启动系统: 选择菜单选项1
  2. 连接API: 点击"连接GATE交易所"
  3. 输入凭证: 使用您的GATE.IO API Key
  4. 开始学习: 观察真实数据，练习交易策略

🔑 API设置:
  1. 访问 https://www.gate.io/myaccount/apiv4keys
  2. 创建API Key，权限选择：现货交易 + 查看
  3. 复制API Key和Secret Key
  4. 在系统中输入凭证

⚠️ 重要提醒:
  • 这是学习和演练系统
  • 不会执行真实交易
  • 所有操作都是虚拟的
  • 请勿将此作为投资建议

💡 学习建议:
  • 从小额资金开始练习
  • 重点学习风险管理
  • 观察市场趋势和模式
  • 制定并测试交易策略
    """
    
    print(guide)

def show_review_report():
    """显示审查报告"""
    print("\n🔍 查看系统审查报告...")
    
    report_file = Path("终极版现货交易系统_深度审查报告.md")
    if report_file.exists():
        print(f"✅ 审查报告位置: {report_file}")
        print("\n📊 审查摘要:")
        print("  • 系统已完成重命名")
        print("  • 发现并记录了代码质量问题")
        print("  • 移除了虚假承诺")
        print("  • 添加了风险警告")
        
        try:
            # 尝试打开报告文件
            os.startfile(str(report_file))
            print("✅ 审查报告已在默认程序中打开")
        except:
            print("💡 请手动打开审查报告文件查看详情")
    else:
        print("❌ 审查报告文件不存在")

def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    issues = check_system_requirements()
    if issues:
        print("\n❌ 发现问题:")
        for issue in issues:
            print(f"  • {issue}")
        print("\n💡 请解决上述问题后重新启动")
        input("\n按回车键退出...")
        return
    
    # 显示系统信息
    show_system_info()
    
    # 主循环
    while True:
        choice = show_menu()
        
        if choice == '1':
            launch_ultimate_system()
        elif choice == '2':
            test_api_connection()
        elif choice == '3':
            show_system_status()
        elif choice == '4':
            show_usage_guide()
        elif choice == '5':
            show_review_report()
        elif choice == '6':
            print("\n👋 感谢使用终极版现货交易系统！")
            break
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序运行错误: {e}")
        input("\n按回车键退出...")

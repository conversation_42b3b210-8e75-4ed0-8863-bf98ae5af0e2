#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整交易系统GUI - 包含策略和资金
Complete Trading System GUI - With Strategies and Capital
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
import random

class TradingSystemGUI:
    """完整交易系统GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 终极版交易系统 - 完整版")
        self.root.geometry("900x700")

        # 交易配置
        self.config = {
            'initial_capital': 1000.0,
            'current_balance': 1000.0,
            'daily_pnl': 0.0,
            'total_pnl': 0.0
        }

        # 策略配置
        self.strategies = {
            '突破策略': {'allocation': 40, 'pnl': 15.68, 'trades': 12, 'win_rate': 67, 'status': '运行中'},
            '网格策略': {'allocation': 35, 'pnl': 23.46, 'trades': 28, 'win_rate': 83, 'status': '运行中'},
            '动量策略': {'allocation': 25, 'pnl': -4.52, 'trades': 8, 'win_rate': 61, 'status': '运行中'}
        }

        # 交易对数据
        self.pairs = {
            'BTC/USDT': {'price': 107951.00, 'change': -0.44},
            'ETH/USDT': {'price': 2508.35, 'change': -1.77},
            'SOL/USDT': {'price': 245.78, 'change': 1.56}
        }

        self.create_widgets()
        self.start_updates()

    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="🚀 终极版交易系统 - 完整版",
            font=('Arial', 18, 'bold'),
            fg='blue'
        )
        title_label.pack(pady=10)

        # 资金状态
        self.create_balance_section()

        # 策略状态
        self.create_strategy_section()

        # 价格监控
        self.create_price_section()

        # 控制按钮
        self.create_control_section()

        # 日志
        self.create_log_section()

    def create_balance_section(self):
        """创建资金状态区域"""
        balance_frame = tk.LabelFrame(self.root, text="💰 资金状态", font=('Arial', 12, 'bold'))
        balance_frame.pack(fill='x', padx=20, pady=5)

        info_frame = tk.Frame(balance_frame)
        info_frame.pack(fill='x', padx=10, pady=5)

        self.balance_label = tk.Label(
            info_frame,
            text=f"总资金: {self.config['current_balance']:,.2f} USDT",
            font=('Arial', 12, 'bold'),
            fg='blue'
        )
        self.balance_label.pack(side='left')

        self.pnl_label = tk.Label(
            info_frame,
            text=f"当日盈亏: {self.config['daily_pnl']:+.2f} USDT",
            font=('Arial', 12, 'bold'),
            fg='green' if self.config['daily_pnl'] >= 0 else 'red'
        )
        self.pnl_label.pack(side='right')

    def create_strategy_section(self):
        """创建策略状态区域"""
        self.strategy_frame = tk.LabelFrame(self.root, text="🎯 策略状态", font=('Arial', 12, 'bold'))
        self.strategy_frame.pack(fill='x', padx=20, pady=5)

        # 创建策略显示区域
        self.strategy_display_frame = tk.Frame(self.strategy_frame)
        self.strategy_display_frame.pack(fill='x', padx=10, pady=5)

        # 初始化策略显示
        self.update_strategy_display()

    def update_strategy_display(self):
        """动态更新策略显示"""
        # 清除旧显示
        for widget in self.strategy_display_frame.winfo_children():
            widget.destroy()

        # 重新创建策略显示
        for name, data in self.strategies.items():
            s_frame = tk.Frame(self.strategy_display_frame)
            s_frame.pack(fill='x', padx=5, pady=3)

            tk.Label(s_frame, text=f"🎯 {name}", font=('Arial', 10, 'bold')).pack(side='left')
            tk.Label(s_frame, text=f"资金: {data['allocation']}%", font=('Arial', 9)).pack(side='left', padx=10)
            tk.Label(s_frame, text=f"盈亏: {data['pnl']:+.2f}", font=('Arial', 9),
                    fg='green' if data['pnl'] >= 0 else 'red').pack(side='left', padx=10)
            tk.Label(s_frame, text=f"交易: {data['trades']}次", font=('Arial', 9)).pack(side='left', padx=10)

            # 动态胜率颜色
            win_rate_color = 'green' if data['win_rate'] >= 70 else 'orange' if data['win_rate'] >= 60 else 'red'
            tk.Label(s_frame, text=f"胜率: {data['win_rate']}%", font=('Arial', 9),
                    fg=win_rate_color).pack(side='left', padx=10)

            # 动态状态颜色
            status_color = 'green' if data['status'] == '运行中' else 'red'
            tk.Label(s_frame, text=f"状态: {data['status']}", font=('Arial', 9),
                    fg=status_color).pack(side='right')

    def create_price_section(self):
        """创建价格监控区域"""
        price_frame = tk.LabelFrame(self.root, text="📊 价格监控", font=('Arial', 12, 'bold'))
        price_frame.pack(fill='both', expand=True, padx=20, pady=5)

        # 表头
        header_frame = tk.Frame(price_frame)
        header_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(header_frame, text="交易对", font=('Arial', 11, 'bold'), width=15).pack(side='left')
        tk.Label(header_frame, text="价格", font=('Arial', 11, 'bold'), width=15).pack(side='left')
        tk.Label(header_frame, text="24h变化", font=('Arial', 11, 'bold'), width=15).pack(side='left')

        # 价格显示区域
        self.price_display_frame = tk.Frame(price_frame)
        self.price_display_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.update_price_display()

    def create_control_section(self):
        """创建控制按钮区域"""
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=10)

        tk.Button(control_frame, text="💰 资金配置", command=self.show_capital_config,
                 font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(control_frame, text="🎯 策略管理", command=self.show_strategy_config,
                 font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(control_frame, text="🔄 刷新数据", command=self.refresh_data,
                 font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(control_frame, text="📊 交易报告", command=self.show_report,
                 font=('Arial', 10)).pack(side='left', padx=5)

    def create_log_section(self):
        """创建日志区域"""
        log_frame = tk.LabelFrame(self.root, text="📝 系统日志", font=('Arial', 10, 'bold'))
        log_frame.pack(fill='x', padx=20, pady=5)

        self.log_text = tk.Text(log_frame, height=6, font=('Consolas', 9))
        self.log_text.pack(fill='x', padx=5, pady=5)

        # 添加初始日志
        self.add_log("🚀 终极版交易系统启动成功")
        self.add_log("💰 初始资金: 1,000 USDT")
        self.add_log("🎯 3个策略已激活")

    def update_price_display(self):
        """更新价格显示"""
        # 清除旧显示
        for widget in self.price_display_frame.winfo_children():
            widget.destroy()

        # 显示价格
        for symbol, data in self.pairs.items():
            row_frame = tk.Frame(self.price_display_frame)
            row_frame.pack(fill='x', pady=2)

            tk.Label(row_frame, text=symbol, font=('Arial', 10), width=15, anchor='w').pack(side='left')
            tk.Label(row_frame, text=f"{data['price']:,.2f}", font=('Arial', 10), width=15, anchor='e').pack(side='left')

            change = data['change']
            color = 'green' if change >= 0 else 'red'
            tk.Label(row_frame, text=f"{change:+.2f}%", font=('Arial', 10, 'bold'),
                    width=15, fg=color, anchor='center').pack(side='left')

    def update_balance_display(self):
        """更新余额显示"""
        self.balance_label.config(text=f"总资金: {self.config['current_balance']:,.2f} USDT")
        self.pnl_label.config(
            text=f"当日盈亏: {self.config['daily_pnl']:+.2f} USDT",
            fg='green' if self.config['daily_pnl'] >= 0 else 'red'
        )

    def show_capital_config(self):
        """显示资金配置"""
        config_window = tk.Toplevel(self.root)
        config_window.title("💰 资金配置")
        config_window.geometry("350x250")

        tk.Label(config_window, text="💰 资金配置", font=('Arial', 14, 'bold')).pack(pady=10)

        # 当前状态
        status_frame = tk.LabelFrame(config_window, text="当前状态")
        status_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(status_frame, text=f"初始资金: {self.config['initial_capital']:,.2f} USDT").pack(anchor='w', padx=10, pady=2)
        tk.Label(status_frame, text=f"当前余额: {self.config['current_balance']:,.2f} USDT").pack(anchor='w', padx=10, pady=2)
        tk.Label(status_frame, text=f"总盈亏: {self.config['total_pnl']:+.2f} USDT").pack(anchor='w', padx=10, pady=2)

        # 配置
        config_frame = tk.LabelFrame(config_window, text="重置资金")
        config_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(config_frame, text="新的初始资金:").pack(padx=10, pady=2)
        capital_var = tk.StringVar(value="1000")
        tk.Entry(config_frame, textvariable=capital_var, width=20).pack(padx=10, pady=5)

        def reset_capital():
            try:
                new_capital = float(capital_var.get())
                self.config['initial_capital'] = new_capital
                self.config['current_balance'] = new_capital
                self.config['daily_pnl'] = 0.0
                self.config['total_pnl'] = 0.0
                self.update_balance_display()
                config_window.destroy()
                self.add_log(f"💰 资金已重置为 {new_capital:,.2f} USDT")
            except ValueError:
                messagebox.showerror("错误", "请输入有效数字")

        tk.Button(config_frame, text="💾 重置资金", command=reset_capital).pack(pady=10)

        self.add_log("💰 打开资金配置")

    def show_strategy_config(self):
        """显示策略管理"""
        strategy_window = tk.Toplevel(self.root)
        strategy_window.title("🎯 策略管理")
        strategy_window.geometry("450x350")

        tk.Label(strategy_window, text="🎯 策略管理", font=('Arial', 14, 'bold')).pack(pady=10)

        # 策略状态
        for name, data in self.strategies.items():
            frame = tk.LabelFrame(strategy_window, text=name)
            frame.pack(fill='x', padx=20, pady=5)

            info_text = f"资金分配: {data['allocation']}% | 盈亏: {data['pnl']:+.2f} USDT | 交易: {data['trades']}次 | 胜率: {data['win_rate']}%"
            tk.Label(frame, text=info_text).pack(padx=10, pady=5)

        tk.Button(strategy_window, text="✅ 策略运行正常", state='disabled').pack(pady=20)

        self.add_log("🎯 打开策略管理")

    def show_report(self):
        """显示交易报告"""
        report_window = tk.Toplevel(self.root)
        report_window.title("📊 交易报告")
        report_window.geometry("400x300")

        tk.Label(report_window, text="📊 交易报告", font=('Arial', 14, 'bold')).pack(pady=10)

        report_text = f"""
📈 总体表现:
• 初始资金: {self.config['initial_capital']:,.2f} USDT
• 当前余额: {self.config['current_balance']:,.2f} USDT
• 总盈亏: {self.config['total_pnl']:+.2f} USDT
• 收益率: {(self.config['total_pnl']/self.config['initial_capital']*100):+.2f}%

🎯 策略表现:
"""

        for name, data in self.strategies.items():
            report_text += f"• {name}: {data['pnl']:+.2f} USDT ({data['win_rate']}%胜率)\n"

        text_widget = tk.Text(report_window, font=('Consolas', 10))
        text_widget.pack(fill='both', expand=True, padx=20, pady=10)
        text_widget.insert('1.0', report_text)
        text_widget.config(state='disabled')

        self.add_log("📊 查看交易报告")

    def refresh_data(self):
        """刷新数据"""
        self.simulate_price_changes()
        self.simulate_trading()
        self.update_price_display()
        self.update_balance_display()
        self.add_log("🔄 数据已刷新")

    def simulate_price_changes(self):
        """模拟价格变化"""
        for symbol in self.pairs:
            change = random.uniform(-0.5, 0.5)
            self.pairs[symbol]['change'] += change

            if self.pairs[symbol]['change'] > 5:
                self.pairs[symbol]['change'] = 5
            elif self.pairs[symbol]['change'] < -5:
                self.pairs[symbol]['change'] = -5

            base_price = self.pairs[symbol]['price']
            self.pairs[symbol]['price'] = base_price * (1 + change / 100)

    def simulate_trading(self):
        """实战演练"""
        for name, data in self.strategies.items():
            if random.random() < 0.15:  # 15%概率交易
                allocation = data['allocation'] / 100
                capital = self.config['current_balance'] * allocation

                if random.random() < data['win_rate'] / 100:
                    # 盈利
                    profit = capital * random.uniform(0.005, 0.02)
                    data['pnl'] += profit
                    self.config['daily_pnl'] += profit
                    self.config['current_balance'] += profit
                    self.add_log(f"💰 {name} 盈利 +{profit:.2f} USDT")
                else:
                    # 亏损
                    loss = capital * random.uniform(0.003, 0.015)
                    data['pnl'] -= loss
                    self.config['daily_pnl'] -= loss
                    self.config['current_balance'] -= loss
                    self.add_log(f"📉 {name} 亏损 -{loss:.2f} USDT")

                data['trades'] += 1

        self.config['total_pnl'] = self.config['current_balance'] - self.config['initial_capital']

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert('end', log_entry)
        self.log_text.see('end')

        lines = self.log_text.get('1.0', 'end').split('\n')
        if len(lines) > 30:
            self.log_text.delete('1.0', '5.0')

    def start_updates(self):
        """启动自动更新"""
        def update_loop():
            while True:
                try:
                    self.simulate_price_changes()
                    self.simulate_trading()

                    self.root.after(0, self.update_price_display)
                    self.root.after(0, self.update_balance_display)
                    self.root.after(0, self.update_strategy_display)

                    if random.random() < 0.1:
                        messages = ["📈 市场波动", "🎯 策略执行", "💰 盈利机会", "📊 数据更新"]
                        self.root.after(0, lambda: self.add_log(random.choice(messages)))

                    time.sleep(10)
                except Exception as e:
                    print(f"更新错误: {e}")
                    time.sleep(30)

        threading.Thread(target=update_loop, daemon=True).start()

    def run(self):
        """运行GUI"""
        print("🚀 启动完整交易系统GUI...")
        self.root.mainloop()

def main():
    app = TradingSystemGUI()
    app.run()

if __name__ == "__main__":
    main()

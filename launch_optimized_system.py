#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动优化版企业级现货交易系统
Launch Optimized Enterprise Spot Trading System
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'numpy', 'pandas', 'psutil', 'ccxt'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动优化版企业级现货交易系统...")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按Enter键退出...")
        return
    
    try:
        # 导入主要模块
        from ultimate_trading_gui import UltimateTradingGUI
        from system_integration import OptimizedSystemFactory
        
        print("✅ 模块导入成功")
        
        # 创建优化版交易系统
        print("🔧 初始化优化系统...")
        
        # 使用工厂模式创建集成系统
        app = OptimizedSystemFactory.create_integrated_system(UltimateTradingGUI)
        
        print("✅ 优化系统初始化完成")
        print("\n🎯 系统功能:")
        print("   📈 智能策略优化")
        print("   🧪 自动化测试框架")
        print("   💡 智能提示系统")
        print("   🎨 用户体验增强")
        print("   📊 实时性能监控")
        print("   🔒 企业级安全框架")
        
        # 显示集成报告
        report = OptimizedSystemFactory.get_optimization_report()
        print(f"\n📋 集成状态: {report['status']}")
        print(f"   版本: 企业级现货交易系统 v2.0")
        print(f"   集成模块: {len(report['integrated_modules'])} 个")
        print(f"   新增功能: {len(report['features_added'])} 项")
        
        print("\n🚀 启动交易系统GUI...")
        print("=" * 60)
        
        # 运行应用
        app.run()
        
    except ImportError as e:
        logger.error(f"模块导入失败: {e}")
        messagebox.showerror(
            "启动失败", 
            f"系统模块导入失败:\n{e}\n\n请检查系统文件完整性"
        )
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        messagebox.showerror(
            "系统错误", 
            f"系统启动时发生错误:\n{e}\n\n请查看日志文件获取详细信息"
        )
    
    print("\n系统已关闭")

if __name__ == "__main__":
    main()

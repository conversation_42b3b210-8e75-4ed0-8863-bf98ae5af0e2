{"data_mtime": 1748505054, "dep_lines": [9, 9, 8, 10, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tkinter.ttk", "tkinter.messagebox", "tkinter", "datetime", "builtins", "_frozen_importlib", "_tkinter", "_typeshed", "abc", "tkinter.font", "typing"], "hash": "eaad2a3f20120ef667c2feaae939c224224f3bc2", "id": "test_api_dialog", "ignore_all": false, "interface_hash": "4cd7cc68531a8184694793a0ba21b2d1e20d947c", "mtime": 1748505053, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\test_api_dialog.py", "plugin_data": null, "size": 11149, "suppressed": [], "version_id": "1.15.0"}
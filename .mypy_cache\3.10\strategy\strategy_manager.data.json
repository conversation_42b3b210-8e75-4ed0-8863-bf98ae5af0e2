{".class": "MypyFile", "_fullname": "strategy.strategy_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BacktestResult": {".class": "SymbolTableNode", "cross_ref": "strategy.backtesting_engine.BacktestResult", "kind": "Gdef"}, "BacktestingEngine": {".class": "SymbolTableNode", "cross_ref": "strategy.backtesting_engine.BacktestingEngine", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SignalGenerator": {".class": "SymbolTableNode", "cross_ref": "strategy.signal_generator.SignalGenerator", "kind": "Gdef"}, "StrategyConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.strategy_manager.StrategyConfig", "name": "StrategyConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "enabled", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "timeframe", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "signal_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "risk_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "strategy.strategy_manager", "mro": ["strategy.strategy_manager.StrategyConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "strategy.strategy_manager.StrategyConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "enabled", "symbols", "timeframe", "signal_config", "risk_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "enabled", "symbols", "timeframe", "signal_config", "risk_config"], "arg_types": ["strategy.strategy_manager.StrategyConfig", "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StrategyConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "strategy.strategy_manager.StrategyConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "enabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symbols"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timeframe"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "signal_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "risk_config"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyConfig.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["strategy.strategy_manager.StrategyConfig"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of StrategyConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["name", "enabled", "symbols", "timeframe", "signal_config", "risk_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.strategy_manager.StrategyConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["name", "enabled", "symbols", "timeframe", "signal_config", "risk_config"], "arg_types": ["builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "strategy.strategy_manager.StrategyConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["name", "enabled", "symbols", "timeframe", "signal_config", "risk_config"], "arg_types": ["builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyConfig.__post_init__", "name": "__post_init__", "type": null}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyConfig.enabled", "name": "enabled", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.strategy_manager.StrategyConfig.name", "name": "name", "type": "builtins.str"}}, "risk_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyConfig.risk_config", "name": "risk_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "signal_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyConfig.signal_config", "name": "signal_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyConfig.symbols", "name": "symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "timeframe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyConfig.timeframe", "name": "timeframe", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.strategy_manager.StrategyConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.strategy_manager.StrategyConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrategyManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.strategy_manager.StrategyManager", "name": "StrategyManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "strategy.strategy_manager", "mro": ["strategy.strategy_manager.StrategyManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "database_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.__init__", "name": "__init__", "type": null}}, "_monitor_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "config", "market_data_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager._monitor_strategy", "name": "_monitor_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "config", "market_data_source"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", "strategy.strategy_manager.StrategyConfig", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_monitor_strategy of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "market_data_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager._process_symbol", "name": "_process_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "market_data_source"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_symbol of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_backtest_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager._save_backtest_result", "name": "_save_backtest_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "result"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", "builtins.str", "strategy.backtesting_engine.BacktestResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_backtest_result of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_trigger_signal_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "strategy_name", "signal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager._trigger_signal_callbacks", "name": "_trigger_signal_callbacks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "strategy_name", "signal"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", "strategy.signal_generator.TradingSignal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trigger_signal_callbacks of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_signal_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.add_signal_callback", "name": "add_signal_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["strategy.strategy_manager.StrategyManager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_signal_callback of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "database": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.database", "name": "database", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "disable_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.disable_strategy", "name": "disable_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_strategy of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.enable_strategy", "name": "enable_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_strategy of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.executor", "name": "executor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "export_strategies_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.export_strategies_config", "name": "export_strategies_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_strategies_config of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_all_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.get_all_performance", "name": "get_all_performance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["strategy.strategy_manager.StrategyManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_performance of StrategyManager", "ret_type": {".class": "Instance", "args": ["builtins.str", "strategy.strategy_manager.StrategyPerformance"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_strategy_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.get_strategy_performance", "name": "get_strategy_performance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_strategy_performance of StrategyManager", "ret_type": {".class": "UnionType", "items": ["strategy.strategy_manager.StrategyPerformance", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_strategies_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.import_strategies_config", "name": "import_strategies_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_strategies_config of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monitoring_tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.monitoring_tasks", "name": "monitoring_tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "performance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.performance", "name": "performance", "type": {".class": "Instance", "args": ["builtins.str", "strategy.strategy_manager.StrategyPerformance"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "register_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.register_strategy", "name": "register_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["strategy.strategy_manager.StrategyManager", "strategy.strategy_manager.StrategyConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_strategy of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_backtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "start_date", "end_date", "market_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager.run_backtest", "name": "run_backtest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbol", "start_date", "end_date", "market_data"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", "builtins.str", "datetime.datetime", "datetime.datetime", {".class": "AnyType", "missing_import_name": "strategy.strategy_manager.pd", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_backtest of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_batch_backtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbols", "start_date", "end_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager.run_batch_backtest", "name": "run_batch_backtest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "strategy_name", "symbols", "start_date", "end_date"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_batch_backtest of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", "strategy.backtesting_engine.BacktestResult"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.running", "name": "running", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "signal_callbacks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.signal_callbacks", "name": "signal_callbacks", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "signal_generators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.signal_generators", "name": "signal_generators", "type": {".class": "Instance", "args": ["builtins.str", "strategy.signal_generator.SignalGenerator"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "start_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "market_data_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager.start_monitoring", "name": "start_monitoring", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "market_data_source"], "arg_types": ["strategy.strategy_manager.StrategyManager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_monitoring of StrategyManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "strategy.strategy_manager.StrategyManager.stop_monitoring", "name": "stop_monitoring", "type": null}}, "strategies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.StrategyManager.strategies", "name": "strategies", "type": {".class": "Instance", "args": ["builtins.str", "strategy.strategy_manager.StrategyConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "unregister_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyManager.unregister_strategy", "name": "unregister_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy_name"], "arg_types": ["strategy.strategy_manager.StrategyManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister_strategy of StrategyManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.strategy_manager.StrategyManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.strategy_manager.StrategyManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrategyPerformance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.strategy_manager.StrategyPerformance", "name": "StrategyPerformance", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyPerformance", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "strategy_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "total_signals", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "successful_signals", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "failed_signals", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "avg_confidence", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "last_signal_time", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "backtest_result", "type": {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "live_pnl", "type": "builtins.float"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "strategy.strategy_manager", "mro": ["strategy.strategy_manager.StrategyPerformance", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "strategy.strategy_manager.StrategyPerformance.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy_name", "total_signals", "successful_signals", "failed_signals", "avg_confidence", "last_signal_time", "backtest_result", "live_pnl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyPerformance.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy_name", "total_signals", "successful_signals", "failed_signals", "avg_confidence", "last_signal_time", "backtest_result", "live_pnl"], "arg_types": ["strategy.strategy_manager.StrategyPerformance", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StrategyPerformance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "strategy.strategy_manager.StrategyPerformance.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "strategy_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_signals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "successful_signals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed_signals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avg_confidence"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_signal_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "backtest_result"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "live_pnl"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strategy_name", "total_signals", "successful_signals", "failed_signals", "avg_confidence", "last_signal_time", "backtest_result", "live_pnl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.strategy_manager.StrategyPerformance.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strategy_name", "total_signals", "successful_signals", "failed_signals", "avg_confidence", "last_signal_time", "backtest_result", "live_pnl"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyPerformance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "strategy.strategy_manager.StrategyPerformance.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strategy_name", "total_signals", "successful_signals", "failed_signals", "avg_confidence", "last_signal_time", "backtest_result", "live_pnl"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyPerformance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "avg_confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.avg_confidence", "name": "avg_confidence", "type": "builtins.float"}}, "backtest_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.backtest_result", "name": "backtest_result", "type": {".class": "UnionType", "items": ["strategy.backtesting_engine.BacktestResult", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "failed_signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.failed_signals", "name": "failed_signals", "type": "builtins.int"}}, "get_success_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.StrategyPerformance.get_success_rate", "name": "get_success_rate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["strategy.strategy_manager.StrategyPerformance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_success_rate of StrategyPerformance", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_signal_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.last_signal_time", "name": "last_signal_time", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "live_pnl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.live_pnl", "name": "live_pnl", "type": "builtins.float"}}, "strategy_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.strategy_manager.StrategyPerformance.strategy_name", "name": "strategy_name", "type": "builtins.str"}}, "successful_signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.successful_signals", "name": "successful_signals", "type": "builtins.int"}}, "total_signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.strategy_manager.StrategyPerformance.total_signals", "name": "total_signals", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.strategy_manager.StrategyPerformance.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.strategy_manager.StrategyPerformance", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "TradingSignal": {".class": "SymbolTableNode", "cross_ref": "strategy.signal_generator.TradingSignal", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.strategy_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_strategy_manager_instance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.strategy_manager._strategy_manager_instance", "name": "_strategy_manager_instance", "type": {".class": "NoneType"}}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_signal_generator": {".class": "SymbolTableNode", "cross_ref": "strategy.signal_generator.get_signal_generator", "kind": "Gdef"}, "get_strategy_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["database_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.strategy_manager.get_strategy_manager", "name": "get_strategy_manager", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.strategy_manager.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.strategy_manager.manager", "name": "manager", "type": "strategy.strategy_manager.StrategyManager"}}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "strategy.strategy_manager.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "strategy.strategy_manager.pd", "source_any": null, "type_of_any": 3}}}, "test_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.strategy_manager.test_config", "name": "test_config", "type": "strategy.strategy_manager.StrategyConfig"}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\strategy\\strategy_manager.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单系统测试
Simple System Test
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

def test_basic_imports():
    """测试基础导入"""
    print("🧪 测试基础模块导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
        
        import json
        print("✅ json 导入成功")
        
        import threading
        print("✅ threading 导入成功")
        
        from datetime import datetime
        print("✅ datetime 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False

def test_gui_module():
    """测试GUI模块"""
    print("\n🖥️ 测试GUI模块...")
    
    try:
        from ultimate_trading_gui import UltimateTradingGUI
        print("✅ 主GUI模块导入成功")
        return True
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False

def test_optimization_module():
    """测试优化模块"""
    print("\n🚀 测试优化模块...")
    
    try:
        from simple_system_integration import SimpleOptimizedSystem
        print("✅ 简化优化系统导入成功")
        
        from simple_system_integration import SimpleOptimizedSystemFactory
        print("✅ 优化系统工厂导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 优化模块导入失败: {e}")
        return False

def test_system_creation():
    """测试系统创建"""
    print("\n🔧 测试系统创建...")
    
    try:
        from ultimate_trading_gui import UltimateTradingGUI
        from simple_system_integration import SimpleOptimizedSystemFactory
        
        print("   正在创建GUI实例...")
        # 不实际创建tkinter窗口，只测试类定义
        gui_class = UltimateTradingGUI
        print("   ✅ GUI类定义正常")
        
        print("   正在测试工厂方法...")
        factory = SimpleOptimizedSystemFactory()
        print("   ✅ 工厂类创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 系统创建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 企业级现货交易系统 - 简单系统测试")
    print("=" * 50)
    
    # 运行测试
    basic_ok = test_basic_imports()
    gui_ok = test_gui_module()
    opt_ok = test_optimization_module()
    create_ok = test_system_creation()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   基础模块: {'✅ 通过' if basic_ok else '❌ 失败'}")
    print(f"   GUI模块: {'✅ 通过' if gui_ok else '❌ 失败'}")
    print(f"   优化模块: {'✅ 通过' if opt_ok else '❌ 失败'}")
    print(f"   系统创建: {'✅ 通过' if create_ok else '❌ 失败'}")
    
    if all([basic_ok, gui_ok, opt_ok, create_ok]):
        print("\n🎉 所有测试通过！")
        print("📱 系统准备就绪，可以启动GUI界面")
        
        # 询问是否启动GUI
        try:
            response = input("\n是否现在启动GUI界面？(y/n): ").lower().strip()
            if response == 'y':
                print("🚀 启动GUI界面...")
                
                from ultimate_trading_gui import UltimateTradingGUI
                from simple_system_integration import SimpleOptimizedSystemFactory
                
                # 创建优化版系统
                app = SimpleOptimizedSystemFactory.create_integrated_system(UltimateTradingGUI)
                
                print("✅ 系统启动成功！")
                app.run()
                
        except KeyboardInterrupt:
            print("\n👋 测试完成")
        except Exception as e:
            print(f"\n❌ GUI启动失败: {e}")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()

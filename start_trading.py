#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
一键启动交易系统
One-Click Trading System Launcher

智能检测配置状态并启动交易系统
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("🚀" + "=" * 58 + "🚀")
    print("🚀                                                        🚀")
    print("🚀     企业级现货交易系统 - 一键启动                      🚀")
    print("🚀     Enterprise Spot Trading System                    🚀")
    print("🚀                                                        🚀")
    print("🚀     版本: 1.0.0                                       🚀")
    print("🚀     状态: 完全就绪                                     🚀")
    print("🚀                                                        🚀")
    print("🚀" + "=" * 58 + "🚀")

def check_system_readiness():
    """检查系统就绪状态"""
    print("\n🔍 检查系统状态...")
    
    issues = []
    
    # 检查关键文件
    required_files = [
        'core/ultimate_trading_gui.py',
        'quick_start.py',
        'config.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            issues.append(f"缺少关键文件: {file_path}")
    
    # 检查配置状态
    env_file = Path('.env')
    if not env_file.exists():
        issues.append("缺少环境配置文件 (.env)")
    else:
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        if 'demo_gate_api_key' in env_content or 'your_api_key_here' in env_content:
            issues.append("API密钥未配置（仍为演示配置）")
    
    return issues

def show_configuration_status():
    """显示配置状态"""
    print("\n📊 配置状态检查:")
    print("-" * 30)
    
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查API配置
        if 'demo_gate_api_key' in content:
            print("❌ API密钥: 演示配置")
        else:
            print("✅ API密钥: 已配置")
        
        # 检查交易模式
        if 'EXCHANGE_SANDBOX=true' in content:
            print("🧪 交易模式: 沙盒模式")
        else:
            print("💰 交易模式: 实盘模式")
        
        # 检查环境
        if 'ENVIRONMENT=development' in content:
            print("🔧 环境: 开发环境")
        else:
            print("🏢 环境: 生产环境")
    else:
        print("❌ 配置文件: 未找到")

def launch_configuration_wizard():
    """启动配置向导"""
    print("\n🔧 启动API配置向导...")
    
    try:
        result = subprocess.run([sys.executable, 'api_setup_assistant.py'], 
                              timeout=300)  # 5分钟超时
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ 配置超时")
        return False
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False

def launch_trading_system():
    """启动交易系统"""
    print("\n🚀 启动交易系统...")
    
    try:
        # 启动GUI
        subprocess.Popen([sys.executable, 'core/ultimate_trading_gui.py'])
        print("✅ 交易界面已启动")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_quick_actions():
    """显示快速操作菜单"""
    print("\n📋 快速操作:")
    print("1. 🔧 配置API密钥")
    print("2. 🚀 启动交易系统")
    print("3. 🎯 运行演示")
    print("4. 🔍 系统诊断")
    print("5. 📊 查看状态")
    print("6. ❌ 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-6): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6']:
                return choice
            else:
                print("❌ 无效选择，请输入1-6")
        except KeyboardInterrupt:
            return '6'

def handle_action(choice: str):
    """处理用户选择的操作"""
    if choice == '1':
        # 配置API密钥
        return launch_configuration_wizard()
    
    elif choice == '2':
        # 启动交易系统
        issues = check_system_readiness()
        if issues:
            print("\n❌ 系统未就绪:")
            for issue in issues:
                print(f"  - {issue}")
            print("\n💡 请先配置API密钥")
            return False
        else:
            return launch_trading_system()
    
    elif choice == '3':
        # 运行演示
        try:
            subprocess.run([sys.executable, 'trading_demo.py'])
            return True
        except Exception as e:
            print(f"❌ 演示启动失败: {e}")
            return False
    
    elif choice == '4':
        # 系统诊断
        try:
            subprocess.run([sys.executable, 'quick_start.py'])
            return True
        except Exception as e:
            print(f"❌ 诊断启动失败: {e}")
            return False
    
    elif choice == '5':
        # 查看状态
        show_configuration_status()
        issues = check_system_readiness()
        if not issues:
            print("\n✅ 系统完全就绪，可以开始交易！")
        else:
            print(f"\n⚠️ 发现 {len(issues)} 个问题需要解决")
        return True
    
    elif choice == '6':
        # 退出
        return False
    
    return True

def auto_launch():
    """自动启动逻辑"""
    print("\n🤖 智能启动检测...")
    
    issues = check_system_readiness()
    
    if not issues:
        # 系统完全就绪，直接启动
        print("✅ 系统完全就绪！")
        
        auto_start = input("是否立即启动交易系统? (Y/n): ").strip().lower()
        if auto_start in ['', 'y', 'yes']:
            return launch_trading_system()
        else:
            return False
    
    elif len(issues) == 1 and "API密钥未配置" in issues[0]:
        # 只需要配置API密钥
        print("⚠️ 需要配置API密钥")
        
        config_now = input("是否现在配置API密钥? (Y/n): ").strip().lower()
        if config_now in ['', 'y', 'yes']:
            if launch_configuration_wizard():
                # 配置成功后询问是否启动
                start_now = input("配置完成！是否立即启动交易系统? (Y/n): ").strip().lower()
                if start_now in ['', 'y', 'yes']:
                    return launch_trading_system()
            return False
        else:
            return False
    
    else:
        # 多个问题，显示详细信息
        print("❌ 系统存在以下问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        print("\n💡 建议使用快速操作菜单解决问题")
        return False

def main():
    """主函数"""
    print_banner()
    
    try:
        # 尝试自动启动
        if auto_launch():
            print("\n🎉 交易系统已启动！")
            print("💡 请在GUI界面中进行交易操作")
            input("\n按回车键退出...")
            return
        
        # 如果自动启动失败，显示菜单
        while True:
            choice = show_quick_actions()
            
            if choice == '6':
                print("\n👋 再见！")
                break
            
            success = handle_action(choice)
            
            if choice == '2' and success:
                # 成功启动交易系统
                print("\n🎉 交易系统已启动！")
                break
            
            # 询问是否继续
            if choice != '5':  # 查看状态不需要询问
                continue_choice = input("\n是否继续使用? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

[{"name": "终极版现货交易GUI", "module": "ultimate_spot_trading_gui", "class": "UltimateSpotTradingGUI", "description": "主要的现货交易学习平台界面", "importable": true, "features": ["apply_parameters", "connect_gate", "connect_simulation_mode", "log_message", "on_closing", "refresh_market_data", "register_resources", "reset_all_indicators", "run", "setup_history_panel"], "dependencies": ["import asyncio", "import json", "import threading", "import time", "import tkinter as tk"], "errors": [], "score": 86}, {"name": "现货滚雪球GUI", "module": "spot_snowball_gui", "class": "SpotSnowballGUI", "description": "专注于滚雪球策略的交易界面", "importable": true, "features": ["apply_parameters", "get_market_data", "initialize_trading_pairs", "refresh_signals", "run", "setup_left_panel", "setup_performance_chart", "setup_positions_table", "setup_right_panel", "setup_signals_analysis"], "dependencies": ["import asyncio", "import json", "import threading", "import tkinter as tk", "from datetime import datetime"], "errors": [], "score": 88}, {"name": "终极版交易GUI", "module": "ultimate_trading_gui", "class": "UltimateTradingGUI", "description": "简洁高效的图形用户界面", "importable": false, "features": [], "dependencies": [], "errors": ["导入错误: No module named 'talib'"], "score": 0}, {"name": "优化版交易GUI", "module": "optimized_trading_gui", "class": "OptimizedTradingGUI", "description": "解决卡顿问题的轻量级版本", "importable": true, "features": ["add_signal_message", "clear_log", "create_config_tab", "create_data_tab", "create_log_tab", "create_status_bar", "create_trading_tab", "create_widgets", "filter_by_category", "generate_mock_signal"], "dependencies": ["import json", "import logging", "import queue", "import threading", "import time"], "errors": [], "score": 85}, {"name": "融合交易GUI", "module": "fusion_trading_gui", "class": "FusionTradingGUI", "description": "结合所有GUI最佳功能的终极体验", "importable": false, "features": [], "dependencies": [], "errors": ["未知错误: unindent does not match any outer indentation level (fusion_trading_gui.py, line 636)"], "score": 0}, {"name": "多交易对GUI", "module": "multi_pairs_gui", "class": "MultiPairsGUI", "description": "支持22个交易对的稳定版本", "importable": true, "features": ["add_signal", "create_table", "create_widgets", "filter_category", "generate_signal", "get_filtered_pairs", "refresh_data", "run", "simulate_price_changes", "start_updates"], "dependencies": ["import json", "import random", "import threading", "import time", "import tkinter as tk"], "errors": [], "score": 61}, {"name": "简单现货GUI", "module": "simple_gui", "class": "SpotTradingGUI", "description": "专业现货交易系统界面", "importable": true, "features": ["activate_emergency_mode", "add_log", "add_risk_alert", "add_status_log", "calculate_margin_ratio", "calculate_trading_fee", "check_batch_trading", "check_daily_loss_limit", "check_drawdown_limit", "check_margin_positions"], "dependencies": ["import hashlib", "import hmac", "import json", "import random", "import threading"], "errors": [], "score": 81}]
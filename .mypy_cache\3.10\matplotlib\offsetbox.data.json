{".class": "MypyFile", "_fullname": "matplotlib.offsetbox", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AnchoredOffsetbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.AnchoredOffsetbox", "name": "AnchoredOffsetbox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.AnchoredOffsetbox", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "loc", "pad", "borderpad", "child", "prop", "<PERSON><PERSON>", "bbox_to_anchor", "bbox_transform", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "loc", "pad", "borderpad", "child", "prop", "<PERSON><PERSON>", "bbox_to_anchor", "bbox_transform", "kwargs"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox", "builtins.str", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["matplotlib.offsetbox.OffsetBox", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.font_manager.FontProperties", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["matplotlib.transforms.BboxBase", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AnchoredOffsetbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "borderpad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.borderpad", "name": "borderpad", "type": "builtins.float"}}, "codes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.codes", "name": "codes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_bbox_to_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.get_bbox_to_anchor", "name": "get_bbox_to_anchor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bbox_to_anchor of AnchoredOffsetbox", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.get_child", "name": "get_child", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_child of AnchoredOffsetbox", "ret_type": {".class": "UnionType", "items": ["matplotlib.offsetbox.OffsetBox", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of AnchoredOffsetbox", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.loc", "name": "loc", "type": "builtins.int"}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.pad", "name": "pad", "type": "builtins.float"}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.patch", "name": "patch", "type": "matplotlib.patches.FancyBboxPatch"}}, "prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.prop", "name": "prop", "type": "matplotlib.font_manager.FontProperties"}}, "set_bbox_to_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.set_bbox_to_anchor", "name": "set_bbox_to_anchor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "transform"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox", "matplotlib.transforms.BboxBase", {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bbox_to_anchor of AnchoredOffsetbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.set_child", "name": "set_child", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "child"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox", {".class": "UnionType", "items": ["matplotlib.offsetbox.OffsetBox", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_child of AnchoredOffsetbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.update_frame", "name": "update_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "fontsize"], "arg_types": ["matplotlib.offsetbox.AnchoredOffsetbox", "matplotlib.transforms.Bbox", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_frame of AnchoredOffsetbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.zorder", "name": "zorder", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.AnchoredOffsetbox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.AnchoredOffsetbox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnchoredText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.AnchoredOffsetbox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.AnchoredText", "name": "AnchoredText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.AnchoredText", "matplotlib.offsetbox.AnchoredOffsetbox", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "s", "loc", "pad", "borderpad", "prop", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnchoredText.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "s", "loc", "pad", "borderpad", "prop", "kwargs"], "arg_types": ["matplotlib.offsetbox.AnchoredText", "builtins.str", "builtins.str", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AnchoredText", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "txt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnchoredText.txt", "name": "txt", "type": "matplotlib.offsetbox.TextArea"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.AnchoredText.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.AnchoredText", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnnotationBbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.artist.Artist", "matplotlib.text._AnnotationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.AnnotationBbox", "name": "AnnotationBbox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.AnnotationBbox", "matplotlib.artist.Artist", "matplotlib.text._AnnotationBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "offsetbox", "xy", "xybox", "xycoords", "boxcoords", "<PERSON><PERSON>", "pad", "annotation_clip", "box_alignment", "bboxprops", "arrowprops", "fontsize", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "offsetbox", "xy", "xybox", "xycoords", "boxcoords", "<PERSON><PERSON>", "pad", "annotation_clip", "box_alignment", "bboxprops", "arrowprops", "fontsize", "kwargs"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", "matplotlib.offsetbox.OffsetBox", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anncoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.offsetbox.AnnotationBbox.anncoords", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.offsetbox.AnnotationBbox.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of AnnotationBbox", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.AnnotationBbox.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of AnnotationBbox", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.offsetbox.AnnotationBbox.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "an<PERSON><PERSON><PERSON>", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of AnnotationBbox", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "arrow_patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.arrow_patch", "name": "arrow_patch", "type": {".class": "UnionType", "items": ["matplotlib.patches.FancyArrowPatch", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "arrowprops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.arrowprops", "name": "arrowprops", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "boxcoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.boxcoords", "name": "boxcoords", "type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of AnnotationBbox", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.get_fontsize", "name": "get_fontsize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontsize of AnnotationBbox", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tightbbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.get_tightbbox", "name": "get_tightbbox", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", {".class": "UnionType", "items": ["matplotlib.backend_bases.RendererBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tightbbox of AnnotationBbox", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offsetbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.offsetbox", "name": "offsetbox", "type": "matplotlib.offsetbox.OffsetBox"}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.patch", "name": "patch", "type": "matplotlib.patches.FancyBboxPatch"}}, "prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.prop", "name": "prop", "type": "matplotlib.font_manager.FontProperties"}}, "set_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.set_figure", "name": "set_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", {".class": "UnionType", "items": ["matplotlib.figure.Figure", "matplotlib.figure.SubFigure"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_figure of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.set_fontsize", "name": "set_fontsize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "s"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", {".class": "UnionType", "items": ["builtins.str", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontsize of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AnnotationBbox.update_positions", "name": "update_positions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_positions of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xyann": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.offsetbox.AnnotationBbox.xyann", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.offsetbox.AnnotationBbox.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of AnnotationBbox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.AnnotationBbox.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of AnnotationBbox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xyann"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.offsetbox.AnnotationBbox.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xyann"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of AnnotationBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "xyann", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AnnotationBbox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of AnnotationBbox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "xybox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.xybox", "name": "xybox", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "zorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AnnotationBbox.zorder", "name": "zorder", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.AnnotationBbox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.AnnotationBbox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AuxTransformBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.AuxTransformBox", "name": "AuxTransformBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.AuxTransformBox", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aux_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aux_transform"], "arg_types": ["matplotlib.offsetbox.AuxTransformBox", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AuxTransformBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox.add_artist", "name": "add_artist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "a"], "arg_types": ["matplotlib.offsetbox.AuxTransformBox", "matplotlib.artist.Artist"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_artist of AuxTransformBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aux_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AuxTransformBox.aux_transform", "name": "aux_transform", "type": "matplotlib.transforms.Transform"}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AuxTransformBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of AuxTransformBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.AuxTransformBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of AuxTransformBox", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offset_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AuxTransformBox.offset_transform", "name": "offset_transform", "type": "matplotlib.transforms.Transform"}}, "ref_offset_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.AuxTransformBox.ref_offset_transform", "name": "ref_offset_transform", "type": "matplotlib.transforms.Transform"}}, "set_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.AuxTransformBox.set_offset", "name": "set_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.offsetbox.AuxTransformBox", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_offset of AuxTransformBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.AuxTransformBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.AuxTransformBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Bbox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Bbox", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BboxBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.BboxBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BboxImage": {".class": "SymbolTableNode", "cross_ref": "matplotlib.image.BboxImage", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Colormap": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Colormap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoordsType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.CoordsType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "DraggableAnnotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.DraggableBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.DraggableAnnotation", "name": "DraggableAnnotation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableAnnotation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.DraggableAnnotation", "matplotlib.offsetbox.DraggableBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "annotation", "use_blit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableAnnotation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "annotation", "use_blit"], "arg_types": ["matplotlib.offsetbox.DraggableAnnotation", "matplotlib.text.Annotation", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DraggableAnnotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableAnnotation.annotation", "name": "annotation", "type": "matplotlib.text.Annotation"}}, "save_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableAnnotation.save_offset", "name": "save_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableAnnotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_offset of DraggableAnnotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableAnnotation.update_offset", "name": "update_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "arg_types": ["matplotlib.offsetbox.DraggableAnnotation", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_offset of DraggableAnnotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.DraggableAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.DraggableAnnotation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DraggableBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.DraggableBase", "name": "DraggableBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.DraggableBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ref_artist", "use_blit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ref_artist", "use_blit"], "arg_types": ["matplotlib.offsetbox.DraggableBase", "matplotlib.artist.Artist", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "background": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableBase.background", "name": "background", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.offsetbox.DraggableBase.canvas", "name": "canvas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canvas of DraggableBase", "ret_type": "matplotlib.backend_bases.FigureCanvasBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.DraggableBase.canvas", "name": "canvas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canvas of DraggableBase", "ret_type": "matplotlib.backend_bases.FigureCanvasBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.offsetbox.DraggableBase.cids", "name": "cids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cids of DraggableBase", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.DraggableBase.cids", "name": "cids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cids of DraggableBase", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.disconnect", "name": "disconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disconnect of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.finalize_offset", "name": "finalize_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_offset of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "got_artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableBase.got_artist", "name": "got_artist", "type": "builtins.bool"}}, "mouse_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableBase.mouse_x", "name": "mouse_x", "type": "builtins.int"}}, "mouse_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableBase.mouse_y", "name": "mouse_y", "type": "builtins.int"}}, "on_motion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.on_motion", "name": "on_motion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evt"], "arg_types": ["matplotlib.offsetbox.DraggableBase", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_motion of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.on_pick", "name": "on_pick", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evt"], "arg_types": ["matplotlib.offsetbox.DraggableBase", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pick of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.on_release", "name": "on_release", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.offsetbox.DraggableBase", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_release of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ref_artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableBase.ref_artist", "name": "ref_artist", "type": "matplotlib.artist.Artist"}}, "save_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.save_offset", "name": "save_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_offset of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableBase.update_offset", "name": "update_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "arg_types": ["matplotlib.offsetbox.DraggableBase", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_offset of DraggableBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.DraggableBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.DraggableBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DraggableOffsetBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.DraggableBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.DraggableOffsetBox", "name": "DraggableOffsetBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableOffsetBox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.DraggableOffsetBox", "matplotlib.offsetbox.DraggableBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ref_artist", "offsetbox", "use_blit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableOffsetBox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ref_artist", "offsetbox", "use_blit"], "arg_types": ["matplotlib.offsetbox.DraggableOffsetBox", "matplotlib.artist.Artist", "matplotlib.offsetbox.OffsetBox", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DraggableOffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_loc_in_canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableOffsetBox.get_loc_in_canvas", "name": "get_loc_in_canvas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableOffsetBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_loc_in_canvas of DraggableOffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offsetbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DraggableOffsetBox.offsetbox", "name": "offsetbox", "type": "matplotlib.offsetbox.OffsetBox"}}, "save_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableOffsetBox.save_offset", "name": "save_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DraggableOffsetBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_offset of DraggableOffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DraggableOffsetBox.update_offset", "name": "update_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dx", "dy"], "arg_types": ["matplotlib.offsetbox.DraggableOffsetBox", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_offset of DraggableOffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.DraggableOffsetBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.DraggableOffsetBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DrawingArea": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.DrawingArea", "name": "DrawingArea", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.DrawingArea", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "clip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "clip"], "arg_types": ["matplotlib.offsetbox.DrawingArea", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DrawingArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea.add_artist", "name": "add_artist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "a"], "arg_types": ["matplotlib.offsetbox.DrawingArea", "matplotlib.artist.Artist"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_artist of DrawingArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clip_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.offsetbox.DrawingArea.clip_children", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.offsetbox.DrawingArea.clip_children", "name": "clip_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DrawingArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_children of DrawingArea", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.DrawingArea.clip_children", "name": "clip_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DrawingArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_children of DrawingArea", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.offsetbox.DrawingArea.clip_children", "name": "clip_children", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["matplotlib.offsetbox.DrawingArea", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_children of DrawingArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "clip_children", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DrawingArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_children of DrawingArea", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dpi_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.dpi_transform", "name": "dpi_transform", "type": "matplotlib.transforms.Transform"}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DrawingArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of DrawingArea", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.DrawingArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of DrawingArea", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.height", "name": "height", "type": "builtins.float"}}, "offset_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.offset_transform", "name": "offset_transform", "type": "matplotlib.transforms.Transform"}}, "set_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.DrawingArea.set_offset", "name": "set_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.offsetbox.DrawingArea", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_offset of DrawingArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.width", "name": "width", "type": "builtins.float"}}, "xdescent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.xdescent", "name": "xdescent", "type": "builtins.float"}}, "ydescent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.DrawingArea.ydescent", "name": "ydescent", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.DrawingArea.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.DrawingArea", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Event": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.Event", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FancyArrowPatch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.FancyArrowPatch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FancyBboxPatch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.FancyBboxPatch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Figure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.Figure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FigureCanvasBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureCanvasBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FontProperties": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.FontProperties", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HPacker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.PackerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.HPacker", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.HPacker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.HPacker", "matplotlib.offsetbox.PackerBase", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.HPacker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.HPacker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Normalize": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Normalize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OffsetBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.artist.Artist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.OffsetBox", "name": "OffsetBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.offsetbox.OffsetBox", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.get_bbox", "name": "get_bbox", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bbox of OffsetBox", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of OffsetBox", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.get_offset", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bbox", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.offsetbox.OffsetBox.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bbox", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "matplotlib.transforms.Bbox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.OffsetBox.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bbox", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "matplotlib.transforms.Bbox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.offsetbox.OffsetBox.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.offsetbox.OffsetBox.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bbox", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "matplotlib.transforms.Bbox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "xdescent", "ydescent", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetBox", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_visible_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.get_visible_children", "name": "get_visible_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_visible_children of OffsetBox", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_extent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.get_window_extent", "name": "get_window_extent", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.offsetbox.OffsetBox", {".class": "UnionType", "items": ["matplotlib.backend_bases.RendererBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_extent of OffsetBox", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.OffsetBox.height", "name": "height", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.set_figure", "name": "set_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.offsetbox.OffsetBox", {".class": "UnionType", "items": ["matplotlib.figure.Figure", "matplotlib.figure.SubFigure"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_figure of OffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.set_height", "name": "set_height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "height"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_height of OffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.set_offset", "name": "set_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.offsetbox.OffsetBox", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_offset of OffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetBox.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib.offsetbox.OffsetBox", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of OffsetBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.OffsetBox.width", "name": "width", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.OffsetBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.OffsetBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OffsetImage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.OffsetImage", "name": "OffsetImage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.OffsetImage", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "arr", "zoom", "cmap", "norm", "interpolation", "origin", "filternorm", "filterrad", "resample", "dpi_cor", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "arr", "zoom", "cmap", "norm", "interpolation", "origin", "filternorm", "filterrad", "resample", "dpi_cor", "kwargs"], "arg_types": ["matplotlib.offsetbox.OffsetImage", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float", {".class": "UnionType", "items": ["matplotlib.colors.Colormap", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.colors.Normalize", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "upper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lower"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.float", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OffsetImage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetImage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of OffsetImage", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.get_data", "name": "get_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetImage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data of OffsetImage", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetImage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of OffsetImage", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.get_zoom", "name": "get_zoom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.OffsetImage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_zoom of OffsetImage", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.OffsetImage.image", "name": "image", "type": "matplotlib.image.BboxImage"}}, "set_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.set_data", "name": "set_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arr"], "arg_types": ["matplotlib.offsetbox.OffsetImage", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_data of OffsetImage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "zoom"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.OffsetImage.set_zoom", "name": "set_zoom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "zoom"], "arg_types": ["matplotlib.offsetbox.OffsetImage", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_zoom of OffsetImage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.OffsetImage.stale", "name": "stale", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.OffsetImage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.OffsetImage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PackerBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.PackerBase", "name": "PackerBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PackerBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.PackerBase", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pad", "sep", "width", "height", "align", "mode", "children"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PackerBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pad", "sep", "width", "height", "align", "mode", "children"], "arg_types": ["matplotlib.offsetbox.PackerBase", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "expand"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PackerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "align": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.align", "name": "align", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}], "uses_pep604_syntax": false}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.height", "name": "height", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "expand"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}], "uses_pep604_syntax": false}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.pad", "name": "pad", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "sep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.sep", "name": "sep", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PackerBase.width", "name": "width", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.PackerBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.PackerBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PaddedBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.PaddedBox", "name": "PaddedBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PaddedBox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.PaddedBox", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "child", "pad", "draw_frame", "patch_attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PaddedBox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "child", "pad", "draw_frame", "patch_attrs"], "arg_types": ["matplotlib.offsetbox.PaddedBox", "matplotlib.artist.Artist", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PaddedBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PaddedBox.draw_frame", "name": "draw_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.offsetbox.PaddedBox", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_frame of PaddedBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PaddedBox.pad", "name": "pad", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.PaddedBox.patch", "name": "patch", "type": "matplotlib.patches.FancyBboxPatch"}}, "update_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.PaddedBox.update_frame", "name": "update_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bbox", "fontsize"], "arg_types": ["matplotlib.offsetbox.PaddedBox", "matplotlib.transforms.Bbox", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_frame of PaddedBox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.PaddedBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.PaddedBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.RendererBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SubFigure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.SubFigure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TextArea": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.OffsetBox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.TextArea", "name": "TextArea", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.TextArea", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "s", "textprops", "multilinebaseline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "s", "textprops", "multilinebaseline"], "arg_types": ["matplotlib.offsetbox.TextArea", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TextArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_multilinebaseline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.get_multilinebaseline", "name": "get_multilinebaseline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.TextArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multilinebaseline of TextArea", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.get_offset", "name": "get_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.TextArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_offset of TextArea", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.get_text", "name": "get_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.offsetbox.TextArea"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text of TextArea", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offset_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.offsetbox.TextArea.offset_transform", "name": "offset_transform", "type": "matplotlib.transforms.Transform"}}, "set_multilinebaseline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.set_multilinebaseline", "name": "set_multilinebaseline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["matplotlib.offsetbox.TextArea", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_multilinebaseline of TextArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.set_offset", "name": "set_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.offsetbox.TextArea", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_offset of TextArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.TextArea.set_text", "name": "set_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.offsetbox.TextArea", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_text of TextArea", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.TextArea.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.TextArea", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VPacker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.offsetbox.PackerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.offsetbox.VPacker", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox.VPacker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.offsetbox", "mro": ["matplotlib.offsetbox.VPacker", "matplotlib.offsetbox.PackerBase", "matplotlib.offsetbox.OffsetBox", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.offsetbox.VPacker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.offsetbox.VPacker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.offsetbox.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_packed_offsets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["widths", "total", "sep", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.offsetbox._get_packed_offsets", "name": "_get_packed_offsets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["widths", "total", "sep", "mode"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "expand"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_packed_offsets", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "martist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mtext": {".class": "SymbolTableNode", "cross_ref": "matplotlib.text", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\offsetbox.pyi"}
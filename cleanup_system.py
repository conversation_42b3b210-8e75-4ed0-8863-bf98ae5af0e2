#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统清理脚本
System Cleanup Script

只保留 ultimate_spot_trading_gui.py 及其必需的依赖文件
删除其他所有GUI界面和不必要的文件
"""

import os
import shutil
from pathlib import Path

def cleanup_system():
    """清理系统，只保留必需文件"""
    
    print("🧹 开始清理系统...")
    print("📋 只保留 ultimate_spot_trading_gui.py 及其依赖文件")
    print("=" * 60)
    
    # 定义需要保留的文件
    keep_files = {
        # 核心GUI文件
        'core/ultimate_spot_trading_gui.py',
        
        # API连接相关
        'core/gate_api_connector.py',
        'core/api_login_dialog.py',
        
        # 策略引擎
        'core/doubling_growth_engine.py',
        'core/fast_profit_engine.py',
        
        # 可选的支持模块（如果存在）
        'core/config_ui.py',
        'core/resource_manager.py', 
        'core/resource_monitor_ui.py',
        
        # 配置文件
        'config.json',
        'requirements.txt',
        
        # 重要文档
        'README.md',
        'API使用指南.md',
        'GUI_INTERFACE_EVALUATION_REPORT.md',
        'FINAL_AUDIT_SUMMARY.md',
        'FIX_SUMMARY.md',
        
        # 启动脚本
        'immediate_fix_script.py',
        
        # 配置目录（如果存在）
        'config/',
        'config/api_credentials.json',
        'config/api_setup.env'
    }
    
    # 定义要删除的GUI文件
    gui_files_to_remove = [
        'core/spot_snowball_gui.py',
        'core/ultimate_trading_gui.py', 
        'core/optimized_trading_gui.py',
        'core/fusion_trading_gui.py',
        'core/multi_pairs_gui.py',
        'core/simple_gui.py',
        'core/simple_spot_gui.py',
        'core/trading_system_gui.py',
        'core/gate_simulation_gui.py',
        'core/perpetual_futures_gui.py',
        'ultimate_fusion_gui.py'
    ]
    
    # 定义要删除的不必要模块
    unnecessary_modules = [
        'core/complete_optimization_system.py',
        'core/simple_system_integration.py',
        'core/simple_system_integration_v2.py',
        'core/system_integration.py',
        'core/final_system_integration.py',
        'core/live_trading_executor.py',
        'core/simple_gate_connector.py',
        'core/ux_enhancement.py',
        'core/testing_framework.py',
        'core/strategy_optimizer.py',
        'core/performance_monitoring.py',
        'core/network_optimizer.py',
        'core/security_optimizer.py'
    ]
    
    # 定义要删除的测试和演示文件
    test_demo_files = [
        'simple_system_test.py',
        'gui_evaluation_script.py',
        'test_*.py',
        'demo_*.py',
        '*_test.py',
        '*_demo.py'
    ]
    
    # 定义要删除的结果和报告目录
    result_dirs = [
        'core/results',
        'core/exports', 
        'core/dashboard_data',
        'core/dashboard_exports',
        'core/crypto_trading_results',
        'core/deployment_results',
        'core/future_roadmap_results',
        'core/immediate_actions',
        'core/integration_results',
        'core/micro_trading_results',
        'core/next_steps_results',
        'core/paper_trading_results',
        'core/pipeline_portfolios',
        'core/portfolio_results',
        'core/__pycache__'
    ]
    
    current_dir = Path('.')
    removed_count = 0
    kept_count = 0
    
    print("🗑️ 删除不必要的GUI文件...")
    for gui_file in gui_files_to_remove:
        file_path = current_dir / gui_file
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {gui_file}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {gui_file} - {e}")
    
    print("\n🗑️ 删除不必要的模块...")
    for module in unnecessary_modules:
        file_path = current_dir / module
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {module}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {module} - {e}")
    
    print("\n🗑️ 删除测试和演示文件...")
    for pattern in test_demo_files:
        for file_path in current_dir.glob(pattern):
            if file_path.is_file():
                try:
                    file_path.unlink()
                    print(f"   ❌ 删除: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ⚠️ 删除失败: {file_path} - {e}")
    
    print("\n🗑️ 删除结果和缓存目录...")
    for dir_name in result_dirs:
        dir_path = current_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            try:
                shutil.rmtree(dir_path)
                print(f"   ❌ 删除目录: {dir_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {dir_name} - {e}")
    
    print("\n🗑️ 删除多余的报告文件...")
    report_patterns = [
        '*_report_*.md',
        '*_report_*.json', 
        'step_by_step_report_*.md',
        'continuous_processing_report_*.json',
        'health_check_report_*.json'
    ]
    
    for pattern in report_patterns:
        for file_path in current_dir.glob(pattern):
            if file_path.is_file() and str(file_path) not in keep_files:
                try:
                    file_path.unlink()
                    print(f"   ❌ 删除: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ⚠️ 删除失败: {file_path} - {e}")
    
    # 清理core目录中的多余文件
    print("\n🗑️ 清理core目录...")
    core_dir = current_dir / 'core'
    if core_dir.exists():
        for file_path in core_dir.iterdir():
            if file_path.is_file():
                relative_path = str(file_path.relative_to(current_dir))
                if relative_path not in keep_files:
                    # 检查是否是重要的核心文件
                    if not any(important in file_path.name.lower() for important in 
                             ['ultimate_spot_trading_gui', 'gate_api_connector', 'api_login_dialog', 
                              'doubling_growth_engine', 'fast_profit_engine']):
                        try:
                            file_path.unlink()
                            print(f"   ❌ 删除: {relative_path}")
                            removed_count += 1
                        except Exception as e:
                            print(f"   ⚠️ 删除失败: {relative_path} - {e}")
    
    print("\n✅ 保留的核心文件:")
    for keep_file in sorted(keep_files):
        file_path = current_dir / keep_file
        if file_path.exists():
            print(f"   ✅ 保留: {keep_file}")
            kept_count += 1
    
    print(f"\n📊 清理统计:")
    print(f"   🗑️ 删除文件: {removed_count} 个")
    print(f"   ✅ 保留文件: {kept_count} 个")
    
    return removed_count, kept_count

def verify_core_functionality():
    """验证核心功能是否完整"""
    
    print("\n🔍 验证核心功能...")
    print("-" * 40)
    
    core_files = [
        'core/ultimate_spot_trading_gui.py',
        'core/gate_api_connector.py', 
        'core/api_login_dialog.py'
    ]
    
    all_good = True
    
    for file_path in core_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - 存在")
        else:
            print(f"❌ {file_path} - 缺失")
            all_good = False
    
    # 测试导入
    print("\n🧪 测试模块导入...")
    try:
        import sys
        sys.path.insert(0, 'core')
        
        from ultimate_spot_trading_gui import UltimateSpotTradingGUI
        print("✅ ultimate_spot_trading_gui - 导入成功")
        
        from gate_api_connector import GateAPIConnector
        print("✅ gate_api_connector - 导入成功")
        
        from api_login_dialog import show_api_login_dialog
        print("✅ api_login_dialog - 导入成功")
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        all_good = False
    
    if all_good:
        print("\n🎉 核心功能验证通过！")
        print("🚀 可以启动系统: python core/ultimate_spot_trading_gui.py")
    else:
        print("\n⚠️ 核心功能验证失败，请检查文件完整性")
    
    return all_good

def main():
    """主函数"""
    print("🧹 终极版现货交易系统 - 清理脚本")
    print("=" * 60)
    print("⚠️ 警告: 此操作将删除除 ultimate_spot_trading_gui.py 外的所有GUI界面")
    print("📋 只保留最佳GUI界面及其必需的依赖文件")
    
    # 确认操作
    response = input("\n是否继续清理？(y/N): ").lower().strip()
    if response != 'y':
        print("❌ 清理操作已取消")
        return False
    
    try:
        # 执行清理
        removed_count, kept_count = cleanup_system()
        
        # 验证功能
        verify_success = verify_core_functionality()
        
        print(f"\n🎉 清理完成!")
        print(f"📊 删除了 {removed_count} 个文件，保留了 {kept_count} 个核心文件")
        
        if verify_success:
            print("\n🚀 系统已准备就绪，可以启动最佳GUI界面:")
            print("   python core/ultimate_spot_trading_gui.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()

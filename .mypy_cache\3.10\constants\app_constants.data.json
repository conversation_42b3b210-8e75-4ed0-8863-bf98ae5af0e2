{".class": "MypyFile", "_fullname": "constants.app_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APP_CONSTANTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.APP_CONSTANTS", "name": "APP_CONSTANTS", "type": "constants.app_constants.AppConstants"}}, "AppConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constants.app_constants.AppConstants", "name": "AppConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constants.app_constants.AppConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constants.app_constants", "mro": ["constants.app_constants.AppConstants", "builtins.object"], "names": {".class": "SymbolTable", "ACCOUNT_DATA_REFRESH_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.ACCOUNT_DATA_REFRESH_INTERVAL", "name": "ACCOUNT_DATA_REFRESH_INTERVAL", "type": "builtins.float"}}, "AMOUNT_DECIMAL_PLACES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.AMOUNT_DECIMAL_PLACES", "name": "AMOUNT_DECIMAL_PLACES", "type": "builtins.int"}}, "ANIMATION_DURATION_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.ANIMATION_DURATION_MS", "name": "ANIMATION_DURATION_MS", "type": "builtins.int"}}, "API_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.API_TIMEOUT", "name": "API_TIMEOUT", "type": "builtins.float"}}, "APP_AUTHOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.APP_AUTHOR", "name": "APP_AUTHOR", "type": "builtins.str"}}, "APP_DESCRIPTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.APP_DESCRIPTION", "name": "APP_DESCRIPTION", "type": "builtins.str"}}, "APP_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.APP_NAME", "name": "APP_NAME", "type": "builtins.str"}}, "APP_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.APP_VERSION", "name": "APP_VERSION", "type": "builtins.str"}}, "AUTO_BACKUP_ENABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.AUTO_BACKUP_ENABLED", "name": "AUTO_BACKUP_ENABLED", "type": "builtins.bool"}}, "BACKUP_INTERVAL_HOURS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.BACKUP_INTERVAL_HOURS", "name": "BACKUP_INTERVAL_HOURS", "type": "builtins.int"}}, "BURST_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.BURST_LIMIT", "name": "BURST_LIMIT", "type": "builtins.int"}}, "CACHE_EXPIRY_SECONDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.CACHE_EXPIRY_SECONDS", "name": "CACHE_EXPIRY_SECONDS", "type": "builtins.int"}}, "CLICK_DELAY_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.CLICK_DELAY_MS", "name": "CLICK_DELAY_MS", "type": "builtins.int"}}, "CONNECTION_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.CONNECTION_TIMEOUT", "name": "CONNECTION_TIMEOUT", "type": "builtins.float"}}, "DEBUG_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEBUG_MODE", "name": "DEBUG_MODE", "type": "builtins.bool"}}, "DEFAULT_CACHE_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_CACHE_SIZE", "name": "DEFAULT_CACHE_SIZE", "type": "builtins.int"}}, "DEFAULT_ENVIRONMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_ENVIRONMENT", "name": "DEFAULT_ENVIRONMENT", "type": "builtins.str"}}, "DEFAULT_HISTORY_DAYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_HISTORY_DAYS", "name": "DEFAULT_HISTORY_DAYS", "type": "builtins.int"}}, "DEFAULT_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_INITIAL_CAPITAL", "name": "DEFAULT_INITIAL_CAPITAL", "type": "builtins.float"}}, "DEFAULT_LANGUAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_LANGUAGE", "name": "DEFAULT_LANGUAGE", "type": "builtins.str"}}, "DEFAULT_LOG_LEVEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_LOG_LEVEL", "name": "DEFAULT_LOG_LEVEL", "type": "builtins.str"}}, "DEFAULT_THEME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_THEME", "name": "DEFAULT_THEME", "type": "builtins.str"}}, "DEFAULT_THREAD_POOL_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_THREAD_POOL_SIZE", "name": "DEFAULT_THREAD_POOL_SIZE", "type": "builtins.int"}}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "type": "builtins.float"}}, "DEFAULT_UPDATE_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_UPDATE_INTERVAL", "name": "DEFAULT_UPDATE_INTERVAL", "type": "builtins.float"}}, "DEFAULT_WINDOW_HEIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_WINDOW_HEIGHT", "name": "DEFAULT_WINDOW_HEIGHT", "type": "builtins.int"}}, "DEFAULT_WINDOW_WIDTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_WINDOW_WIDTH", "name": "DEFAULT_WINDOW_WIDTH", "type": "builtins.int"}}, "DEFAULT_WINDOW_X": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_WINDOW_X", "name": "DEFAULT_WINDOW_X", "type": "builtins.int"}}, "DEFAULT_WINDOW_Y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DEFAULT_WINDOW_Y", "name": "DEFAULT_WINDOW_Y", "type": "builtins.int"}}, "DOUBLE_CLICK_INTERVAL_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.DOUBLE_CLICK_INTERVAL_MS", "name": "DOUBLE_CLICK_INTERVAL_MS", "type": "builtins.int"}}, "ENABLE_PROFILING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.ENABLE_PROFILING", "name": "ENABLE_PROFILING", "type": "builtins.bool"}}, "ENVIRONMENT_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.ENVIRONMENT_TYPES", "name": "ENVIRONMENT_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "EXECUTION_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.EXECUTION_TIMEOUT", "name": "EXECUTION_TIMEOUT", "type": "builtins.float"}}, "FADE_DURATION_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.FADE_DURATION_MS", "name": "FADE_DURATION_MS", "type": "builtins.int"}}, "FAST_UPDATE_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.FAST_UPDATE_INTERVAL", "name": "FAST_UPDATE_INTERVAL", "type": "builtins.float"}}, "HOVER_DELAY_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.HOVER_DELAY_MS", "name": "HOVER_DELAY_MS", "type": "builtins.int"}}, "LOCKOUT_DURATION_MINUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.LOCKOUT_DURATION_MINUTES", "name": "LOCKOUT_DURATION_MINUTES", "type": "builtins.int"}}, "LOG_DATE_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.LOG_DATE_FORMAT", "name": "LOG_DATE_FORMAT", "type": "builtins.str"}}, "LOG_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.LOG_FORMAT", "name": "LOG_FORMAT", "type": "builtins.str"}}, "LOG_LEVELS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.LOG_LEVELS", "name": "LOG_LEVELS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LONG_OPERATION_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.LONG_OPERATION_TIMEOUT", "name": "LONG_OPERATION_TIMEOUT", "type": "builtins.float"}}, "MARKET_DATA_REFRESH_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MARKET_DATA_REFRESH_INTERVAL", "name": "MARKET_DATA_REFRESH_INTERVAL", "type": "builtins.float"}}, "MAX_BACKUP_FILES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_BACKUP_FILES", "name": "MAX_BACKUP_FILES", "type": "builtins.int"}}, "MAX_CACHE_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_CACHE_SIZE", "name": "MAX_CACHE_SIZE", "type": "builtins.int"}}, "MAX_CONFIG_FILE_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_CONFIG_FILE_SIZE", "name": "MAX_CONFIG_FILE_SIZE", "type": "builtins.int"}}, "MAX_CPU_USAGE_PERCENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_CPU_USAGE_PERCENT", "name": "MAX_CPU_USAGE_PERCENT", "type": "builtins.float"}}, "MAX_DATA_FILE_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_DATA_FILE_SIZE", "name": "MAX_DATA_FILE_SIZE", "type": "builtins.int"}}, "MAX_HISTORY_DAYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_HISTORY_DAYS", "name": "MAX_HISTORY_DAYS", "type": "builtins.int"}}, "MAX_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_INITIAL_CAPITAL", "name": "MAX_INITIAL_CAPITAL", "type": "builtins.float"}}, "MAX_LOGIN_ATTEMPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_LOGIN_ATTEMPTS", "name": "MAX_LOGIN_ATTEMPTS", "type": "builtins.int"}}, "MAX_LOG_FILE_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_LOG_FILE_SIZE", "name": "MAX_LOG_FILE_SIZE", "type": "builtins.int"}}, "MAX_LOG_RECORDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_LOG_RECORDS", "name": "MAX_LOG_RECORDS", "type": "builtins.int"}}, "MAX_MEMORY_USAGE_PERCENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_MEMORY_USAGE_PERCENT", "name": "MAX_MEMORY_USAGE_PERCENT", "type": "builtins.float"}}, "MAX_ORDER_RECORDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_ORDER_RECORDS", "name": "MAX_ORDER_RECORDS", "type": "builtins.int"}}, "MAX_PASSWORD_LENGTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_PASSWORD_LENGTH", "name": "MAX_PASSWORD_LENGTH", "type": "builtins.int"}}, "MAX_RETRY_ATTEMPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_RETRY_ATTEMPTS", "name": "MAX_RETRY_ATTEMPTS", "type": "builtins.int"}}, "MAX_THREAD_POOL_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_THREAD_POOL_SIZE", "name": "MAX_THREAD_POOL_SIZE", "type": "builtins.int"}}, "MAX_TRADE_RECORDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_TRADE_RECORDS", "name": "MAX_TRADE_RECORDS", "type": "builtins.int"}}, "MAX_WINDOW_HEIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_WINDOW_HEIGHT", "name": "MAX_WINDOW_HEIGHT", "type": "builtins.int"}}, "MAX_WINDOW_WIDTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MAX_WINDOW_WIDTH", "name": "MAX_WINDOW_WIDTH", "type": "builtins.int"}}, "MIN_HISTORY_DAYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_HISTORY_DAYS", "name": "MIN_HISTORY_DAYS", "type": "builtins.int"}}, "MIN_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_INITIAL_CAPITAL", "name": "MIN_INITIAL_CAPITAL", "type": "builtins.float"}}, "MIN_NUMPY_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_NUMPY_VERSION", "name": "MIN_NUMPY_VERSION", "type": "builtins.str"}}, "MIN_PANDAS_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_PANDAS_VERSION", "name": "MIN_PANDAS_VERSION", "type": "builtins.str"}}, "MIN_PASSWORD_LENGTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_PASSWORD_LENGTH", "name": "MIN_PASSWORD_LENGTH", "type": "builtins.int"}}, "MIN_PYTHON_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_PYTHON_VERSION", "name": "MIN_PYTHON_VERSION", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "MIN_THREAD_POOL_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_THREAD_POOL_SIZE", "name": "MIN_THREAD_POOL_SIZE", "type": "builtins.int"}}, "MIN_TKINTER_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_TKINTER_VERSION", "name": "MIN_TKINTER_VERSION", "type": "builtins.str"}}, "MIN_WINDOW_HEIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_WINDOW_HEIGHT", "name": "MIN_WINDOW_HEIGHT", "type": "builtins.int"}}, "MIN_WINDOW_WIDTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.MIN_WINDOW_WIDTH", "name": "MIN_WINDOW_WIDTH", "type": "builtins.int"}}, "ORDER_DATA_REFRESH_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.ORDER_DATA_REFRESH_INTERVAL", "name": "ORDER_DATA_REFRESH_INTERVAL", "type": "builtins.float"}}, "PERCENTAGE_DECIMAL_PLACES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.PERCENTAGE_DECIMAL_PLACES", "name": "PERCENTAGE_DECIMAL_PLACES", "type": "builtins.int"}}, "PERFORMANCE_UPDATE_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.PERFORMANCE_UPDATE_INTERVAL", "name": "PERFORMANCE_UPDATE_INTERVAL", "type": "builtins.float"}}, "POSITION_DATA_REFRESH_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.POSITION_DATA_REFRESH_INTERVAL", "name": "POSITION_DATA_REFRESH_INTERVAL", "type": "builtins.float"}}, "PRICE_DECIMAL_PLACES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.PRICE_DECIMAL_PLACES", "name": "PRICE_DECIMAL_PLACES", "type": "builtins.int"}}, "RATE_LIMIT_PER_MINUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.RATE_LIMIT_PER_MINUTE", "name": "RATE_LIMIT_PER_MINUTE", "type": "builtins.int"}}, "RATE_LIMIT_PER_SECOND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.RATE_LIMIT_PER_SECOND", "name": "RATE_LIMIT_PER_SECOND", "type": "builtins.int"}}, "RECOMMENDED_PYTHON_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.RECOMMENDED_PYTHON_VERSION", "name": "RECOMMENDED_PYTHON_VERSION", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "REQUIRE_SPECIAL_CHARS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.REQUIRE_SPECIAL_CHARS", "name": "REQUIRE_SPECIAL_CHARS", "type": "builtins.bool"}}, "RETRY_DELAY_BASE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.RETRY_DELAY_BASE", "name": "RETRY_DELAY_BASE", "type": "builtins.float"}}, "RETRY_DELAY_MULTIPLIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.RETRY_DELAY_MULTIPLIER", "name": "RETRY_DELAY_MULTIPLIER", "type": "builtins.float"}}, "SESSION_TIMEOUT_MINUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.SESSION_TIMEOUT_MINUTES", "name": "SESSION_TIMEOUT_MINUTES", "type": "builtins.int"}}, "SLOW_UPDATE_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.SLOW_UPDATE_INTERVAL", "name": "SLOW_UPDATE_INTERVAL", "type": "builtins.float"}}, "SUPPORTED_LANGUAGES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.SUPPORTED_LANGUAGES", "name": "SUPPORTED_LANGUAGES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SUPPORTED_THEMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.SUPPORTED_THEMES", "name": "SUPPORTED_THEMES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SYSTEM_MONITOR_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.SYSTEM_MONITOR_INTERVAL", "name": "SYSTEM_MONITOR_INTERVAL", "type": "builtins.float"}}, "TRANSITION_DURATION_MS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.TRANSITION_DURATION_MS", "name": "TRANSITION_DURATION_MS", "type": "builtins.int"}}, "VERBOSE_LOGGING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.VERBOSE_LOGGING", "name": "VERBOSE_LOGGING", "type": "builtins.bool"}}, "WEBSOCKET_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.app_constants.AppConstants.WEBSOCKET_TIMEOUT", "name": "WEBSOCKET_TIMEOUT", "type": "builtins.float"}}, "get_app_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.get_app_info", "name": "get_app_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_app_info of AppConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.get_app_info", "name": "get_app_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_app_info of AppConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_max_window_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.get_max_window_size", "name": "get_max_window_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_max_window_size of AppConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.get_max_window_size", "name": "get_max_window_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_max_window_size of AppConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_min_window_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.get_min_window_size", "name": "get_min_window_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_min_window_size of AppConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.get_min_window_size", "name": "get_min_window_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_min_window_size of AppConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.get_retry_delay", "name": "get_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "attempt"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_retry_delay of AppConstants", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.get_retry_delay", "name": "get_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "attempt"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_retry_delay of AppConstants", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_window_geometry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.get_window_geometry", "name": "get_window_geometry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_geometry of AppConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.get_window_geometry", "name": "get_window_geometry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_geometry of AppConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.is_valid_language", "name": "is_valid_language", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_language of AppConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.is_valid_language", "name": "is_valid_language", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_language of AppConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "theme"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.app_constants.AppConstants.is_valid_theme", "name": "is_valid_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "theme"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_theme of AppConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.app_constants.AppConstants.is_valid_theme", "name": "is_valid_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "theme"], "arg_types": [{".class": "TypeType", "item": "constants.app_constants.AppConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_theme of AppConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constants.app_constants.AppConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constants.app_constants.AppConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.app_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\app_constants.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单管理系统
Order Management System for Real Trading
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Callable, Dict, List, Optional

from gate_trading_engine import GateIOTradingEngine, SmartOrderRouter


class OrderManagementSystem:
    """订单管理系统"""

    def __init__(self, trading_engine: GateIOTradingEngine):
        """初始化订单管理系统"""
        self.engine = trading_engine
        self.router = SmartOrderRouter(trading_engine)

        # 订单队列和状态
        self.pending_orders = []  # 待执行订单
        self.executing_orders = {}  # 执行中订单
        self.completed_orders = []  # 已完成订单

        # 监控和回调
        self.order_callbacks = []  # 订单状态回调
        self.monitoring_active = False
        self.monitor_thread = None

        # 统计信息
        self.stats = {
            "total_orders": 0,
            "successful_orders": 0,
            "failed_orders": 0,
            "total_volume": 0.0,
            "total_fees": 0.0,
        }

    def add_order_callback(self, callback: Callable):
        """添加订单状态回调函数"""
        self.order_callbacks.append(callback)

    def submit_order(
        self,
        strategy_name: str,
        symbol: str,
        side: str,
        amount: float,
        order_type: str = "market",
        price: float = None,
        priority: int = 1,
    ) -> str:
        """
        提交订单到队列

        Args:
            strategy_name: 策略名称
            symbol: 交易对
            side: 买卖方向
            amount: 数量/金额
            order_type: 订单类型
            price: 价格（限价单）
            priority: 优先级（1-5，1最高）

        Returns:
            订单队列ID
        """
        try:
            queue_id = (
                f"queue_{int(time.time() * 1000)}_{len(self.pending_orders)}"
            )

            order_request = {
                "queue_id": queue_id,
                "strategy_name": strategy_name,
                "symbol": symbol,
                "side": side,
                "amount": amount,
                "order_type": order_type,
                "price": price,
                "priority": priority,
                "submit_time": datetime.now(),
                "status": "pending",
            }

            # 按优先级插入队列
            inserted = False
            for i, pending_order in enumerate(self.pending_orders):
                if priority < pending_order["priority"]:  # 数字越小优先级越高
                    self.pending_orders.insert(i, order_request)
                    inserted = True
                    break

            if not inserted:
                self.pending_orders.append(order_request)

            print(f"📝 订单已提交到队列: {queue_id} ({strategy_name})")
            return queue_id

        except Exception as e:
            print(f"❌ 提交订单失败: {e}")
            return ""

    def start_order_processing(self):
        """启动订单处理"""
        if self.monitoring_active:
            return

        print("🚀 启动订单管理系统...")
        self.monitoring_active = True

        # 启动订单处理线程
        self.monitor_thread = threading.Thread(
            target=self._order_processing_loop, daemon=True
        )
        self.monitor_thread.start()

        print("✅ 订单管理系统已启动")

    def stop_order_processing(self):
        """停止订单处理"""
        if not self.monitoring_active:
            return

        print("🛑 停止订单管理系统...")
        self.monitoring_active = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        print("✅ 订单管理系统已停止")

    def _order_processing_loop(self):
        """订单处理循环"""
        while self.monitoring_active:
            try:
                # 处理待执行订单
                self._process_pending_orders()

                # 监控执行中订单
                self._monitor_executing_orders()

                # 清理已完成订单
                self._cleanup_completed_orders()

                time.sleep(1)  # 1秒检查一次

            except Exception as e:
                print(f"❌ 订单处理循环异常: {e}")
                time.sleep(5)

    def _process_pending_orders(self):
        """处理待执行订单"""
        if not self.pending_orders:
            return

        # 取出最高优先级订单
        order_request = self.pending_orders.pop(0)

        try:
            print(f"⚡ 执行订单: {order_request['queue_id']}")

            # 更新状态
            order_request["status"] = "executing"
            order_request["execute_time"] = datetime.now()

            # 执行订单
            result = self.router.execute_strategy_order(
                order_request["strategy_name"],
                order_request["symbol"],
                order_request["side"],
                order_request["amount"],
                order_request["order_type"],
                order_request["price"],
            )

            # 处理执行结果
            if result["success"]:
                order_request["status"] = "executed"
                order_request["order_id"] = result["order_id"]
                order_request["execute_price"] = result.get("price", 0)
                order_request["execute_amount"] = result.get("amount", 0)

                # 添加到执行中订单（等待成交确认）
                self.executing_orders[result["order_id"]] = order_request

                # 更新统计
                self.stats["total_orders"] += 1
                self.stats["successful_orders"] += 1
                self.stats["total_volume"] += result.get("value", 0)

            else:
                order_request["status"] = "failed"
                order_request["error"] = result["error"]

                # 添加到已完成订单
                self.completed_orders.append(order_request)

                # 更新统计
                self.stats["total_orders"] += 1
                self.stats["failed_orders"] += 1

            # 调用回调函数
            self._notify_order_status(order_request)

        except Exception as e:
            print(f"❌ 处理订单异常: {e}")
            order_request["status"] = "error"
            order_request["error"] = str(e)
            self.completed_orders.append(order_request)

    def _monitor_executing_orders(self):
        """监控执行中订单"""
        completed_order_ids = []

        for order_id, order_request in self.executing_orders.items():
            try:
                # 查询订单状态
                status_result = self.engine.get_order_status(order_id)

                if status_result["success"]:
                    order_info = status_result["order"]
                    order_status = order_info.get("status", "unknown")

                    # 检查是否已完成
                    if order_status in ["filled", "cancelled", "expired"]:
                        order_request["status"] = "completed"
                        order_request["final_status"] = order_status
                        order_request["complete_time"] = datetime.now()

                        if order_status == "filled":
                            order_request["fill_price"] = float(
                                order_info.get("price", 0)
                            )
                            order_request["fill_amount"] = float(
                                order_info.get("amount", 0)
                            )
                            order_request["fee"] = float(
                                order_info.get("fee", 0)
                            )
                            self.stats["total_fees"] += order_request["fee"]

                        # 移动到已完成订单
                        self.completed_orders.append(order_request)
                        completed_order_ids.append(order_id)

                        # 调用回调函数
                        self._notify_order_status(order_request)

            except Exception as e:
                print(f"❌ 监控订单{order_id}异常: {e}")

        # 清理已完成的执行中订单
        for order_id in completed_order_ids:
            del self.executing_orders[order_id]

    def _cleanup_completed_orders(self):
        """清理已完成订单（保留最近100个）"""
        if len(self.completed_orders) > 100:
            self.completed_orders = self.completed_orders[-100:]

    def _notify_order_status(self, order_request: Dict):
        """通知订单状态变化"""
        for callback in self.order_callbacks:
            try:
                callback(order_request)
            except Exception as e:
                print(f"❌ 订单回调异常: {e}")

    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        return {
            "pending_count": len(self.pending_orders),
            "executing_count": len(self.executing_orders),
            "completed_count": len(self.completed_orders),
            "total_processed": self.stats["total_orders"],
        }

    def get_order_stats(self) -> Dict:
        """获取订单统计"""
        success_rate = 0
        if self.stats["total_orders"] > 0:
            success_rate = (
                self.stats["successful_orders"] / self.stats["total_orders"]
            ) * 100

        return {
            "total_orders": self.stats["total_orders"],
            "successful_orders": self.stats["successful_orders"],
            "failed_orders": self.stats["failed_orders"],
            "success_rate": success_rate,
            "total_volume": self.stats["total_volume"],
            "total_fees": self.stats["total_fees"],
        }

    def cancel_pending_order(self, queue_id: str) -> bool:
        """取消待执行订单"""
        for i, order in enumerate(self.pending_orders):
            if order["queue_id"] == queue_id:
                order["status"] = "cancelled"
                order["cancel_time"] = datetime.now()
                self.completed_orders.append(order)
                del self.pending_orders[i]
                print(f"✅ 已取消待执行订单: {queue_id}")
                return True
        return False

    def cancel_executing_order(self, order_id: str) -> bool:
        """取消执行中订单"""
        if order_id in self.executing_orders:
            result = self.engine.cancel_order(order_id)
            if result["success"]:
                order_request = self.executing_orders[order_id]
                order_request["status"] = "cancelled"
                order_request["cancel_time"] = datetime.now()
                self.completed_orders.append(order_request)
                del self.executing_orders[order_id]
                print(f"✅ 已取消执行中订单: {order_id}")
                return True
        return False

    def get_recent_orders(self, limit: int = 20) -> List[Dict]:
        """获取最近订单"""
        all_orders = []

        # 添加待执行订单
        for order in self.pending_orders:
            all_orders.append({**order, "category": "pending"})

        # 添加执行中订单
        for order in self.executing_orders.values():
            all_orders.append({**order, "category": "executing"})

        # 添加已完成订单
        for order in self.completed_orders[-limit:]:
            all_orders.append({**order, "category": "completed"})

        # 按时间排序
        all_orders.sort(
            key=lambda x: x.get("submit_time", datetime.min), reverse=True
        )

        return all_orders[:limit]


class TradingSystemIntegrator:
    """交易系统集成器"""

    def __init__(
        self, api_key: str = "", api_secret: str = "", demo_mode: bool = True
    ):
        """初始化交易系统集成器"""
        self.demo_mode = demo_mode

        # 初始化核心组件
        self.trading_engine = GateIOTradingEngine(
            api_key, api_secret, testnet=True, demo_mode=demo_mode
        )
        self.order_manager = OrderManagementSystem(self.trading_engine)

        # 启动订单管理
        self.order_manager.start_order_processing()

        print(
            f"🎯 交易系统集成器初始化完成 ({'演示模式' if demo_mode else '真实交易模式'})"
        )

    def execute_strategy_trade(
        self, strategy_name: str, symbol: str, side: str, amount: float
    ) -> str:
        """执行策略交易"""
        return self.order_manager.submit_order(
            strategy_name=strategy_name,
            symbol=symbol,
            side=side,
            amount=amount,
            order_type="market",
            priority=1,
        )

    def get_account_info(self) -> Dict:
        """获取账户信息"""
        balance = self.trading_engine.get_account_balance()
        stats = self.order_manager.get_order_stats()
        queue_status = self.order_manager.get_queue_status()

        return {
            "balance": balance,
            "order_stats": stats,
            "queue_status": queue_status,
            "demo_mode": self.demo_mode,
        }

    def stop(self):
        """停止交易系统"""
        self.order_manager.stop_order_processing()
        print("✅ 交易系统已停止")


# 全局交易系统实例
trading_system = None


def get_trading_system(
    api_key: str = "", api_secret: str = "", demo_mode: bool = True
) -> TradingSystemIntegrator:
    """获取交易系统实例"""
    global trading_system
    if trading_system is None:
        trading_system = TradingSystemIntegrator(
            api_key, api_secret, demo_mode
        )
    return trading_system

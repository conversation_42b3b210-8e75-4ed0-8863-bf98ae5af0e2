#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一错误处理系统
Unified Error Handling System

提供统一的异常处理、错误记录和恢复机制
"""

import functools
import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, Optional

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""

    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class ErrorCategory(Enum):
    """错误类别"""

    NETWORK = "NETWORK"
    API = "API"
    DATA = "DATA"
    TRADING = "TRADING"
    SYSTEM = "SYSTEM"
    VALIDATION = "VALIDATION"
    SECURITY = "SECURITY"


class TradingError(Exception):
    """交易系统基础异常"""

    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Dict = None,
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now()


class NetworkError(TradingError):
    """网络相关错误"""

    def __init__(self, message: str, details: Dict = None):
        super().__init__(
            message, ErrorCategory.NETWORK, ErrorSeverity.HIGH, details
        )


class APIError(TradingError):
    """API相关错误"""

    def __init__(
        self, message: str, api_response: Dict = None, details: Dict = None
    ):
        details = details or {}
        if api_response:
            details["api_response"] = api_response
        super().__init__(
            message, ErrorCategory.API, ErrorSeverity.HIGH, details
        )


class DataError(TradingError):
    """数据相关错误"""

    def __init__(
        self, message: str, data_source: str = None, details: Dict = None
    ):
        details = details or {}
        if data_source:
            details["data_source"] = data_source
        super().__init__(
            message, ErrorCategory.DATA, ErrorSeverity.MEDIUM, details
        )


class TradingOperationError(TradingError):
    """交易操作错误"""

    def __init__(
        self, message: str, operation: str = None, details: Dict = None
    ):
        details = details or {}
        if operation:
            details["operation"] = operation
        super().__init__(
            message, ErrorCategory.TRADING, ErrorSeverity.CRITICAL, details
        )


class ValidationError(TradingError):
    """验证错误"""

    def __init__(
        self,
        message: str,
        field: str = None,
        value: Any = None,
        details: Dict = None,
    ):
        details = details or {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        super().__init__(
            message, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, details
        )


class SecurityError(TradingError):
    """安全相关错误"""

    def __init__(self, message: str, details: Dict = None):
        super().__init__(
            message, ErrorCategory.SECURITY, ErrorSeverity.CRITICAL, details
        )


class ErrorHandler:
    """错误处理器"""

    def __init__(self):
        self.error_counts = {}
        self.error_history = []
        self.max_history = 1000

    def handle_error(self, error: Exception, context: str = None) -> bool:
        """
        处理错误

        Args:
            error: 异常对象
            context: 错误上下文

        Returns:
            bool: 是否应该继续执行
        """
        try:
            # 记录错误
            self._log_error(error, context)

            # 更新错误统计
            self._update_error_stats(error)

            # 保存错误历史
            self._save_error_history(error, context)

            # 根据错误类型决定处理策略
            return self._determine_recovery_strategy(error)

        except Exception as e:
            logger.critical(f"错误处理器本身出错: {e}")
            return False

    def _log_error(self, error: Exception, context: str = None):
        """记录错误日志"""
        error_info = {
            "type": type(error).__name__,
            "message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat(),
        }

        if isinstance(error, TradingError):
            error_info.update(
                {
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "details": error.details,
                }
            )

            # 根据严重程度选择日志级别
            if error.severity == ErrorSeverity.CRITICAL:
                logger.critical(f"严重错误: {error.message}", extra=error_info)
            elif error.severity == ErrorSeverity.HIGH:
                logger.error(f"高级错误: {error.message}", extra=error_info)
            elif error.severity == ErrorSeverity.MEDIUM:
                logger.warning(f"中级错误: {error.message}", extra=error_info)
            else:
                logger.info(f"低级错误: {error.message}", extra=error_info)
        else:
            logger.error(f"未分类错误: {error}", extra=error_info)

        # 记录堆栈跟踪
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"堆栈跟踪: {traceback.format_exc()}")

    def _update_error_stats(self, error: Exception):
        """更新错误统计"""
        error_type = type(error).__name__
        self.error_counts[error_type] = (
            self.error_counts.get(error_type, 0) + 1
        )

    def _save_error_history(self, error: Exception, context: str = None):
        """保存错误历史"""
        error_record = {
            "timestamp": datetime.now(),
            "type": type(error).__name__,
            "message": str(error),
            "context": context,
        }

        if isinstance(error, TradingError):
            error_record.update(
                {
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "details": error.details,
                }
            )

        self.error_history.append(error_record)

        # 限制历史记录数量
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history :]

    def _determine_recovery_strategy(self, error: Exception) -> bool:
        """确定恢复策略"""
        if isinstance(error, TradingError):
            if error.severity == ErrorSeverity.CRITICAL:
                # 严重错误，停止执行
                logger.critical("检测到严重错误，停止系统运行")
                return False
            elif error.severity == ErrorSeverity.HIGH:
                # 高级错误，可能需要人工干预
                logger.error("检测到高级错误，建议检查系统状态")
                return True
            else:
                # 中低级错误，可以继续执行
                return True
        else:
            # 未分类错误，保守处理
            logger.warning("检测到未分类错误，继续执行但需要关注")
            return True

    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return {
            "error_counts": self.error_counts.copy(),
            "total_errors": sum(self.error_counts.values()),
            "recent_errors": len(
                [
                    e
                    for e in self.error_history
                    if (datetime.now() - e["timestamp"]).seconds < 3600
                ]
            ),
        }

    def get_recent_errors(self, limit: int = 10) -> list:
        """获取最近的错误"""
        return self.error_history[-limit:]


# 全局错误处理器
_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """获取错误处理器实例"""
    return _error_handler


def handle_errors(context: str = None, reraise: bool = False):
    """
    错误处理装饰器

    Args:
        context: 错误上下文描述
        reraise: 是否重新抛出异常
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or f"{func.__module__}.{func.__name__}"
                should_continue = _error_handler.handle_error(e, error_context)

                if reraise or not should_continue:
                    raise

                return None

        return wrapper

    return decorator


def safe_execute(
    func: Callable, *args, default=None, context: str = None, **kwargs
):
    """
    安全执行函数

    Args:
        func: 要执行的函数
        *args: 函数参数
        default: 出错时的默认返回值
        context: 错误上下文
        **kwargs: 函数关键字参数
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_context = context or f"{func.__module__}.{func.__name__}"
        _error_handler.handle_error(e, error_context)
        return default

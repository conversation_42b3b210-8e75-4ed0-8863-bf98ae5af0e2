{".class": "MypyFile", "_fullname": "matplotlib.projections.polar", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InvertedPolarTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.InvertedPolarTransform", "name": "InvertedPolarTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.InvertedPolarTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.InvertedPolarTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "axis", "use_rmin", "apply_theta_transforms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.InvertedPolarTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "axis", "use_rmin", "apply_theta_transforms"], "arg_types": ["matplotlib.projections.polar.InvertedPolarTransform", {".class": "UnionType", "items": ["matplotlib.projections.polar.PolarAxes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvertedPolarTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.InvertedPolarTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.InvertedPolarTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.InvertedPolarTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedPolarTransform", "ret_type": "matplotlib.projections.polar.PolarTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.InvertedPolarTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.InvertedPolarTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.InvertedPolarTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Line2D": {".class": "SymbolTableNode", "cross_ref": "matplotlib.lines.Line2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PolarAffine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Affine2DBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.PolarAffine", "name": "PolarAffine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAffine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.PolarAffine", "matplotlib.transforms.Affine2DBase", "matplotlib.transforms.AffineBase", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "scale_transform", "limits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAffine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "scale_transform", "limits"], "arg_types": ["matplotlib.projections.polar.PolarAffine", "matplotlib.transforms.Transform", "matplotlib.transforms.BboxBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PolarAffine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.PolarAffine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.PolarAffine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PolarAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axes._axes.Axes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.PolarAxes", "name": "PolarAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.PolarAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "InvertedPolarTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.InvertedPolarTransform", "name": "InvertedPolarTransform", "type": "builtins.type"}}, "PolarAffine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.PolarAffine", "name": "PolarAffine", "type": "builtins.type"}}, "PolarTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.PolarTransform", "name": "PolarTransform", "type": "builtins.type"}}, "RadialLocator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.RadialLocator", "name": "RadialLocator", "type": "builtins.type"}}, "ThetaFormatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.ThetaFormatter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "builtins.type"}}, "ThetaLocator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib.projections.polar.PolarAxes.ThetaLocator", "name": "ThetaLocator", "type": "builtins.type"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "theta_offset", "theta_direction", "rlabel_position", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "theta_offset", "theta_direction", "rlabel_position", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.can_pan", "name": "can_pan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_pan of PolarAxes", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.can_zoom", "name": "can_zoom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_zoom of PolarAxes", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drag_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "button", "key", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.drag_pan", "name": "drag_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "button", "key", "x", "y"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drag_pan of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.end_pan", "name": "end_pan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_pan of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_coord": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theta", "r"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.format_coord", "name": "format_coord", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "theta", "r"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_coord of PolarAxes", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_data_ratio", "name": "get_data_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data_ratio of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rlabel_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_rlabel_position", "name": "get_rlabel_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rlabel_position of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_rmax", "name": "get_rmax", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rmax of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_rmin", "name": "get_rmin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rmin of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rorigin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_rorigin", "name": "get_rorigin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rorigin of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rsign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_rsign", "name": "get_rsign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rsign of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_theta_direction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_theta_direction", "name": "get_theta_direction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_theta_direction of PolarAxes", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_theta_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_theta_offset", "name": "get_theta_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_theta_offset of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_thetamax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_thetamax", "name": "get_thetamax", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_thetamax of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_thetamin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_thetamin", "name": "get_thetamin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_thetamin of PolarAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_text1_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_xaxis_text1_transform", "name": "get_xaxis_text1_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_text1_transform of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_text2_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_xaxis_text2_transform", "name": "get_xaxis_text2_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_text2_transform of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "which"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_xaxis_transform", "name": "get_xaxis_transform", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "which"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tick1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tick2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grid"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_transform of PolarAxes", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_text1_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_yaxis_text1_transform", "name": "get_yaxis_text1_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_text1_transform of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_text2_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_yaxis_text2_transform", "name": "get_yaxis_text2_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_text2_transform of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "which"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.get_yaxis_transform", "name": "get_yaxis_transform", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "which"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tick1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tick2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grid"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_transform of PolarAxes", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.PolarAxes.name", "name": "name", "type": "builtins.str"}}, "set_rgrids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "radii", "labels", "angle", "fmt", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rgrids", "name": "set_rgrids", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "radii", "labels", "angle", "fmt", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "matplotlib.text.Text"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rgrids of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["matplotlib.lines.Line2D"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["matplotlib.text.Text"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rlabel_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rlabel_position", "name": "set_rlabel_position", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rlabel_position of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rlim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 4], "arg_names": ["self", "bottom", "top", "emit", "auto", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rlim", "name": "set_rlim", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 4], "arg_names": ["self", "bottom", "top", "emit", "auto", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rlim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rmax", "name": "set_rmax", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rmax"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rmax of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rmin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rmin", "name": "set_rmin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rmin"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rmin of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rorigin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rorigin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rorigin", "name": "set_rorigin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rorigin"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rorigin of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rscale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rscale", "name": "set_rscale", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rscale of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rticks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_rticks", "name": "set_rticks", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rticks of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theta_direction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "direction"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_theta_direction", "name": "set_theta_direction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "direction"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clockwise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "counterclockwise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "anticlockwise"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theta_direction of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theta_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_theta_offset", "name": "set_theta_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theta_offset of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theta_zero_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "loc", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_theta_zero_location", "name": "set_theta_zero_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "loc", "offset"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "NW"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "W"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "SW"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "S"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "SE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "E"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "NE"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theta_zero_location of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_thetagrids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "angles", "labels", "fmt", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetagrids", "name": "set_thetagrids", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "angles", "labels", "fmt", "kwargs"], "arg_types": ["matplotlib.projections.polar.PolarAxes", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "matplotlib.text.Text"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetagrids of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["matplotlib.lines.Line2D"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["matplotlib.text.Text"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_thetalim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetalim", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetalim", "name": "set_thetalim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetalim", "name": "set_thetalim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "thetamin", "thetamax"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetalim", "name": "set_thetalim", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "thetamin", "thetamax"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetalim", "name": "set_thetalim", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "thetamin", "thetamax"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "thetamin", "thetamax"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetalim of PolarAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_thetamax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thetamax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetamax", "name": "set_thetamax", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thetamax"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetamax of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_thetamin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thetamin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.set_thetamin", "name": "set_thetamin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thetamin"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_thetamin of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarAxes.start_pan", "name": "start_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "button"], "arg_types": ["matplotlib.projections.polar.PolarAxes", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_pan of PolarAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_sticky_edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.PolarAxes.use_sticky_edges", "name": "use_sticky_edges", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.PolarAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.PolarAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PolarTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.PolarTransform", "name": "PolarTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.PolarTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "axis", "use_rmin", "apply_theta_transforms", "scale_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "axis", "use_rmin", "apply_theta_transforms", "scale_transform"], "arg_types": ["matplotlib.projections.polar.PolarTransform", {".class": "UnionType", "items": ["matplotlib.projections.polar.PolarAxes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PolarTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.PolarTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.PolarTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar.PolarTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of PolarTransform", "ret_type": "matplotlib.projections.polar.InvertedPolarTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.PolarTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.PolarTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.PolarTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RadialAxis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axis.YAxis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.RadialAxis", "name": "Radial<PERSON><PERSON>s", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.RadialAxis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.RadialAxis", "matplotlib.axis.YAxis", "matplotlib.axis.Axis", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "axis_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.RadialAxis.axis_name", "name": "axis_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.RadialAxis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.RadialAxis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RadialLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Locator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.RadialLocator", "name": "RadialLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.RadialLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.RadialLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "base", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.RadialLocator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "base", "axes"], "arg_types": ["matplotlib.projections.polar.RadialLocator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["matplotlib.projections.polar.PolarAxes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RadialLocator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.RadialLocator.base", "name": "base", "type": "matplotlib.ticker.Locator"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.RadialLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.RadialLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RadialTick": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axis.YTick"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.RadialTick", "name": "RadialTick", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.RadialTick", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.RadialTick", "matplotlib.axis.YTick", "matplotlib.axis.Tick", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.RadialTick.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.RadialTick", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Text": {".class": "SymbolTableNode", "cross_ref": "matplotlib.text.Text", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ThetaAxis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axis.XAxis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.ThetaAxis", "name": "ThetaAxis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaAxis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.ThetaAxis", "matplotlib.axis.XAxis", "matplotlib.axis.Axis", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "axis_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.ThetaAxis.axis_name", "name": "axis_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.ThetaAxis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.ThetaAxis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThetaFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.ThetaFormatter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.ThetaFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.ThetaFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.ThetaFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThetaLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Locator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.ThetaLocator", "name": "ThetaLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.ThetaLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaLocator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "base"], "arg_types": ["matplotlib.projections.polar.ThetaLocator", "matplotlib.ticker.Locator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThetaLocator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.ThetaLocator.axis", "name": "axis", "type": {".class": "UnionType", "items": ["matplotlib.projections.polar._AxisWrapper", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.polar.ThetaLocator.base", "name": "base", "type": "matplotlib.ticker.Locator"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.ThetaLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.ThetaLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThetaTick": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axis.XTick"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar.ThetaTick", "name": "ThetaTick", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaTick", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar.ThetaTick", "matplotlib.axis.XTick", "matplotlib.axis.Tick", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "axes", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar.ThetaTick.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "axes", "args", "kwargs"], "arg_types": ["matplotlib.projections.polar.ThetaTick", "matplotlib.projections.polar.PolarAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThetaTick", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar.ThetaTick.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar.ThetaTick", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AxisWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar._AxisWrapper", "name": "_AxisWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar._AxisWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "arg_types": ["matplotlib.projections.polar._AxisWrapper", "matplotlib.axis.Axis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AxisWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.get_data_interval", "name": "get_data_interval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar._AxisWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data_interval of _AxisWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_minpos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.get_minpos", "name": "get_minpos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar._AxisWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_minpos of _AxisWrapper", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tick_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.get_tick_space", "name": "get_tick_space", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar._AxisWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tick_space of _AxisWrapper", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_view_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.get_view_interval", "name": "get_view_interval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.polar._AxisWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_interval of _AxisWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_data_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.set_data_interval", "name": "set_data_interval", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "arg_types": ["matplotlib.projections.polar._AxisWrapper", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_data_interval of _AxisWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_view_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._AxisWrapper.set_view_interval", "name": "set_view_interval", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "arg_types": ["matplotlib.projections.polar._AxisWrapper", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_view_interval of _AxisWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar._AxisWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar._AxisWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WedgeBbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Bbox"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.polar._WedgeBbox", "name": "_WedgeBbox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._WedgeBbox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.polar", "mro": ["matplotlib.projections.polar._WedgeBbox", "matplotlib.transforms.Bbox", "matplotlib.transforms.BboxBase", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "center", "viewLim", "originLim", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.polar._WedgeBbox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "center", "viewLim", "originLim", "kwargs"], "arg_types": ["matplotlib.projections.polar._WedgeBbox", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "matplotlib.transforms.Bbox", "matplotlib.transforms.Bbox", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WedgeBbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.polar._WedgeBbox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.polar._WedgeBbox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.polar.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "maxis": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mticker": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mtransforms": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\projections\\polar.pyi"}
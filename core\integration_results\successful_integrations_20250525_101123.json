{"AdaptiveDualMAStrategy": {"strategy_name": "AdaptiveDualMAStrategy", "strategy_file": "../strategies\\adaptive_dual_ma_strategy.py", "validation_score": 13.88826197654943, "sharpe_ratio": 0.1256105339587444, "max_drawdown": -0.4022996711372282, "win_rate": 0.4801641586867305, "r_squared": 0.003299525804962067, "tracking_error": 0.23930326110524364, "alpha_contribution": 0.046760997589284474, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.698788", "status": "SUCCESS"}, "AdaptiveNeuralNetworkStrategy": {"strategy_name": "AdaptiveNeuralNetworkStrategy", "strategy_file": "../strategies\\adaptive_neural_network_strategy.py", "validation_score": 18.353701400883846, "sharpe_ratio": -0.5972133402602744, "max_drawdown": -0.41470138446667726, "win_rate": 0.49247606019151846, "r_squared": 0.010428763250991069, "tracking_error": 0.18962865124223865, "alpha_contribution": -0.0798915609798359, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.779896", "status": "SUCCESS"}, "adaptive_parameters": {"strategy_name": "adaptive_parameters", "strategy_file": "../strategies\\adaptive_parameters.py", "validation_score": 31.730922473516554, "sharpe_ratio": 0.2604016715130509, "max_drawdown": -0.31647994064372037, "win_rate": 0.5129958960328317, "r_squared": 0.010697499872223237, "tracking_error": 0.23991802486347671, "alpha_contribution": 0.0650343468145414, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.868066", "status": "SUCCESS"}, "AdvancedEnsembleStrategy": {"strategy_name": "AdvancedEnsembleStrategy", "strategy_file": "../strategies\\advanced_ensemble_strategy.py", "validation_score": 19.06222378063054, "sharpe_ratio": -0.55453847846412, "max_drawdown": -0.39976064064593364, "win_rate": 0.4952120383036936, "r_squared": 0.005063678189724974, "tracking_error": 0.24143810948493066, "alpha_contribution": -0.13255529016218573, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:22.952120", "status": "SUCCESS"}, "BollingerBandsStrategy": {"strategy_name": "BollingerBandsStrategy", "strategy_file": "../strategies\\advanced_strategies.py", "validation_score": 38.43304708368234, "sharpe_ratio": -0.05180682372433076, "max_drawdown": -0.12460425635480687, "win_rate": 0.5006839945280438, "r_squared": 0.011435591085299901, "tracking_error": 0.16068266242590892, "alpha_contribution": -0.010118265387563472, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.028878", "status": "SUCCESS"}, "AlligatorTradingStrategy": {"strategy_name": "AlligatorTradingStrategy", "strategy_file": "../strategies\\alligator_trading_strategy.py", "validation_score": 42.931601507824496, "sharpe_ratio": 0.7591154528993052, "max_drawdown": -0.24112243992109114, "win_rate": 0.5047879616963065, "r_squared": 0.0123989869529596, "tracking_error": 0.23401543825758248, "alpha_contribution": 0.1587682642323191, "recommendation": "谨慎：策略存在一定风险，需要进一步优化", "integration_time": "2025-05-25 10:11:23.104973", "status": "SUCCESS"}, "ArbitrageStrategy": {"strategy_name": "ArbitrageStrategy", "strategy_file": "../strategies\\arbitrage_strategy.py", "validation_score": 82.68013860430347, "sharpe_ratio": 1.12299578446607, "max_drawdown": -0.044722314437577876, "win_rate": 0.5526675786593708, "r_squared": 0.004069252293076864, "tracking_error": 0.04664336524096581, "alpha_contribution": 0.07257734840918198, "recommendation": "强烈推荐：策略表现优异，符合机构级标准", "integration_time": "2025-05-25 10:11:23.204656", "status": "SUCCESS"}, "Strategy": {"strategy_name": "Strategy", "strategy_file": "../strategies\\auto_trading_strategy.py", "validation_score": 12.60233088147718, "sharpe_ratio": 0.005682067885072185, "max_drawdown": -0.34523264382909563, "win_rate": 0.5006839945280438, "r_squared": 0.000394622892442853, "tracking_error": 0.22527042228314187, "alpha_contribution": 0.017927376994262525, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.282903", "status": "SUCCESS"}, "BaggingEnsembleStrategy": {"strategy_name": "BaggingEnsembleStrategy", "strategy_file": "../strategies\\bagging_ensemble_strategy.py", "validation_score": 12.062564026880988, "sharpe_ratio": -0.06222213373005013, "max_drawdown": -0.36306780861656923, "win_rate": 0.5198358413132695, "r_squared": 0.028192744633628086, "tracking_error": 0.23797800423036009, "alpha_contribution": 0.007545917197755675, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.362332", "status": "SUCCESS"}, "BaseStrategy": {"strategy_name": "BaseStrategy", "strategy_file": "../strategies\\base_strategy.py", "validation_score": 22.44846672478775, "sharpe_ratio": 0.48125248879126825, "max_drawdown": -0.2613632906383669, "win_rate": 0.506155950752394, "r_squared": 0.0057222582295713975, "tracking_error": 0.24235640976301406, "alpha_contribution": 0.15355505616136983, "recommendation": "不推荐：策略风险过高，不符合机构标准", "integration_time": "2025-05-25 10:11:23.457006", "status": "SUCCESS"}}
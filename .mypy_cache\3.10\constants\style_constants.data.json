{".class": "MypyFile", "_fullname": "constants.style_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "STYLE_CONSTANTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.STYLE_CONSTANTS", "name": "STYLE_CONSTANTS", "type": "constants.style_constants.StyleConstants"}}, "StyleConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constants.style_constants.StyleConstants", "name": "StyleConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constants.style_constants.StyleConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constants.style_constants", "mro": ["constants.style_constants.StyleConstants", "builtins.object"], "names": {".class": "SymbolTable", "ALPHA_ACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_ACTIVE", "name": "ALPHA_ACTIVE", "type": "builtins.float"}}, "ALPHA_DISABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_DISABLED", "name": "ALPHA_DISABLED", "type": "builtins.float"}}, "ALPHA_FOCUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_FOCUS", "name": "ALPHA_FOCUS", "type": "builtins.float"}}, "ALPHA_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_HIGH", "name": "ALPHA_HIGH", "type": "builtins.float"}}, "ALPHA_HOVER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_HOVER", "name": "ALPHA_HOVER", "type": "builtins.float"}}, "ALPHA_LOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_LOW", "name": "ALPHA_LOW", "type": "builtins.float"}}, "ALPHA_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_MEDIUM", "name": "ALPHA_MEDIUM", "type": "builtins.float"}}, "ALPHA_OPAQUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_OPAQUE", "name": "ALPHA_OPAQUE", "type": "builtins.float"}}, "ALPHA_TRANSPARENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_TRANSPARENT", "name": "ALPHA_TRANSPARENT", "type": "builtins.float"}}, "ALPHA_VERY_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_VERY_HIGH", "name": "ALPHA_VERY_HIGH", "type": "builtins.float"}}, "ALPHA_VERY_LOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ALPHA_VERY_LOW", "name": "ALPHA_VERY_LOW", "type": "builtins.float"}}, "ANIMATION_DURATION_FAST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ANIMATION_DURATION_FAST", "name": "ANIMATION_DURATION_FAST", "type": "builtins.int"}}, "ANIMATION_DURATION_INSTANT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ANIMATION_DURATION_INSTANT", "name": "ANIMATION_DURATION_INSTANT", "type": "builtins.int"}}, "ANIMATION_DURATION_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ANIMATION_DURATION_NORMAL", "name": "ANIMATION_DURATION_NORMAL", "type": "builtins.int"}}, "ANIMATION_DURATION_SLOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ANIMATION_DURATION_SLOW", "name": "ANIMATION_DURATION_SLOW", "type": "builtins.int"}}, "ANIMATION_DURATION_VERY_SLOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ANIMATION_DURATION_VERY_SLOW", "name": "ANIMATION_DURATION_VERY_SLOW", "type": "builtins.int"}}, "BORDER_RADIUS_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_LARGE", "name": "BORDER_RADIUS_LARGE", "type": "builtins.int"}}, "BORDER_RADIUS_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_MEDIUM", "name": "BORDER_RADIUS_MEDIUM", "type": "builtins.int"}}, "BORDER_RADIUS_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_NONE", "name": "BORDER_RADIUS_NONE", "type": "builtins.int"}}, "BORDER_RADIUS_ROUND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_ROUND", "name": "BORDER_RADIUS_ROUND", "type": "builtins.int"}}, "BORDER_RADIUS_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_SMALL", "name": "BORDER_RADIUS_SMALL", "type": "builtins.int"}}, "BORDER_RADIUS_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_RADIUS_XLARGE", "name": "BORDER_RADIUS_XLARGE", "type": "builtins.int"}}, "BORDER_STYLE_DASHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_STYLE_DASHED", "name": "BORDER_STYLE_DASHED", "type": "builtins.str"}}, "BORDER_STYLE_DOTTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_STYLE_DOTTED", "name": "BORDER_STYLE_DOTTED", "type": "builtins.str"}}, "BORDER_STYLE_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_STYLE_NONE", "name": "BORDER_STYLE_NONE", "type": "builtins.str"}}, "BORDER_STYLE_SOLID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BORDER_STYLE_SOLID", "name": "BORDER_STYLE_SOLID", "type": "builtins.str"}}, "BREAKPOINT_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BREAKPOINT_LARGE", "name": "BREAKPOINT_LARGE", "type": "builtins.int"}}, "BREAKPOINT_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BREAKPOINT_MEDIUM", "name": "BREAKPOINT_MEDIUM", "type": "builtins.int"}}, "BREAKPOINT_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BREAKPOINT_SMALL", "name": "BREAKPOINT_SMALL", "type": "builtins.int"}}, "BREAKPOINT_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BREAKPOINT_XLARGE", "name": "BREAKPOINT_XLARGE", "type": "builtins.int"}}, "BUTTON_HEIGHT_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_HEIGHT_LARGE", "name": "BUTTON_HEIGHT_LARGE", "type": "builtins.int"}}, "BUTTON_HEIGHT_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_HEIGHT_MEDIUM", "name": "BUTTON_HEIGHT_MEDIUM", "type": "builtins.int"}}, "BUTTON_HEIGHT_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_HEIGHT_SMALL", "name": "BUTTON_HEIGHT_SMALL", "type": "builtins.int"}}, "BUTTON_HEIGHT_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_HEIGHT_XLARGE", "name": "BUTTON_HEIGHT_XLARGE", "type": "builtins.int"}}, "BUTTON_WIDTH_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_WIDTH_LARGE", "name": "BUTTON_WIDTH_LARGE", "type": "builtins.int"}}, "BUTTON_WIDTH_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_WIDTH_MEDIUM", "name": "BUTTON_WIDTH_MEDIUM", "type": "builtins.int"}}, "BUTTON_WIDTH_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_WIDTH_SMALL", "name": "BUTTON_WIDTH_SMALL", "type": "builtins.int"}}, "BUTTON_WIDTH_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.BUTTON_WIDTH_XLARGE", "name": "BUTTON_WIDTH_XLARGE", "type": "builtins.int"}}, "COLOR_BLACK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_BLACK", "name": "COLOR_BLACK", "type": "builtins.str"}}, "COLOR_BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_BUY", "name": "COLOR_BUY", "type": "builtins.str"}}, "COLOR_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_ERROR", "name": "COLOR_ERROR", "type": "builtins.str"}}, "COLOR_GRAY_100": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_100", "name": "COLOR_GRAY_100", "type": "builtins.str"}}, "COLOR_GRAY_200": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_200", "name": "COLOR_GRAY_200", "type": "builtins.str"}}, "COLOR_GRAY_300": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_300", "name": "COLOR_GRAY_300", "type": "builtins.str"}}, "COLOR_GRAY_400": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_400", "name": "COLOR_GRAY_400", "type": "builtins.str"}}, "COLOR_GRAY_500": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_500", "name": "COLOR_GRAY_500", "type": "builtins.str"}}, "COLOR_GRAY_600": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_600", "name": "COLOR_GRAY_600", "type": "builtins.str"}}, "COLOR_GRAY_700": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_700", "name": "COLOR_GRAY_700", "type": "builtins.str"}}, "COLOR_GRAY_800": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_800", "name": "COLOR_GRAY_800", "type": "builtins.str"}}, "COLOR_GRAY_900": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_GRAY_900", "name": "COLOR_GRAY_900", "type": "builtins.str"}}, "COLOR_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_INFO", "name": "COLOR_INFO", "type": "builtins.str"}}, "COLOR_LOSS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_LOSS", "name": "COLOR_LOSS", "type": "builtins.str"}}, "COLOR_NEUTRAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_NEUTRAL", "name": "COLOR_NEUTRAL", "type": "builtins.str"}}, "COLOR_PRIMARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_PRIMARY", "name": "COLOR_PRIMARY", "type": "builtins.str"}}, "COLOR_PROFIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_PROFIT", "name": "COLOR_PROFIT", "type": "builtins.str"}}, "COLOR_SECONDARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_SECONDARY", "name": "COLOR_SECONDARY", "type": "builtins.str"}}, "COLOR_SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_SELL", "name": "COLOR_SELL", "type": "builtins.str"}}, "COLOR_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_SUCCESS", "name": "COLOR_SUCCESS", "type": "builtins.str"}}, "COLOR_TRANSPARENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_TRANSPARENT", "name": "COLOR_TRANSPARENT", "type": "builtins.str"}}, "COLOR_WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_WARNING", "name": "COLOR_WARNING", "type": "builtins.str"}}, "COLOR_WHITE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.COLOR_WHITE", "name": "COLOR_WHITE", "type": "builtins.str"}}, "EASING_EASE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.EASING_EASE", "name": "EASING_EASE", "type": "builtins.str"}}, "EASING_EASE_IN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.EASING_EASE_IN", "name": "EASING_EASE_IN", "type": "builtins.str"}}, "EASING_EASE_IN_OUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.EASING_EASE_IN_OUT", "name": "EASING_EASE_IN_OUT", "type": "builtins.str"}}, "EASING_EASE_OUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.EASING_EASE_OUT", "name": "EASING_EASE_OUT", "type": "builtins.str"}}, "EASING_LINEAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.EASING_LINEAR", "name": "EASING_LINEAR", "type": "builtins.str"}}, "ENTRY_HEIGHT_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_HEIGHT_LARGE", "name": "ENTRY_HEIGHT_LARGE", "type": "builtins.int"}}, "ENTRY_HEIGHT_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_HEIGHT_MEDIUM", "name": "ENTRY_HEIGHT_MEDIUM", "type": "builtins.int"}}, "ENTRY_HEIGHT_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_HEIGHT_SMALL", "name": "ENTRY_HEIGHT_SMALL", "type": "builtins.int"}}, "ENTRY_WIDTH_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_WIDTH_LARGE", "name": "ENTRY_WIDTH_LARGE", "type": "builtins.int"}}, "ENTRY_WIDTH_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_WIDTH_MEDIUM", "name": "ENTRY_WIDTH_MEDIUM", "type": "builtins.int"}}, "ENTRY_WIDTH_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_WIDTH_SMALL", "name": "ENTRY_WIDTH_SMALL", "type": "builtins.int"}}, "ENTRY_WIDTH_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.ENTRY_WIDTH_XLARGE", "name": "ENTRY_WIDTH_XLARGE", "type": "builtins.int"}}, "FONT_FAMILY_CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_CODE", "name": "FONT_FAMILY_CODE", "type": "builtins.str"}}, "FONT_FAMILY_MONO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_MONO", "name": "FONT_FAMILY_MONO", "type": "builtins.str"}}, "FONT_FAMILY_SANS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_SANS", "name": "FONT_FAMILY_SANS", "type": "builtins.str"}}, "FONT_FAMILY_SERIF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_SERIF", "name": "FONT_FAMILY_SERIF", "type": "builtins.str"}}, "FONT_FAMILY_SYSTEM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_SYSTEM", "name": "FONT_FAMILY_SYSTEM", "type": "builtins.str"}}, "FONT_FAMILY_UI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_FAMILY_UI", "name": "FONT_FAMILY_UI", "type": "builtins.str"}}, "FONT_SIZE_HUGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_HUGE", "name": "FONT_SIZE_HUGE", "type": "builtins.int"}}, "FONT_SIZE_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_LARGE", "name": "FONT_SIZE_LARGE", "type": "builtins.int"}}, "FONT_SIZE_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_MEDIUM", "name": "FONT_SIZE_MEDIUM", "type": "builtins.int"}}, "FONT_SIZE_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_NORMAL", "name": "FONT_SIZE_NORMAL", "type": "builtins.int"}}, "FONT_SIZE_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_SMALL", "name": "FONT_SIZE_SMALL", "type": "builtins.int"}}, "FONT_SIZE_TINY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_TINY", "name": "FONT_SIZE_TINY", "type": "builtins.int"}}, "FONT_SIZE_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_XLARGE", "name": "FONT_SIZE_XLARGE", "type": "builtins.int"}}, "FONT_SIZE_XXLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_SIZE_XXLARGE", "name": "FONT_SIZE_XXLARGE", "type": "builtins.int"}}, "FONT_STYLE_ITALIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_STYLE_ITALIC", "name": "FONT_STYLE_ITALIC", "type": "builtins.str"}}, "FONT_STYLE_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_STYLE_NORMAL", "name": "FONT_STYLE_NORMAL", "type": "builtins.str"}}, "FONT_WEIGHT_BOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_WEIGHT_BOLD", "name": "FONT_WEIGHT_BOLD", "type": "builtins.str"}}, "FONT_WEIGHT_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FONT_WEIGHT_NORMAL", "name": "FONT_WEIGHT_NORMAL", "type": "builtins.str"}}, "FRAME_BORDER_WIDTH_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FRAME_BORDER_WIDTH_MEDIUM", "name": "FRAME_BORDER_WIDTH_MEDIUM", "type": "builtins.int"}}, "FRAME_BORDER_WIDTH_THICK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FRAME_BORDER_WIDTH_THICK", "name": "FRAME_BORDER_WIDTH_THICK", "type": "builtins.int"}}, "FRAME_BORDER_WIDTH_THIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FRAME_BORDER_WIDTH_THIN", "name": "FRAME_BORDER_WIDTH_THIN", "type": "builtins.int"}}, "FRAME_MIN_HEIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FRAME_MIN_HEIGHT", "name": "FRAME_MIN_HEIGHT", "type": "builtins.int"}}, "FRAME_MIN_WIDTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.FRAME_MIN_WIDTH", "name": "FRAME_MIN_WIDTH", "type": "builtins.int"}}, "GRID_COLUMNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.GRID_COLUMNS", "name": "GRID_COLUMNS", "type": "builtins.int"}}, "GRID_GUTTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.GRID_GUTTER", "name": "GRID_GUTTER", "type": "builtins.int"}}, "GRID_MARGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.GRID_MARGIN", "name": "GRID_MARGIN", "type": "builtins.int"}}, "MARGIN_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_LARGE", "name": "MARGIN_LARGE", "type": "builtins.int"}}, "MARGIN_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_MEDIUM", "name": "MARGIN_MEDIUM", "type": "builtins.int"}}, "MARGIN_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_NONE", "name": "MARGIN_NONE", "type": "builtins.int"}}, "MARGIN_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_SMALL", "name": "MARGIN_SMALL", "type": "builtins.int"}}, "MARGIN_TINY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_TINY", "name": "MARGIN_TINY", "type": "builtins.int"}}, "MARGIN_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.MARGIN_XLARGE", "name": "MARGIN_XLARGE", "type": "builtins.int"}}, "PADDING_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_LARGE", "name": "PADDING_LARGE", "type": "builtins.int"}}, "PADDING_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_MEDIUM", "name": "PADDING_MEDIUM", "type": "builtins.int"}}, "PADDING_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_NONE", "name": "PADDING_NONE", "type": "builtins.int"}}, "PADDING_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_SMALL", "name": "PADDING_SMALL", "type": "builtins.int"}}, "PADDING_TINY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_TINY", "name": "PADDING_TINY", "type": "builtins.int"}}, "PADDING_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_XLARGE", "name": "PADDING_XLARGE", "type": "builtins.int"}}, "PADDING_XXLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.PADDING_XXLARGE", "name": "PADDING_XXLARGE", "type": "builtins.int"}}, "SCROLLBAR_MIN_HEIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SCROLLBAR_MIN_HEIGHT", "name": "SCROLLBAR_MIN_HEIGHT", "type": "builtins.int"}}, "SCROLLBAR_WIDTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SCROLLBAR_WIDTH", "name": "SCROLLBAR_WIDTH", "type": "builtins.int"}}, "SHADOW_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SHADOW_LARGE", "name": "SHADOW_LARGE", "type": "builtins.str"}}, "SHADOW_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SHADOW_MEDIUM", "name": "SHADOW_MEDIUM", "type": "builtins.str"}}, "SHADOW_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SHADOW_NONE", "name": "SHADOW_NONE", "type": "builtins.str"}}, "SHADOW_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SHADOW_SMALL", "name": "SHADOW_SMALL", "type": "builtins.str"}}, "SHADOW_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SHADOW_XLARGE", "name": "SHADOW_XLARGE", "type": "builtins.str"}}, "SPACING_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_LARGE", "name": "SPACING_LARGE", "type": "builtins.int"}}, "SPACING_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_MEDIUM", "name": "SPACING_MEDIUM", "type": "builtins.int"}}, "SPACING_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_NONE", "name": "SPACING_NONE", "type": "builtins.int"}}, "SPACING_SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_SMALL", "name": "SPACING_SMALL", "type": "builtins.int"}}, "SPACING_TINY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_TINY", "name": "SPACING_TINY", "type": "builtins.int"}}, "SPACING_XLARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.SPACING_XLARGE", "name": "SPACING_XLARGE", "type": "builtins.int"}}, "Z_INDEX_BACKGROUND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_BACKGROUND", "name": "Z_INDEX_BACKGROUND", "type": "builtins.int"}}, "Z_INDEX_DROPDOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_DROPDOWN", "name": "Z_INDEX_DROPDOWN", "type": "builtins.int"}}, "Z_INDEX_ELEVATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_ELEVATED", "name": "Z_INDEX_ELEVATED", "type": "builtins.int"}}, "Z_INDEX_MODAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_MODAL", "name": "Z_INDEX_MODAL", "type": "builtins.int"}}, "Z_INDEX_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_NORMAL", "name": "Z_INDEX_NORMAL", "type": "builtins.int"}}, "Z_INDEX_TOOLTIP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.style_constants.StyleConstants.Z_INDEX_TOOLTIP", "name": "Z_INDEX_TOOLTIP", "type": "builtins.int"}}, "get_button_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.get_button_style", "name": "get_button_style", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "size"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_button_style of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "any"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.get_button_style", "name": "get_button_style", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "size"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_button_style of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "any"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_font_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "family", "size", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.get_font_tuple", "name": "get_font_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "family", "size", "weight"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_font_tuple of StyleConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.get_font_tuple", "name": "get_font_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "family", "size", "weight"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_font_tuple of StyleConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_margin_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "margin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.get_margin_dict", "name": "get_margin_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "margin"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_margin_dict of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.get_margin_dict", "name": "get_margin_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "margin"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_margin_dict of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_padding_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.get_padding_dict", "name": "get_padding_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "padding"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_padding_dict of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.get_padding_dict", "name": "get_padding_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "padding"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_padding_dict of StyleConstants", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_responsive_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "base_size", "screen_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.get_responsive_size", "name": "get_responsive_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "base_size", "screen_width"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_responsive_size of StyleConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.get_responsive_size", "name": "get_responsive_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "base_size", "screen_width"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_responsive_size of StyleConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hex_to_rgba": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "hex_color"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.hex_to_rgba", "name": "hex_to_rgba", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "hex_color"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hex_to_rgba of StyleConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.hex_to_rgba", "name": "hex_to_rgba", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "hex_color"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hex_to_rgba of StyleConstants", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rgba_to_hex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "r", "g", "b", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.style_constants.StyleConstants.rgba_to_hex", "name": "rgba_to_hex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "r", "g", "b", "a"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int", "builtins.int", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rgba_to_hex of StyleConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.style_constants.StyleConstants.rgba_to_hex", "name": "rgba_to_hex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "r", "g", "b", "a"], "arg_types": [{".class": "TypeType", "item": "constants.style_constants.StyleConstants"}, "builtins.int", "builtins.int", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rgba_to_hex of StyleConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constants.style_constants.StyleConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constants.style_constants.StyleConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.style_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\style_constants.py"}
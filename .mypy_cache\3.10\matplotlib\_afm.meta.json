{"data_mtime": 1748490877, "dep_lines": [42, 38, 39, 40, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 30, 30, 30], "dependencies": ["matplotlib._mathtext_data", "collections", "logging", "re", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "1ba627db71dbcaaf9113dd41296884041ae8a354", "id": "matplotlib._afm", "ignore_all": true, "interface_hash": "59cac33adf62d1f13981bb3614c38a6f16310d6b", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\_afm.py", "plugin_data": null, "size": 16692, "suppressed": [], "version_id": "1.15.0"}
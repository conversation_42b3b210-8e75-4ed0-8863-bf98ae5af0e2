{".class": "MypyFile", "_fullname": "matplotlib.backend_bases", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Artist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist.Artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Bbox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Bbox", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BboxBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.BboxBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CapStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.CapStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CloseEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.CloseEvent", "name": "CloseEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.CloseEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.CloseEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.CloseEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.CloseEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Colorbar": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colorbar.Colorbar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Cursors": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_tools.Cursors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DrawEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.DrawEvent", "name": "DrawEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.DrawEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.DrawEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "canvas", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.DrawEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "canvas", "renderer"], "arg_types": ["matplotlib.backend_bases.DrawEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DrawEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.DrawEvent.renderer", "name": "renderer", "type": "matplotlib.backend_bases.RendererBase"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.DrawEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.DrawEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Event": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.Event", "name": "Event", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.Event", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "canvas", "guiEvent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.Event.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "canvas", "guiEvent"], "arg_types": ["matplotlib.backend_bases.Event", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Event", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.Event.canvas", "name": "canvas", "type": "matplotlib.backend_bases.FigureCanvasBase"}}, "guiEvent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.Event.guiEvent", "name": "guiEvent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.Event.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.Event.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.Event", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Figure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.Figure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FigureCanvasBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.FigureCanvasBase", "name": "FigureCanvasBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.FigureCanvasBase", "builtins.object"], "names": {".class": "SymbolTable", "_T": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.FigureCanvasBase._T", "name": "_T", "upper_bound": "matplotlib.backend_bases.FigureCanvasBase", "values": [], "variance": 0}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "bbox"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.blit", "name": "blit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "bbox"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["matplotlib.transforms.BboxBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blit of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "button_pick_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.button_pick_id", "name": "button_pick_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "button_pick_id of FigureCanvasBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.button_pick_id", "name": "button_pick_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "button_pick_id of FigureCanvasBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.callbacks", "name": "callbacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callbacks of FigureCanvasBase", "ret_type": "matplotlib.cbook.CallbackRegistry", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.callbacks", "name": "callbacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callbacks of FigureCanvasBase", "ret_type": "matplotlib.cbook.CallbackRegistry", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "device_pixel_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.device_pixel_ratio", "name": "device_pixel_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "device_pixel_ratio of FigureCanvasBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.device_pixel_ratio", "name": "device_pixel_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "device_pixel_ratio of FigureCanvasBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "draw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.draw", "name": "draw", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.draw_idle", "name": "draw_idle", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_idle of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.events", "name": "events", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.figure", "name": "figure", "type": "matplotlib.figure.Figure"}}, "filetypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.filetypes", "name": "filetypes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fixed_dpi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.fixed_dpi", "name": "fixed_dpi", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}}}, "flush_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.flush_events", "name": "flush_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush_events of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_default_filename", "name": "get_default_filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_filename of FigureCanvasBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_filetype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_default_filetype", "name": "get_default_filetype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_filetype of FigureCanvasBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_default_filetype", "name": "get_default_filetype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_filetype of FigureCanvasBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_supported_filetypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_supported_filetypes", "name": "get_supported_filetypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_filetypes of FigureCanvasBase", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_supported_filetypes", "name": "get_supported_filetypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_filetypes of FigureCanvasBase", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_supported_filetypes_grouped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_supported_filetypes_grouped", "name": "get_supported_filetypes_grouped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_filetypes_grouped of FigureCanvasBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_supported_filetypes_grouped", "name": "get_supported_filetypes_grouped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_filetypes_grouped of FigureCanvasBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_width_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "physical"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.get_width_height", "name": "get_width_height", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "physical"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width_height of FigureCanvasBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grab_mouse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.grab_mouse", "name": "grab_mouse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "matplotlib.axes._axes.Axes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grab_mouse of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inaxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.inaxes", "name": "inaxes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inaxes of FigureCanvasBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_saving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.is_saving", "name": "is_saving", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_saving of FigureCanvasBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.manager", "name": "manager", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "matplotlib.backend_bases.FigureManagerBase"], "uses_pep604_syntax": true}}}, "manager_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.manager_class", "name": "manager_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "manager_class of FigureCanvasBase", "ret_type": {".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.manager_class", "name": "manager_class", "type": "matplotlib._api.classproperty"}}}, "mouse_grabber": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.mouse_grabber", "name": "mouse_grabber", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "matplotlib.axes._axes.Axes"], "uses_pep604_syntax": true}}}, "mpl_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "s", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.mpl_connect", "name": "mpl_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "s", "func"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mpl_connect of FigureCanvasBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mpl_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.mpl_disconnect", "name": "mpl_disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cid"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mpl_disconnect of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "figure", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.new_manager", "name": "new_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "figure", "num"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, "matplotlib.figure.Figure", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_manager of FigureCanvasBase", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.new_manager", "name": "new_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "figure", "num"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, "matplotlib.figure.Figure", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_manager of FigureCanvasBase", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "interval", "callbacks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.new_timer", "name": "new_timer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "interval", "callbacks"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_timer of FigureCanvasBase", "ret_type": "matplotlib.backend_bases.TimerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "filename", "dpi", "facecolor", "edgecolor", "orientation", "format", "bbox_inches", "pad_inches", "bbox_extra_artists", "backend", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.print_figure", "name": "print_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "filename", "dpi", "facecolor", "edgecolor", "orientation", "format", "bbox_inches", "pad_inches", "bbox_extra_artists", "backend", "kwargs"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tight"}, "matplotlib.transforms.Bbox", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_figure of FigureCanvasBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release_mouse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.release_mouse", "name": "release_mouse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "matplotlib.axes._axes.Axes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release_mouse of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "required_interactive_framework": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.required_interactive_framework", "name": "required_interactive_framework", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scroll_pick_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.scroll_pick_id", "name": "scroll_pick_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scroll_pick_id of FigureCanvasBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.scroll_pick_id", "name": "scroll_pick_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scroll_pick_id of FigureCanvasBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.set_cursor", "name": "set_cursor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "matplotlib.backend_tools.Cursors"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cursor of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_event_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.start_event_loop", "name": "start_event_loop", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_event_loop of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_event_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureCanvasBase.stop_event_loop", "name": "stop_event_loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_event_loop of FigureCanvasBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_blit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.supports_blit", "name": "supports_blit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_blit of FigureCanvasBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.supports_blit", "name": "supports_blit", "type": "matplotlib._api.classproperty"}}}, "toolbar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.toolbar", "name": "toolbar", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "matplotlib.backend_bases.NavigationToolbar2"], "uses_pep604_syntax": true}}}, "widgetlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureCanvasBase.widgetlock", "name": "widgetlock", "type": "matplotlib.widgets.LockDraw"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.FigureCanvasBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.FigureCanvasBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FigureManagerBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.FigureManagerBase", "name": "FigureManagerBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.FigureManagerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "canvas", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "canvas", "num"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase", "matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "button_press_handler_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.button_press_handler_id", "name": "button_press_handler_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.canvas", "name": "canvas", "type": "matplotlib.backend_bases.FigureCanvasBase"}}, "create_with_canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "canvas_class", "figure", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureManagerBase.create_with_canvas", "name": "create_with_canvas", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "canvas_class", "figure", "num"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}, {".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, "matplotlib.figure.Figure", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_with_canvas of FigureManagerBase", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureManagerBase.create_with_canvas", "name": "create_with_canvas", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "canvas_class", "figure", "num"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}, {".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, "matplotlib.figure.Figure", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_with_canvas of FigureManagerBase", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "destroy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.destroy", "name": "destroy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "destroy of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "full_screen_toggle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.full_screen_toggle", "name": "full_screen_toggle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_screen_toggle of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.get_window_title", "name": "get_window_title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_title of FigureManagerBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_press_handler_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.key_press_handler_id", "name": "key_press_handler_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.num", "name": "num", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}}}, "pyplot_show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureManagerBase.pyplot_show", "name": "pyplot_show", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pyplot_show of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureManagerBase.pyplot_show", "name": "pyplot_show", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pyplot_show of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "w", "h"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.resize", "name": "resize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "w", "h"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resize of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_window_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.set_window_title", "name": "set_window_title", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "title"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_window_title of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.FigureManagerBase.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.FigureManagerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_main_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.FigureManagerBase.start_main_loop", "name": "start_main_loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_main_loop of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.FigureManagerBase.start_main_loop", "name": "start_main_loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_main_loop of FigureManagerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "toolbar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.toolbar", "name": "toolbar", "type": {".class": "UnionType", "items": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.ToolContainerBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "toolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.FigureManagerBase.toolmanager", "name": "toolmanager", "type": {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolManager", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.FigureManagerBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.FigureManagerBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FontProperties": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.FontProperties", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GraphicsContextBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.GraphicsContextBase", "name": "GraphicsContextBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_properties": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.copy_properties", "name": "copy_properties", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gc"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_properties of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_alpha", "name": "get_alpha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_alpha of GraphicsContextBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_antialiased", "name": "get_antialiased", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_antialiased of GraphicsContextBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_capstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_capstyle", "name": "get_capstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_capstyle of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "butt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "projecting"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_clip_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_clip_path", "name": "get_clip_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_clip_path of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.TransformedPath", "matplotlib.transforms.Transform"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_clip_rectangle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_clip_rectangle", "name": "get_clip_rectangle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_clip_rectangle of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.transforms.Bbox", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dashes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_dashes", "name": "get_dashes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dashes of GraphicsContextBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_forced_alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_forced_alpha", "name": "get_forced_alpha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_forced_alpha of GraphicsContextBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_gid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_gid", "name": "get_gid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_gid of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_hatch", "name": "get_hatch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_hatch_color", "name": "get_hatch_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch_color of GraphicsContextBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_hatch_linewidth", "name": "get_hatch_linewidth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch_linewidth of GraphicsContextBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "density"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_hatch_path", "name": "get_hatch_path", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "density"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch_path of GraphicsContextBase", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_joinstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_joinstyle", "name": "get_joinstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_joinstyle of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "miter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bevel"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_linewidth", "name": "get_linewidth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_linewidth of GraphicsContextBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_rgb", "name": "get_rgb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rgb of GraphicsContextBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sketch_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_sketch_params", "name": "get_sketch_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sketch_params of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_snap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_snap", "name": "get_snap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_snap of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.get_url", "name": "get_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_url of GraphicsContextBase", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.restore", "name": "restore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_alpha", "name": "set_alpha", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alpha of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_antialiased", "name": "set_antialiased", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_antialiased of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_capstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_capstyle", "name": "set_capstyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cs"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CapStyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_capstyle of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_clip_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_clip_path", "name": "set_clip_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["matplotlib.transforms.TransformedPath", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_clip_path of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_clip_rectangle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rectangle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_clip_rectangle", "name": "set_clip_rectangle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rectangle"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["matplotlib.transforms.Bbox", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_clip_rectangle of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_dashes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dash_offset", "dash_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_dashes", "name": "set_dashes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dash_offset", "dash_list"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_dashes of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_foreground": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fg", "isRGBA"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_foreground", "name": "set_foreground", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fg", "isRGBA"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_foreground of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_gid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_gid", "name": "set_gid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_gid of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_hatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_hatch", "name": "set_hatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hatch"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_hatch of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_hatch_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hatch_color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_hatch_color", "name": "set_hatch_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hatch_color"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_hatch_color of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_hatch_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hatch_linewidth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_hatch_linewidth", "name": "set_hatch_linewidth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hatch_linewidth"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_hatch_linewidth of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_joinstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "js"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_joinstyle", "name": "set_joinstyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "js"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.JoinStyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_joinstyle of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_linewidth", "name": "set_linewidth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "w"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_linewidth of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_sketch_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "scale", "length", "randomness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_sketch_params", "name": "set_sketch_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "scale", "length", "randomness"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_sketch_params of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_snap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "snap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_snap", "name": "set_snap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "snap"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_snap of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.GraphicsContextBase.set_url", "name": "set_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["matplotlib.backend_bases.GraphicsContextBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_url of GraphicsContextBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.GraphicsContextBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.GraphicsContextBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JoinStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.JoinStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KeyEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.LocationEvent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.KeyEvent", "name": "KeyEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.KeyEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.KeyEvent", "matplotlib.backend_bases.LocationEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "canvas", "key", "x", "y", "guiEvent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.KeyEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "canvas", "key", "x", "y", "guiEvent"], "arg_types": ["matplotlib.backend_bases.KeyEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeyEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.KeyEvent.key", "name": "key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.KeyEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.KeyEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.LineStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LocationEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.LocationEvent", "name": "LocationEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.LocationEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.LocationEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 5], "arg_names": ["self", "name", "canvas", "x", "y", "guiEvent", "modifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.LocationEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 5], "arg_names": ["self", "name", "canvas", "x", "y", "guiEvent", "modifiers"], "arg_types": ["matplotlib.backend_bases.LocationEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocationEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inaxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.LocationEvent.inaxes", "name": "inaxes", "type": {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.LocationEvent.x", "name": "x", "type": "builtins.int"}}, "xdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.LocationEvent.xdata", "name": "xdata", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.LocationEvent.y", "name": "y", "type": "builtins.int"}}, "ydata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.LocationEvent.ydata", "name": "ydata", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.LocationEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.LocationEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MouseButton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.MouseButton", "name": "MouseB<PERSON>on", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "matplotlib.backend_bases.MouseButton", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.MouseButton", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BACK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases.MouseButton.BACK", "name": "BACK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "FORWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases.MouseButton.FORWARD", "name": "FORWARD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}, "LEFT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases.MouseButton.LEFT", "name": "LEFT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "MIDDLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases.MouseButton.MIDDLE", "name": "MIDDLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "RIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases.MouseButton.RIGHT", "name": "RIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.MouseButton.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.MouseButton", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MouseEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.LocationEvent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.MouseEvent", "name": "MouseEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.MouseEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.MouseEvent", "matplotlib.backend_bases.LocationEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "name", "canvas", "x", "y", "button", "key", "step", "dblclick", "guiEvent", "buttons", "modifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.MouseEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "name", "canvas", "x", "y", "button", "key", "step", "dblclick", "guiEvent", "buttons", "modifiers"], "arg_types": ["matplotlib.backend_bases.MouseEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["matplotlib.backend_bases.MouseButton", {".class": "LiteralType", "fallback": "builtins.str", "value": "up"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "down"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.backend_bases.MouseButton"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MouseEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "button": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.MouseEvent.button", "name": "button", "type": {".class": "UnionType", "items": ["matplotlib.backend_bases.MouseButton", {".class": "LiteralType", "fallback": "builtins.str", "value": "up"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "down"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "dblclick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.MouseEvent.dblclick", "name": "dblclick", "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.MouseEvent.key", "name": "key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.MouseEvent.step", "name": "step", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.MouseEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.MouseEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NavigationToolbar2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.NavigationToolbar2", "name": "NavigationToolbar2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.NavigationToolbar2", "builtins.object"], "names": {".class": "SymbolTable", "UNKNOWN_SAVED_STATUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2.UNKNOWN_SAVED_STATUS", "name": "UNKNOWN_SAVED_STATUS", "type": "builtins.object"}}, "_PanInfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo", "name": "_PanInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["button", "axes", "cid"]}}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.NavigationToolbar2._PanInfo", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "button"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "axes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cid"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "button", "axes", "cid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "button", "axes", "cid"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _PanInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _PanInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _PanInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _PanInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "button", "axes", "cid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "button", "axes", "cid"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _PanInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo._source", "name": "_source", "type": "builtins.str"}}, "axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.axes", "name": "axes", "type": {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "axes-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.axes", "kind": "<PERSON><PERSON><PERSON>"}, "button": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.button", "name": "button", "type": "matplotlib.backend_bases.MouseButton"}}, "button-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.button", "kind": "<PERSON><PERSON><PERSON>"}, "cid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.cid", "name": "cid", "type": "builtins.int"}}, "cid-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.cid", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._PanInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": "matplotlib.backend_bases.NavigationToolbar2._PanInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.backend_bases.MouseButton", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_ZoomInfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo", "name": "_ZoomInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["direction", "start_xy", "axes", "cid", "cbar"]}}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.NavigationToolbar2._ZoomInfo", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "direction"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "start_xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "axes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cbar"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "direction", "start_xy", "axes", "cid", "cbar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "direction", "start_xy", "axes", "cid", "cbar"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _ZoomInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _ZoomInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ZoomInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ZoomInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "direction", "start_xy", "axes", "cid", "cbar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "direction", "start_xy", "axes", "cid", "cbar"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _ZoomInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo._source", "name": "_source", "type": "builtins.str"}}, "axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.axes", "name": "axes", "type": {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "axes-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.axes", "kind": "<PERSON><PERSON><PERSON>"}, "cbar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.cbar", "name": "cbar", "type": "matplotlib.colorbar.Colorbar"}}, "cbar-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.cbar", "kind": "<PERSON><PERSON><PERSON>"}, "cid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.cid", "name": "cid", "type": "builtins.int"}}, "cid-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.cid", "kind": "<PERSON><PERSON><PERSON>"}, "direction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.direction", "name": "direction", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}}}, "direction-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.direction", "kind": "<PERSON><PERSON><PERSON>"}, "start_xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.start_xy", "name": "start_xy", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "start_xy-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.start_xy", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": "matplotlib.backend_bases.NavigationToolbar2._ZoomInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["matplotlib.axes._axes.Axes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "matplotlib.colorbar.Colorbar"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "canvas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "canvas"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "back": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.back", "name": "back", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2.canvas", "name": "canvas", "type": "matplotlib.backend_bases.FigureCanvasBase"}}, "configure_subplots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.configure_subplots", "name": "configure_subplots", "type": null}}, "drag_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.drag_pan", "name": "drag_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drag_pan of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drag_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.drag_zoom", "name": "drag_zoom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drag_zoom of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "event", "x0", "y0", "x1", "y1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.draw_rubberband", "name": "draw_rubberband", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "event", "x0", "y0", "x1", "y1"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_rubberband of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "home": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.home", "name": "home", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "home of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2.mode", "name": "mode", "type": "matplotlib.backend_bases._Mode"}}, "mouse_move": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.mouse_move", "name": "mouse_move", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.MouseEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouse_move of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.pan", "name": "pan", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pan of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "press_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.press_pan", "name": "press_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press_pan of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "press_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.press_zoom", "name": "press_zoom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press_zoom of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.push_current", "name": "push_current", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_current of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.release_pan", "name": "release_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release_pan of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.release_zoom", "name": "release_zoom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "matplotlib.backend_bases.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release_zoom of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.remove_rubberband", "name": "remove_rubberband", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_rubberband of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.save_figure", "name": "save_figure", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_figure of NavigationToolbar2", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "builtins.object"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_history_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.set_history_buttons", "name": "set_history_buttons", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_history_buttons of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.set_message", "name": "set_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_message of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subplot_tool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2.subplot_tool", "name": "subplot_tool", "type": "matplotlib.widgets.SubplotTool"}}, "toolitems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.NavigationToolbar2.toolitems", "name": "toolitems", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NavigationToolbar2.zoom", "name": "zoom", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zoom of NavigationToolbar2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NavigationToolbar2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.NavigationToolbar2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonGuiException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.NonGuiException", "name": "NonGuiException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.NonGuiException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.NonGuiException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.NonGuiException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.NonGuiException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PickEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.PickEvent", "name": "PickEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.PickEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.PickEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "name", "canvas", "mouseevent", "artist", "guiEvent", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.PickEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "name", "canvas", "mouseevent", "artist", "guiEvent", "kwargs"], "arg_types": ["matplotlib.backend_bases.PickEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase", "matplotlib.backend_bases.MouseEvent", "matplotlib.artist.Artist", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PickEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.PickEvent.artist", "name": "artist", "type": "matplotlib.artist.Artist"}}, "mouseevent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.PickEvent.mouseevent", "name": "mouseevent", "type": "matplotlib.backend_bases.MouseEvent"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.PickEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.PickEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.RendererBase", "name": "RendererBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.RendererBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.close_group", "name": "close_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.backend_bases.RendererBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_group of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_gouraud_triangles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gc", "triangles_array", "colors_array", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_gouraud_triangles", "name": "draw_gouraud_triangles", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gc", "triangles_array", "colors_array", "transform"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_gouraud_triangles of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "gc", "x", "y", "im", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_image", "name": "draw_image", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "gc", "x", "y", "im", "transform"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "builtins.float", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["matplotlib.transforms.Affine2DBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_image of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_markers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "gc", "marker_path", "marker_trans", "path", "trans", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_markers", "name": "draw_markers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "gc", "marker_path", "marker_trans", "path", "trans", "rgbFace"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_markers of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "gc", "path", "transform", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "gc", "path", "transform", "rgbFace"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gc", "master_transform", "paths", "all_transforms", "offsets", "offset_trans", "facecolors", "edgecolors", "linewidths", "linestyles", "antialiaseds", "urls", "offset_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_path_collection", "name": "draw_path_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gc", "master_transform", "paths", "all_transforms", "offsets", "offset_trans", "facecolors", "edgecolors", "linewidths", "linestyles", "antialiaseds", "urls", "offset_position"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.transforms.Transform", {".class": "Instance", "args": ["matplotlib.path.Path"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.LineStyleType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.LineStyleType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path_collection of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_quad_mesh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gc", "master_transform", "meshWidth", "meshHeight", "coordinates", "offsets", "offsetTrans", "facecolors", "antialiased", "edgecolors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_quad_mesh", "name": "draw_quad_mesh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gc", "master_transform", "meshWidth", "meshHeight", "coordinates", "offsets", "offsetTrans", "facecolors", "antialiased", "edgecolors"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.transforms.Transform", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "matplotlib.transforms.Transform", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_quad_mesh of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_tex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "mtext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_tex", "name": "draw_tex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "mtext"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "builtins.float", "builtins.float", "builtins.str", "matplotlib.font_manager.FontProperties", "builtins.float", {".class": "UnionType", "items": ["matplotlib.text.Text", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_tex of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "<PERSON><PERSON><PERSON>", "mtext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.draw_text", "name": "draw_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "<PERSON><PERSON><PERSON>", "mtext"], "arg_types": ["matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "builtins.float", "builtins.float", "builtins.str", "matplotlib.font_manager.FontProperties", "builtins.float", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "TeX"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.text.Text", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_text of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flipy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.flipy", "name": "flipy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flipy of RendererBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_canvas_width_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.get_canvas_width_height", "name": "get_canvas_width_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_canvas_width_height of RendererBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_image_magnification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.get_image_magnification", "name": "get_image_magnification", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_image_magnification of RendererBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_texmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.get_texmanager", "name": "get_texmanager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_texmanager of RendererBase", "ret_type": "matplotlib.texmanager.TexManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_text_width_height_descent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "prop", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.get_text_width_height_descent", "name": "get_text_width_height_descent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "prop", "<PERSON><PERSON><PERSON>"], "arg_types": ["matplotlib.backend_bases.RendererBase", "builtins.str", "matplotlib.font_manager.FontProperties", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "TeX"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text_width_height_descent of RendererBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_gc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.new_gc", "name": "new_gc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_gc of RendererBase", "ret_type": "matplotlib.backend_bases.GraphicsContextBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "gid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.open_group", "name": "open_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "gid"], "arg_types": ["matplotlib.backend_bases.RendererBase", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_group of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_image_nocomposite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.option_image_nocomposite", "name": "option_image_nocomposite", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "option_image_nocomposite of RendererBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_scale_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.option_scale_image", "name": "option_scale_image", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "option_scale_image of RendererBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "points_to_pixels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.points_to_pixels", "name": "points_to_pixels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "points"], "arg_types": ["matplotlib.backend_bases.RendererBase", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "points_to_pixels of RendererBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.start_filter", "name": "start_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_filter of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_rasterizing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.start_rasterizing", "name": "start_rasterizing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_rasterizing of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.stop_filter", "name": "stop_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filter_func"], "arg_types": ["matplotlib.backend_bases.RendererBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_filter of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_rasterizing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.RendererBase.stop_rasterizing", "name": "stop_rasterizing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_rasterizing of RendererBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.RendererBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.RendererBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResizeEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.ResizeEvent", "name": "ResizeEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ResizeEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.ResizeEvent", "matplotlib.backend_bases.Event", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "canvas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ResizeEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "canvas"], "arg_types": ["matplotlib.backend_bases.ResizeEvent", "builtins.str", "matplotlib.backend_bases.FigureCanvasBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResizeEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.ResizeEvent.height", "name": "height", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.ResizeEvent.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.ResizeEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.ResizeEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ShowBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases._Backend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.ShowBase", "name": "ShowBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ShowBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.ShowBase", "matplotlib.backend_bases._Backend", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ShowBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "block"], "arg_types": ["matplotlib.backend_bases.ShowBase", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ShowBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.ShowBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.ShowBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TexManager": {".class": "SymbolTableNode", "cross_ref": "matplotlib.texmanager.TexManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Text": {".class": "SymbolTableNode", "cross_ref": "matplotlib.text.Text", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TimerBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.TimerBase", "name": "TimerBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.TimerBase", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "interval", "callbacks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "interval", "callbacks"], "arg_types": ["matplotlib.backend_bases.TimerBase", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.add_callback", "name": "add_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "arg_types": ["matplotlib.backend_bases.TimerBase", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_callback of TimerBase", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.TimerBase.callbacks", "name": "callbacks", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.backend_bases.TimerBase.interval", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.backend_bases.TimerBase.interval", "name": "interval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interval of TimerBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.TimerBase.interval", "name": "interval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interval of TimerBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backend_bases.TimerBase.interval", "name": "interval", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interval"], "arg_types": ["matplotlib.backend_bases.TimerBase", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interval of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "interval", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interval of TimerBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "remove_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.remove_callback", "name": "remove_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "arg_types": ["matplotlib.backend_bases.TimerBase", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_callback of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "single_shot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.backend_bases.TimerBase.single_shot", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.backend_bases.TimerBase.single_shot", "name": "single_shot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "single_shot of TimerBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases.TimerBase.single_shot", "name": "single_shot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "single_shot of TimerBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ss"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backend_bases.TimerBase.single_shot", "name": "single_shot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ss"], "arg_types": ["matplotlib.backend_bases.TimerBase", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "single_shot of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "single_shot", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "single_shot of TimerBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "interval"], "arg_types": ["matplotlib.backend_bases.TimerBase", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.TimerBase.stop", "name": "stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_bases.TimerBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop of TimerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.TimerBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.TimerBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_tools.ToolBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolContainerBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases.ToolContainerBase", "name": "ToolContainerBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases.ToolContainerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toolmanager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toolmanager"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "matplotlib.backend_managers.ToolManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tool", "group", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.add_tool", "name": "add_tool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tool", "group", "position"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "matplotlib.backend_tools.ToolBase", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tool of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "group", "position", "image", "description", "toggle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.add_toolitem", "name": "add_toolitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "group", "position", "image", "description", "toggle"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "builtins.str", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_toolitem of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.remove_toolitem", "name": "remove_toolitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_toolitem of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.set_message", "name": "set_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_message of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toggle_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "toggled"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.toggle_toolitem", "name": "toggle_toolitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "toggled"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toggle_toolitem of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases.ToolContainerBase.toolmanager", "name": "toolmanager", "type": "matplotlib.backend_managers.ToolManager"}}, "trigger_tool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.ToolContainerBase.trigger_tool", "name": "trigger_tool", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trigger_tool of ToolContainerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases.ToolContainerBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases.ToolContainerBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolManager": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_managers.ToolManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TransformedPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.TransformedPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases._Backend", "name": "_Backend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases._Backend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases._Backend", "builtins.object"], "names": {".class": "SymbolTable", "FigureCanvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases._Backend.FigureCanvas", "name": "FigureCanvas", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "FigureManager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases._Backend.FigureManager", "name": "FigureManager", "type": {".class": "TypeType", "item": "matplotlib.backend_bases.FigureManagerBase"}}}, "backend_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases._Backend.backend_version", "name": "backend_version", "type": "builtins.str"}}, "draw_if_interactive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases._Backend.draw_if_interactive", "name": "draw_if_interactive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_if_interactive of _Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases._Backend.draw_if_interactive", "name": "draw_if_interactive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_if_interactive of _Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.backend_bases._Backend.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export of _Backend", "ret_type": {".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases._Backend.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export of _Backend", "ret_type": {".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mainloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_bases._Backend.mainloop", "name": "mainloop", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "new_figure_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "num", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases._Backend.new_figure_manager", "name": "new_figure_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "num", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_figure_manager of _Backend", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases._Backend.new_figure_manager", "name": "new_figure_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "num", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_figure_manager of _Backend", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new_figure_manager_given_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "num", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases._Backend.new_figure_manager_given_figure", "name": "new_figure_manager_given_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "num", "figure"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_figure_manager_given_figure of _Backend", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases._Backend.new_figure_manager_given_figure", "name": "new_figure_manager_given_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "num", "figure"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_figure_manager_given_figure of _Backend", "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backend_bases._Backend.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of _Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_bases._Backend.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["cls", "block"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backend_bases._Backend"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of _Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases._Backend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases._Backend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_bases._Mode", "name": "_Mode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "matplotlib.backend_bases._Mode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "matplotlib.backend_bases", "mro": ["matplotlib.backend_bases._Mode", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases._Mode.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": ""}, "type_ref": "builtins.str"}}}, "PAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases._Mode.PAN", "name": "PAN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pan/zoom"}, "type_ref": "builtins.str"}}}, "ZOOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_bases._Mode.ZOOM", "name": "ZOOM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "zoom rect"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_bases._Mode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_bases._Mode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_bases.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_api": {".class": "SymbolTableNode", "cross_ref": "matplotlib._api", "kind": "Gdef", "module_hidden": true, "module_public": false}, "button_press_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["event", "canvas", "toolbar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.button_press_handler", "name": "button_press_handler", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["event", "canvas", "toolbar"], "arg_types": ["matplotlib.backend_bases.MouseEvent", {".class": "UnionType", "items": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "button_press_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cursors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "matplotlib.backend_bases.cursors", "line": 398, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "matplotlib.backend_tools.Cursors"}}, "get_registered_canvas_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.get_registered_canvas_class", "name": "get_registered_canvas_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["format"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_registered_canvas_class", "ret_type": {".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_press_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["event", "canvas", "toolbar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.key_press_handler", "name": "key_press_handler", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["event", "canvas", "toolbar"], "arg_types": ["matplotlib.backend_bases.KeyEvent", {".class": "UnionType", "items": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.backend_bases.NavigationToolbar2", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_press_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "register_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["format", "backend", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_bases.register_backend", "name": "register_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["format", "backend", "description"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "matplotlib.backend_bases.FigureCanvasBase"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transforms": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms", "kind": "Gdef", "module_hidden": true, "module_public": false}, "widgets": {".class": "SymbolTableNode", "cross_ref": "matplotlib.widgets", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backend_bases.pyi"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理系统
Configuration Management System

统一管理系统配置，提供环境变量支持和配置验证
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class TradingConfig:
    """交易配置"""
    # 基础配置
    initial_capital: float = 10000.0
    daily_loss_limit: float = 300.0
    max_position_size: float = 0.1  # 最大仓位比例
    
    # 风险管理
    stop_loss_pct: float = 0.03  # 止损比例
    take_profit_pct: float = 0.06  # 止盈比例
    max_drawdown: float = 0.15  # 最大回撤
    
    # 交易参数
    min_trade_amount: float = 10.0  # 最小交易金额
    max_trades_per_day: int = 50  # 每日最大交易次数
    trading_pairs: list = None  # 交易对列表
    
    def __post_init__(self):
        if self.trading_pairs is None:
            self.trading_pairs = ["BTC/USDT", "ETH/USDT", "BNB/USDT"]

@dataclass
class ExchangeConfig:
    """交易所配置"""
    name: str = "gate"
    api_key: str = ""
    api_secret: str = ""
    passphrase: str = ""
    sandbox: bool = True
    testnet: bool = True
    timeout: int = 30000
    rateLimit: int = 1000
    
@dataclass
class DatabaseConfig:
    """数据库配置"""
    type: str = "sqlite"
    host: str = "localhost"
    port: int = 5432
    database: str = "trading.db"
    username: str = ""
    password: str = ""
    
@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/trading.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    
@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_alerts: bool = True
    alert_email: str = ""
    alert_webhook: str = ""
    check_interval: int = 60  # 秒
    performance_threshold: float = 0.8
    
@dataclass
class OptimizationConfig:
    """优化系统配置"""
    enable_strategy_optimization: bool = True
    enable_smart_hints: bool = True
    enable_auto_testing: bool = True
    optimization_frequency: str = "daily"  # daily, weekly, manual
    hint_display_duration: int = 5  # seconds
    testing_schedule: str = "00:00"  # HH:MM format
    max_optimization_iterations: int = 100
    performance_threshold: float = 0.1  # minimum improvement required

@dataclass
class SystemConfig:
    """系统总配置"""
    trading: TradingConfig
    exchange: ExchangeConfig
    database: DatabaseConfig
    logging: LoggingConfig
    monitoring: MonitoringConfig
    optimization: OptimizationConfig = None
    
    # 系统信息
    version: str = "2.0.0"
    environment: str = "development"  # development, testing, production
    debug: bool = True
    
    def __post_init__(self):
        if self.optimization is None:
            self.optimization = OptimizationConfig()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config: Optional[SystemConfig] = None
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            # 首先尝试从文件加载
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.config = self._dict_to_config(config_data)
                logger.info(f"配置已从文件加载: {self.config_file}")
            else:
                # 使用默认配置
                self.config = self._create_default_config()
                logger.info("使用默认配置")
            
            # 应用环境变量覆盖
            self._apply_env_overrides()
            
            # 验证配置
            self._validate_config()
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self.config = self._create_default_config()
    
    def _create_default_config(self) -> SystemConfig:
        """创建默认配置"""
        return SystemConfig(
            trading=TradingConfig(),
            exchange=ExchangeConfig(),
            database=DatabaseConfig(),
            logging=LoggingConfig(),
            monitoring=MonitoringConfig()
        )
    
    def _dict_to_config(self, config_data: Dict[str, Any]) -> SystemConfig:
        """将字典转换为配置对象"""
        return SystemConfig(
            trading=TradingConfig(**config_data.get('trading', {})),
            exchange=ExchangeConfig(**config_data.get('exchange', {})),
            database=DatabaseConfig(**config_data.get('database', {})),
            logging=LoggingConfig(**config_data.get('logging', {})),
            monitoring=MonitoringConfig(**config_data.get('monitoring', {})),
            version=config_data.get('version', '1.0.0'),
            environment=config_data.get('environment', 'development'),
            debug=config_data.get('debug', True)
        )
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        # 交易所配置
        if os.getenv('EXCHANGE_API_KEY'):
            self.config.exchange.api_key = os.getenv('EXCHANGE_API_KEY')
        if os.getenv('EXCHANGE_API_SECRET'):
            self.config.exchange.api_secret = os.getenv('EXCHANGE_API_SECRET')
        if os.getenv('EXCHANGE_SANDBOX'):
            self.config.exchange.sandbox = os.getenv('EXCHANGE_SANDBOX').lower() == 'true'
        
        # 交易配置
        if os.getenv('INITIAL_CAPITAL'):
            self.config.trading.initial_capital = float(os.getenv('INITIAL_CAPITAL'))
        if os.getenv('DAILY_LOSS_LIMIT'):
            self.config.trading.daily_loss_limit = float(os.getenv('DAILY_LOSS_LIMIT'))
        
        # 系统配置
        if os.getenv('ENVIRONMENT'):
            self.config.environment = os.getenv('ENVIRONMENT')
        if os.getenv('DEBUG'):
            self.config.debug = os.getenv('DEBUG').lower() == 'true'
    
    def _validate_config(self):
        """验证配置"""
        errors = []
        
        # 验证交易配置
        if self.config.trading.initial_capital <= 0:
            errors.append("初始资金必须大于0")
        
        if self.config.trading.daily_loss_limit <= 0:
            errors.append("日亏损限制必须大于0")
        
        if not (0 < self.config.trading.max_position_size <= 1):
            errors.append("最大仓位比例必须在0-1之间")
        
        # 验证交易所配置
        if self.config.environment == 'production':
            if not self.config.exchange.api_key:
                errors.append("生产环境必须配置API密钥")
            if not self.config.exchange.api_secret:
                errors.append("生产环境必须配置API密钥")
        
        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为字典
            config_dict = asdict(self.config)
            
            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到: {self.config_file}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def get_config(self) -> SystemConfig:
        """获取配置"""
        return self.config
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        self._validate_config()
    
    def get_exchange_config(self) -> Dict[str, Any]:
        """获取交易所配置字典"""
        return {
            'apiKey': self.config.exchange.api_key,
            'secret': self.config.exchange.api_secret,
            'password': self.config.exchange.passphrase,
            'sandbox': self.config.exchange.sandbox,
            'timeout': self.config.exchange.timeout,
            'rateLimit': self.config.exchange.rateLimit,
        }
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.config.environment == 'production'
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self.config.debug

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_config() -> SystemConfig:
    """获取系统配置"""
    return get_config_manager().get_config()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合功能测试脚本
Comprehensive Features Test: Multi-Exchange, Backtesting, Monitoring, Security
"""

import time
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.multi_exchange_interface import *
from institutional_framework.strategy_backtest_system import *
from institutional_framework.monitoring_alert_system import *
from institutional_framework.secure_key_management import *


def test_multi_exchange_support():
    """测试多交易所支持"""
    print("🔍 测试多交易所支持...")
    
    try:
        # 创建交易所管理器
        manager = ExchangeManager()
        
        # 添加Gate.io接口（使用现有的）
        from institutional_framework.gate_io_client import GateIOClient
        gate_client = GateIOClient()
        manager.add_exchange('gate', gate_client)
        
        # 创建Binance接口（演示模式）
        binance_interface = BinanceInterface(testnet=True)
        manager.add_exchange('binance', binance_interface)
        
        # 创建OKX接口（演示模式）
        okx_interface = OKXInterface(testnet=True)
        manager.add_exchange('okx', okx_interface)
        
        print(f"✅ 已添加 {len(manager.exchanges)} 个交易所")
        
        # 测试交易所状态
        print("📊 检查交易所状态...")
        status = manager.get_exchange_status()
        for exchange_id, info in status.items():
            print(f"   {info['name']}: {info['status']}")
        
        # 测试最优价格查询
        print("💰 查询最优价格...")
        best_price = manager.get_best_price('BTC_USDT', 'buy')
        if best_price['best_exchange']:
            print(f"   最优买入价格: {best_price['best_price']:.2f} ({best_price['best_exchange']})")
        
        # 测试套利机会
        print("🔍 寻找套利机会...")
        opportunities = manager.find_arbitrage_opportunities(['BTC_USDT', 'ETH_USDT'])
        print(f"   发现 {len(opportunities)} 个套利机会")
        
        for opp in opportunities[:2]:  # 显示前2个
            print(f"   {opp['symbol']}: {opp['profit_percent']:.2f}% 利润")
        
        print("✅ 多交易所支持测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 多交易所测试失败: {e}")
        return False


def test_strategy_backtesting():
    """测试策略回测系统"""
    print("\n🔍 测试策略回测系统...")
    
    try:
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=10000.0, commission=0.001)
        
        # 生成示例数据
        print("📊 生成历史数据...")
        engine.generate_sample_data('BTC_USDT', days=30, start_price=50000.0)
        
        # 测试移动平均策略
        print("🎯 测试移动平均策略...")
        result = engine.run_backtest(
            StrategyLibrary.moving_average_strategy,
            ['BTC_USDT'],
            start_date=datetime.now() - timedelta(days=25),
            end_date=datetime.now() - timedelta(days=5)
        )
        
        if result['success']:
            print(f"   交易次数: {result['trades']}")
            print(f"   最终价值: {result['final_value']:.2f}")
            print(f"   总收益: {result['metrics']['total_return']:.2%}")
            print(f"   夏普比率: {result['metrics']['sharpe_ratio']:.3f}")
        
        # 测试RSI策略
        print("🎯 测试RSI策略...")
        engine2 = BacktestEngine(initial_capital=10000.0, commission=0.001)
        engine2.generate_sample_data('BTC_USDT', days=30, start_price=50000.0)
        
        result2 = engine2.run_backtest(
            StrategyLibrary.rsi_strategy,
            ['BTC_USDT'],
            start_date=datetime.now() - timedelta(days=25),
            end_date=datetime.now() - timedelta(days=5)
        )
        
        if result2['success']:
            print(f"   RSI策略交易次数: {result2['trades']}")
            print(f"   RSI策略收益: {result2['metrics']['total_return']:.2%}")
        
        # 生成性能报告
        print("\n📋 性能报告:")
        print(engine.get_performance_report())
        
        # 保存结果
        engine.save_results("backtest_results.json")
        
        print("✅ 策略回测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 策略回测测试失败: {e}")
        return False


def test_monitoring_system():
    """测试监控告警系统"""
    print("\n🔍 测试监控告警系统...")
    
    try:
        # 创建监控系统
        monitor = MonitoringSystem()
        
        # 添加数据源
        def get_mock_prices():
            return {
                'BTC_USDT': {'price': 50000.0, 'change': 15.5},  # 触发价格波动告警
                'ETH_USDT': {'price': 3000.0, 'change': 2.1}
            }
        
        def get_mock_balances():
            return {
                'USDT': {'available': 50.0},  # 触发余额不足告警
                'BTC': {'available': 0.001}
            }
        
        def get_mock_trade_stats():
            return {
                'total_orders': 10,
                'failed_orders': 3  # 30%失败率，触发交易异常告警
            }
        
        monitor.add_data_source('prices', get_mock_prices)
        monitor.add_data_source('balances', get_mock_balances)
        monitor.add_data_source('trade_stats', get_mock_trade_stats)
        
        # 启动监控
        monitor.start_monitoring()
        
        # 等待告警生成
        print("⏳ 等待告警生成...")
        time.sleep(15)
        
        # 检查告警
        active_alerts = monitor.get_active_alerts()
        print(f"📢 生成了 {len(active_alerts)} 个告警")
        
        for alert in active_alerts[:3]:  # 显示前3个
            print(f"   {alert.level.value.upper()}: {alert.title}")
        
        # 确认告警
        if active_alerts:
            monitor.acknowledge_alert(active_alerts[0].alert_id)
        
        # 获取监控状态
        status = monitor.get_monitoring_status()
        print(f"📊 监控状态: {status}")
        
        # 停止监控
        monitor.stop_monitoring()
        
        print("✅ 监控告警系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")
        return False


def test_key_management():
    """测试API密钥管理"""
    print("\n🔍 测试API密钥管理...")
    
    try:
        # 设置演示密钥
        setup_demo_keys()
        
        # 获取密钥管理器
        km = get_key_manager("demo_password")
        
        # 列出交易所
        exchanges = km.list_exchanges()
        print(f"📋 管理 {len(exchanges)} 个交易所密钥")
        
        for exchange in exchanges:
            print(f"   {exchange['exchange_name']}: {'启用' if exchange['enabled'] else '禁用'}")
        
        # 测试密钥获取
        gate_keys = km.get_exchange_keys('gate_demo')
        if gate_keys:
            print(f"🔑 Gate.io密钥: {gate_keys['api_key'][:8]}...")
        
        # 测试密钥验证
        validation = km.validate_keys('gate_demo')
        print(f"✅ 密钥验证: {'通过' if validation['valid'] else '失败'}")
        
        # 测试安全状态
        security_status = km.get_security_status()
        print(f"🛡️ 安全状态: {security_status}")
        
        # 导出配置
        km.export_keys("key_export.json", include_secrets=False)
        
        print("✅ API密钥管理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 密钥管理测试失败: {e}")
        return False


def test_integrated_workflow():
    """测试集成工作流"""
    print("\n🔍 测试集成工作流...")
    
    try:
        # 1. 初始化密钥管理
        km = get_key_manager("demo_password")
        
        # 2. 初始化交易所管理器
        manager = ExchangeManager()
        
        # 使用密钥管理器的密钥配置交易所
        exchanges = km.list_exchanges()
        for exchange in exchanges:
            if exchange['enabled']:
                exchange_id = exchange['exchange_id']
                keys = km.get_exchange_keys(exchange_id)
                
                if keys:
                    print(f"🔗 配置交易所: {keys['exchange_name']}")
                    # 这里可以添加实际的交易所接口初始化
        
        # 3. 启动监控系统
        monitor = MonitoringSystem()
        
        # 添加交易所状态监控
        def get_exchange_status():
            return manager.get_exchange_status()
        
        monitor.add_data_source('exchange_status', get_exchange_status)
        monitor.start_monitoring()
        
        # 4. 运行简单回测
        engine = BacktestEngine(initial_capital=5000.0)
        engine.generate_sample_data('BTC_USDT', days=10)
        
        result = engine.run_backtest(
            StrategyLibrary.moving_average_strategy,
            ['BTC_USDT'],
            start_date=datetime.now() - timedelta(days=8),
            end_date=datetime.now() - timedelta(days=2)
        )
        
        # 5. 生成综合报告
        print("\n📊 集成工作流报告:")
        print(f"   密钥管理: {len(km.list_exchanges())} 个交易所")
        print(f"   交易所连接: {len(manager.exchanges)} 个")
        print(f"   监控规则: {len(monitor.rules)} 个")
        print(f"   回测收益: {result['metrics']['total_return']:.2%}" if result['success'] else "   回测失败")
        
        # 清理
        monitor.stop_monitoring()
        
        print("✅ 集成工作流测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成工作流测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 综合功能测试开始")
    print("=" * 80)
    
    # 测试各个模块
    multi_exchange_success = test_multi_exchange_support()
    backtesting_success = test_strategy_backtesting()
    monitoring_success = test_monitoring_system()
    key_management_success = test_key_management()
    integrated_success = test_integrated_workflow()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 综合测试结果总结:")
    print(f"   多交易所支持: {'✅ 通过' if multi_exchange_success else '❌ 失败'}")
    print(f"   策略回测系统: {'✅ 通过' if backtesting_success else '❌ 失败'}")
    print(f"   监控告警系统: {'✅ 通过' if monitoring_success else '❌ 失败'}")
    print(f"   API密钥管理: {'✅ 通过' if key_management_success else '❌ 失败'}")
    print(f"   集成工作流: {'✅ 通过' if integrated_success else '❌ 失败'}")
    
    # 计算总体成功率
    tests = [multi_exchange_success, backtesting_success, monitoring_success, 
             key_management_success, integrated_success]
    
    success_count = sum(tests)
    total_tests = len(tests)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 总体测试结果: {success_count}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎊 综合功能测试大部分通过!")
        print("💡 多交易所、回测、监控、安全管理功能基本正常")
        print("🔗 系统具备了企业级交易平台的核心功能")
        print("🛡️ 支持多交易所套利、策略回测、实时监控等高级功能")
    else:
        print("\n⚠️ 部分综合功能测试失败")
        print("💡 建议检查系统配置和依赖项")
    
    print("\n🔮 下一步可以实现:")
    print("   📈 高级图表分析集成")
    print("   🤖 机器学习策略开发")
    print("   🌐 Web界面和API服务")
    print("   📱 移动端监控应用")
    
    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

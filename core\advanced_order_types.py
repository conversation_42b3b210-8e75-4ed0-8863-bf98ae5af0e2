#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级订单类型系统
Advanced Order Types System for Professional Trading
"""

import time
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from gate_trading_engine import GateIOTradingEngine


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    STOP_LIMIT = "stop_limit"
    ICEBERG = "iceberg"
    TWAP = "twap"
    CONDITIONAL = "conditional"


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"
    ACTIVE = "active"
    TRIGGERED = "triggered"
    FILLED = "filled"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    FAILED = "failed"


class AdvancedOrder:
    """高级订单基类"""
    
    def __init__(self, order_id: str, symbol: str, side: str, amount: float, 
                 order_type: OrderType, strategy_name: str = ""):
        """初始化高级订单"""
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.amount = amount
        self.order_type = order_type
        self.strategy_name = strategy_name
        
        self.status = OrderStatus.PENDING
        self.create_time = datetime.now()
        self.trigger_time = None
        self.fill_time = None
        
        self.filled_amount = 0.0
        self.average_price = 0.0
        self.total_fee = 0.0
        
        self.child_orders = []  # 子订单
        self.error_message = ""
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查是否可以触发"""
        return False
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """触发订单"""
        return False
    
    def update_status(self, new_status: OrderStatus):
        """更新订单状态"""
        self.status = new_status
        if new_status == OrderStatus.TRIGGERED:
            self.trigger_time = datetime.now()
        elif new_status == OrderStatus.FILLED:
            self.fill_time = datetime.now()


class StopLossOrder(AdvancedOrder):
    """止损订单"""
    
    def __init__(self, order_id: str, symbol: str, side: str, amount: float,
                 stop_price: float, limit_price: float = None, strategy_name: str = ""):
        """
        初始化止损订单
        
        Args:
            stop_price: 止损触发价格
            limit_price: 限价（可选，如果不设置则为市价止损）
        """
        super().__init__(order_id, symbol, side, amount, OrderType.STOP_LOSS, strategy_name)
        self.stop_price = stop_price
        self.limit_price = limit_price
        self.is_limit_order = limit_price is not None
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查止损触发条件"""
        if self.status != OrderStatus.ACTIVE:
            return False
        
        if self.side == "sell":
            # 卖出止损：价格跌破止损价
            return current_price <= self.stop_price
        else:
            # 买入止损：价格涨破止损价
            return current_price >= self.stop_price
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """触发止损订单"""
        try:
            self.update_status(OrderStatus.TRIGGERED)
            
            # 执行市价或限价订单
            if self.is_limit_order:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount, 
                    self.limit_price, "limit"
                )
            else:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount, 
                    order_type="market"
                )
            
            if result['success']:
                self.child_orders.append(result['order_id'])
                print(f"✅ 止损订单触发成功: {self.order_id}")
                return True
            else:
                self.error_message = result['error']
                self.update_status(OrderStatus.FAILED)
                print(f"❌ 止损订单触发失败: {result['error']}")
                return False
                
        except Exception as e:
            self.error_message = str(e)
            self.update_status(OrderStatus.FAILED)
            print(f"❌ 止损订单触发异常: {e}")
            return False


class TakeProfitOrder(AdvancedOrder):
    """止盈订单"""
    
    def __init__(self, order_id: str, symbol: str, side: str, amount: float,
                 target_price: float, limit_price: float = None, strategy_name: str = ""):
        """
        初始化止盈订单
        
        Args:
            target_price: 止盈触发价格
            limit_price: 限价（可选）
        """
        super().__init__(order_id, symbol, side, amount, OrderType.TAKE_PROFIT, strategy_name)
        self.target_price = target_price
        self.limit_price = limit_price
        self.is_limit_order = limit_price is not None
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查止盈触发条件"""
        if self.status != OrderStatus.ACTIVE:
            return False
        
        if self.side == "sell":
            # 卖出止盈：价格涨到目标价
            return current_price >= self.target_price
        else:
            # 买入止盈：价格跌到目标价
            return current_price <= self.target_price
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """触发止盈订单"""
        try:
            self.update_status(OrderStatus.TRIGGERED)
            
            if self.is_limit_order:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount,
                    self.limit_price, "limit"
                )
            else:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount,
                    order_type="market"
                )
            
            if result['success']:
                self.child_orders.append(result['order_id'])
                print(f"✅ 止盈订单触发成功: {self.order_id}")
                return True
            else:
                self.error_message = result['error']
                self.update_status(OrderStatus.FAILED)
                return False
                
        except Exception as e:
            self.error_message = str(e)
            self.update_status(OrderStatus.FAILED)
            return False


class IcebergOrder(AdvancedOrder):
    """冰山订单"""
    
    def __init__(self, order_id: str, symbol: str, side: str, total_amount: float,
                 price: float, slice_amount: float, strategy_name: str = ""):
        """
        初始化冰山订单
        
        Args:
            total_amount: 总数量
            slice_amount: 每次显示的数量
        """
        super().__init__(order_id, symbol, side, total_amount, OrderType.ICEBERG, strategy_name)
        self.price = price
        self.slice_amount = slice_amount
        self.remaining_amount = total_amount
        self.current_slice_id = None
        self.slice_count = 0
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查是否需要下一片"""
        return (self.status == OrderStatus.ACTIVE and 
                self.remaining_amount > 0 and 
                self.current_slice_id is None)
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """执行下一片订单"""
        try:
            if self.remaining_amount <= 0:
                self.update_status(OrderStatus.FILLED)
                return True
            
            # 计算本次切片数量
            slice_size = min(self.slice_amount, self.remaining_amount)
            
            # 下单
            result = trading_engine.place_order(
                self.symbol, self.side, slice_size, self.price, "limit"
            )
            
            if result['success']:
                self.current_slice_id = result['order_id']
                self.child_orders.append(result['order_id'])
                self.slice_count += 1
                
                print(f"✅ 冰山订单第{self.slice_count}片执行: {slice_size}")
                return True
            else:
                self.error_message = result['error']
                return False
                
        except Exception as e:
            self.error_message = str(e)
            return False
    
    def update_slice_fill(self, filled_amount: float):
        """更新切片成交"""
        self.remaining_amount -= filled_amount
        self.filled_amount += filled_amount
        self.current_slice_id = None
        
        if self.remaining_amount <= 0:
            self.update_status(OrderStatus.FILLED)


class TWAPOrder(AdvancedOrder):
    """时间加权平均价格订单"""
    
    def __init__(self, order_id: str, symbol: str, side: str, total_amount: float,
                 duration_minutes: int, interval_minutes: int = 1, strategy_name: str = ""):
        """
        初始化TWAP订单
        
        Args:
            duration_minutes: 执行总时长（分钟）
            interval_minutes: 执行间隔（分钟）
        """
        super().__init__(order_id, symbol, side, total_amount, OrderType.TWAP, strategy_name)
        self.duration_minutes = duration_minutes
        self.interval_minutes = interval_minutes
        
        # 计算执行参数
        self.total_slices = duration_minutes // interval_minutes
        self.slice_amount = total_amount / self.total_slices
        self.remaining_amount = total_amount
        
        self.start_time = None
        self.next_execution_time = None
        self.executed_slices = 0
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查是否到执行时间"""
        if self.status != OrderStatus.ACTIVE:
            return False
        
        current_time = datetime.now()
        
        # 首次执行
        if self.start_time is None:
            self.start_time = current_time
            self.next_execution_time = current_time
            return True
        
        # 检查是否到下次执行时间
        return (current_time >= self.next_execution_time and 
                self.remaining_amount > 0)
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """执行TWAP切片"""
        try:
            if self.remaining_amount <= 0:
                self.update_status(OrderStatus.FILLED)
                return True
            
            # 计算本次执行数量
            slice_size = min(self.slice_amount, self.remaining_amount)
            
            # 市价执行
            result = trading_engine.place_order(
                self.symbol, self.side, slice_size, order_type="market"
            )
            
            if result['success']:
                self.child_orders.append(result['order_id'])
                self.executed_slices += 1
                self.remaining_amount -= slice_size
                self.filled_amount += slice_size
                
                # 设置下次执行时间
                self.next_execution_time = datetime.now() + timedelta(minutes=self.interval_minutes)
                
                print(f"✅ TWAP订单第{self.executed_slices}片执行: {slice_size}")
                
                if self.remaining_amount <= 0:
                    self.update_status(OrderStatus.FILLED)
                
                return True
            else:
                self.error_message = result['error']
                return False
                
        except Exception as e:
            self.error_message = str(e)
            return False


class ConditionalOrder(AdvancedOrder):
    """条件订单"""
    
    def __init__(self, order_id: str, symbol: str, side: str, amount: float,
                 condition_symbol: str, condition_operator: str, condition_value: float,
                 execution_price: float = None, strategy_name: str = ""):
        """
        初始化条件订单
        
        Args:
            condition_symbol: 条件币种
            condition_operator: 条件操作符 (>, <, >=, <=, ==)
            condition_value: 条件值
            execution_price: 执行价格（可选，默认市价）
        """
        super().__init__(order_id, symbol, side, amount, OrderType.CONDITIONAL, strategy_name)
        self.condition_symbol = condition_symbol
        self.condition_operator = condition_operator
        self.condition_value = condition_value
        self.execution_price = execution_price
    
    def can_trigger(self, current_price: float, market_data: Dict) -> bool:
        """检查条件是否满足"""
        if self.status != OrderStatus.ACTIVE:
            return False
        
        # 获取条件币种价格
        condition_price = market_data.get(self.condition_symbol, {}).get('price', 0)
        if condition_price <= 0:
            return False
        
        # 检查条件
        if self.condition_operator == ">":
            return condition_price > self.condition_value
        elif self.condition_operator == "<":
            return condition_price < self.condition_value
        elif self.condition_operator == ">=":
            return condition_price >= self.condition_value
        elif self.condition_operator == "<=":
            return condition_price <= self.condition_value
        elif self.condition_operator == "==":
            return abs(condition_price - self.condition_value) < 0.01
        
        return False
    
    def trigger(self, trading_engine: GateIOTradingEngine) -> bool:
        """触发条件订单"""
        try:
            self.update_status(OrderStatus.TRIGGERED)
            
            if self.execution_price:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount,
                    self.execution_price, "limit"
                )
            else:
                result = trading_engine.place_order(
                    self.symbol, self.side, self.amount,
                    order_type="market"
                )
            
            if result['success']:
                self.child_orders.append(result['order_id'])
                print(f"✅ 条件订单触发成功: {self.order_id}")
                return True
            else:
                self.error_message = result['error']
                self.update_status(OrderStatus.FAILED)
                return False
                
        except Exception as e:
            self.error_message = str(e)
            self.update_status(OrderStatus.FAILED)
            return False


class AdvancedOrderManager:
    """高级订单管理器"""
    
    def __init__(self, trading_engine: GateIOTradingEngine):
        """初始化高级订单管理器"""
        self.trading_engine = trading_engine
        self.active_orders = {}  # 活跃订单
        self.completed_orders = []  # 已完成订单
        
        self.monitoring_active = False
        self.monitor_thread = None
        
        # 市场数据缓存
        self.market_data = {}
    
    def start_monitoring(self):
        """启动订单监控"""
        if self.monitoring_active:
            return
        
        print("🚀 启动高级订单监控...")
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("✅ 高级订单监控已启动")
    
    def stop_monitoring(self):
        """停止订单监控"""
        if not self.monitoring_active:
            return
        
        print("🛑 停止高级订单监控...")
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("✅ 高级订单监控已停止")
    
    def _monitoring_loop(self):
        """订单监控循环"""
        while self.monitoring_active:
            try:
                self._update_market_data()
                self._check_order_triggers()
                time.sleep(1)  # 1秒检查一次
                
            except Exception as e:
                print(f"❌ 订单监控异常: {e}")
                time.sleep(5)
    
    def _update_market_data(self):
        """更新市场数据"""
        # 这里应该从实时数据源获取价格
        # 暂时使用模拟数据
        symbols = ["BTC_USDT", "ETH_USDT", "SOL_USDT"]
        for symbol in symbols:
            # 模拟价格更新
            if symbol not in self.market_data:
                self.market_data[symbol] = {'price': 50000.0}
            else:
                # 模拟价格波动
                current_price = self.market_data[symbol]['price']
                change = current_price * 0.001 * (2 * time.time() % 1 - 0.5)
                self.market_data[symbol]['price'] = current_price + change
    
    def _check_order_triggers(self):
        """检查订单触发条件"""
        triggered_orders = []
        
        for order_id, order in self.active_orders.items():
            try:
                symbol_price = self.market_data.get(order.symbol, {}).get('price', 0)
                
                if order.can_trigger(symbol_price, self.market_data):
                    if order.trigger(self.trading_engine):
                        triggered_orders.append(order_id)
                
            except Exception as e:
                print(f"❌ 检查订单{order_id}触发异常: {e}")
        
        # 移除已触发的订单
        for order_id in triggered_orders:
            order = self.active_orders.pop(order_id)
            self.completed_orders.append(order)
    
    def add_order(self, order: AdvancedOrder) -> bool:
        """添加高级订单"""
        try:
            order.update_status(OrderStatus.ACTIVE)
            self.active_orders[order.order_id] = order
            print(f"✅ 添加高级订单: {order.order_id} ({order.order_type.value})")
            return True
            
        except Exception as e:
            print(f"❌ 添加订单失败: {e}")
            return False
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id in self.active_orders:
            order = self.active_orders.pop(order_id)
            order.update_status(OrderStatus.CANCELLED)
            self.completed_orders.append(order)
            print(f"✅ 已取消订单: {order_id}")
            return True
        return False
    
    def get_order_status(self, order_id: str) -> Optional[AdvancedOrder]:
        """获取订单状态"""
        if order_id in self.active_orders:
            return self.active_orders[order_id]
        
        for order in self.completed_orders:
            if order.order_id == order_id:
                return order
        
        return None
    
    def get_active_orders(self) -> List[AdvancedOrder]:
        """获取活跃订单"""
        return list(self.active_orders.values())
    
    def get_completed_orders(self, limit: int = 50) -> List[AdvancedOrder]:
        """获取已完成订单"""
        return self.completed_orders[-limit:]

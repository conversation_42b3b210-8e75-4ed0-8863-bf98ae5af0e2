#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gate.io API客户端
Gate.io API Client for Real-time Market Data
"""

import requests
import json
import hmac
import hashlib
import time
from urllib.parse import urlencode
import websocket
import threading
from typing import Dict, List, Optional, Callable


class GateIOClient:
    """Gate.io REST API客户端"""

    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        """
        初始化Gate.io客户端

        Args:
            api_key: API密钥 (可选，公开数据不需要)
            api_secret: API密钥 (可选，公开数据不需要)
            testnet: 是否使用测试网络
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet

        # API基础URL
        if testnet:
            self.base_url = "https://api-testnet.gateapi.io/api/v4"
        else:
            self.base_url = "https://api.gateio.ws/api/v4"

        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def _generate_signature(self, method: str, url: str, query_string: str = "", body: str = "") -> Dict[str, str]:
        """生成API签名"""
        if not self.api_key or not self.api_secret:
            return {}

        timestamp = str(int(time.time()))

        # 构建签名字符串
        message = f"{method}\n{url}\n{query_string}\n{hashlib.sha512(body.encode()).hexdigest()}\n{timestamp}"

        # 生成签名
        signature = hmac.new(
            self.api_secret.encode(),
            message.encode(),
            hashlib.sha512
        ).hexdigest()

        return {
            'KEY': self.api_key,
            'Timestamp': timestamp,
            'SIGN': signature
        }

    def _request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"

        # 准备查询参数
        query_string = ""
        if params:
            query_string = urlencode(params)
            if method == "GET":
                url += f"?{query_string}"

        # 准备请求体
        body = ""
        if data:
            body = json.dumps(data)

        # 生成签名头
        headers = self._generate_signature(method, endpoint, query_string, body)

        try:
            if method == "GET":
                response = self.session.get(url, headers=headers, timeout=10)
            elif method == "POST":
                response = self.session.post(url, headers=headers, data=body, timeout=10)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            return {"error": str(e)}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {"error": "JSON解析失败"}

    def get_currency_pairs(self) -> List[Dict]:
        """获取所有现货交易对"""
        return self._request("GET", "/spot/currency_pairs")

    def get_tickers(self, currency_pair: str = None) -> List[Dict]:
        """获取行情数据"""
        params = {}
        if currency_pair:
            params['currency_pair'] = currency_pair
        return self._request("GET", "/spot/tickers", params=params)

    def get_order_book(self, currency_pair: str, limit: int = 10) -> Dict:
        """获取订单簿"""
        params = {
            'currency_pair': currency_pair,
            'limit': limit
        }
        return self._request("GET", "/spot/order_book", params=params)

    def get_trades(self, currency_pair: str, limit: int = 100) -> List[Dict]:
        """获取最近交易记录"""
        params = {
            'currency_pair': currency_pair,
            'limit': limit
        }
        return self._request("GET", "/spot/trades", params=params)

    def get_candlesticks(self, currency_pair: str, interval: str = "1m", limit: int = 100) -> List[List]:
        """获取K线数据"""
        params = {
            'currency_pair': currency_pair,
            'interval': interval,
            'limit': limit
        }
        return self._request("GET", "/spot/candlesticks", params=params)


class GateIOWebSocket:
    """Gate.io WebSocket客户端"""

    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        """
        初始化WebSocket客户端

        Args:
            api_key: API密钥 (可选，公开数据不需要)
            api_secret: API密钥 (可选，公开数据不需要)
            testnet: 是否使用测试网络
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet

        # WebSocket URL
        if testnet:
            self.ws_url = "wss://api.gateio.ws/ws/v4/"  # 测试网和正式网使用相同URL
        else:
            self.ws_url = "wss://api.gateio.ws/ws/v4/"

        self.ws = None
        self.callbacks = {}
        self.running = False
        self.thread = None

    def _generate_auth(self, channel: str, event: str, timestamp: int) -> Dict:
        """生成WebSocket认证信息"""
        if not self.api_key or not self.api_secret:
            return {}

        message = f"channel={channel}&event={event}&time={timestamp}"
        signature = hmac.new(
            self.api_secret.encode(),
            message.encode(),
            hashlib.sha512
        ).hexdigest()

        return {
            "method": "api_key",
            "KEY": self.api_key,
            "SIGN": signature
        }

    def _on_message(self, ws, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            channel = data.get('channel', '')

            # 调用对应的回调函数
            if channel in self.callbacks:
                self.callbacks[channel](data)

        except json.JSONDecodeError as e:
            print(f"WebSocket消息解析失败: {e}")
        except Exception as e:
            print(f"WebSocket消息处理失败: {e}")

    def _on_error(self, ws, error):
        """处理WebSocket错误"""
        print(f"WebSocket错误: {error}")

    def _on_close(self, ws, close_status_code, close_msg):
        """处理WebSocket关闭"""
        print("WebSocket连接已关闭")
        self.running = False

    def _on_open(self, ws):
        """处理WebSocket连接打开"""
        print("WebSocket连接已建立")
        self.running = True

    def connect(self):
        """连接WebSocket"""
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close,
            on_open=self._on_open
        )

        # 在新线程中运行
        self.thread = threading.Thread(target=self.ws.run_forever)
        self.thread.daemon = True
        self.thread.start()

    def disconnect(self):
        """断开WebSocket连接"""
        self.running = False
        if self.ws:
            self.ws.close()

    def subscribe(self, channel: str, payload: List[str], callback: Callable, auth_required: bool = False):
        """订阅频道"""
        if not self.ws or not self.running:
            print("WebSocket未连接")
            return

        # 注册回调函数
        self.callbacks[channel] = callback

        # 构建订阅消息
        timestamp = int(time.time())
        message = {
            "time": timestamp,
            "channel": channel,
            "event": "subscribe",
            "payload": payload
        }

        # 如果需要认证
        if auth_required:
            message["auth"] = self._generate_auth(channel, "subscribe", timestamp)

        # 发送订阅消息
        self.ws.send(json.dumps(message))
        print(f"已订阅频道: {channel}")

    def unsubscribe(self, channel: str, payload: List[str]):
        """取消订阅频道"""
        if not self.ws or not self.running:
            print("WebSocket未连接")
            return

        # 移除回调函数
        if channel in self.callbacks:
            del self.callbacks[channel]

        # 构建取消订阅消息
        timestamp = int(time.time())
        message = {
            "time": timestamp,
            "channel": channel,
            "event": "unsubscribe",
            "payload": payload
        }

        # 发送取消订阅消息
        self.ws.send(json.dumps(message))
        print(f"已取消订阅频道: {channel}")


class GateIODataManager:
    """Gate.io数据管理器"""

    def __init__(self, testnet: bool = True):
        """初始化数据管理器"""
        self.testnet = testnet
        self.rest_client = GateIOClient(testnet=testnet)
        self.ws_client = GateIOWebSocket(testnet=testnet)

        # 数据存储
        self.tickers = {}
        self.order_books = {}
        self.trades = {}

        # 回调函数
        self.ticker_callbacks = []
        self.trade_callbacks = []
        self.orderbook_callbacks = []

    def start(self):
        """启动数据管理器"""
        print("🚀 启动Gate.io数据管理器...")

        # 连接WebSocket
        self.ws_client.connect()

        # 等待连接建立
        time.sleep(2)

        print("✅ Gate.io数据管理器启动成功")

    def stop(self):
        """停止数据管理器"""
        print("🛑 停止Gate.io数据管理器...")
        self.ws_client.disconnect()
        print("✅ Gate.io数据管理器已停止")

    def subscribe_tickers(self, currency_pairs: List[str]):
        """订阅行情数据"""
        def ticker_callback(data):
            if data.get('event') == 'update' and 'result' in data:
                result = data['result']
                currency_pair = result.get('currency_pair')
                if currency_pair:
                    self.tickers[currency_pair] = result

                    # 调用回调函数
                    for callback in self.ticker_callbacks:
                        callback(currency_pair, result)

        self.ws_client.subscribe("spot.tickers", currency_pairs, ticker_callback)

    def subscribe_trades(self, currency_pairs: List[str]):
        """订阅交易数据"""
        def trade_callback(data):
            if data.get('event') == 'update' and 'result' in data:
                result = data['result']
                currency_pair = result.get('currency_pair')
                if currency_pair:
                    if currency_pair not in self.trades:
                        self.trades[currency_pair] = []
                    self.trades[currency_pair].append(result)

                    # 保持最近100条记录
                    if len(self.trades[currency_pair]) > 100:
                        self.trades[currency_pair] = self.trades[currency_pair][-100:]

                    # 调用回调函数
                    for callback in self.trade_callbacks:
                        callback(currency_pair, result)

        self.ws_client.subscribe("spot.trades", currency_pairs, trade_callback)

    def add_ticker_callback(self, callback: Callable):
        """添加行情数据回调函数"""
        self.ticker_callbacks.append(callback)

    def add_trade_callback(self, callback: Callable):
        """添加交易数据回调函数"""
        self.trade_callbacks.append(callback)

    def get_ticker(self, currency_pair: str) -> Optional[Dict]:
        """获取行情数据"""
        return self.tickers.get(currency_pair)

    def get_recent_trades(self, currency_pair: str, limit: int = 10) -> List[Dict]:
        """获取最近交易记录"""
        trades = self.trades.get(currency_pair, [])
        return trades[-limit:] if trades else []

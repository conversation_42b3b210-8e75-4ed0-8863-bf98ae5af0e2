{"data_mtime": 1748495455, "dep_lines": [11, 12, 13, 291, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["os", "pathlib", "typing", "time", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "ef95e0058bb4c864beecaf743b60e89809293419", "id": "constants.path_constants", "ignore_all": false, "interface_hash": "6047d69c920ada80edb162060484edc3c847d717", "mtime": 1748495454, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\path_constants.py", "plugin_data": null, "size": 10921, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "core.strategy", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BacktestResult": {".class": "SymbolTableNode", "cross_ref": "core.strategy.backtesting_engine.BacktestResult", "kind": "Gdef"}, "BacktestingEngine": {".class": "SymbolTableNode", "cross_ref": "core.strategy.backtesting_engine.BacktestingEngine", "kind": "Gdef"}, "SignalGenerator": {".class": "SymbolTableNode", "cross_ref": "core.strategy.signal_generator.SignalGenerator", "kind": "Gdef"}, "StrategyConfig": {".class": "SymbolTableNode", "cross_ref": "core.strategy.strategy_manager.StrategyConfig", "kind": "Gdef"}, "StrategyManager": {".class": "SymbolTableNode", "cross_ref": "core.strategy.strategy_manager.StrategyManager", "kind": "Gdef"}, "TradingSignal": {".class": "SymbolTableNode", "cross_ref": "core.strategy.signal_generator.TradingSignal", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.strategy.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.strategy.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.strategy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.strategy.__version__", "name": "__version__", "type": "builtins.str"}}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.strategy.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "get_signal_generator": {".class": "SymbolTableNode", "cross_ref": "core.strategy.signal_generator.get_signal_generator", "kind": "Gdef"}, "get_strategy_manager": {".class": "SymbolTableNode", "cross_ref": "core.strategy.strategy_manager.get_strategy_manager", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\strategy\\__init__.py"}
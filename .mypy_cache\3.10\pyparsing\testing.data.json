{".class": "MypyFile", "_fullname": "pyparsing.testing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Keyword": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Keyword", "kind": "Gdef"}, "ParseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseException", "kind": "Gdef"}, "ParserElement": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__compat__": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.__compat__", "kind": "Gdef"}, "__diag__": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.__diag__", "kind": "Gdef"}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.testing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "pyparsing_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.testing.pyparsing_test", "name": "pyparsing_test", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.testing", "mro": ["pyparsing.testing.pyparsing_test", "builtins.object"], "names": {".class": "SymbolTable", "TestParseResultsAsserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts", "name": "TestParseResultsAsserts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.testing", "mro": ["pyparsing.testing.pyparsing_test.TestParseResultsAsserts", "builtins.object"], "names": {".class": "SymbolTable", "assertParseAndCheckDict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "expr", "test_string", "expected_dict", "msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertParseAndCheckDict", "name": "assertParseAndCheckDict", "type": null}}, "assertParseAndCheckList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "expr", "test_string", "expected_list", "msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertParseAndCheckList", "name": "assertParseAndCheckList", "type": null}}, "assertParseResultsEquals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "result", "expected_list", "expected_dict", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertParseResultsEquals", "name": "assertParseResultsEquals", "type": null}}, "assertRaisesParseException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "expected_msg", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertRaisesParseException", "name": "assertRaisesParseException", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertRaisesParseException", "name": "assertRaisesParseException", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "expected_msg", "msg"], "arg_types": ["pyparsing.testing.pyparsing_test.TestParseResultsAsserts", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assertRaisesParseException of TestParseResultsAsserts", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "assertRunTestResults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "run_tests_report", "expected_parse_results", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.assertRunTestResults", "name": "assertRunTestResults", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.testing.pyparsing_test.TestParseResultsAsserts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "reset_pyparsing_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context", "name": "reset_pyparsing_context", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.testing", "mro": ["pyparsing.testing.pyparsing_test.reset_pyparsing_context", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.__init__", "name": "__init__", "type": null}}, "_save_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context._save_context", "name": "_save_context", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.copy", "name": "copy", "type": null}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.restore", "name": "restore", "type": null}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.save", "name": "save", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.testing.pyparsing_test.reset_pyparsing_context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.testing.pyparsing_test.reset_pyparsing_context", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "with_line_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["s", "start_line", "end_line", "expand_tabs", "eol_mark", "mark_spaces", "mark_control", "indent", "base_1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.testing.pyparsing_test.with_line_numbers", "name": "with_line_numbers", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["s", "start_line", "end_line", "expand_tabs", "eol_mark", "mark_spaces", "mark_control", "indent", "base_1"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_line_numbers of pyparsing_test", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.testing.pyparsing_test.with_line_numbers", "name": "with_line_numbers", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["s", "start_line", "end_line", "expand_tabs", "eol_mark", "mark_spaces", "mark_control", "indent", "base_1"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_line_numbers of pyparsing_test", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.testing.pyparsing_test.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.testing.pyparsing_test", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\testing.py"}
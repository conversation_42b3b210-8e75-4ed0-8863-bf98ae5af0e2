{"data_mtime": 1748490878, "dep_lines": [6, 35, 46, 47, 48, 49, 2169, 2302, 4, 6, 8, 9, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 5, 5, 10, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "pyparsing.util", "pyparsing.exceptions", "pyparsing.actions", "pyparsing.results", "pyparsing.unicode", "pyparsing.testing", "pyparsing.diagram", "__future__", "collections", "os", "typing", "abc", "enum", "string", "copy", "warnings", "re", "sys", "traceback", "types", "operator", "functools", "threading", "pathlib", "builtins", "pprint", "inspect", "html", "itertools", "contextlib", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "typing_extensions"], "hash": "8fb57429bf64272676c95e490c85f589aa46de5b", "id": "pyparsing.core", "ignore_all": true, "interface_hash": "5538f2029f6cbede48208cbe3abcac7a0b56f4f5", "mtime": 1748487517, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\core.py", "plugin_data": null, "size": 234772, "suppressed": [], "version_id": "1.15.0"}
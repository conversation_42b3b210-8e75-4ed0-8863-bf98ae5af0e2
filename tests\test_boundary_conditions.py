#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
边界条件测试
Boundary Condition Tests

测试系统在各种边界条件下的行为
"""

import unittest
import sys
import os
from pathlib import Path
import random
import time
import threading
import queue
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入测试目标
from core.gate_api_connector import APICredentials, get_api_connector
from core.doubling_growth_engine import DoublingGrowthEngine
from core.fast_profit_engine import FastProfitEngine, TradingOpportunity
from core.resource_manager import get_resource_manager

class TestBoundaryConditions(unittest.TestCase):
    """边界条件测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建资源管理器
        self.resource_manager = get_resource_manager()
    
    def tearDown(self):
        """测试后清理"""
        # 清理资源
        if hasattr(self, 'resource_manager') and self.resource_manager:
            self.resource_manager.cleanup_all()
    
    def test_api_connector_empty_credentials(self):
        """测试API连接器空凭证"""
        # 获取API连接器
        connector = get_api_connector()
        
        # 创建空凭证
        empty_credentials = APICredentials(
            api_key="",
            secret_key="",
            sandbox=True
        )
        
        # 尝试连接
        success, message = connector.connect(empty_credentials)
        
        # 验证结果
        self.assertFalse(success, "空凭证应该连接失败")
        self.assertIn("API凭证设置失败", message, "错误消息应该包含凭证设置失败信息")
    
    def test_api_connector_invalid_credentials(self):
        """测试API连接器无效凭证"""
        # 获取API连接器
        connector = get_api_connector()
        
        # 创建无效凭证
        invalid_credentials = APICredentials(
            api_key="invalid_key",
            secret_key="invalid_secret",
            sandbox=True
        )
        
        # 尝试连接
        success, message = connector.connect(invalid_credentials)
        
        # 验证结果
        self.assertFalse(success, "无效凭证应该连接失败")
    
    def test_api_connector_network_error(self):
        """测试API连接器网络错误"""
        # 获取API连接器
        connector = get_api_connector()
        
        # 创建有效凭证但设置无效URL
        if hasattr(connector, 'exchange') and connector.exchange:
            # 保存原始URL
            original_urls = connector.exchange.urls.copy() if hasattr(connector.exchange, 'urls') else {}
            
            try:
                # 设置无效URL
                if hasattr(connector.exchange, 'urls'):
                    connector.exchange.urls['api'] = 'https://invalid.url.that.does.not.exist'
                
                # 创建测试凭证
                test_credentials = APICredentials(
                    api_key="test_key",
                    secret_key="test_secret",
                    sandbox=True
                )
                
                # 尝试连接
                success, message = connector.connect(test_credentials)
                
                # 验证结果
                self.assertFalse(success, "无效URL应该连接失败")
                
            finally:
                # 恢复原始URL
                if hasattr(connector.exchange, 'urls'):
                    connector.exchange.urls = original_urls
    
    def test_doubling_engine_extreme_values(self):
        """测试倍增引擎极端值"""
        # 测试极小初始资金
        tiny_engine = DoublingGrowthEngine(0.01)
        self.assertEqual(tiny_engine.initial_capital, 0.01, "应该接受极小初始资金")
        self.assertEqual(tiny_engine.target_capital, 0.02, "目标资金应该是初始资金的2倍")
        
        # 测试极大初始资金
        huge_engine = DoublingGrowthEngine(1000000000.0)  # 10亿
        self.assertEqual(huge_engine.initial_capital, 1000000000.0, "应该接受极大初始资金")
        self.assertEqual(huge_engine.target_capital, 2000000000.0, "目标资金应该是初始资金的2倍")
        
        # 测试负初始资金
        with self.assertRaises(Exception):
            # 这应该引发异常
            negative_engine = DoublingGrowthEngine(-1000.0)
            negative_engine._simulate_growth_step()
    
    def test_doubling_engine_rapid_growth(self):
        """测试倍增引擎快速增长"""
        engine = DoublingGrowthEngine(1000.0)
        
        # 保存原始增长加速因子
        original_acceleration = engine.growth_acceleration
        
        try:
            # 设置极高的增长加速因子
            engine.growth_acceleration = 100.0
            
            # 执行多次增长步骤
            for _ in range(10):
                engine._simulate_growth_step()
            
            # 验证结果
            self.assertGreater(engine.current_capital, engine.initial_capital, "资金应该增长")
            self.assertLessEqual(engine.doubling_progress, 1.0, "倍增进度不应超过100%")
            
        finally:
            # 恢复原始增长加速因子
            engine.growth_acceleration = original_acceleration
    
    def test_fast_profit_engine_extreme_opportunities(self):
        """测试快速盈利引擎极端交易机会"""
        engine = FastProfitEngine(1000.0)
        
        # 创建极高置信度的交易机会
        high_confidence_opportunity = TradingOpportunity(
            symbol='BTC/USDT',
            strategy='scalping',
            direction='long',
            entry_price=50000.0,
            target_price=50400.0,  # 0.8% 目标
            stop_loss=49250.0,     # 1.5% 止损
            confidence=1.0,        # 100%置信度
            expected_profit=8.0,
            risk_amount=20.0,
            hold_time=30
        )
        
        # 执行交易
        result = engine.execute_trade(high_confidence_opportunity)
        
        # 验证结果
        if result:
            self.assertIn(result.result, ['win', 'loss'], "交易结果应该是win或loss")
        
        # 创建极低置信度的交易机会
        low_confidence_opportunity = TradingOpportunity(
            symbol='BTC/USDT',
            strategy='scalping',
            direction='long',
            entry_price=50000.0,
            target_price=50400.0,
            stop_loss=49250.0,
            confidence=0.0,        # 0%置信度
            expected_profit=8.0,
            risk_amount=20.0,
            hold_time=30
        )
        
        # 执行交易
        result = engine.execute_trade(low_confidence_opportunity)
        
        # 验证结果
        if result:
            self.assertIn(result.result, ['win', 'loss'], "交易结果应该是win或loss")
    
    def test_fast_profit_engine_consecutive_losses(self):
        """测试快速盈利引擎连续亏损"""
        engine = FastProfitEngine(1000.0)
        
        # 模拟连续亏损
        for _ in range(5):
            engine._update_statistics(-10.0, 'loss')
        
        # 验证结果
        self.assertEqual(engine.consecutive_losses, 5, "连续亏损计数应该正确")
        self.assertFalse(engine._can_trade(), "连续亏损后应该不能交易")
    
    def test_resource_manager_thread_cleanup(self):
        """测试资源管理器线程清理"""
        # 创建测试线程
        def test_thread_func():
            time.sleep(0.1)
        
        test_thread = threading.Thread(target=test_thread_func)
        test_thread.start()
        
        # 注册线程
        self.resource_manager.register_thread(test_thread)
        
        # 等待线程结束
        test_thread.join()
        
        # 检查资源泄漏
        self.resource_manager._check_resource_leaks()
        
        # 验证线程已被清理
        self.assertNotIn(test_thread, self.resource_manager.threads, "死亡线程应该被清理")
    
    def test_resource_manager_file_cleanup(self):
        """测试资源管理器文件清理"""
        # 创建临时文件
        temp_file_path = Path("temp_test_file.txt")
        temp_file = open(temp_file_path, 'w')
        
        # 注册文件句柄
        self.resource_manager.register_file_handle(temp_file)
        
        # 关闭文件
        temp_file.close()
        
        # 检查资源泄漏
        self.resource_manager._check_resource_leaks()
        
        # 验证文件已被清理
        self.assertNotIn(temp_file, self.resource_manager.file_handles, "关闭的文件应该被清理")
        
        # 删除临时文件
        if temp_file_path.exists():
            os.remove(temp_file_path)
    
    def test_resource_manager_memory_tracking(self):
        """测试资源管理器内存跟踪"""
        # 创建大量对象来增加内存使用
        large_objects = []
        for _ in range(10):
            large_objects.append([0] * 1000000)  # 每个列表约8MB
        
        # 获取内存使用情况
        usage = self.resource_manager.get_resource_usage()
        
        # 验证内存使用信息存在
        self.assertIn('memory', usage, "应该包含内存使用信息")
        self.assertIn('rss', usage['memory'], "应该包含物理内存使用信息")
        self.assertIn('rss_mb', usage['memory'], "应该包含MB格式的内存使用信息")
        self.assertIn('percent', usage['memory'], "应该包含内存使用百分比")
        
        # 清理大对象
        large_objects.clear()
    
    def test_concurrent_resource_access(self):
        """测试并发资源访问"""
        # 创建共享资源
        shared_resource = {'value': 0}
        
        # 注册资源
        self.resource_manager.register_resource('test', 'shared', shared_resource)
        
        # 创建线程函数
        def increment_resource():
            for _ in range(100):
                # 获取资源锁
                if self.resource_manager.lock_resource('test', 'shared'):
                    try:
                        # 修改资源
                        resource = self.resource_manager.get_resource('test', 'shared')
                        resource['value'] += 1
                        time.sleep(0.001)  # 增加竞争条件的可能性
                    finally:
                        # 释放资源锁
                        self.resource_manager.unlock_resource('test', 'shared')
        
        # 创建多个线程
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=increment_resource)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        final_resource = self.resource_manager.get_resource('test', 'shared')
        self.assertEqual(final_resource['value'], 1000, "并发访问应该正确处理")

if __name__ == '__main__':
    unittest.main()
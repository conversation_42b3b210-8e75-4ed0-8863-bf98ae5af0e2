#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国际化语言管理器
Internationalization Language Manager

提供多语言支持功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional


class LanguageManager:
    """语言管理器"""
    
    # 支持的语言
    SUPPORTED_LANGUAGES = {
        "zh_CN": "简体中文",
        "zh_TW": "繁體中文", 
        "en_US": "English",
        "ja_JP": "日本語",
        "ko_KR": "한국어"
    }
    
    # 默认语言包
    DEFAULT_TRANSLATIONS = {
        "zh_CN": {
            # 通用
            "app_title": "终极版现货交易系统",
            "app_subtitle": "专业级加密货币交易平台",
            "loading": "正在加载...",
            "success": "成功",
            "error": "错误",
            "warning": "警告",
            "info": "信息",
            "confirm": "确认",
            "cancel": "取消",
            "ok": "确定",
            "yes": "是",
            "no": "否",
            "save": "保存",
            "load": "加载",
            "export": "导出",
            "import": "导入",
            "refresh": "刷新",
            "close": "关闭",
            
            # 菜单和按钮
            "connect": "连接",
            "disconnect": "断开连接",
            "start": "开始",
            "stop": "停止",
            "pause": "暂停",
            "resume": "恢复",
            "reset": "重置",
            "clear": "清空",
            "delete": "删除",
            "edit": "编辑",
            "view": "查看",
            "settings": "设置",
            "help": "帮助",
            "about": "关于",
            
            # 交易相关
            "trading": "交易",
            "buy": "买入",
            "sell": "卖出",
            "order": "订单",
            "position": "持仓",
            "balance": "余额",
            "profit": "盈利",
            "loss": "亏损",
            "pnl": "盈亏",
            "volume": "成交量",
            "price": "价格",
            "amount": "数量",
            "symbol": "交易对",
            "side": "方向",
            "type": "类型",
            "status": "状态",
            "time": "时间",
            "fee": "手续费",
            
            # 策略相关
            "strategy": "策略",
            "signal": "信号",
            "backtest": "回测",
            "optimization": "优化",
            "parameter": "参数",
            "performance": "性能",
            "analysis": "分析",
            "report": "报告",
            
            # 风险管理
            "risk": "风险",
            "risk_management": "风险管理",
            "stop_loss": "止损",
            "take_profit": "止盈",
            "max_drawdown": "最大回撤",
            "win_rate": "胜率",
            "risk_level": "风险等级",
            "low_risk": "低风险",
            "medium_risk": "中风险",
            "high_risk": "高风险",
            "critical_risk": "严重风险",
            
            # 状态信息
            "connected": "已连接",
            "disconnected": "已断开",
            "online": "在线",
            "offline": "离线",
            "running": "运行中",
            "stopped": "已停止",
            "paused": "已暂停",
            "completed": "已完成",
            "failed": "失败",
            "pending": "等待中",
            
            # 消息提示
            "connection_success": "连接成功",
            "connection_failed": "连接失败",
            "operation_success": "操作成功",
            "operation_failed": "操作失败",
            "data_saved": "数据已保存",
            "data_loaded": "数据已加载",
            "invalid_input": "输入无效",
            "insufficient_balance": "余额不足",
            "order_placed": "订单已提交",
            "order_cancelled": "订单已取消",
            
            # 标签页
            "tab_monitor": "实时监控",
            "tab_positions": "持仓管理", 
            "tab_history": "交易历史",
            "tab_market": "市场数据",
            "tab_strategy": "策略管理",
            "tab_chart": "K线图表",
            "tab_performance": "性能监控",
            
            # 风险警告
            "risk_warning": "风险警告",
            "risk_disclaimer": "投资有风险，交易需谨慎",
            "simulation_mode": "模拟模式",
            "real_mode": "真实模式"
        },
        
        "en_US": {
            # Common
            "app_title": "Ultimate Spot Trading System",
            "app_subtitle": "Professional Cryptocurrency Trading Platform",
            "loading": "Loading...",
            "success": "Success",
            "error": "Error", 
            "warning": "Warning",
            "info": "Information",
            "confirm": "Confirm",
            "cancel": "Cancel",
            "ok": "OK",
            "yes": "Yes",
            "no": "No",
            "save": "Save",
            "load": "Load",
            "export": "Export",
            "import": "Import",
            "refresh": "Refresh",
            "close": "Close",
            
            # Menu and Buttons
            "connect": "Connect",
            "disconnect": "Disconnect",
            "start": "Start",
            "stop": "Stop",
            "pause": "Pause",
            "resume": "Resume",
            "reset": "Reset",
            "clear": "Clear",
            "delete": "Delete",
            "edit": "Edit",
            "view": "View",
            "settings": "Settings",
            "help": "Help",
            "about": "About",
            
            # Trading
            "trading": "Trading",
            "buy": "Buy",
            "sell": "Sell",
            "order": "Order",
            "position": "Position",
            "balance": "Balance",
            "profit": "Profit",
            "loss": "Loss",
            "pnl": "P&L",
            "volume": "Volume",
            "price": "Price",
            "amount": "Amount",
            "symbol": "Symbol",
            "side": "Side",
            "type": "Type",
            "status": "Status",
            "time": "Time",
            "fee": "Fee",
            
            # Strategy
            "strategy": "Strategy",
            "signal": "Signal",
            "backtest": "Backtest",
            "optimization": "Optimization",
            "parameter": "Parameter",
            "performance": "Performance",
            "analysis": "Analysis",
            "report": "Report",
            
            # Risk Management
            "risk": "Risk",
            "risk_management": "Risk Management",
            "stop_loss": "Stop Loss",
            "take_profit": "Take Profit",
            "max_drawdown": "Max Drawdown",
            "win_rate": "Win Rate",
            "risk_level": "Risk Level",
            "low_risk": "Low Risk",
            "medium_risk": "Medium Risk",
            "high_risk": "High Risk",
            "critical_risk": "Critical Risk",
            
            # Status
            "connected": "Connected",
            "disconnected": "Disconnected",
            "online": "Online",
            "offline": "Offline",
            "running": "Running",
            "stopped": "Stopped",
            "paused": "Paused",
            "completed": "Completed",
            "failed": "Failed",
            "pending": "Pending",
            
            # Messages
            "connection_success": "Connection Successful",
            "connection_failed": "Connection Failed",
            "operation_success": "Operation Successful",
            "operation_failed": "Operation Failed",
            "data_saved": "Data Saved",
            "data_loaded": "Data Loaded",
            "invalid_input": "Invalid Input",
            "insufficient_balance": "Insufficient Balance",
            "order_placed": "Order Placed",
            "order_cancelled": "Order Cancelled",
            
            # Tabs
            "tab_monitor": "Real-time Monitor",
            "tab_positions": "Position Management",
            "tab_history": "Trading History",
            "tab_market": "Market Data",
            "tab_strategy": "Strategy Management",
            "tab_chart": "K-line Chart",
            "tab_performance": "Performance Monitor",
            
            # Risk Warning
            "risk_warning": "Risk Warning",
            "risk_disclaimer": "Trading involves risk, please trade carefully",
            "simulation_mode": "Simulation Mode",
            "real_mode": "Real Mode"
        }
    }
    
    def __init__(self, language: str = "zh_CN"):
        """
        初始化语言管理器
        
        Args:
            language: 语言代码
        """
        self.current_language = language
        self.translations = {}
        self.load_translations()
    
    def load_translations(self) -> None:
        """加载翻译文件"""
        try:
            # 加载默认翻译
            self.translations = self.DEFAULT_TRANSLATIONS.copy()
            
            # 尝试加载外部翻译文件
            lang_dir = Path(__file__).parent / "languages"
            if lang_dir.exists():
                for lang_file in lang_dir.glob("*.json"):
                    lang_code = lang_file.stem
                    try:
                        with open(lang_file, 'r', encoding='utf-8') as f:
                            external_translations = json.load(f)
                        
                        if lang_code in self.translations:
                            self.translations[lang_code].update(external_translations)
                        else:
                            self.translations[lang_code] = external_translations
                            
                    except Exception as e:
                        print(f"加载语言文件失败 {lang_file}: {e}")
                        
        except Exception as e:
            print(f"加载翻译失败: {e}")
    
    def set_language(self, language: str) -> bool:
        """
        设置当前语言
        
        Args:
            language: 语言代码
            
        Returns:
            是否设置成功
        """
        if language in self.SUPPORTED_LANGUAGES:
            self.current_language = language
            return True
        return False
    
    def get_text(self, key: str, default: Optional[str] = None) -> str:
        """
        获取翻译文本
        
        Args:
            key: 翻译键
            default: 默认文本
            
        Returns:
            翻译后的文本
        """
        try:
            # 获取当前语言的翻译
            current_translations = self.translations.get(self.current_language, {})
            
            if key in current_translations:
                return current_translations[key]
            
            # 如果当前语言没有，尝试中文
            if self.current_language != "zh_CN":
                zh_translations = self.translations.get("zh_CN", {})
                if key in zh_translations:
                    return zh_translations[key]
            
            # 如果都没有，返回默认值或键名
            return default if default is not None else key
            
        except Exception:
            return default if default is not None else key
    
    def get_language_name(self, language: str = None) -> str:
        """
        获取语言显示名称
        
        Args:
            language: 语言代码，None表示当前语言
            
        Returns:
            语言显示名称
        """
        if language is None:
            language = self.current_language
        return self.SUPPORTED_LANGUAGES.get(language, language)
    
    def get_supported_languages(self) -> Dict[str, str]:
        """
        获取支持的语言列表
        
        Returns:
            语言代码到显示名称的映射
        """
        return self.SUPPORTED_LANGUAGES.copy()
    
    def add_translation(self, language: str, key: str, text: str) -> bool:
        """
        添加翻译
        
        Args:
            language: 语言代码
            key: 翻译键
            text: 翻译文本
            
        Returns:
            是否添加成功
        """
        try:
            if language not in self.translations:
                self.translations[language] = {}
            
            self.translations[language][key] = text
            return True
            
        except Exception as e:
            print(f"添加翻译失败: {e}")
            return False
    
    def save_translations(self, language: str = None) -> bool:
        """
        保存翻译到文件
        
        Args:
            language: 语言代码，None表示保存所有语言
            
        Returns:
            是否保存成功
        """
        try:
            lang_dir = Path(__file__).parent / "languages"
            lang_dir.mkdir(exist_ok=True)
            
            languages_to_save = [language] if language else self.translations.keys()
            
            for lang in languages_to_save:
                if lang in self.translations:
                    lang_file = lang_dir / f"{lang}.json"
                    with open(lang_file, 'w', encoding='utf-8') as f:
                        json.dump(self.translations[lang], f, 
                                indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存翻译失败: {e}")
            return False
    
    def format_text(self, key: str, **kwargs) -> str:
        """
        格式化翻译文本
        
        Args:
            key: 翻译键
            **kwargs: 格式化参数
            
        Returns:
            格式化后的文本
        """
        text = self.get_text(key)
        try:
            return text.format(**kwargs)
        except Exception:
            return text


# 全局语言管理器实例
_language_manager = None


def get_language_manager() -> LanguageManager:
    """获取全局语言管理器实例"""
    global _language_manager
    if _language_manager is None:
        _language_manager = LanguageManager()
    return _language_manager


def _(key: str, default: Optional[str] = None) -> str:
    """快捷方式：获取翻译文本"""
    return get_language_manager().get_text(key, default)


def set_language(language: str) -> bool:
    """快捷方式：设置语言"""
    return get_language_manager().set_language(language)


if __name__ == "__main__":
    # 测试代码
    print("🌍 国际化语言管理器测试")
    
    lang = LanguageManager()
    
    # 测试中文
    print(f"中文标题: {lang.get_text('app_title')}")
    
    # 测试英文
    lang.set_language("en_US")
    print(f"English title: {lang.get_text('app_title')}")
    
    # 测试支持的语言
    languages = lang.get_supported_languages()
    print(f"支持的语言: {languages}")
    
    print("🎉 国际化语言管理器测试完成")

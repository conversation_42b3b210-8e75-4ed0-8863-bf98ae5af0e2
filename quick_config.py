#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速配置脚本
Quick Configuration Script

帮助用户快速配置交易系统
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("🔧" + "=" * 58 + "🔧")
    print("🔧                                                        🔧")
    print("🔧     企业级现货交易系统 - 快速配置                      🔧")
    print("🔧     Quick Configuration Tool                          🔧")
    print("🔧                                                        🔧")
    print("🔧" + "=" * 58 + "🔧")

def show_configuration_options():
    """显示配置选项"""
    print("\n📋 配置选项:")
    print("1. 🔧 创建配置文件模板")
    print("2. 📝 打开配置文件编辑")
    print("3. 🔍 验证当前配置")
    print("4. 🚀 测试API连接")
    print("5. 📊 查看配置状态")
    print("6. 📚 查看配置指南")
    print("7. ❌ 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-7): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                return choice
            else:
                print("❌ 无效选择，请输入1-7")
        except KeyboardInterrupt:
            return '7'

def create_config_template():
    """创建配置文件模板"""
    print("\n🔧 创建配置文件模板...")
    
    env_file = Path('.env')
    template_file = Path('.env.template')
    
    if env_file.exists():
        backup = input("配置文件已存在，是否备份? (y/n): ").strip().lower()
        if backup in ['y', 'yes']:
            backup_name = f".env.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy(env_file, backup_name)
            print(f"✅ 已备份为: {backup_name}")
    
    if template_file.exists():
        shutil.copy(template_file, env_file)
        print("✅ 配置模板已创建: .env")
        print("💡 请编辑 .env 文件，替换您的真实API密钥")
        return True
    else:
        print("❌ 模板文件不存在")
        return False

def open_config_editor():
    """打开配置文件编辑器"""
    print("\n📝 打开配置文件编辑...")
    
    env_file = Path('.env')
    if not env_file.exists():
        create_new = input("配置文件不存在，是否创建? (y/n): ").strip().lower()
        if create_new in ['y', 'yes']:
            if not create_config_template():
                return False
        else:
            return False
    
    try:
        # 尝试用记事本打开
        os.system(f'notepad {env_file}')
        print("✅ 配置文件已在记事本中打开")
        print("💡 请替换API密钥后保存文件")
        return True
    except Exception as e:
        print(f"❌ 无法打开编辑器: {e}")
        print(f"💡 请手动编辑文件: {env_file}")
        return False

def validate_config():
    """验证配置"""
    print("\n🔍 验证当前配置...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 检查API密钥
        if 'YOUR_GATE_IO_API_KEY_HERE' in content:
            issues.append("API Key未配置")
        elif 'demo_gate_api_key' in content:
            issues.append("仍在使用演示API Key")
        
        if 'YOUR_GATE_IO_API_SECRET_HERE' in content:
            issues.append("API Secret未配置")
        elif 'demo_gate_secret' in content:
            issues.append("仍在使用演示API Secret")
        
        # 检查密码
        if 'YOUR_STRONG_PASSWORD_HERE' in content:
            issues.append("主密码未设置")
        
        # 检查邮箱
        if '<EMAIL>' in content:
            issues.append("邮箱地址未配置（可选）")
        
        if issues:
            print("⚠️ 发现配置问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ 配置验证通过")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🚀 测试API连接...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'core/api_connection_tester.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ API连接测试通过")
            return True
        else:
            print("❌ API连接测试失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ API连接测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_config_status():
    """显示配置状态"""
    print("\n📊 配置状态:")
    print("-" * 40)
    
    env_file = Path('.env')
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # API配置状态
            if 'YOUR_GATE_IO_API_KEY_HERE' in content or 'demo_gate_api_key' in content:
                print("❌ API密钥: 未配置")
            else:
                print("✅ API密钥: 已配置")
            
            # 交易模式
            if 'EXCHANGE_SANDBOX=true' in content:
                print("🧪 交易模式: 沙盒模式")
            else:
                print("💰 交易模式: 实盘模式")
            
            # 环境设置
            if 'ENVIRONMENT=development' in content:
                print("🔧 环境: 开发环境")
            else:
                print("🏢 环境: 生产环境")
            
            # 资金设置
            import re
            capital_match = re.search(r'INITIAL_CAPITAL=(\d+)', content)
            if capital_match:
                capital = capital_match.group(1)
                print(f"💰 初始资金: {capital} USDT")
            
            loss_match = re.search(r'DAILY_LOSS_LIMIT=(\d+)', content)
            if loss_match:
                loss_limit = loss_match.group(1)
                print(f"🛡️ 日亏损限制: {loss_limit} USDT")
                
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
    else:
        print("❌ 配置文件: 不存在")

def show_config_guide():
    """显示配置指南"""
    print("\n📚 配置指南:")
    print("-" * 40)
    
    guide_file = Path('用户配置指南.md')
    if guide_file.exists():
        try:
            # 尝试用默认程序打开
            os.startfile(str(guide_file))
            print("✅ 配置指南已打开")
        except Exception:
            print("💡 请查看文件: 用户配置指南.md")
    else:
        print("❌ 配置指南文件不存在")
    
    print("\n🎯 快速配置步骤:")
    print("1. 访问 https://www.gate.io 获取API密钥")
    print("2. 运行选项1创建配置模板")
    print("3. 运行选项2编辑配置文件")
    print("4. 替换API密钥和其他设置")
    print("5. 运行选项3验证配置")
    print("6. 运行选项4测试API连接")

def main():
    """主函数"""
    print_banner()
    
    print("\n🎯 欢迎使用快速配置工具！")
    print("此工具将帮助您快速配置交易系统")
    
    while True:
        try:
            choice = show_configuration_options()
            
            if choice == '1':
                create_config_template()
            elif choice == '2':
                open_config_editor()
            elif choice == '3':
                validate_config()
            elif choice == '4':
                test_api_connection()
            elif choice == '5':
                show_config_status()
            elif choice == '6':
                show_config_guide()
            elif choice == '7':
                print("\n👋 再见！")
                break
            
            # 询问是否继续
            if choice != '7':
                continue_choice = input("\n是否继续配置? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
    
    print("\n💡 配置完成后，请运行: python start_trading.py")
    input("按回车键退出...")

if __name__ == "__main__":
    main()

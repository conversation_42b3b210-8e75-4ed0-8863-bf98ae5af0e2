#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行所有测试
Run All Tests

执行所有测试套件
"""

import unittest
import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(Path("logs") / "test_results.log", encoding='utf-8', mode='w')
    ]
)

logger = logging.getLogger("测试运行器")

# 确保logs目录存在
Path("logs").mkdir(exist_ok=True)

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("终极版现货交易系统 - 全面测试套件")
    print("=" * 60)
    
    # 发现并加载所有测试
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover(start_dir=str(Path(__file__).parent), pattern="test_*.py")
    
    # 运行测试
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print(f"测试结果摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("=" * 60)
    
    # 记录测试结果
    logger.info(f"测试完成 - 运行: {result.testsRun}, 成功: {result.testsRun - len(result.failures) - len(result.errors)}, 失败: {len(result.failures)}, 错误: {len(result.errors)}")
    
    # 如果有失败或错误，记录详细信息
    if result.failures:
        logger.error("测试失败:")
        for test, traceback in result.failures:
            logger.error(f"{test}: {traceback}")
    
    if result.errors:
        logger.error("测试错误:")
        for test, traceback in result.errors:
            logger.error(f"{test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
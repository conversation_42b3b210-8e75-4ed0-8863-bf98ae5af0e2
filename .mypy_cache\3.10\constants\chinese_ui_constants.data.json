{".class": "MypyFile", "_fullname": "constants.chinese_ui_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CHINESE_UI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.CHINESE_UI", "name": "CHINESE_UI", "type": "constants.chinese_ui_constants.ChineseUIConstants"}}, "ChineseUIConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "constants.chinese_ui_constants.ChineseUIConstants", "name": "ChineseUIConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "constants.chinese_ui_constants.ChineseUIConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "constants.chinese_ui_constants", "mro": ["constants.chinese_ui_constants.ChineseUIConstants", "builtins.object"], "names": {".class": "SymbolTable", "ACCOUNT_BALANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_BALANCE", "name": "ACCOUNT_BALANCE", "type": "builtins.str"}}, "ACCOUNT_EQUITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_EQUITY", "name": "ACCOUNT_EQUITY", "type": "builtins.str"}}, "ACCOUNT_FREE_MARGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_FREE_MARGIN", "name": "ACCOUNT_FREE_MARGIN", "type": "builtins.str"}}, "ACCOUNT_INFO_PANEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_INFO_PANEL", "name": "ACCOUNT_INFO_PANEL", "type": "builtins.str"}}, "ACCOUNT_MARGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_MARGIN", "name": "ACCOUNT_MARGIN", "type": "builtins.str"}}, "ACCOUNT_MARGIN_LEVEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_MARGIN_LEVEL", "name": "ACCOUNT_MARGIN_LEVEL", "type": "builtins.str"}}, "ACCOUNT_REALIZED_PL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_REALIZED_PL", "name": "ACCOUNT_REALIZED_PL", "type": "builtins.str"}}, "ACCOUNT_UNREALIZED_PL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ACCOUNT_UNREALIZED_PL", "name": "ACCOUNT_UNREALIZED_PL", "type": "builtins.str"}}, "ALL_ORDER_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ALL_ORDER_TYPES", "name": "ALL_ORDER_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ANALYZE_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ANALYZE_POSITIONS", "name": "ANALYZE_POSITIONS", "type": "builtins.str"}}, "APP_SUBTITLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.APP_SUBTITLE", "name": "APP_SUBTITLE", "type": "builtins.str"}}, "APP_TITLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.APP_TITLE", "name": "APP_TITLE", "type": "builtins.str"}}, "BUY_BUTTON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.BUY_BUTTON", "name": "BUY_BUTTON", "type": "builtins.str"}}, "CANCEL_ALL_ORDERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CANCEL_ALL_ORDERS", "name": "CANCEL_ALL_ORDERS", "type": "builtins.str"}}, "CANCEL_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CANCEL_ORDER", "name": "CANCEL_ORDER", "type": "builtins.str"}}, "CHART_INDICATORS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CHART_INDICATORS", "name": "CHART_INDICATORS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CHART_TIMEFRAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CHART_TIMEFRAMES", "name": "CHART_TIMEFRAMES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CLOSE_ALL_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CLOSE_ALL_POSITIONS", "name": "CLOSE_ALL_POSITIONS", "type": "builtins.str"}}, "CLOSE_POSITION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CLOSE_POSITION", "name": "CLOSE_POSITION", "type": "builtins.str"}}, "CONFIRM_CANCEL_ALL_ORDERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONFIRM_CANCEL_ALL_ORDERS", "name": "CONFIRM_CANCEL_ALL_ORDERS", "type": "builtins.str"}}, "CONFIRM_CLOSE_ALL_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONFIRM_CLOSE_ALL_POSITIONS", "name": "CONFIRM_CLOSE_ALL_POSITIONS", "type": "builtins.str"}}, "CONFIRM_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONFIRM_EMERGENCY_STOP", "name": "CONFIRM_EMERGENCY_STOP", "type": "builtins.str"}}, "CONFIRM_LARGE_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONFIRM_LARGE_ORDER", "name": "CONFIRM_LARGE_ORDER", "type": "builtins.str"}}, "CONFIRM_START_LIVE_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONFIRM_START_LIVE_TRADING", "name": "CONFIRM_START_LIVE_TRADING", "type": "builtins.str"}}, "CONNECT_EXCHANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CONNECT_EXCHANGE", "name": "CONNECT_EXCHANGE", "type": "builtins.str"}}, "CURRENCY_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.CURRENCY_FORMAT", "name": "CURRENCY_FORMAT", "type": "builtins.str"}}, "DATE_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DATE_FORMAT", "name": "DATE_FORMAT", "type": "builtins.str"}}, "DIALOG_APPLY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_APPLY", "name": "DIALOG_APPLY", "type": "builtins.str"}}, "DIALOG_CANCEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CANCEL", "name": "DIALOG_CANCEL", "type": "builtins.str"}}, "DIALOG_CLOSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CLOSE", "name": "DIALOG_CLOSE", "type": "builtins.str"}}, "DIALOG_CONFIRM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CONFIRM", "name": "DIALOG_CONFIRM", "type": "builtins.str"}}, "DIALOG_CONFIRM_CANCEL_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CONFIRM_CANCEL_ALL", "name": "DIALOG_CONFIRM_CANCEL_ALL", "type": "builtins.str"}}, "DIALOG_CONFIRM_CLOSE_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CONFIRM_CLOSE_ALL", "name": "DIALOG_CONFIRM_CLOSE_ALL", "type": "builtins.str"}}, "DIALOG_CONFIRM_LIVE_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_CONFIRM_LIVE_TRADING", "name": "DIALOG_CONFIRM_LIVE_TRADING", "type": "builtins.str"}}, "DIALOG_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_EMERGENCY_STOP", "name": "DIALOG_EMERGENCY_STOP", "type": "builtins.str"}}, "DIALOG_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_ERROR", "name": "DIALOG_ERROR", "type": "builtins.str"}}, "DIALOG_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_INFO", "name": "DIALOG_INFO", "type": "builtins.str"}}, "DIALOG_NO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_NO", "name": "DIALOG_NO", "type": "builtins.str"}}, "DIALOG_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_OK", "name": "DIALOG_OK", "type": "builtins.str"}}, "DIALOG_ORDER_PLACED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_ORDER_PLACED", "name": "DIALOG_ORDER_PLACED", "type": "builtins.str"}}, "DIALOG_POSITION_ANALYSIS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_POSITION_ANALYSIS", "name": "DIALOG_POSITION_ANALYSIS", "type": "builtins.str"}}, "DIALOG_SYSTEM_SETTINGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_SYSTEM_SETTINGS", "name": "DIALOG_SYSTEM_SETTINGS", "type": "builtins.str"}}, "DIALOG_TRADING_REPORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_TRADING_REPORT", "name": "DIALOG_TRADING_REPORT", "type": "builtins.str"}}, "DIALOG_WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_WARNING", "name": "DIALOG_WARNING", "type": "builtins.str"}}, "DIALOG_YES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.DIALOG_YES", "name": "DIALOG_YES", "type": "builtins.str"}}, "EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.EMERGENCY_STOP", "name": "EMERGENCY_STOP", "type": "builtins.str"}}, "ERROR_CANCEL_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_CANCEL_FAILED", "name": "ERROR_CANCEL_FAILED", "type": "builtins.str"}}, "ERROR_CLOSE_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_CLOSE_FAILED", "name": "ERROR_CLOSE_FAILED", "type": "builtins.str"}}, "ERROR_CONNECTION_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_CONNECTION_FAILED", "name": "ERROR_CONNECTION_FAILED", "type": "builtins.str"}}, "ERROR_DATA_LOAD_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_DATA_LOAD_FAILED", "name": "ERROR_DATA_LOAD_FAILED", "type": "builtins.str"}}, "ERROR_EXPORT_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_EXPORT_FAILED", "name": "ERROR_EXPORT_FAILED", "type": "builtins.str"}}, "ERROR_INSUFFICIENT_BALANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_INSUFFICIENT_BALANCE", "name": "ERROR_INSUFFICIENT_BALANCE", "type": "builtins.str"}}, "ERROR_INVALID_INPUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_INVALID_INPUT", "name": "ERROR_INVALID_INPUT", "type": "builtins.str"}}, "ERROR_ORDER_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ERROR_ORDER_FAILED", "name": "ERROR_ORDER_FAILED", "type": "builtins.str"}}, "EXPORT_DATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.EXPORT_DATA", "name": "EXPORT_DATA", "type": "builtins.str"}}, "GENERATE_REPORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.GENERATE_REPORT", "name": "GENERATE_REPORT", "type": "builtins.str"}}, "HISTORY_COLUMNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.HISTORY_COLUMNS", "name": "HISTORY_COLUMNS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LIVE_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.LIVE_MODE", "name": "LIVE_MODE", "type": "builtins.str"}}, "MARKET_COLUMNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MARKET_COLUMNS", "name": "MARKET_COLUMNS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MENU_EDIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MENU_EDIT", "name": "MENU_EDIT", "type": "builtins.str"}}, "MENU_FILE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MENU_FILE", "name": "MENU_FILE", "type": "builtins.str"}}, "MENU_HELP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MENU_HELP", "name": "MENU_HELP", "type": "builtins.str"}}, "MENU_TOOLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MENU_TOOLS", "name": "MENU_TOOLS", "type": "builtins.str"}}, "MENU_VIEW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MENU_VIEW", "name": "MENU_VIEW", "type": "builtins.str"}}, "MODE_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MODE_LABEL", "name": "MODE_LABEL", "type": "builtins.str"}}, "MONITOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR", "name": "MONITOR", "type": "builtins.str"}}, "MONITOR_API_STATUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_API_STATUS", "name": "MONITOR_API_STATUS", "type": "builtins.str"}}, "MONITOR_CPU_USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_CPU_USAGE", "name": "MONITOR_CPU_USAGE", "type": "builtins.str"}}, "MONITOR_MEMORY_USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_MEMORY_USAGE", "name": "MONITOR_MEMORY_USAGE", "type": "builtins.str"}}, "MONITOR_NETWORK_STATUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_NETWORK_STATUS", "name": "MONITOR_NETWORK_STATUS", "type": "builtins.str"}}, "MONITOR_SYSTEM_TIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_SYSTEM_TIME", "name": "MONITOR_SYSTEM_TIME", "type": "builtins.str"}}, "MONITOR_TRADING_STATUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_TRADING_STATUS", "name": "MONITOR_TRADING_STATUS", "type": "builtins.str"}}, "MONITOR_UPTIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MONITOR_UPTIME", "name": "MONITOR_UPTIME", "type": "builtins.str"}}, "MSG_ANALYSIS_COMPLETED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_ANALYSIS_COMPLETED", "name": "MSG_ANALYSIS_COMPLETED", "type": "builtins.str"}}, "MSG_ANALYZING_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_ANALYZING_POSITIONS", "name": "MSG_ANALYZING_POSITIONS", "type": "builtins.str"}}, "MSG_BUY_ORDER_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_BUY_ORDER_SUCCESS", "name": "MSG_BUY_ORDER_SUCCESS", "type": "builtins.str"}}, "MSG_CANCELLING_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_CANCELLING_ORDER", "name": "MSG_CANCELLING_ORDER", "type": "builtins.str"}}, "MSG_CLOSING_POSITION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_CLOSING_POSITION", "name": "MSG_CLOSING_POSITION", "type": "builtins.str"}}, "MSG_CONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_CONNECTED", "name": "MSG_CONNECTED", "type": "builtins.str"}}, "MSG_CONNECTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_CONNECTING", "name": "MSG_CONNECTING", "type": "builtins.str"}}, "MSG_CONNECTION_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_CONNECTION_FAILED", "name": "MSG_CONNECTION_FAILED", "type": "builtins.str"}}, "MSG_DATA_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_DATA_REFRESHED", "name": "MSG_DATA_REFRESHED", "type": "builtins.str"}}, "MSG_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_EMERGENCY_STOP", "name": "MSG_EMERGENCY_STOP", "type": "builtins.str"}}, "MSG_GENERATING_REPORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_GENERATING_REPORT", "name": "MSG_GENERATING_REPORT", "type": "builtins.str"}}, "MSG_HISTORY_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_HISTORY_REFRESHED", "name": "MSG_HISTORY_REFRESHED", "type": "builtins.str"}}, "MSG_MARKET_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_MARKET_REFRESHED", "name": "MSG_MARKET_REFRESHED", "type": "builtins.str"}}, "MSG_ORDERS_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_ORDERS_REFRESHED", "name": "MSG_ORDERS_REFRESHED", "type": "builtins.str"}}, "MSG_ORDER_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_ORDER_CANCELLED", "name": "MSG_ORDER_CANCELLED", "type": "builtins.str"}}, "MSG_PLACING_BUY_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_PLACING_BUY_ORDER", "name": "MSG_PLACING_BUY_ORDER", "type": "builtins.str"}}, "MSG_PLACING_SELL_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_PLACING_SELL_ORDER", "name": "MSG_PLACING_SELL_ORDER", "type": "builtins.str"}}, "MSG_POSITIONS_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_POSITIONS_REFRESHED", "name": "MSG_POSITIONS_REFRESHED", "type": "builtins.str"}}, "MSG_POSITION_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_POSITION_CLOSED", "name": "MSG_POSITION_CLOSED", "type": "builtins.str"}}, "MSG_REFRESHING_DATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REFRESHING_DATA", "name": "MSG_REFRESHING_DATA", "type": "builtins.str"}}, "MSG_REFRESHING_HISTORY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REFRESHING_HISTORY", "name": "MSG_REFRESHING_HISTORY", "type": "builtins.str"}}, "MSG_REFRESHING_MARKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REFRESHING_MARKET", "name": "MSG_REFRESHING_MARKET", "type": "builtins.str"}}, "MSG_REFRESHING_ORDERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REFRESHING_ORDERS", "name": "MSG_REFRESHING_ORDERS", "type": "builtins.str"}}, "MSG_REFRESHING_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REFRESHING_POSITIONS", "name": "MSG_REFRESHING_POSITIONS", "type": "builtins.str"}}, "MSG_REPORT_GENERATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_REPORT_GENERATED", "name": "MSG_REPORT_GENERATED", "type": "builtins.str"}}, "MSG_SELL_ORDER_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_SELL_ORDER_SUCCESS", "name": "MSG_SELL_ORDER_SUCCESS", "type": "builtins.str"}}, "MSG_TRADING_PAUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_TRADING_PAUSED", "name": "MSG_TRADING_PAUSED", "type": "builtins.str"}}, "MSG_TRADING_STARTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_TRADING_STARTED", "name": "MSG_TRADING_STARTED", "type": "builtins.str"}}, "MSG_TRADING_STOPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.MSG_TRADING_STOPPED", "name": "MSG_TRADING_STOPPED", "type": "builtins.str"}}, "ORDER_COLUMNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_COLUMNS", "name": "ORDER_COLUMNS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ORDER_SIDE_BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_SIDE_BUY", "name": "ORDER_SIDE_BUY", "type": "builtins.str"}}, "ORDER_SIDE_SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_SIDE_SELL", "name": "ORDER_SIDE_SELL", "type": "builtins.str"}}, "ORDER_STATUS_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_CANCELLED", "name": "ORDER_STATUS_CANCELLED", "type": "builtins.str"}}, "ORDER_STATUS_EXPIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_EXPIRED", "name": "ORDER_STATUS_EXPIRED", "type": "builtins.str"}}, "ORDER_STATUS_FILLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_FILLED", "name": "ORDER_STATUS_FILLED", "type": "builtins.str"}}, "ORDER_STATUS_PARTIAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_PARTIAL", "name": "ORDER_STATUS_PARTIAL", "type": "builtins.str"}}, "ORDER_STATUS_PENDING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_PENDING", "name": "ORDER_STATUS_PENDING", "type": "builtins.str"}}, "ORDER_STATUS_REJECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_STATUS_REJECTED", "name": "ORDER_STATUS_REJECTED", "type": "builtins.str"}}, "ORDER_TYPE_ICEBERG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_ICEBERG", "name": "ORDER_TYPE_ICEBERG", "type": "builtins.str"}}, "ORDER_TYPE_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_LABEL", "name": "ORDER_TYPE_LABEL", "type": "builtins.str"}}, "ORDER_TYPE_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_LIMIT", "name": "ORDER_TYPE_LIMIT", "type": "builtins.str"}}, "ORDER_TYPE_MARKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_MARKET", "name": "ORDER_TYPE_MARKET", "type": "builtins.str"}}, "ORDER_TYPE_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_STOP", "name": "ORDER_TYPE_STOP", "type": "builtins.str"}}, "ORDER_TYPE_STOP_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_STOP_LIMIT", "name": "ORDER_TYPE_STOP_LIMIT", "type": "builtins.str"}}, "ORDER_TYPE_TWA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.ORDER_TYPE_TWA", "name": "ORDER_TYPE_TWA", "type": "builtins.str"}}, "PAPER_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.PAPER_MODE", "name": "PAPER_MODE", "type": "builtins.str"}}, "PAUSE_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.PAUSE_TRADING", "name": "PAUSE_TRADING", "type": "builtins.str"}}, "PERCENTAGE_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.PERCENTAGE_FORMAT", "name": "PERCENTAGE_FORMAT", "type": "builtins.str"}}, "POSITION_COLUMNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.POSITION_COLUMNS", "name": "POSITION_COLUMNS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "POSITION_LONG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.POSITION_LONG", "name": "POSITION_LONG", "type": "builtins.str"}}, "POSITION_SHORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.POSITION_SHORT", "name": "POSITION_SHORT", "type": "builtins.str"}}, "PRICE_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.PRICE_FORMAT", "name": "PRICE_FORMAT", "type": "builtins.str"}}, "PRICE_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.PRICE_LABEL", "name": "PRICE_LABEL", "type": "builtins.str"}}, "QUANTITY_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.QUANTITY_FORMAT", "name": "QUANTITY_FORMAT", "type": "builtins.str"}}, "QUANTITY_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.QUANTITY_LABEL", "name": "QUANTITY_LABEL", "type": "builtins.str"}}, "QUICK_TRADING_PANEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.QUICK_TRADING_PANEL", "name": "QUICK_TRADING_PANEL", "type": "builtins.str"}}, "REFRESH_DATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.REFRESH_DATA", "name": "REFRESH_DATA", "type": "builtins.str"}}, "RISK_CORRELATION_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_CORRELATION_LIMIT", "name": "RISK_CORRELATION_LIMIT", "type": "builtins.str"}}, "RISK_MANAGEMENT_PANEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_MANAGEMENT_PANEL", "name": "RISK_MANAGEMENT_PANEL", "type": "builtins.str"}}, "RISK_MAX_DAILY_LOSS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_MAX_DAILY_LOSS", "name": "RISK_MAX_DAILY_LOSS", "type": "builtins.str"}}, "RISK_MAX_DRAWDOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_MAX_DRAWDOWN", "name": "RISK_MAX_DRAWDOWN", "type": "builtins.str"}}, "RISK_MAX_POSITION_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_MAX_POSITION_SIZE", "name": "RISK_MAX_POSITION_SIZE", "type": "builtins.str"}}, "RISK_STOP_LOSS_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_STOP_LOSS_RATIO", "name": "RISK_STOP_LOSS_RATIO", "type": "builtins.str"}}, "RISK_TAKE_PROFIT_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_TAKE_PROFIT_RATIO", "name": "RISK_TAKE_PROFIT_RATIO", "type": "builtins.str"}}, "RISK_WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.RISK_WARNING", "name": "RISK_WARNING", "type": "builtins.str"}}, "SELL_BUTTON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SELL_BUTTON", "name": "SELL_BUTTON", "type": "builtins.str"}}, "SETTINGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SETTINGS", "name": "SETTINGS", "type": "builtins.str"}}, "START_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.START_TRADING", "name": "START_TRADING", "type": "builtins.str"}}, "STATUS_CONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_CONNECTED", "name": "STATUS_CONNECTED", "type": "builtins.str"}}, "STATUS_DISCONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_DISCONNECTED", "name": "STATUS_DISCONNECTED", "type": "builtins.str"}}, "STATUS_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_EMERGENCY_STOP", "name": "STATUS_EMERGENCY_STOP", "type": "builtins.str"}}, "STATUS_PAUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_PAUSED", "name": "STATUS_PAUSED", "type": "builtins.str"}}, "STATUS_STOPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_STOPPED", "name": "STATUS_STOPPED", "type": "builtins.str"}}, "STATUS_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STATUS_TRADING", "name": "STATUS_TRADING", "type": "builtins.str"}}, "STOP_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.STOP_TRADING", "name": "STOP_TRADING", "type": "builtins.str"}}, "SUCCESS_CONNECTION_ESTABLISHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_CONNECTION_ESTABLISHED", "name": "SUCCESS_CONNECTION_ESTABLISHED", "type": "builtins.str"}}, "SUCCESS_DATA_EXPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_DATA_EXPORTED", "name": "SUCCESS_DATA_EXPORTED", "type": "builtins.str"}}, "SUCCESS_ORDER_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_ORDER_CANCELLED", "name": "SUCCESS_ORDER_CANCELLED", "type": "builtins.str"}}, "SUCCESS_ORDER_PLACED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_ORDER_PLACED", "name": "SUCCESS_ORDER_PLACED", "type": "builtins.str"}}, "SUCCESS_POSITION_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_POSITION_CLOSED", "name": "SUCCESS_POSITION_CLOSED", "type": "builtins.str"}}, "SUCCESS_SETTINGS_SAVED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SUCCESS_SETTINGS_SAVED", "name": "SUCCESS_SETTINGS_SAVED", "type": "builtins.str"}}, "SYMBOL_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.SYMBOL_LABEL", "name": "SYMBOL_LABEL", "type": "builtins.str"}}, "TAB_CHARTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_CHARTS", "name": "TAB_CHARTS", "type": "builtins.str"}}, "TAB_HISTORY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_HISTORY", "name": "TAB_HISTORY", "type": "builtins.str"}}, "TAB_MARKET_DATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_MARKET_DATA", "name": "TAB_MARKET_DATA", "type": "builtins.str"}}, "TAB_MONITOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_MONITOR", "name": "TAB_MONITOR", "type": "builtins.str"}}, "TAB_ORDERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_ORDERS", "name": "TAB_ORDERS", "type": "builtins.str"}}, "TAB_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TAB_POSITIONS", "name": "TAB_POSITIONS", "type": "builtins.str"}}, "TIME_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIME_FORMAT", "name": "TIME_FORMAT", "type": "builtins.str"}}, "TIME_FORMAT_SHORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIME_FORMAT_SHORT", "name": "TIME_FORMAT_SHORT", "type": "builtins.str"}}, "TIP_DOUBLE_CLICK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIP_DOUBLE_CLICK", "name": "TIP_DOUBLE_CLICK", "type": "builtins.str"}}, "TIP_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIP_EMERGENCY_STOP", "name": "TIP_EMERGENCY_STOP", "type": "builtins.str"}}, "TIP_LIVE_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIP_LIVE_MODE", "name": "TIP_LIVE_MODE", "type": "builtins.str"}}, "TIP_PAPER_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIP_PAPER_MODE", "name": "TIP_PAPER_MODE", "type": "builtins.str"}}, "TIP_RIGHT_CLICK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TIP_RIGHT_CLICK", "name": "TIP_RIGHT_CLICK", "type": "builtins.str"}}, "TRADING_CONTROL_CENTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.TRADING_CONTROL_CENTER", "name": "TRADING_CONTROL_CENTER", "type": "builtins.str"}}, "WARNING_HIGH_RISK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.WARNING_HIGH_RISK", "name": "WARNING_HIGH_RISK", "type": "builtins.str"}}, "WARNING_LARGE_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.WARNING_LARGE_ORDER", "name": "WARNING_LARGE_ORDER", "type": "builtins.str"}}, "WARNING_LOW_BALANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.WARNING_LOW_BALANCE", "name": "WARNING_LOW_BALANCE", "type": "builtins.str"}}, "WARNING_MARKET_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.WARNING_MARKET_CLOSED", "name": "WARNING_MARKET_CLOSED", "type": "builtins.str"}}, "format_currency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_currency", "name": "format_currency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "amount"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_currency of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_currency", "name": "format_currency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "amount"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_currency of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_percentage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_percentage", "name": "format_percentage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_percentage of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_percentage", "name": "format_percentage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_percentage of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_price": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "price"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_price", "name": "format_price", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "price"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_price of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_price", "name": "format_price", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "price"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_price of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_quantity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "quantity"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_quantity", "name": "format_quantity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "quantity"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_quantity of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.format_quantity", "name": "format_quantity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "quantity"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_quantity of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_order_side_chinese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "english_side"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_side_chinese", "name": "get_order_side_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_side"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_side_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_side_chinese", "name": "get_order_side_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_side"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_side_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_order_status_chinese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "english_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_status_chinese", "name": "get_order_status_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_status"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_status_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_status_chinese", "name": "get_order_status_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_status"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_status_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_order_type_chinese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "english_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_type_chinese", "name": "get_order_type_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_type"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_type_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "constants.chinese_ui_constants.ChineseUIConstants.get_order_type_chinese", "name": "get_order_type_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "english_type"], "arg_types": [{".class": "TypeType", "item": "constants.chinese_ui_constants.ChineseUIConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order_type_chinese of ChineseUIConstants", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "constants.chinese_ui_constants.ChineseUIConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "constants.chinese_ui_constants.ChineseUIConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "constants.chinese_ui_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\chinese_ui_constants.py"}
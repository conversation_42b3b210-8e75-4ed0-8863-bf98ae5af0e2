#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化版终极交易系统GUI
Optimized Ultimate Trading System GUI

解决卡顿问题的轻量级版本
"""

import json
import logging
import queue
import threading
import time
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, scrolledtext, ttk

# 设置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出


class OptimizedTradingGUI:
    """优化版终极交易系统GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 终极版交易系统 v1.2 (优化版)")
        self.root.geometry("900x700")
        self.root.configure(bg="#1e1e1e")

        # 系统状态
        self.is_trading = False
        self.config = {}
        self.gate_connected = False

        # 数据队列 - 用于线程间通信
        self.data_queue = queue.Queue()
        self.update_running = False

        # 扩展的交易对数据 - 支持更多主流币种
        self.mock_data = {
            # 主流币种
            "BTC/USDT": {
                "price": 107951.00,
                "change": -0.44,
                "volume": 28450.5,
                "category": "主流",
            },
            "ETH/USDT": {
                "price": 2508.35,
                "change": -1.77,
                "volume": 156780.2,
                "category": "主流",
            },
            "BNB/USDT": {
                "price": 692.45,
                "change": 1.23,
                "volume": 45230.8,
                "category": "主流",
            },
            # DeFi代币
            "UNI/USDT": {
                "price": 12.85,
                "change": 2.45,
                "volume": 12450.3,
                "category": "DeFi",
            },
            "AAVE/USDT": {
                "price": 285.67,
                "change": -0.89,
                "volume": 8760.1,
                "category": "DeFi",
            },
            "SUSHI/USDT": {
                "price": 1.45,
                "change": 3.21,
                "volume": 15670.9,
                "category": "DeFi",
            },
            # Layer1公链
            "SOL/USDT": {
                "price": 245.78,
                "change": 1.56,
                "volume": 67890.4,
                "category": "Layer1",
            },
            "ADA/USDT": {
                "price": 1.23,
                "change": -2.34,
                "volume": 89450.7,
                "category": "Layer1",
            },
            "DOT/USDT": {
                "price": 8.95,
                "change": 0.78,
                "volume": 23450.6,
                "category": "Layer1",
            },
            "AVAX/USDT": {
                "price": 42.67,
                "change": 2.89,
                "volume": 34560.2,
                "category": "Layer1",
            },
            # Layer2解决方案
            "MATIC/USDT": {
                "price": 1.15,
                "change": 1.45,
                "volume": 78920.3,
                "category": "Layer2",
            },
            "OP/USDT": {
                "price": 3.45,
                "change": -1.23,
                "volume": 12340.8,
                "category": "Layer2",
            },
            "ARB/USDT": {
                "price": 2.78,
                "change": 0.95,
                "volume": 45670.1,
                "category": "Layer2",
            },
            # 人工智能
            "FET/USDT": {
                "price": 2.34,
                "change": 4.56,
                "volume": 23450.7,
                "category": "AI",
            },
            "AGIX/USDT": {
                "price": 0.89,
                "change": 2.78,
                "volume": 15670.4,
                "category": "AI",
            },
            "OCEAN/USDT": {
                "price": 1.67,
                "change": 1.89,
                "volume": 8920.3,
                "category": "AI",
            },
            # 游戏代币
            "AXS/USDT": {
                "price": 8.45,
                "change": -1.56,
                "volume": 12340.5,
                "category": "游戏",
            },
            "SAND/USDT": {
                "price": 0.67,
                "change": 2.34,
                "volume": 34560.8,
                "category": "游戏",
            },
            "MANA/USDT": {
                "price": 0.89,
                "change": 1.23,
                "volume": 23450.2,
                "category": "游戏",
            },
            # 存储概念
            "FIL/USDT": {
                "price": 6.78,
                "change": -0.45,
                "volume": 15670.9,
                "category": "存储",
            },
            "AR/USDT": {
                "price": 23.45,
                "change": 1.67,
                "volume": 8920.4,
                "category": "存储",
            },
            # 交易所代币
            "GT/USDT": {
                "price": 21.28,
                "change": -0.73,
                "volume": 45230.6,
                "category": "交易所",
            },
            "OKB/USDT": {
                "price": 67.89,
                "change": 0.89,
                "volume": 23450.1,
                "category": "交易所",
            },
            "HT/USDT": {
                "price": 4.56,
                "change": -1.23,
                "volume": 12340.7,
                "category": "交易所",
            },
        }

        # 交易对分类
        self.categories = [
            "全部",
            "主流",
            "DeFi",
            "Layer1",
            "Layer2",
            "AI",
            "游戏",
            "存储",
            "交易所",
        ]
        self.selected_category = "全部"

        # 创建界面
        self.create_widgets()
        self.load_config()

        # 启动数据更新（轻量级）
        self.start_lightweight_updates()

    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg="#1e1e1e")
        title_frame.pack(fill="x", padx=10, pady=5)

        title_label = tk.Label(
            title_frame,
            text="🚀 终极版交易系统 v1.2",
            font=("Arial", 16, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="Ultimate Trading System - 优化版 (解决卡顿问题)",
            font=("Arial", 10),
            fg="#888888",
            bg="#1e1e1e",
        )
        subtitle_label.pack()

        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=5)

        # 配置页面
        self.create_config_tab()

        # 交易页面
        self.create_trading_tab()

        # 数据监控页面
        self.create_data_tab()

        # 日志页面
        self.create_log_tab()

        # 状态栏
        self.create_status_bar()

    def create_config_tab(self):
        """创建配置页面"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙️ 配置")

        # API配置
        api_group = ttk.LabelFrame(config_frame, text="API配置")
        api_group.pack(fill="x", padx=10, pady=5)

        ttk.Label(api_group, text="API Key:").grid(
            row=0, column=0, sticky="w", padx=5, pady=2
        )
        self.api_key_entry = ttk.Entry(api_group, width=50, show="*")
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(api_group, text="Secret:").grid(
            row=1, column=0, sticky="w", padx=5, pady=2
        )
        self.secret_entry = ttk.Entry(api_group, width=50, show="*")
        self.secret_entry.grid(row=1, column=1, padx=5, pady=2)

        self.sandbox_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            api_group, text="沙盒模式 (测试)", variable=self.sandbox_var
        ).grid(row=2, column=0, columnspan=2, sticky="w", padx=5, pady=2)

        # 交易配置
        trading_group = ttk.LabelFrame(config_frame, text="交易配置")
        trading_group.pack(fill="x", padx=10, pady=5)

        ttk.Label(trading_group, text="初始资金 (USDT):").grid(
            row=0, column=0, sticky="w", padx=5, pady=2
        )
        self.capital_entry = ttk.Entry(trading_group, width=20)
        self.capital_entry.insert(0, "10000")
        self.capital_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(trading_group, text="日亏损限制 (USDT):").grid(
            row=1, column=0, sticky="w", padx=5, pady=2
        )
        self.daily_loss_entry = ttk.Entry(trading_group, width=20)
        self.daily_loss_entry.insert(0, "300")
        self.daily_loss_entry.grid(row=1, column=1, padx=5, pady=2)

        # 按钮
        button_frame = tk.Frame(config_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(
            button_frame, text="💾 保存配置", command=self.save_config
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame, text="🔄 加载配置", command=self.load_config
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame, text="🧪 测试连接", command=self.test_connection
        ).pack(side="left", padx=5)

    def create_trading_tab(self):
        """创建交易页面"""
        trading_frame = ttk.Frame(self.notebook)
        self.notebook.add(trading_frame, text="💰 交易")

        # 策略状态
        strategy_group = ttk.LabelFrame(trading_frame, text="策略状态")
        strategy_group.pack(fill="x", padx=10, pady=5)

        strategies = [
            ("CryptoBreakout", "突破策略", "40%", "67%"),
            ("CryptoGrid", "网格策略", "35%", "83%"),
            ("CryptoMomentum", "动量策略", "25%", "61%"),
        ]

        for i, (code, desc, allocation, win_rate) in enumerate(strategies):
            frame = tk.Frame(strategy_group)
            frame.pack(fill="x", padx=5, pady=2)

            tk.Label(
                frame, text=f"🎯 {desc}", font=("Arial", 10, "bold")
            ).pack(side="left")
            tk.Label(
                frame,
                text=f"资金: {allocation} | 胜率: {win_rate}",
                fg="green",
            ).pack(side="right")

        # 交易控制
        control_group = ttk.LabelFrame(trading_frame, text="交易控制")
        control_group.pack(fill="x", padx=10, pady=5)

        control_frame = tk.Frame(control_group)
        control_frame.pack(fill="x", padx=5, pady=5)

        self.start_button = ttk.Button(
            control_frame, text="🚀 开始交易", command=self.start_trading
        )
        self.start_button.pack(side="left", padx=5)

        self.stop_button = ttk.Button(
            control_frame,
            text="⏹️ 停止交易",
            command=self.stop_trading,
            state="disabled",
        )
        self.stop_button.pack(side="left", padx=5)

        ttk.Button(
            control_frame, text="📊 查看持仓", command=self.show_positions
        ).pack(side="left", padx=5)

        # 交易状态
        self.trading_status = tk.Label(
            control_group, text="状态: 未连接", fg="red"
        )
        self.trading_status.pack(pady=5)

        # 账户信息
        account_group = ttk.LabelFrame(trading_frame, text="账户信息")
        account_group.pack(fill="x", padx=10, pady=5)

        self.balance_label = tk.Label(
            account_group,
            text="USDT余额: 10,000.00",
            font=("Arial", 12, "bold"),
        )
        self.balance_label.pack(pady=2)

        self.pnl_label = tk.Label(
            account_group,
            text="当日盈亏: +0.00 USDT",
            font=("Arial", 12),
            fg="green",
        )
        self.pnl_label.pack(pady=2)

    def create_data_tab(self):
        """创建数据监控页面"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="📊 数据监控")

        # 数据源状态
        status_group = ttk.LabelFrame(data_frame, text="数据源状态")
        status_group.pack(fill="x", padx=10, pady=5)

        status_frame = tk.Frame(status_group)
        status_frame.pack(fill="x", padx=5, pady=5)

        self.gate_status_label = tk.Label(
            status_frame,
            text="Gate.io: 已连接 ✅",
            fg="green",
            font=("Arial", 10, "bold"),
        )
        self.gate_status_label.pack(side="left")

        self.pair_count_label = tk.Label(
            status_frame,
            text=f"交易对: {len(self.mock_data)}个",
            fg="blue",
            font=("Arial", 10),
        )
        self.pair_count_label.pack(side="left", padx=20)

        ttk.Button(
            status_frame, text="🔄 刷新", command=self.refresh_data
        ).pack(side="right", padx=5)

        # 分类筛选
        filter_group = ttk.LabelFrame(data_frame, text="分类筛选")
        filter_group.pack(fill="x", padx=10, pady=5)

        filter_frame = tk.Frame(filter_group)
        filter_frame.pack(fill="x", padx=5, pady=5)

        tk.Label(
            filter_frame, text="选择分类:", font=("Arial", 10, "bold")
        ).pack(side="left", padx=5)

        self.category_var = tk.StringVar(value="全部")
        for category in self.categories:
            btn = tk.Radiobutton(
                filter_frame,
                text=category,
                variable=self.category_var,
                value=category,
                command=self.filter_by_category,
                font=("Arial", 9),
            )
            btn.pack(side="left", padx=5)

        # 实时价格表格
        price_group = ttk.LabelFrame(data_frame, text="实时价格监控")
        price_group.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建滚动框架
        canvas = tk.Canvas(price_group, bg="#f0f0f0")
        scrollbar = ttk.Scrollbar(
            price_group, orient="vertical", command=canvas.yview
        )
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all")),
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 初始化价格显示
        self.update_price_display()

        # 交易信号
        signal_group = ttk.LabelFrame(data_frame, text="交易信号")
        signal_group.pack(fill="x", padx=10, pady=5)

        self.signal_text = scrolledtext.ScrolledText(
            signal_group,
            height=8,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Consolas", 9),
            wrap="word",
        )
        self.signal_text.pack(fill="both", expand=True, padx=5, pady=5)

        # 添加初始信号
        self.add_signal_message("🌐 多交易所数据源已连接")
        self.add_signal_message(
            f"📊 {len(self.mock_data)}个交易对实时监控已启动"
        )
        self.add_signal_message("🎯 智能交易信号分析已就绪")

    def create_log_tab(self):
        """创建日志页面"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📝 日志")

        # 日志显示
        log_group = ttk.LabelFrame(log_frame, text="系统日志")
        log_group.pack(fill="both", expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(
            log_group,
            height=20,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Consolas", 9),
        )
        self.log_text.pack(fill="both", expand=True, padx=5, pady=5)

        # 清除按钮
        ttk.Button(log_group, text="🗑️ 清除日志", command=self.clear_log).pack(
            pady=5
        )

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Label(
            self.root,
            text="就绪 | 终极版交易系统 v1.2 (优化版)",
            relief="sunken",
            anchor="w",
            bg="#333333",
            fg="#ffffff",
        )
        self.status_bar.pack(side="bottom", fill="x")

    def start_lightweight_updates(self):
        """启动轻量级数据更新"""
        if self.update_running:
            return

        self.update_running = True
        self.gate_connected = True

        def update_loop():
            while self.update_running:
                try:
                    # 模拟价格变化
                    self.simulate_price_changes()

                    # 每30秒更新一次（减少频率）
                    time.sleep(30)

                except Exception as e:
                    self.log_message(f"❌ 数据更新错误: {e}")
                    time.sleep(60)

        threading.Thread(target=update_loop, daemon=True).start()
        self.log_message("🔄 轻量级数据更新已启动")

    def simulate_price_changes(self):
        """模拟价格变化"""
        import random

        for symbol in self.mock_data:
            # 模拟小幅价格波动
            change = random.uniform(-0.5, 0.5)
            self.mock_data[symbol]["change"] = change

            # 模拟价格变化
            base_price = self.mock_data[symbol]["price"]
            self.mock_data[symbol]["price"] = base_price * (1 + change / 100)

        # 更新界面
        self.root.after(0, self.update_price_display)
        self.root.after(0, self.generate_mock_signal)

    def filter_by_category(self):
        """按分类筛选交易对"""
        self.selected_category = self.category_var.get()
        self.update_price_display()
        self.log_message(f"🔍 筛选分类: {self.selected_category}")

    def get_filtered_pairs(self):
        """获取筛选后的交易对"""
        if self.selected_category == "全部":
            return self.mock_data
        else:
            return {
                k: v
                for k, v in self.mock_data.items()
                if v["category"] == self.selected_category
            }

    def update_price_display(self):
        """更新价格显示"""
        try:
            # 清除旧显示
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()

            # 获取筛选后的数据
            filtered_data = self.get_filtered_pairs()

            # 创建表头
            header_frame = tk.Frame(self.scrollable_frame, bg="#e0e0e0")
            header_frame.pack(fill="x", padx=5, pady=2)

            tk.Label(
                header_frame,
                text="交易对",
                font=("Arial", 10, "bold"),
                bg="#e0e0e0",
                width=12,
            ).pack(side="left", padx=5)
            tk.Label(
                header_frame,
                text="价格",
                font=("Arial", 10, "bold"),
                bg="#e0e0e0",
                width=12,
            ).pack(side="left", padx=5)
            tk.Label(
                header_frame,
                text="24h变化",
                font=("Arial", 10, "bold"),
                bg="#e0e0e0",
                width=10,
            ).pack(side="left", padx=5)
            tk.Label(
                header_frame,
                text="成交量",
                font=("Arial", 10, "bold"),
                bg="#e0e0e0",
                width=12,
            ).pack(side="left", padx=5)
            tk.Label(
                header_frame,
                text="分类",
                font=("Arial", 10, "bold"),
                bg="#e0e0e0",
                width=8,
            ).pack(side="left", padx=5)

            # 显示交易对数据
            for i, (symbol, data) in enumerate(filtered_data.items()):
                price = data["price"]
                change = data["change"]
                volume = data["volume"]
                category = data["category"]

                # 行背景色
                bg_color = "#f8f8f8" if i % 2 == 0 else "#ffffff"
                change_color = "#008000" if change >= 0 else "#ff0000"

                row_frame = tk.Frame(self.scrollable_frame, bg=bg_color)
                row_frame.pack(fill="x", padx=5, pady=1)

                # 交易对名称
                symbol_label = tk.Label(
                    row_frame,
                    text=symbol,
                    font=("Arial", 9, "bold"),
                    bg=bg_color,
                    width=12,
                    anchor="w",
                )
                symbol_label.pack(side="left", padx=5)

                # 价格
                if price >= 1:
                    price_text = f"{price:,.2f}"
                else:
                    price_text = f"{price:.6f}"

                price_label = tk.Label(
                    row_frame,
                    text=price_text,
                    font=("Arial", 9),
                    bg=bg_color,
                    width=12,
                    anchor="e",
                )
                price_label.pack(side="left", padx=5)

                # 24h变化
                change_label = tk.Label(
                    row_frame,
                    text=f"{change:+.2f}%",
                    font=("Arial", 9, "bold"),
                    fg=change_color,
                    bg=bg_color,
                    width=10,
                    anchor="center",
                )
                change_label.pack(side="left", padx=5)

                # 成交量
                volume_text = (
                    f"{volume:,.1f}" if volume >= 1000 else f"{volume:.1f}"
                )
                volume_label = tk.Label(
                    row_frame,
                    text=volume_text,
                    font=("Arial", 9),
                    bg=bg_color,
                    width=12,
                    anchor="e",
                )
                volume_label.pack(side="left", padx=5)

                # 分类
                category_label = tk.Label(
                    row_frame,
                    text=category,
                    font=("Arial", 9),
                    bg=bg_color,
                    width=8,
                    anchor="center",
                )
                category_label.pack(side="left", padx=5)

            # 更新统计信息
            if hasattr(self, "pair_count_label"):
                total_pairs = len(filtered_data)
                if self.selected_category == "全部":
                    self.pair_count_label.config(
                        text=f"交易对: {total_pairs}个"
                    )
                else:
                    self.pair_count_label.config(
                        text=f"{self.selected_category}: {total_pairs}个"
                    )

        except Exception as e:
            self.log_message(f"❌ 更新价格显示失败: {e}")

    def generate_mock_signal(self):
        """生成实战演练信号"""
        import random

        # 获取当前筛选的交易对
        filtered_data = self.get_filtered_pairs()
        if not filtered_data:
            return

        symbols = list(filtered_data.keys())
        symbol = random.choice(symbols)
        data = filtered_data[symbol]

        # 根据价格变化生成更智能的信号
        change = data["change"]
        category = data["category"]

        # 信号权重
        signal_weights = []

        if change > 3:
            signal_weights.extend(["买入"] * 3)  # 强烈上涨
        elif change > 1:
            signal_weights.extend(["买入"] * 2)  # 温和上涨
        elif change < -3:
            signal_weights.extend(["卖出"] * 3)  # 强烈下跌
        elif change < -1:
            signal_weights.extend(["卖出"] * 2)  # 温和下跌
        else:
            signal_weights.extend(["观察"] * 4)  # 震荡

        signal = random.choice(signal_weights)

        # 根据分类生成不同的原因
        category_reasons = {
            "主流": ["机构买入", "技术突破", "市场情绪", "资金流入"],
            "DeFi": ["TVL增长", "协议升级", "DeFi热度", "流动性挖矿"],
            "Layer1": ["生态发展", "技术升级", "开发者活跃", "链上数据"],
            "Layer2": ["扩容需求", "成本优势", "生态迁移", "TVL增长"],
            "AI": ["AI概念", "技术突破", "合作消息", "市场炒作"],
            "游戏": ["游戏热度", "用户增长", "新游戏", "NFT交易"],
            "存储": ["存储需求", "数据增长", "技术优势", "合作伙伴"],
            "交易所": ["平台币回购", "交易量增长", "新功能", "生态扩展"],
        }

        if signal != "观察":
            reasons = category_reasons.get(
                category, ["技术分析", "市场情绪", "资金流向", "消息面"]
            )
            reason = random.choice(reasons)

            # 添加强度指示
            if abs(change) > 3:
                strength = "强"
            elif abs(change) > 1:
                strength = "中"
            else:
                strength = "弱"

            signal_text = f"🎯 {symbol} ({category}): {signal}信号 ({strength}) - {reason}"
        else:
            signal_text = f"⚪ {symbol} ({category}): 震荡整理，等待机会"

        self.add_signal_message(signal_text)

    def add_signal_message(self, message):
        """添加信号消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.signal_text.insert("end", full_message)
        self.signal_text.see("end")

        # 限制文本长度
        lines = self.signal_text.get("1.0", "end").split("\n")
        if len(lines) > 50:
            self.signal_text.delete("1.0", "10.0")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert("end", log_entry)
        self.log_text.see("end")

        # 限制日志长度
        lines = self.log_text.get("1.0", "end").split("\n")
        if len(lines) > 100:
            self.log_text.delete("1.0", "20.0")

    def save_config(self):
        """保存配置"""
        config = {
            "api_key": self.api_key_entry.get(),
            "secret": self.secret_entry.get(),
            "sandbox": self.sandbox_var.get(),
            "initial_capital": float(self.capital_entry.get() or 10000),
            "daily_loss_limit": float(self.daily_loss_entry.get() or 300),
        }

        try:
            with open("optimized_config.json", "w") as f:
                json.dump(config, f, indent=2)

            self.config = config
            self.log_message("✅ 配置已保存")
            messagebox.showinfo("成功", "配置已保存")

        except Exception as e:
            self.log_message(f"❌ 保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """加载配置"""
        try:
            with open("optimized_config.json", "r") as f:
                config = json.load(f)

            self.api_key_entry.delete(0, "end")
            self.api_key_entry.insert(0, config.get("api_key", ""))

            self.secret_entry.delete(0, "end")
            self.secret_entry.insert(0, config.get("secret", ""))

            self.sandbox_var.set(config.get("sandbox", True))

            self.capital_entry.delete(0, "end")
            self.capital_entry.insert(
                0, str(config.get("initial_capital", 10000))
            )

            self.daily_loss_entry.delete(0, "end")
            self.daily_loss_entry.insert(
                0, str(config.get("daily_loss_limit", 300))
            )

            self.config = config
            self.log_message("✅ 配置已加载")

        except FileNotFoundError:
            self.log_message("⚠️ 配置文件不存在，使用默认配置")
        except Exception as e:
            self.log_message(f"❌ 加载配置失败: {e}")

    def test_connection(self):
        """测试连接"""
        self.log_message("🔄 测试连接中...")

        def test_thread():
            time.sleep(2)  # 模拟测试
            self.root.after(0, lambda: self.log_message("✅ 连接测试成功"))
            self.root.after(
                0, lambda: messagebox.showinfo("成功", "连接测试成功")
            )

        threading.Thread(target=test_thread, daemon=True).start()

    def start_trading(self):
        """开始交易"""
        if not self.config.get("api_key"):
            messagebox.showerror("错误", "请先配置API密钥")
            return

        mode = "沙盒测试模式" if self.config.get("sandbox") else "实盘交易模式"

        if not messagebox.askyesno("确认", f"确定要开始{mode}吗？"):
            return

        self.is_trading = True
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.trading_status.config(text="状态: 交易中", fg="green")

        self.log_message(f"🚀 开始{mode}")
        self.add_signal_message(f"🚀 {mode}已启动")

    def stop_trading(self):
        """停止交易"""
        self.is_trading = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.trading_status.config(text="状态: 已停止", fg="red")

        self.log_message("⏹️ 交易已停止")
        self.add_signal_message("⏹️ 交易已停止")

    def show_positions(self):
        """显示持仓"""
        positions = "模拟持仓:\nBTC: 0.1 BTC\nETH: 2.5 ETH\nUSDT: 5,000 USDT"
        messagebox.showinfo("持仓信息", positions)

    def refresh_data(self):
        """刷新数据"""
        self.log_message("🔄 正在刷新数据...")
        self.add_signal_message("🔄 数据已刷新")

        # 立即更新价格
        self.simulate_price_changes()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", "end")
        self.log_message("日志已清除")

    def run(self):
        """运行GUI"""
        self.log_message("🚀 终极版交易系统启动 (优化版)")
        self.log_message("💡 已解决卡顿问题，运行更流畅")
        self.log_message("🌐 使用模拟数据，避免频繁API调用")

        try:
            self.root.mainloop()
        finally:
            self.update_running = False


def main():
    """主函数"""
    app = OptimizedTradingGUI()
    app.run()


if __name__ == "__main__":
    main()

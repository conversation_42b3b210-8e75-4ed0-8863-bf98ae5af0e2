#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安装依赖包
Install Requirements

自动安装交易系统所需的Python包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🔧 安装交易系统依赖包")
    print("=" * 40)
    
    # 必需的包列表
    required_packages = [
        "ccxt",           # 交易所API
        "pandas",         # 数据处理
        "numpy",          # 数值计算
        "talib-binary",   # 技术指标 (二进制版本，更容易安装)
        "requests",       # HTTP请求
        "websocket-client", # WebSocket连接
    ]
    
    # 检查和安装包
    failed_packages = []
    
    for package in required_packages:
        print(f"\n检查 {package}...")
        
        # 特殊处理talib
        check_name = "talib" if package == "talib-binary" else package
        
        if check_package(check_name):
            print(f"✅ {package} 已安装")
        else:
            print(f"📦 正在安装 {package}...")
            if not install_package(package):
                failed_packages.append(package)
    
    # 安装结果
    print("\n" + "=" * 40)
    if failed_packages:
        print("❌ 以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        
        print("\n💡 解决方案:")
        print("1. 检查网络连接")
        print("2. 升级pip: python -m pip install --upgrade pip")
        print("3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        
        if "talib-binary" in failed_packages:
            print("4. TA-Lib安装问题:")
            print("   - Windows: pip install TA-Lib")
            print("   - Linux: sudo apt-get install ta-lib")
            print("   - macOS: brew install ta-lib")
    else:
        print("✅ 所有依赖包安装成功!")
        print("\n🎉 现在可以运行交易系统了:")
        print("python start_trading.py")

if __name__ == "__main__":
    main()

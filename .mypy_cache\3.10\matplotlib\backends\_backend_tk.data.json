{".class": "MypyFile", "_fullname": "matplotlib.backends._backend_tk", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CloseEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.CloseEvent", "kind": "Gdef"}, "ConfigureSubplotsTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ConfigureSubplotsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.ConfigureSubplotsTk", "name": "ConfigureSubplotsTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ConfigureSubplotsTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.ConfigureSubplotsTk", "matplotlib.backend_tools.ConfigureSubplotsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ConfigureSubplotsTk.trigger", "name": "trigger", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.ConfigureSubplotsTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.ConfigureSubplotsTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FigureCanvasBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureCanvasBase", "kind": "Gdef"}, "FigureCanvasTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.FigureCanvasBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk", "name": "FigureCanvasTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.FigureCanvasTk", "matplotlib.backend_bases.FigureCanvasBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "figure", "master"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.__init__", "name": "__init__", "type": null}}, "_event_loop_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._event_loop_id", "name": "_event_loop_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_event_mpl_coords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._event_mpl_coords", "name": "_event_mpl_coords", "type": null}}, "_get_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._get_key", "name": "_get_key", "type": null}}, "_idle_draw_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._idle_draw_id", "name": "_idle_draw_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_mpl_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._mpl_buttons", "name": "_mpl_buttons", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._mpl_buttons", "name": "_mpl_buttons", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mpl_buttons of FigureCanvasTk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_mpl_modifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["event", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._mpl_modifiers", "name": "_mpl_modifiers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._mpl_modifiers", "name": "_mpl_modifiers", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["event", "exclude"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mpl_modifiers of FigureCanvasTk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_rubberband_rect_black": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._rubberband_rect_black", "name": "_rubberband_rect_black", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_rubberband_rect_white": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._rubberband_rect_white", "name": "_rubberband_rect_white", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tkcanvas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._tkcanvas", "name": "_tk<PERSON>vas", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tkcanvas_image_region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._tkcanvas_image_region", "name": "_tkcanvas_image_region", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tkphoto": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._tkphoto", "name": "_tkphoto", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_update_device_pixel_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk._update_device_pixel_ratio", "name": "_update_device_pixel_ratio", "type": null}}, "button_dblclick_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.button_dblclick_event", "name": "button_dblclick_event", "type": null}}, "button_press_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "event", "dblclick"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.button_press_event", "name": "button_press_event", "type": null}}, "button_release_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.button_release_event", "name": "button_release_event", "type": null}}, "draw_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.draw_idle", "name": "draw_idle", "type": null}}, "enter_notify_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.enter_notify_event", "name": "enter_notify_event", "type": null}}, "flush_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.flush_events", "name": "flush_events", "type": null}}, "get_tk_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.get_tk_widget", "name": "get_tk_widget", "type": null}}, "key_press": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.key_press", "name": "key_press", "type": null}}, "key_release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.key_release", "name": "key_release", "type": null}}, "leave_notify_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.leave_notify_event", "name": "leave_notify_event", "type": null}}, "manager_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.manager_class", "name": "manager_class", "type": "matplotlib._api.classproperty"}}, "motion_notify_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.motion_notify_event", "name": "motion_notify_event", "type": null}}, "new_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.new_timer", "name": "new_timer", "type": null}}, "required_interactive_framework": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.required_interactive_framework", "name": "required_interactive_framework", "type": "builtins.str"}}, "resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.resize", "name": "resize", "type": null}}, "scroll_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.scroll_event", "name": "scroll_event", "type": null}}, "scroll_event_windows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.scroll_event_windows", "name": "scroll_event_windows", "type": null}}, "set_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.set_cursor", "name": "set_cursor", "type": null}}, "start_event_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.start_event_loop", "name": "start_event_loop", "type": null}}, "stop_event_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.stop_event_loop", "name": "stop_event_loop", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.FigureCanvasTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.FigureCanvasTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FigureManagerBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureManagerBase", "kind": "Gdef"}, "FigureManagerTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.FigureManagerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.FigureManagerTk", "name": "FigureManagerTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.FigureManagerTk", "matplotlib.backend_bases.FigureManagerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "canvas", "num", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.__init__", "name": "__init__", "type": null}}, "_owns_mainloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk._owns_mainloop", "name": "_owns_mainloop", "type": "builtins.bool"}}, "_shown": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk._shown", "name": "_shown", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_update_window_dpi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk._update_window_dpi", "name": "_update_window_dpi", "type": null}}, "_window_dpi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk._window_dpi", "name": "_window_dpi", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_window_dpi_cbname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk._window_dpi_cbname", "name": "_window_dpi_cbname", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_with_canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "canvas_class", "figure", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.create_with_canvas", "name": "create_with_canvas", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.create_with_canvas", "name": "create_with_canvas", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "canvas_class", "figure", "num"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backends._backend_tk.FigureManagerTk"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_with_canvas of FigureManagerTk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "destroy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.destroy", "name": "destroy", "type": null}}, "full_screen_toggle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.full_screen_toggle", "name": "full_screen_toggle", "type": null}}, "get_window_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.get_window_title", "name": "get_window_title", "type": null}}, "resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.resize", "name": "resize", "type": null}}, "set_window_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.set_window_title", "name": "set_window_title", "type": null}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.show", "name": "show", "type": null}}, "start_main_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.start_main_loop", "name": "start_main_loop", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.start_main_loop", "name": "start_main_loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.backends._backend_tk.FigureManagerTk"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_main_loop of FigureManagerTk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.window", "name": "window", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.FigureManagerTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.FigureManagerTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gcf": {".class": "SymbolTableNode", "cross_ref": "matplotlib._pylab_helpers.Gcf", "kind": "Gdef"}, "HelpTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolHelpBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.HelpTk", "name": "HelpTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.HelpTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.HelpTk", "matplotlib.backend_tools.ToolHelpBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.HelpTk.trigger", "name": "trigger", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.HelpTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.HelpTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageTk": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageTk", "kind": "Gdef"}, "KeyEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.KeyEvent", "kind": "Gdef"}, "LocationEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.LocationEvent", "kind": "Gdef"}, "MouseButton": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.MouseButton", "kind": "Gdef"}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.MouseEvent", "kind": "Gdef"}, "NavigationToolbar2": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.NavigationToolbar2", "kind": "Gdef"}, "NavigationToolbar2Tk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.NavigationToolbar2", "tkinter.Frame"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk", "name": "NavigationToolbar2Tk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.NavigationToolbar2Tk", "matplotlib.backend_bases.NavigationToolbar2", "tkinter.Frame", "tkinter.Widget", "tkinter.BaseWidget", "tkinter.Misc", "tkinter.Pack", "tkinter.Place", "tkinter.Grid", "builtins.object"], "names": {".class": "SymbolTable", "_Button": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "text", "image_file", "toggle", "command"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._But<PERSON>", "name": "_<PERSON><PERSON>", "type": null}}, "_Spacer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._Spacer", "name": "_Spacer", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "canvas", "window", "pack_toolbar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.__init__", "name": "__init__", "type": null}}, "_buttons": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._buttons", "name": "_buttons", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_label_font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._label_font", "name": "_label_font", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_message_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._message_label", "name": "_message_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._rescale", "name": "_rescale", "type": null}}, "_set_image_for_button": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._set_image_for_button", "name": "_set_image_for_button", "type": null}}, "_update_buttons_checked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk._update_buttons_checked", "name": "_update_buttons_checked", "type": null}}, "draw_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "event", "x0", "y0", "x1", "y1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.draw_rubberband", "name": "draw_rubberband", "type": null}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.message", "name": "message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.pan", "name": "pan", "type": null}}, "remove_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.remove_rubberband", "name": "remove_rubberband", "type": null}}, "save_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.save_figure", "name": "save_figure", "type": null}}, "set_history_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.set_history_buttons", "name": "set_history_buttons", "type": null}}, "set_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.set_message", "name": "set_message", "type": null}}, "zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.zoom", "name": "zoom", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.NavigationToolbar2Tk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.NavigationToolbar2Tk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResizeEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.ResizeEvent", "kind": "Gdef"}, "RubberbandTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.RubberbandBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.RubberbandTk", "name": "RubberbandTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.RubberbandTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.RubberbandTk", "matplotlib.backend_tools.RubberbandBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "draw_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "x1", "y1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.RubberbandTk.draw_rubberband", "name": "draw_rubberband", "type": null}}, "remove_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.RubberbandTk.remove_rubberband", "name": "remove_rubberband", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.RubberbandTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.RubberbandTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaveFigureTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.SaveFigureBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.SaveFigureTk", "name": "SaveFigureTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.SaveFigureTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.SaveFigureTk", "matplotlib.backend_tools.SaveFigureBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.SaveFigureTk.trigger", "name": "trigger", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.SaveFigureTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.SaveFigureTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleDialog": {".class": "SymbolTableNode", "cross_ref": "tkinter.simpledialog.SimpleDialog", "kind": "Gdef"}, "TK_PHOTO_COMPOSITE_OVERLAY": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._tkagg.TK_PHOTO_COMPOSITE_OVERLAY", "kind": "Gdef"}, "TK_PHOTO_COMPOSITE_SET": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._tkagg.TK_PHOTO_COMPOSITE_SET", "kind": "Gdef"}, "TimerBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.TimerBase", "kind": "Gdef"}, "TimerTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.TimerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.TimerTk", "name": "TimerTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.TimerTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.TimerTk", "matplotlib.backend_bases.TimerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "parent", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.TimerTk.__init__", "name": "__init__", "type": null}}, "_on_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.TimerTk._on_timer", "name": "_on_timer", "type": null}}, "_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.TimerTk._timer", "name": "_timer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_timer_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.TimerTk._timer_start", "name": "_timer_start", "type": null}}, "_timer_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.TimerTk._timer_stop", "name": "_timer_stop", "type": null}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.TimerTk.parent", "name": "parent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.TimerTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.TimerTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolContainerBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.ToolContainerBase", "kind": "Gdef"}, "Toolbar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "matplotlib.backends._backend_tk.Toolbar", "line": 1067, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "matplotlib.backends._backend_tk.ToolbarTk"}}, "ToolbarTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.ToolContainerBase", "tkinter.Frame"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk.ToolbarTk", "name": "ToolbarTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk.ToolbarTk", "matplotlib.backend_bases.ToolContainerBase", "tkinter.Frame", "tkinter.Widget", "tkinter.BaseWidget", "tkinter.Misc", "tkinter.Pack", "tkinter.Place", "tkinter.Grid", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "toolmanager", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk.__init__", "name": "__init__", "type": null}}, "_add_separator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._add_separator", "name": "_add_separator", "type": null}}, "_button_click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._button_click", "name": "_button_click", "type": null}}, "_get_groupframe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._get_groupframe", "name": "_get_groupframe", "type": null}}, "_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._groups", "name": "_groups", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_label_font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._label_font", "name": "_label_font", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._message", "name": "_message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_message_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._message_label", "name": "_message_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._rescale", "name": "_rescale", "type": null}}, "_toolitems": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends._backend_tk.ToolbarTk._toolitems", "name": "_toolitems", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "group", "position", "image_file", "description", "toggle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk.add_toolitem", "name": "add_toolitem", "type": null}}, "remove_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk.remove_toolitem", "name": "remove_toolitem", "type": null}}, "set_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk.set_message", "name": "set_message", "type": null}}, "toggle_toolitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "toggled"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.ToolbarTk.toggle_toolitem", "name": "toggle_toolitem", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk.ToolbarTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk.ToolbarTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Backend": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases._Backend", "kind": "Gdef"}, "_BackendTk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases._Backend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends._backend_tk._BackendTk", "name": "_BackendTk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk._BackendTk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends._backend_tk", "mro": ["matplotlib.backends._backend_tk._BackendTk", "matplotlib.backend_bases._Backend", "builtins.object"], "names": {".class": "SymbolTable", "FigureCanvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._BackendTk.FigureCanvas", "name": "FigureCanvas", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["figure", "master"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["matplotlib.backends._backend_tk.FigureCanvasTk"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib.backends._backend_tk.FigureCanvasTk", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FigureManager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._BackendTk.FigureManager", "name": "FigureManager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["canvas", "num", "window"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["matplotlib.backends._backend_tk.FigureManagerTk"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib.backends._backend_tk.FigureManagerTk", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "backend_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._BackendTk.backend_version", "name": "backend_version", "type": null}}, "mainloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._BackendTk.mainloop", "name": "mainloop", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [{".class": "TypeType", "item": "matplotlib.backends._backend_tk.FigureManagerTk"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends._backend_tk._BackendTk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends._backend_tk._BackendTk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Mode": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases._Mode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends._backend_tk.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_api": {".class": "SymbolTableNode", "cross_ref": "matplotlib._api", "kind": "Gdef"}, "_blit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk._blit", "name": "_blit", "type": null}}, "_blit_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._blit_args", "name": "_blit_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_blit_tcl_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._blit_tcl_name", "name": "_blit_tcl_name", "type": "builtins.str"}}, "_c_internal_utils": {".class": "SymbolTableNode", "cross_ref": "matplotlib._c_internal_utils", "kind": "Gdef"}, "_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk._log", "name": "_log", "type": "logging.Logger"}}, "_restore_foreground_window_at_end": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backends._backend_tk._restore_foreground_window_at_end", "name": "_restore_foreground_window_at_end", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.backends._backend_tk._restore_foreground_window_at_end", "name": "_restore_foreground_window_at_end", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_restore_foreground_window_at_end", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tkagg": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._tkagg", "kind": "Gdef"}, "add_tooltip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["widget", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.add_tooltip", "name": "add_tooltip", "type": null}}, "backend_tools": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_tools", "kind": "Gdef"}, "blit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["photoimage", "aggimage", "offsets", "bbox"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends._backend_tk.blit", "name": "blit", "type": null}}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "cursord": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends._backend_tk.cursord", "name": "cursord", "type": {".class": "Instance", "args": ["matplotlib.backend_tools.Cursors", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cursors": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.cursors", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "mpl": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pathlib": {".class": "SymbolTableNode", "cross_ref": "pathlib", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tk": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef"}, "tkinter": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py"}
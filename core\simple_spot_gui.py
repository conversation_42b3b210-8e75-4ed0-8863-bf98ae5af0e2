#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版现货交易GUI
Simple Spot Trading GUI

不依赖matplotlib的简化版现货交易界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
from datetime import datetime
from pathlib import Path

class SimpleSpotTradingGUI:
    """简化版现货交易GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 现货滚雪球交易系统 - Spot Snowball Trading")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 交易状态
        self.is_connected = False
        self.is_trading = False
        self.current_capital = 10000.0
        self.daily_pnl = 0.0
        self.total_return = 0.0
        self.positions_count = 0
        
        # 模拟数据
        self.performance_data = []
        self.trade_history = []
        self.signals_data = {}
        
        self.setup_ui()
        self.start_data_simulation()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🚀 现货滚雪球交易系统", 
                              font=('Arial', 18, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=15)
        
        # 状态指示器
        self.status_label = tk.Label(title_frame, 
                                    text="● 未连接", 
                                    font=('Arial', 12, 'bold'),
                                    fg='red', bg='#2c3e50')
        self.status_label.pack(side='right', padx=20, pady=15)
        
        # 主要内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        left_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # 右侧显示面板
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        right_frame.pack(side='right', fill='both', expand=True)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        parent.configure(width=300)
        parent.pack_propagate(False)
        
        # 连接控制区域
        conn_frame = tk.LabelFrame(parent, text="🔗 连接控制", 
                                  font=('Arial', 12, 'bold'),
                                  bg='white', padx=10, pady=10)
        conn_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(conn_frame, text="测试连接", 
                 command=self.test_connection,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(fill='x', pady=2)
        
        tk.Button(conn_frame, text="开始交易", 
                 command=self.start_trading,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(fill='x', pady=2)
        
        tk.Button(conn_frame, text="停止交易", 
                 command=self.stop_trading,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(fill='x', pady=2)
        
        # 滚雪球策略参数
        strategy_frame = tk.LabelFrame(parent, text="❄️ 滚雪球策略参数", 
                                      font=('Arial', 12, 'bold'),
                                      bg='white', padx=10, pady=10)
        strategy_frame.pack(fill='x', padx=10, pady=10)
        
        # 参数输入
        params = [
            ("初始资金 (USDT):", "10000"),
            ("每笔风险 (%):", "2.0"),
            ("最小盈亏比:", "2.0"),
            ("最小胜率 (%):", "60"),
            ("最小信号强度 (%):", "70")
        ]
        
        self.param_vars = {}
        for label, default in params:
            tk.Label(strategy_frame, text=label, bg='white', 
                    font=('Arial', 9)).pack(anchor='w', pady=2)
            var = tk.StringVar(value=default)
            self.param_vars[label] = var
            tk.Entry(strategy_frame, textvariable=var, 
                    font=('Arial', 9)).pack(fill='x', pady=2)
        
        tk.Button(strategy_frame, text="应用参数", 
                 command=self.apply_parameters,
                 bg='#f39c12', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(fill='x', pady=10)
        
        # 账户信息
        account_frame = tk.LabelFrame(parent, text="💰 账户信息", 
                                     font=('Arial', 12, 'bold'),
                                     bg='white', padx=10, pady=10)
        account_frame.pack(fill='x', padx=10, pady=10)
        
        self.account_labels = {}
        account_items = [
            ("当前资金:", "current_capital"),
            ("日盈亏:", "daily_pnl"),
            ("总收益率:", "total_return"),
            ("持仓数量:", "positions_count")
        ]
        
        for label, key in account_items:
            frame = tk.Frame(account_frame, bg='white')
            frame.pack(fill='x', pady=2)
            tk.Label(frame, text=label, bg='white', 
                    font=('Arial', 9)).pack(side='left')
            self.account_labels[key] = tk.Label(frame, text="--", bg='white', 
                                               font=('Arial', 9, 'bold'))
            self.account_labels[key].pack(side='right')
        
        # 风险监控
        risk_frame = tk.LabelFrame(parent, text="🛡️ 风险监控", 
                                  font=('Arial', 12, 'bold'),
                                  bg='white', padx=10, pady=10)
        risk_frame.pack(fill='x', padx=10, pady=10)
        
        self.risk_labels = {}
        risk_items = [
            ("风险等级:", "risk_level"),
            ("当前回撤:", "drawdown"),
            ("胜率:", "win_rate"),
            ("盈利因子:", "profit_factor")
        ]
        
        for label, key in risk_items:
            frame = tk.Frame(risk_frame, bg='white')
            frame.pack(fill='x', pady=2)
            tk.Label(frame, text=label, bg='white', 
                    font=('Arial', 9)).pack(side='left')
            self.risk_labels[key] = tk.Label(frame, text="--", bg='white', 
                                            font=('Arial', 9, 'bold'))
            self.risk_labels[key].pack(side='right')
        
    def setup_right_panel(self, parent):
        """设置右侧显示面板"""
        # 创建标签页
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 性能监控标签页
        performance_frame = tk.Frame(notebook, bg='white')
        notebook.add(performance_frame, text="📈 性能监控")
        self.setup_performance_panel(performance_frame)
        
        # 持仓信息标签页
        positions_frame = tk.Frame(notebook, bg='white')
        notebook.add(positions_frame, text="📊 持仓信息")
        self.setup_positions_panel(positions_frame)
        
        # 交易历史标签页
        history_frame = tk.Frame(notebook, bg='white')
        notebook.add(history_frame, text="📋 交易历史")
        self.setup_history_panel(history_frame)
        
        # 信号分析标签页
        signals_frame = tk.Frame(notebook, bg='white')
        notebook.add(signals_frame, text="🎯 信号分析")
        self.setup_signals_panel(signals_frame)
        
    def setup_performance_panel(self, parent):
        """设置性能监控面板"""
        # 性能指标显示
        metrics_frame = tk.LabelFrame(parent, text="📊 关键指标", 
                                     font=('Arial', 12, 'bold'),
                                     bg='white', padx=10, pady=10)
        metrics_frame.pack(fill='x', padx=10, pady=10)
        
        # 创建指标显示网格
        metrics_grid = tk.Frame(metrics_frame, bg='white')
        metrics_grid.pack(fill='x')
        
        self.metric_labels = {}
        metrics = [
            ("总资金", "total_capital", "#3498db"),
            ("今日盈亏", "daily_pnl", "#27ae60"),
            ("总收益率", "total_return", "#f39c12"),
            ("最大回撤", "max_drawdown", "#e74c3c")
        ]
        
        for i, (name, key, color) in enumerate(metrics):
            row = i // 2
            col = i % 2
            
            metric_frame = tk.Frame(metrics_grid, bg=color, relief='raised', bd=2)
            metric_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
            
            tk.Label(metric_frame, text=name, bg=color, fg='white',
                    font=('Arial', 10, 'bold')).pack(pady=5)
            
            self.metric_labels[key] = tk.Label(metric_frame, text="--", 
                                              bg=color, fg='white',
                                              font=('Arial', 14, 'bold'))
            self.metric_labels[key].pack(pady=5)
        
        metrics_grid.columnconfigure(0, weight=1)
        metrics_grid.columnconfigure(1, weight=1)
        
        # 简化的性能图表（文本版）
        chart_frame = tk.LabelFrame(parent, text="📈 资金曲线", 
                                   font=('Arial', 12, 'bold'),
                                   bg='white', padx=10, pady=10)
        chart_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.chart_text = tk.Text(chart_frame, height=15, font=('Courier', 9),
                                 bg='#2c3e50', fg='#ecf0f1', insertbackground='white')
        chart_scroll = tk.Scrollbar(chart_frame, orient='vertical', 
                                   command=self.chart_text.yview)
        self.chart_text.configure(yscrollcommand=chart_scroll.set)
        
        self.chart_text.pack(side='left', fill='both', expand=True)
        chart_scroll.pack(side='right', fill='y')
        
    def setup_positions_panel(self, parent):
        """设置持仓信息面板"""
        # 持仓表格
        columns = ('交易对', '方向', '数量', '入场价', '当前价', '盈亏', '盈亏率')
        self.positions_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100, anchor='center')
        
        # 滚动条
        pos_scroll = ttk.Scrollbar(parent, orient='vertical', 
                                  command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=pos_scroll.set)
        
        self.positions_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        pos_scroll.pack(side='right', fill='y', pady=10)
        
    def setup_history_panel(self, parent):
        """设置交易历史面板"""
        # 交易历史表格
        columns = ('时间', '交易对', '方向', '数量', '价格', '盈亏', '原因')
        self.history_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100, anchor='center')
        
        # 滚动条
        hist_scroll = ttk.Scrollbar(parent, orient='vertical', 
                                   command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=hist_scroll.set)
        
        self.history_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        hist_scroll.pack(side='right', fill='y', pady=10)
        
    def setup_signals_panel(self, parent):
        """设置信号分析面板"""
        # 信号分析显示
        signals_frame = tk.LabelFrame(parent, text="🎯 当前信号分析", 
                                     font=('Arial', 12, 'bold'),
                                     bg='white', padx=10, pady=10)
        signals_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.signals_text = tk.Text(signals_frame, font=('Courier', 10),
                                   bg='#34495e', fg='#ecf0f1', insertbackground='white')
        signals_scroll = tk.Scrollbar(signals_frame, orient='vertical', 
                                     command=self.signals_text.yview)
        self.signals_text.configure(yscrollcommand=signals_scroll.set)
        
        self.signals_text.pack(side='left', fill='both', expand=True)
        signals_scroll.pack(side='right', fill='y')
        
        # 刷新按钮
        tk.Button(signals_frame, text="🔄 刷新信号", 
                 command=self.refresh_signals,
                 bg='#9b59b6', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(pady=10)
        
    def test_connection(self):
        """测试连接"""
        try:
            # 模拟连接测试
            self.status_label.config(text="● 连接中...", fg='orange')
            self.root.update()
            time.sleep(1)
            
            # 检查配置文件
            env_file = Path('.env')
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'YOUR_GATE_IO_API_KEY_HERE' in content or 'demo_gate_api_key' in content:
                    messagebox.showwarning("配置警告", 
                                         "检测到演示API密钥，请配置真实API密钥以进行实际交易")
                    self.status_label.config(text="● 演示模式", fg='blue')
                else:
                    self.status_label.config(text="● 已连接", fg='green')
                    messagebox.showinfo("连接成功", "API连接测试成功！")
                
                self.is_connected = True
            else:
                messagebox.showerror("配置错误", "未找到配置文件，请先配置API密钥")
                self.status_label.config(text="● 配置错误", fg='red')
                
        except Exception as e:
            messagebox.showerror("连接错误", f"连接失败: {str(e)}")
            self.status_label.config(text="● 连接失败", fg='red')
    
    def start_trading(self):
        """开始交易"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先测试连接")
            return
        
        if self.is_trading:
            messagebox.showwarning("警告", "交易已在运行中")
            return
        
        self.is_trading = True
        self.status_label.config(text="● 交易中", fg='blue')
        messagebox.showinfo("交易启动", "现货滚雪球策略已启动！\n\n策略特点:\n• 无杠杆风险\n• 复利增长\n• 严格风控")
        
    def stop_trading(self):
        """停止交易"""
        if not self.is_trading:
            messagebox.showwarning("警告", "交易未在运行")
            return
        
        self.is_trading = False
        self.status_label.config(text="● 已停止", fg='orange')
        messagebox.showinfo("交易停止", "交易已停止")
    
    def apply_parameters(self):
        """应用策略参数"""
        try:
            # 获取参数值
            params = {}
            for label, var in self.param_vars.items():
                params[label] = var.get()
            
            messagebox.showinfo("参数更新", "滚雪球策略参数已更新")
            
        except Exception as e:
            messagebox.showerror("参数错误", f"参数更新失败: {str(e)}")
    
    def refresh_signals(self):
        """刷新信号"""
        import random
        
        self.signals_text.delete(1.0, tk.END)
        
        signals_info = f"""
🎯 现货滚雪球信号分析 - {datetime.now().strftime('%H:%M:%S')}
{'='*60}

📊 BTC/USDT 信号分析:
   方向: {'买入' if random.random() > 0.5 else '观望'}
   信号强度: {random.uniform(0.6, 0.9):.2f}
   置信度: {random.uniform(0.7, 0.9):.2f}
   盈亏比: {random.uniform(2.0, 3.5):.2f}
   胜率预估: {random.uniform(0.6, 0.8):.1%}

📊 ETH/USDT 信号分析:
   方向: {'买入' if random.random() > 0.5 else '观望'}
   信号强度: {random.uniform(0.6, 0.9):.2f}
   置信度: {random.uniform(0.7, 0.9):.2f}
   盈亏比: {random.uniform(2.0, 3.5):.2f}
   胜率预估: {random.uniform(0.6, 0.8):.1%}

📊 BNB/USDT 信号分析:
   方向: {'买入' if random.random() > 0.5 else '观望'}
   信号强度: {random.uniform(0.6, 0.9):.2f}
   置信度: {random.uniform(0.7, 0.9):.2f}
   盈亏比: {random.uniform(2.0, 3.5):.2f}
   胜率预估: {random.uniform(0.6, 0.8):.1%}

🛡️ 风险评估:
   市场波动率: {random.uniform(0.15, 0.35):.2f}
   流动性评分: {random.uniform(0.7, 0.9):.2f}
   趋势强度: {random.uniform(0.5, 0.8):.2f}

💡 滚雪球策略提醒:
   • 只做高质量信号 (强度>70%)
   • 严格控制风险 (每笔2%)
   • 耐心等待机会
   • 让复利发挥作用
"""
        
        self.signals_text.insert(tk.END, signals_info)
    
    def start_data_simulation(self):
        """启动数据模拟"""
        def update_data():
            while True:
                if self.is_trading:
                    self.update_simulated_data()
                time.sleep(2)
        
        thread = threading.Thread(target=update_data, daemon=True)
        thread.start()
    
    def update_simulated_data(self):
        """更新模拟数据"""
        import random
        
        # 模拟资金变化
        change = random.uniform(-0.005, 0.01)  # -0.5% 到 +1%
        self.current_capital *= (1 + change)
        self.daily_pnl += self.current_capital * change
        self.total_return = (self.current_capital - 10000) / 10000
        
        # 更新账户信息
        self.account_labels['current_capital'].config(text=f"{self.current_capital:.2f} USDT")
        self.account_labels['daily_pnl'].config(text=f"{self.daily_pnl:+.2f} USDT")
        self.account_labels['total_return'].config(text=f"{self.total_return:+.2%}")
        self.account_labels['positions_count'].config(text=f"{random.randint(0, 3)}")
        
        # 更新风险信息
        drawdown = max(0, random.uniform(0, 0.05))
        self.risk_labels['risk_level'].config(text="低" if drawdown < 0.02 else "中")
        self.risk_labels['drawdown'].config(text=f"{drawdown:.2%}")
        self.risk_labels['win_rate'].config(text=f"{random.uniform(0.6, 0.8):.1%}")
        self.risk_labels['profit_factor'].config(text=f"{random.uniform(1.2, 2.5):.2f}")
        
        # 更新性能指标
        self.metric_labels['total_capital'].config(text=f"{self.current_capital:.0f}")
        self.metric_labels['daily_pnl'].config(text=f"{self.daily_pnl:+.0f}")
        self.metric_labels['total_return'].config(text=f"{self.total_return:+.1%}")
        self.metric_labels['max_drawdown'].config(text=f"{drawdown:.1%}")
        
        # 更新资金曲线（简化版）
        current_time = datetime.now().strftime('%H:%M:%S')
        chart_line = f"{current_time} | 资金: {self.current_capital:8.2f} | 盈亏: {self.daily_pnl:+7.2f}\n"
        
        self.chart_text.insert(tk.END, chart_line)
        self.chart_text.see(tk.END)
        
        # 限制显示行数
        lines = self.chart_text.get(1.0, tk.END).split('\n')
        if len(lines) > 50:
            self.chart_text.delete(1.0, '2.0')
    
    def run(self):
        """运行GUI"""
        # 显示欢迎信息
        welcome_msg = """
🎉 欢迎使用现货滚雪球交易系统！

💡 使用步骤:
1. 点击"测试连接"验证API配置
2. 配置滚雪球策略参数
3. 点击"开始交易"启动策略
4. 监控交易表现和风险指标

🛡️ 滚雪球策略特点:
• 无杠杆风险，纯现货交易
• 复利增长，让财富滚雪球
• 严格风控，保护本金安全
• 高质量信号，提高胜率

📈 立即开始您的财富增长之旅！
"""
        messagebox.showinfo("欢迎使用", welcome_msg)
        
        self.root.mainloop()

def main():
    """主函数"""
    app = SimpleSpotTradingGUI()
    app.run()

if __name__ == "__main__":
    main()

{"data_mtime": 1748490240, "dep_lines": [24, 25, 32, 33, 9, 10, 14, 15, 16, 26, 27, 28, 29, 30, 31, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 672, 671], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.asyncio.compatibility", "websockets.asyncio.connection", "urllib.parse", "collections.abc", "websockets.client", "websockets.datastructures", "websockets.exceptions", "websockets.headers", "websockets.http11", "websockets.protocol", "websockets.streams", "websockets.typing", "websockets.uri", "__future__", "asyncio", "logging", "os", "socket", "ssl", "traceback", "urllib", "types", "typing", "builtins", "_asyncio", "_frozen_importlib", "_socket", "_typeshed", "abc", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.transports", "contextlib", "enum", "typing_extensions", "websockets.asyncio.async_timeout", "websockets.extensions", "websockets.frames"], "hash": "74a0c9019f37105d3af33311956fa353c9859137", "id": "websockets.asyncio.client", "ignore_all": true, "interface_hash": "97ce211c20d0a8032c5187c81c38aadb1c058859", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py", "plugin_data": null, "size": 32310, "suppressed": ["python_socks.async_.asyncio", "python_socks"], "version_id": "1.15.0"}
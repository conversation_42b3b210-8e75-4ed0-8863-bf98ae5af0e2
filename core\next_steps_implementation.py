#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下一步行动计划实现
Next Steps Implementation

基于历史性成就，实现下一步的深度分析和优化
"""

import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class NextStepsManager:
    """
    下一步行动管理器

    负责分析处理结果，识别优秀策略，构建精品组合
    """

    def __init__(self):
        """初始化下一步行动管理器"""
        self.excellent_strategies = []
        self.good_strategies = []
        self.optimization_candidates = []
        self.elite_portfolios = []

        # 结果目录
        self.results_dir = "next_steps_results"
        os.makedirs(self.results_dir, exist_ok=True)

        logger.info("下一步行动管理器初始化完成")

    def analyze_processing_results(self) -> Dict[str, Any]:
        """
        分析处理结果

        Returns:
            Dict[str, Any]: 分析结果
        """
        logger.info("开始分析处理结果")

        try:
            # 读取策略排名
            rankings_file = "integration_results/strategy_rankings.csv"
            if not os.path.exists(rankings_file):
                raise FileNotFoundError(f"策略排名文件不存在: {rankings_file}")

            df = pd.read_csv(rankings_file)

            # 分析策略质量分布
            analysis = {
                "total_strategies": len(df),
                "excellent_strategies": [],  # >=80分
                "good_strategies": [],  # 60-79分
                "moderate_strategies": [],  # 40-59分
                "poor_strategies": [],  # <40分
                "statistics": {},
                "top_performers": {},
                "risk_analysis": {},
            }

            # 按评分分类
            for _, row in df.iterrows():
                score = row["validation_score"]
                strategy_info = {
                    "name": row["strategy_name"],
                    "score": score,
                    "sharpe_ratio": row["sharpe_ratio"],
                    "max_drawdown": row["max_drawdown"],
                    "recommendation": row["recommendation"],
                }

                if score >= 80:
                    analysis["excellent_strategies"].append(strategy_info)
                elif score >= 60:
                    analysis["good_strategies"].append(strategy_info)
                elif score >= 40:
                    analysis["moderate_strategies"].append(strategy_info)
                else:
                    analysis["poor_strategies"].append(strategy_info)

            # 统计信息
            analysis["statistics"] = {
                "total_strategies": len(df),
                "excellent_count": len(analysis["excellent_strategies"]),
                "good_count": len(analysis["good_strategies"]),
                "moderate_count": len(analysis["moderate_strategies"]),
                "poor_count": len(analysis["poor_strategies"]),
                "avg_score": df["validation_score"].mean(),
                "std_score": df["validation_score"].std(),
                "max_score": df["validation_score"].max(),
                "min_score": df["validation_score"].min(),
            }

            # 顶级表现者分析
            top_5 = df.head(5)
            analysis["top_performers"] = {
                "best_overall": {
                    "name": top_5.iloc[0]["strategy_name"],
                    "score": top_5.iloc[0]["validation_score"],
                    "sharpe": top_5.iloc[0]["sharpe_ratio"],
                },
                "best_sharpe": df.loc[df["sharpe_ratio"].idxmax()].to_dict(),
                "lowest_drawdown": df.loc[
                    df["max_drawdown"].idxmax()
                ].to_dict(),
                "top_5_list": top_5.to_dict("records"),
            }

            # 风险分析
            analysis["risk_analysis"] = {
                "avg_sharpe": df["sharpe_ratio"].mean(),
                "avg_drawdown": df["max_drawdown"].mean(),
                "high_sharpe_strategies": df[df["sharpe_ratio"] > 1.0][
                    "strategy_name"
                ].tolist(),
                "low_drawdown_strategies": df[df["max_drawdown"] > -0.15][
                    "strategy_name"
                ].tolist(),
            }

            # 保存分析结果
            with open(
                f"{self.results_dir}/processing_analysis.json",
                "w",
                encoding="utf-8",
            ) as f:
                json.dump(
                    analysis, f, ensure_ascii=False, indent=2, default=str
                )

            logger.info(
                f"处理结果分析完成，发现 {analysis['statistics']['excellent_count']} 个优秀策略"
            )
            return analysis

        except Exception as e:
            logger.error(f"分析处理结果失败: {e}")
            return {}

    def identify_elite_strategies(
        self, analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        识别精英策略

        Args:
            analysis: 分析结果

        Returns:
            List[Dict[str, Any]]: 精英策略列表
        """
        logger.info("开始识别精英策略")

        elite_strategies = []

        # 优秀策略（>=80分）
        for strategy in analysis.get("excellent_strategies", []):
            strategy["tier"] = "EXCELLENT"
            strategy["priority"] = "HIGH"
            strategy["action"] = "IMMEDIATE_DEPLOYMENT"
            elite_strategies.append(strategy)

        # 良好策略（60-79分）
        for strategy in analysis.get("good_strategies", []):
            strategy["tier"] = "GOOD"
            strategy["priority"] = "MEDIUM"
            strategy["action"] = "OPTIMIZATION_THEN_DEPLOYMENT"
            elite_strategies.append(strategy)

        # 特殊筛选：高夏普比率策略
        for strategy in analysis.get("moderate_strategies", []):
            if strategy["sharpe_ratio"] > 1.0:
                strategy["tier"] = "HIGH_SHARPE"
                strategy["priority"] = "MEDIUM"
                strategy["action"] = "DEEP_OPTIMIZATION"
                elite_strategies.append(strategy)

        # 特殊筛选：低回撤策略
        for strategy in analysis.get("moderate_strategies", []):
            if strategy["max_drawdown"] > -0.10:  # 回撤小于10%
                strategy["tier"] = "LOW_DRAWDOWN"
                strategy["priority"] = "MEDIUM"
                strategy["action"] = "RISK_ANALYSIS"
                if strategy not in elite_strategies:
                    elite_strategies.append(strategy)

        # 按优先级排序
        elite_strategies.sort(
            key=lambda x: (
                {"HIGH": 0, "MEDIUM": 1, "LOW": 2}[x["priority"]],
                -x["score"],
            )
        )

        # 保存精英策略
        with open(
            f"{self.results_dir}/elite_strategies.json", "w", encoding="utf-8"
        ) as f:
            json.dump(
                elite_strategies, f, ensure_ascii=False, indent=2, default=str
            )

        logger.info(f"识别出 {len(elite_strategies)} 个精英策略")
        return elite_strategies

    def create_elite_portfolios(
        self, elite_strategies: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        创建精英投资组合

        Args:
            elite_strategies: 精英策略列表

        Returns:
            List[Dict[str, Any]]: 精英投资组合列表
        """
        logger.info("开始创建精英投资组合")

        portfolios = []

        # 组合1：顶级策略组合（仅优秀策略）
        excellent_strategies = [
            s for s in elite_strategies if s["tier"] == "EXCELLENT"
        ]
        if excellent_strategies:
            portfolio1 = {
                "name": "Elite_Excellence_Portfolio",
                "description": "仅包含优秀策略的顶级组合",
                "strategies": excellent_strategies,
                "strategy_count": len(excellent_strategies),
                "allocation_method": "equal_weight",
                "risk_level": "LOW",
                "expected_sharpe": np.mean(
                    [s["sharpe_ratio"] for s in excellent_strategies]
                ),
                "expected_drawdown": np.mean(
                    [s["max_drawdown"] for s in excellent_strategies]
                ),
                "deployment_priority": "IMMEDIATE",
            }
            portfolios.append(portfolio1)

        # 组合2：平衡策略组合（优秀+良好策略）
        balanced_strategies = [
            s for s in elite_strategies if s["tier"] in ["EXCELLENT", "GOOD"]
        ]
        if len(balanced_strategies) >= 3:
            portfolio2 = {
                "name": "Elite_Balanced_Portfolio",
                "description": "优秀和良好策略的平衡组合",
                "strategies": balanced_strategies[:8],  # 最多8个策略
                "strategy_count": min(8, len(balanced_strategies)),
                "allocation_method": "risk_parity",
                "risk_level": "MEDIUM",
                "expected_sharpe": np.mean(
                    [s["sharpe_ratio"] for s in balanced_strategies[:8]]
                ),
                "expected_drawdown": np.mean(
                    [s["max_drawdown"] for s in balanced_strategies[:8]]
                ),
                "deployment_priority": "HIGH",
            }
            portfolios.append(portfolio2)

        # 组合3：高夏普比率组合
        high_sharpe_strategies = [
            s for s in elite_strategies if s["sharpe_ratio"] > 1.0
        ]
        if len(high_sharpe_strategies) >= 3:
            portfolio3 = {
                "name": "Elite_HighSharpe_Portfolio",
                "description": "高夏普比率策略组合",
                "strategies": high_sharpe_strategies[:6],
                "strategy_count": min(6, len(high_sharpe_strategies)),
                "allocation_method": "sharpe_weighted",
                "risk_level": "MEDIUM",
                "expected_sharpe": np.mean(
                    [s["sharpe_ratio"] for s in high_sharpe_strategies[:6]]
                ),
                "expected_drawdown": np.mean(
                    [s["max_drawdown"] for s in high_sharpe_strategies[:6]]
                ),
                "deployment_priority": "MEDIUM",
            }
            portfolios.append(portfolio3)

        # 组合4：低风险组合
        low_risk_strategies = [
            s for s in elite_strategies if s["max_drawdown"] > -0.15
        ]
        if len(low_risk_strategies) >= 3:
            portfolio4 = {
                "name": "Elite_LowRisk_Portfolio",
                "description": "低风险策略组合",
                "strategies": low_risk_strategies[:5],
                "strategy_count": min(5, len(low_risk_strategies)),
                "allocation_method": "equal_weight",
                "risk_level": "LOW",
                "expected_sharpe": np.mean(
                    [s["sharpe_ratio"] for s in low_risk_strategies[:5]]
                ),
                "expected_drawdown": np.mean(
                    [s["max_drawdown"] for s in low_risk_strategies[:5]]
                ),
                "deployment_priority": "HIGH",
            }
            portfolios.append(portfolio4)

        # 保存精英组合
        with open(
            f"{self.results_dir}/elite_portfolios.json", "w", encoding="utf-8"
        ) as f:
            json.dump(portfolios, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"创建了 {len(portfolios)} 个精英投资组合")
        return portfolios

    def generate_optimization_plan(
        self, elite_strategies: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        生成优化计划

        Args:
            elite_strategies: 精英策略列表

        Returns:
            Dict[str, Any]: 优化计划
        """
        logger.info("生成策略优化计划")

        optimization_plan = {
            "immediate_deployment": [],
            "optimization_required": [],
            "deep_optimization": [],
            "risk_analysis_required": [],
            "timeline": {},
            "resource_allocation": {},
        }

        # 按行动类型分类
        for strategy in elite_strategies:
            action = strategy.get("action", "UNKNOWN")

            if action == "IMMEDIATE_DEPLOYMENT":
                optimization_plan["immediate_deployment"].append(strategy)
            elif action == "OPTIMIZATION_THEN_DEPLOYMENT":
                optimization_plan["optimization_required"].append(strategy)
            elif action == "DEEP_OPTIMIZATION":
                optimization_plan["deep_optimization"].append(strategy)
            elif action == "RISK_ANALYSIS":
                optimization_plan["risk_analysis_required"].append(strategy)

        # 时间线规划
        optimization_plan["timeline"] = {
            "week_1": {
                "tasks": ["部署优秀策略", "开始良好策略优化"],
                "strategies": optimization_plan["immediate_deployment"][:3],
            },
            "week_2": {
                "tasks": ["完成良好策略优化", "开始深度优化"],
                "strategies": optimization_plan["optimization_required"][:5],
            },
            "week_3": {
                "tasks": ["深度优化高潜力策略", "风险分析"],
                "strategies": optimization_plan["deep_optimization"][:3],
            },
            "week_4": {"tasks": ["组合构建", "实盘准备"], "strategies": []},
        }

        # 资源分配
        optimization_plan["resource_allocation"] = {
            "high_priority": len(optimization_plan["immediate_deployment"]),
            "medium_priority": len(optimization_plan["optimization_required"]),
            "research_priority": len(optimization_plan["deep_optimization"]),
            "total_strategies": len(elite_strategies),
        }

        # 保存优化计划
        with open(
            f"{self.results_dir}/optimization_plan.json", "w", encoding="utf-8"
        ) as f:
            json.dump(
                optimization_plan, f, ensure_ascii=False, indent=2, default=str
            )

        logger.info("策略优化计划生成完成")
        return optimization_plan

    def generate_next_steps_report(
        self,
        analysis: Dict[str, Any],
        elite_strategies: List[Dict[str, Any]],
        portfolios: List[Dict[str, Any]],
        optimization_plan: Dict[str, Any],
    ) -> str:
        """
        生成下一步行动报告

        Args:
            analysis: 分析结果
            elite_strategies: 精英策略
            portfolios: 精英组合
            optimization_plan: 优化计划

        Returns:
            str: 报告文件路径
        """
        logger.info("生成下一步行动报告")

        report_content = f"""# 🎯 下一步行动计划报告
## Next Steps Action Plan Report

**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**基于**: 200个策略的历史性处理成果
**目标**: 将技术成就转化为实际投资价值

---

## 📊 处理结果分析

### 策略质量分布
- **总策略数**: {analysis['statistics']['total_strategies']}
- **优秀策略** (≥80分): {analysis['statistics']['excellent_count']} 个
- **良好策略** (60-79分): {analysis['statistics']['good_count']} 个
- **中等策略** (40-59分): {analysis['statistics']['moderate_count']} 个
- **较差策略** (<40分): {analysis['statistics']['poor_count']} 个

### 关键统计指标
- **平均评分**: {analysis['statistics']['avg_score']:.2f}分
- **最高评分**: {analysis['statistics']['max_score']:.2f}分
- **评分标准差**: {analysis['statistics']['std_score']:.2f}分
- **平均夏普比率**: {analysis['risk_analysis']['avg_sharpe']:.3f}
- **平均最大回撤**: {analysis['risk_analysis']['avg_drawdown']:.2%}

---

## 🏆 精英策略识别

### 发现的精英策略总数: {len(elite_strategies)}

"""

        # 添加顶级策略详情
        if analysis.get("excellent_strategies"):
            report_content += "### 🥇 优秀策略 (≥80分)\n"
            for i, strategy in enumerate(analysis["excellent_strategies"], 1):
                report_content += f"{i}. **{strategy['name']}**\n"
                report_content += f"   - 评分: {strategy['score']:.2f}分\n"
                report_content += (
                    f"   - 夏普比率: {strategy['sharpe_ratio']:.3f}\n"
                )
                report_content += (
                    f"   - 最大回撤: {strategy['max_drawdown']:.2%}\n\n"
                )

        if analysis.get("good_strategies"):
            report_content += "### 🥈 良好策略 (60-79分)\n"
            for i, strategy in enumerate(analysis["good_strategies"], 1):
                report_content += f"{i}. **{strategy['name']}**\n"
                report_content += f"   - 评分: {strategy['score']:.2f}分\n"
                report_content += (
                    f"   - 夏普比率: {strategy['sharpe_ratio']:.3f}\n"
                )
                report_content += (
                    f"   - 最大回撤: {strategy['max_drawdown']:.2%}\n\n"
                )

        # 添加精英组合信息
        report_content += f"""---

## 📈 精英投资组合

### 创建的精英组合数量: {len(portfolios)}

"""

        for i, portfolio in enumerate(portfolios, 1):
            report_content += f"""### {i}. {portfolio['name']}
- **描述**: {portfolio['description']}
- **策略数量**: {portfolio['strategy_count']}个
- **风险等级**: {portfolio['risk_level']}
- **预期夏普比率**: {portfolio['expected_sharpe']:.3f}
- **预期最大回撤**: {portfolio['expected_drawdown']:.2%}
- **部署优先级**: {portfolio['deployment_priority']}

"""

        # 添加优化计划
        report_content += f"""---

## 🔧 优化计划

### 立即部署策略: {len(optimization_plan['immediate_deployment'])}个
### 需要优化策略: {len(optimization_plan['optimization_required'])}个
### 深度优化策略: {len(optimization_plan['deep_optimization'])}个
### 风险分析策略: {len(optimization_plan['risk_analysis_required'])}个

### 时间线规划
- **第1周**: {', '.join(optimization_plan['timeline']['week_1']['tasks'])}
- **第2周**: {', '.join(optimization_plan['timeline']['week_2']['tasks'])}
- **第3周**: {', '.join(optimization_plan['timeline']['week_3']['tasks'])}
- **第4周**: {', '.join(optimization_plan['timeline']['week_4']['tasks'])}

---

## 🎯 立即行动建议

### 今天就可以开始的行动:
1. **部署顶级策略**: 立即部署评分≥80分的优秀策略
2. **构建精英组合**: 基于优秀策略构建低风险组合
3. **启动优化**: 对良好策略开始参数优化
4. **风险评估**: 对所有精英策略进行深度风险分析

### 本周内完成的目标:
1. **实盘准备**: 准备小规模实盘测试环境
2. **监控系统**: 完善实时监控和预警系统
3. **资金分配**: 制定资金分配和风险控制方案
4. **备选方案**: 准备备选策略和应急预案

### 本月内达成的里程碑:
1. **实盘验证**: 完成至少3个策略的实盘验证
2. **组合优化**: 完成精英组合的构建和测试
3. **系统升级**: 基于实盘经验优化系统
4. **规模扩展**: 准备扩大投资规模

---

## 💡 成功关键要素

1. **质量优先**: 始终坚持质量第一，不追求数量
2. **风险控制**: 严格的风险管理和止损机制
3. **渐进部署**: 小规模开始，逐步扩大规模
4. **持续监控**: 实时监控和及时调整
5. **数据驱动**: 基于数据做决策，避免主观判断

---

## 🚀 预期成果

基于当前的精英策略质量，预期可以实现:
- **年化收益率**: 15-25%
- **夏普比率**: 1.5-2.5
- **最大回撤**: <15%
- **胜率**: >60%

**这将是一个具有机构级质量的量化交易系统！**

---

**报告生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**下一步**: 开始执行立即行动建议
**目标**: 将技术成就转化为实际投资收益
"""

        # 保存报告
        report_file = f"{self.results_dir}/next_steps_action_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report_content)

        logger.info(f"下一步行动报告已生成: {report_file}")
        return report_file


def main():
    """主函数"""
    print("开始执行下一步行动计划")
    print("=" * 60)

    try:
        # 创建管理器
        manager = NextStepsManager()

        # 1. 分析处理结果
        print("步骤1: 分析处理结果...")
        analysis = manager.analyze_processing_results()

        if not analysis:
            print("错误: 无法分析处理结果")
            return

        print(f"发现 {analysis['statistics']['total_strategies']} 个策略")
        print(f"其中优秀策略 {analysis['statistics']['excellent_count']} 个")
        print(f"良好策略 {analysis['statistics']['good_count']} 个")

        # 2. 识别精英策略
        print("\n步骤2: 识别精英策略...")
        elite_strategies = manager.identify_elite_strategies(analysis)
        print(f"识别出 {len(elite_strategies)} 个精英策略")

        # 3. 创建精英组合
        print("\n步骤3: 创建精英投资组合...")
        portfolios = manager.create_elite_portfolios(elite_strategies)
        print(f"创建了 {len(portfolios)} 个精英投资组合")

        # 4. 生成优化计划
        print("\n步骤4: 生成优化计划...")
        optimization_plan = manager.generate_optimization_plan(
            elite_strategies
        )
        print("优化计划生成完成")

        # 5. 生成报告
        print("\n步骤5: 生成下一步行动报告...")
        report_file = manager.generate_next_steps_report(
            analysis, elite_strategies, portfolios, optimization_plan
        )
        print(f"报告已生成: {report_file}")

        # 显示关键结果
        print("\n" + "=" * 60)
        print("关键发现:")
        print(f"- 优秀策略: {analysis['statistics']['excellent_count']} 个")
        print(f"- 精英策略: {len(elite_strategies)} 个")
        print(f"- 精英组合: {len(portfolios)} 个")
        print(
            f"- 立即可部署: {len(optimization_plan['immediate_deployment'])} 个策略"
        )

        if analysis.get("excellent_strategies"):
            best_strategy = analysis["excellent_strategies"][0]
            print(
                f"- 最佳策略: {best_strategy['name']} (评分: {best_strategy['score']:.2f})"
            )

        print("\n下一步: 查看生成的报告文件，开始执行行动计划！")

    except Exception as e:
        print(f"执行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()

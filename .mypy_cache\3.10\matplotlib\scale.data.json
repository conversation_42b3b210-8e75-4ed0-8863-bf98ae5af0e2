{".class": "MypyFile", "_fullname": "matplotlib.scale", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsinhScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.AsinhScale", "name": "AsinhScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.AsinhScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "axis", "linear_width", "base", "subs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "axis", "linear_width", "base", "subs", "kwargs"], "arg_types": ["matplotlib.scale.AsinhScale", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsinhScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_tick_multipliers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.AsinhScale.auto_tick_multipliers", "name": "auto_tick_multipliers", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhScale.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.AsinhScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of AsinhScale", "ret_type": "matplotlib.scale.AsinhTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linear_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.AsinhScale.linear_width", "name": "linear_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.AsinhScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linear_width of AsinhScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.AsinhScale.linear_width", "name": "linear_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.AsinhScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linear_width of AsinhScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.AsinhScale.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.AsinhScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.AsinhScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsinhTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.AsinhTransform", "name": "AsinhTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.AsinhTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "linear_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "linear_width"], "arg_types": ["matplotlib.scale.AsinhTransform", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsinhTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.AsinhTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.AsinhTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.AsinhTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of AsinhTransform", "ret_type": "matplotlib.scale.InvertedAsinhTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linear_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.AsinhTransform.linear_width", "name": "linear_width", "type": "builtins.float"}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.AsinhTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.AsinhTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.AsinhTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Axis": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axis.Axis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FuncScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.FuncScale", "name": "FuncScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.FuncScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "axis", "functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "axis", "functions"], "arg_types": ["matplotlib.scale.FuncScale", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuncScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.FuncScale.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.FuncScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.FuncScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FuncScaleLog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.LogScale"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.FuncScaleLog", "name": "FuncScaleLog", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncScaleLog", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.FuncScaleLog", "matplotlib.scale.LogScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "axis", "functions", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncScaleLog.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "axis", "functions", "base"], "arg_types": ["matplotlib.scale.FuncScaleLog", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuncScaleLog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.FuncScaleLog.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.FuncScaleLog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of FuncScaleLog", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.FuncScaleLog.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.FuncScaleLog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of FuncScaleLog", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncScaleLog.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.FuncScaleLog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of FuncScaleLog", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.FuncScaleLog.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.FuncScaleLog", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FuncTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.FuncTransform", "name": "FuncTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.FuncTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "forward", "inverse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "forward", "inverse"], "arg_types": ["matplotlib.scale.FuncTransform", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuncTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.FuncTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.FuncTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.FuncTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of FuncTransform", "ret_type": "matplotlib.scale.FuncTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.FuncTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.FuncTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.FuncTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvertedAsinhTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.InvertedAsinhTransform", "name": "InvertedAsinhTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedAsinhTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.InvertedAsinhTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "linear_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedAsinhTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "linear_width"], "arg_types": ["matplotlib.scale.InvertedAsinhTransform", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvertedAsinhTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedAsinhTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedAsinhTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.InvertedAsinhTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedAsinhTransform", "ret_type": "matplotlib.scale.AsinhTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linear_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedAsinhTransform.linear_width", "name": "linear_width", "type": "builtins.float"}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedAsinhTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.InvertedAsinhTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.InvertedAsinhTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvertedLogTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.InvertedLogTransform", "name": "InvertedLogTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedLogTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.InvertedLogTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedLogTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "base"], "arg_types": ["matplotlib.scale.InvertedLogTransform", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvertedLogTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedLogTransform.base", "name": "base", "type": "builtins.float"}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedLogTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedLogTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.InvertedLogTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedLogTransform", "ret_type": "matplotlib.scale.LogTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedLogTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.InvertedLogTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.InvertedLogTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvertedSymmetricalLogTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform", "name": "InvertedSymmetricalLogTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.InvertedSymmetricalLogTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base", "lint<PERSON><PERSON>", "linscale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base", "lint<PERSON><PERSON>", "linscale"], "arg_types": ["matplotlib.scale.InvertedSymmetricalLogTransform", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvertedSymmetricalLogTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.base", "name": "base", "type": "builtins.float"}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.InvertedSymmetricalLogTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedSymmetricalLogTransform", "ret_type": "matplotlib.scale.SymmetricalLogTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invlinthresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.invlinthresh", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "builtins.float"}}, "linscale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.linscale", "name": "linscale", "type": "builtins.float"}}, "linthresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.linthresh", "name": "lint<PERSON><PERSON>", "type": "builtins.float"}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.InvertedSymmetricalLogTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.InvertedSymmetricalLogTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LinearScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LinearScale", "name": "LinearScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LinearScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LinearScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LinearScale.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LinearScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LinearScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LogScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LogScale", "name": "LogScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LogScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "axis", "base", "subs", "nonpositive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "axis", "base", "subs", "nonpositive"], "arg_types": ["matplotlib.scale.LogScale", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "clip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mask"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.LogScale.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of LogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.LogScale.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of LogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogScale.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of LogScale", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogScale.name", "name": "name", "type": "builtins.str"}}, "subs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogScale.subs", "name": "subs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LogScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LogScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LogTransform", "name": "LogTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LogTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "base", "nonpositive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "base", "nonpositive"], "arg_types": ["matplotlib.scale.LogTransform", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "clip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mask"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogTransform.base", "name": "base", "type": "builtins.float"}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of LogTransform", "ret_type": "matplotlib.scale.InvertedLogTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LogTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LogTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogisticTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LogisticTransform", "name": "LogisticTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogisticTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LogisticTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nonpositive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogisticTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nonpositive"], "arg_types": ["matplotlib.scale.LogisticTransform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mask"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clip"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogisticTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogisticTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogisticTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogisticTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of LogisticTransform", "ret_type": "matplotlib.scale.LogitTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogisticTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LogisticTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LogisticTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogitScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LogitScale", "name": "LogitScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LogitScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "axis", "nonpositive", "one_half", "use_overline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "axis", "nonpositive", "one_half", "use_overline"], "arg_types": ["matplotlib.scale.LogitScale", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mask"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clip"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogitScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitScale.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogitScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of LogitScale", "ret_type": "matplotlib.scale.LogitTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogitScale.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LogitScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LogitScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogitTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.LogitTransform", "name": "LogitTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.LogitTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nonpositive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nonpositive"], "arg_types": ["matplotlib.scale.LogitTransform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mask"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clip"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogitTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogitTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.LogitTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.LogitTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of LogitTransform", "ret_type": "matplotlib.scale.LogisticTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.LogitTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.LogitTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.LogitTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScaleBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.ScaleBase", "name": "ScaleBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.ScaleBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.ScaleBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "arg_types": ["matplotlib.scale.ScaleBase", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScaleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.ScaleBase.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.ScaleBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of ScaleBase", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "limit_range_for_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vmin", "vmax", "minpos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.ScaleBase.limit_range_for_scale", "name": "limit_range_for_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vmin", "vmax", "minpos"], "arg_types": ["matplotlib.scale.ScaleBase", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "limit_range_for_scale of ScaleBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_locators_and_formatters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.ScaleBase.set_default_locators_and_formatters", "name": "set_default_locators_and_formatters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "arg_types": ["matplotlib.scale.ScaleBase", "matplotlib.axis.Axis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_locators_and_formatters of ScaleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.ScaleBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.ScaleBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymmetricalLogScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.scale.ScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.SymmetricalLogScale", "name": "SymmetricalLogScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.SymmetricalLogScale", "matplotlib.scale.ScaleBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "axis", "base", "lint<PERSON><PERSON>", "subs", "linscale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "axis", "base", "lint<PERSON><PERSON>", "subs", "linscale"], "arg_types": ["matplotlib.scale.SymmetricalLogScale", {".class": "UnionType", "items": ["matplotlib.axis.Axis", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SymmetricalLogScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.SymmetricalLogScale.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.SymmetricalLogScale.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogScale.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of SymmetricalLogScale", "ret_type": "matplotlib.scale.SymmetricalLogTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linscale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.SymmetricalLogScale.linscale", "name": "linscale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linscale of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.SymmetricalLogScale.linscale", "name": "linscale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linscale of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "linthresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.scale.SymmetricalLogScale.linthresh", "name": "lint<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linthresh of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.scale.SymmetricalLogScale.linthresh", "name": "lint<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogScale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linthresh of SymmetricalLogScale", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogScale.name", "name": "name", "type": "builtins.str"}}, "subs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogScale.subs", "name": "subs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.SymmetricalLogScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.SymmetricalLogScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymmetricalLogTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.scale.SymmetricalLogTransform", "name": "SymmetricalLogTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.scale", "mro": ["matplotlib.scale.SymmetricalLogTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base", "lint<PERSON><PERSON>", "linscale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base", "lint<PERSON><PERSON>", "linscale"], "arg_types": ["matplotlib.scale.SymmetricalLogTransform", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SymmetricalLogTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogTransform.base", "name": "base", "type": "builtins.float"}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.SymmetricalLogTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.scale.SymmetricalLogTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of SymmetricalLogTransform", "ret_type": "matplotlib.scale.InvertedSymmetricalLogTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linscale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogTransform.linscale", "name": "linscale", "type": "builtins.float"}}, "linthresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogTransform.linthresh", "name": "lint<PERSON><PERSON>", "type": "builtins.float"}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.scale.SymmetricalLogTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.scale.SymmetricalLogTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.scale.SymmetricalLogTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.scale.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_scale_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.get_scale_names", "name": "get_scale_names", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scale_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_scale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scale_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.register_scale", "name": "register_scale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scale_class"], "arg_types": [{".class": "TypeType", "item": "matplotlib.scale.ScaleBase"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_scale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scale_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["scale", "axis", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.scale.scale_factory", "name": "scale_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["scale", "axis", "kwargs"], "arg_types": ["builtins.str", "matplotlib.axis.Axis", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scale_factory", "ret_type": "matplotlib.scale.ScaleBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\scale.pyi"}
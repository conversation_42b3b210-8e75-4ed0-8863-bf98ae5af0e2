#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置界面
Configuration UI

提供图形化的配置管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Callable

from core.config_manager import get_config_manager, get_config

class ConfigUI:
    """配置界面"""
    
    def __init__(self, parent):
        """初始化配置界面"""
        self.parent = parent
        self.config_manager = get_config_manager()
        self.config = get_config()
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("⚙️ 系统配置")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.configure(bg='#2d2d2d')
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置
        self.load_config_to_ui()
    
    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        width = 800
        height = 600
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg='#2d2d2d')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_frame = tk.Frame(main_frame, bg='#2d2d2d')
        title_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(title_frame, text="⚙️ 系统配置管理",
                font=('Arial', 16, 'bold'), bg='#2d2d2d', fg='white').pack(side='left')
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # 创建各个配置页
        self.create_trading_tab(notebook)
        self.create_exchange_tab(notebook)
        self.create_system_tab(notebook)
        self.create_logging_tab(notebook)
        self.create_monitoring_tab(notebook)
        self.create_optimization_tab(notebook)
        
        # 底部按钮
        button_frame = tk.Frame(main_frame, bg='#2d2d2d')
        button_frame.pack(fill='x', pady=(20, 0))
        
        tk.Button(button_frame, text="📤 导出配置",
                 command=self.export_config,
                 bg='#607D8B', fg='white', font=('Arial', 10),
                 relief='flat', padx=15, pady=8).pack(side='left')
        
        tk.Button(button_frame, text="📥 导入配置",
                 command=self.import_config,
                 bg='#607D8B', fg='white', font=('Arial', 10),
                 relief='flat', padx=15, pady=8).pack(side='left', padx=(10, 0))
        
        tk.Button(button_frame, text="🔄 重置为默认",
                 command=self.reset_to_default,
                 bg='#f44336', fg='white', font=('Arial', 10),
                 relief='flat', padx=15, pady=8).pack(side='left', padx=(10, 0))
        
        tk.Button(button_frame, text="❌ 取消",
                 command=self.cancel,
                 bg='#f44336', fg='white', font=('Arial', 10),
                 relief='flat', padx=15, pady=8).pack(side='right')
        
        tk.Button(button_frame, text="💾 保存",
                 command=self.save_config,
                 bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='right', padx=(0, 10))
    
    def create_trading_tab(self, notebook):
        """创建交易配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="📊 交易配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 初始资金
        self.create_form_field(form_frame, "初始资金:", "trading.initial_capital", 
                              input_type="float", row=0)
        
        # 日亏损限制
        self.create_form_field(form_frame, "日亏损限制:", "trading.daily_loss_limit", 
                              input_type="float", row=1)
        
        # 最大仓位比例
        self.create_form_field(form_frame, "最大仓位比例:", "trading.max_position_size", 
                              input_type="float", row=2)
        
        # 止损比例
        self.create_form_field(form_frame, "止损比例:", "trading.stop_loss_pct", 
                              input_type="float", row=3)
        
        # 止盈比例
        self.create_form_field(form_frame, "止盈比例:", "trading.take_profit_pct", 
                              input_type="float", row=4)
        
        # 最大回撤
        self.create_form_field(form_frame, "最大回撤:", "trading.max_drawdown", 
                              input_type="float", row=5)
        
        # 最小交易金额
        self.create_form_field(form_frame, "最小交易金额:", "trading.min_trade_amount", 
                              input_type="float", row=6)
        
        # 每日最大交易次数
        self.create_form_field(form_frame, "每日最大交易次数:", "trading.max_trades_per_day", 
                              input_type="int", row=7)
        
        # 交易对列表
        self.create_form_field(form_frame, "交易对列表:", "trading.trading_pairs", 
                              input_type="list", row=8)
    
    def create_exchange_tab(self, notebook):
        """创建交易所配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="🏦 交易所配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 交易所名称
        self.create_form_field(form_frame, "交易所名称:", "exchange.name", 
                              input_type="str", row=0)
        
        # API Key
        self.create_form_field(form_frame, "API Key:", "exchange.api_key", 
                              input_type="str", row=1)
        
        # API Secret
        self.create_form_field(form_frame, "API Secret:", "exchange.api_secret", 
                              input_type="password", row=2)
        
        # Passphrase
        self.create_form_field(form_frame, "Passphrase:", "exchange.passphrase", 
                              input_type="password", row=3)
        
        # 沙盒模式
        self.create_form_field(form_frame, "沙盒模式:", "exchange.sandbox", 
                              input_type="bool", row=4)
        
        # 测试网络
        self.create_form_field(form_frame, "测试网络:", "exchange.testnet", 
                              input_type="bool", row=5)
        
        # 超时时间
        self.create_form_field(form_frame, "超时时间(ms):", "exchange.timeout", 
                              input_type="int", row=6)
        
        # 请求限制
        self.create_form_field(form_frame, "请求限制(ms):", "exchange.rateLimit", 
                              input_type="int", row=7)
    
    def create_system_tab(self, notebook):
        """创建系统配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="🖥️ 系统配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 版本
        self.create_form_field(form_frame, "版本:", "version", 
                              input_type="str", row=0, readonly=True)
        
        # 环境
        self.create_form_field(form_frame, "环境:", "environment", 
                              input_type="combo", row=1,
                              values=["development", "testing", "production"])
        
        # 调试模式
        self.create_form_field(form_frame, "调试模式:", "debug", 
                              input_type="bool", row=2)
    
    def create_logging_tab(self, notebook):
        """创建日志配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="📝 日志配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 日志级别
        self.create_form_field(form_frame, "日志级别:", "logging.level", 
                              input_type="combo", row=0,
                              values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        
        # 日志格式
        self.create_form_field(form_frame, "日志格式:", "logging.format", 
                              input_type="str", row=1)
        
        # 日志文件路径
        self.create_form_field(form_frame, "日志文件路径:", "logging.file_path", 
                              input_type="str", row=2)
        
        # 最大文件大小
        self.create_form_field(form_frame, "最大文件大小(字节):", "logging.max_file_size", 
                              input_type="int", row=3)
        
        # 备份数量
        self.create_form_field(form_frame, "备份数量:", "logging.backup_count", 
                              input_type="int", row=4)
    
    def create_monitoring_tab(self, notebook):
        """创建监控配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="📡 监控配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 启用警报
        self.create_form_field(form_frame, "启用警报:", "monitoring.enable_alerts", 
                              input_type="bool", row=0)
        
        # 警报邮箱
        self.create_form_field(form_frame, "警报邮箱:", "monitoring.alert_email", 
                              input_type="str", row=1)
        
        # 警报Webhook
        self.create_form_field(form_frame, "警报Webhook:", "monitoring.alert_webhook", 
                              input_type="str", row=2)
        
        # 检查间隔
        self.create_form_field(form_frame, "检查间隔(秒):", "monitoring.check_interval", 
                              input_type="int", row=3)
        
        # 性能阈值
        self.create_form_field(form_frame, "性能阈值:", "monitoring.performance_threshold", 
                              input_type="float", row=4)
    
    def create_optimization_tab(self, notebook):
        """创建优化配置选项卡"""
        tab = tk.Frame(notebook, bg='#2d2d2d')
        notebook.add(tab, text="🚀 优化配置")
        
        # 创建表单
        form_frame = tk.Frame(tab, bg='#2d2d2d')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 启用策略优化
        self.create_form_field(form_frame, "启用策略优化:", "optimization.enable_strategy_optimization", 
                              input_type="bool", row=0)
        
        # 启用智能提示
        self.create_form_field(form_frame, "启用智能提示:", "optimization.enable_smart_hints", 
                              input_type="bool", row=1)
        
        # 启用自动测试
        self.create_form_field(form_frame, "启用自动测试:", "optimization.enable_auto_testing", 
                              input_type="bool", row=2)
        
        # 优化频率
        self.create_form_field(form_frame, "优化频率:", "optimization.optimization_frequency", 
                              input_type="combo", row=3,
                              values=["daily", "weekly", "manual"])
        
        # 提示显示时间
        self.create_form_field(form_frame, "提示显示时间(秒):", "optimization.hint_display_duration", 
                              input_type="int", row=4)
        
        # 测试计划
        self.create_form_field(form_frame, "测试计划(HH:MM):", "optimization.testing_schedule", 
                              input_type="str", row=5)
        
        # 最大优化迭代次数
        self.create_form_field(form_frame, "最大优化迭代次数:", "optimization.max_optimization_iterations", 
                              input_type="int", row=6)
        
        # 性能阈值
        self.create_form_field(form_frame, "性能阈值:", "optimization.performance_threshold", 
                              input_type="float", row=7)
    
    def create_form_field(self, parent, label_text, config_key, input_type="str", 
                         row=0, readonly=False, values=None):
        """创建表单字段"""
        # 标签
        tk.Label(parent, text=label_text, bg='#2d2d2d', fg='white',
                font=('Arial', 10)).grid(row=row, column=0, sticky='w', pady=5)
        
        # 输入控件
        if input_type == "str" or input_type == "float" or input_type == "int":
            var = tk.StringVar()
            entry = tk.Entry(parent, textvariable=var, font=('Arial', 10),
                           bg='#3d3d3d', fg='white', insertbackground='white',
                           width=40, state='readonly' if readonly else 'normal')
            entry.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            # 存储变量和控件
            setattr(self, f"var_{config_key.replace('.', '_')}", var)
            setattr(self, f"widget_{config_key.replace('.', '_')}", entry)
            
        elif input_type == "password":
            var = tk.StringVar()
            entry = tk.Entry(parent, textvariable=var, font=('Arial', 10),
                           bg='#3d3d3d', fg='white', insertbackground='white',
                           width=40, show='*')
            entry.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            # 存储变量和控件
            setattr(self, f"var_{config_key.replace('.', '_')}", var)
            setattr(self, f"widget_{config_key.replace('.', '_')}", entry)
            
        elif input_type == "bool":
            var = tk.BooleanVar()
            check = tk.Checkbutton(parent, text="", variable=var,
                                 bg='#2d2d2d', fg='white', selectcolor='#4CAF50')
            check.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            # 存储变量和控件
            setattr(self, f"var_{config_key.replace('.', '_')}", var)
            setattr(self, f"widget_{config_key.replace('.', '_')}", check)
            
        elif input_type == "combo":
            var = tk.StringVar()
            combo = ttk.Combobox(parent, textvariable=var, font=('Arial', 10),
                               width=38, values=values, state='readonly')
            combo.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            # 存储变量和控件
            setattr(self, f"var_{config_key.replace('.', '_')}", var)
            setattr(self, f"widget_{config_key.replace('.', '_')}", combo)
            
        elif input_type == "list":
            var = tk.StringVar()
            entry = tk.Entry(parent, textvariable=var, font=('Arial', 10),
                           bg='#3d3d3d', fg='white', insertbackground='white',
                           width=40)
            entry.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            # 存储变量和控件
            setattr(self, f"var_{config_key.replace('.', '_')}", var)
            setattr(self, f"widget_{config_key.replace('.', '_')}", entry)
    
    def get_config_value(self, config_key):
        """获取配置值"""
        keys = config_key.split('.')
        value = self.config
        
        for key in keys:
            if hasattr(value, key):
                value = getattr(value, key)
            else:
                return None
        
        return value
    
    def set_config_value(self, config_key, value):
        """设置配置值"""
        keys = config_key.split('.')
        target = self.config
        
        # 遍历到倒数第二级
        for key in keys[:-1]:
            if hasattr(target, key):
                target = getattr(target, key)
            else:
                return False
        
        # 设置最后一级的值
        if hasattr(target, keys[-1]):
            setattr(target, keys[-1], value)
            return True
        
        return False
    
    def load_config_to_ui(self):
        """加载配置到界面"""
        # 遍历所有配置字段
        for attr_name in dir(self):
            if attr_name.startswith("var_"):
                config_key = attr_name[4:].replace('_', '.', 1)
                var = getattr(self, attr_name)
                value = self.get_config_value(config_key)
                
                if isinstance(var, tk.BooleanVar):
                    var.set(bool(value))
                elif isinstance(var, tk.StringVar):
                    if isinstance(value, list):
                        var.set(", ".join(map(str, value)))
                    else:
                        var.set(str(value) if value is not None else "")
    
    def save_config_from_ui(self):
        """从界面保存配置"""
        # 遍历所有配置字段
        for attr_name in dir(self):
            if attr_name.startswith("var_"):
                config_key = attr_name[4:].replace('_', '.', 1)
                var = getattr(self, attr_name)
                
                # 获取变量类型和值
                if isinstance(var, tk.BooleanVar):
                    value = var.get()
                elif isinstance(var, tk.StringVar):
                    value = var.get()
                    
                    # 处理特殊类型
                    if config_key.endswith('.trading_pairs'):
                        value = [item.strip() for item in value.split(',') if item.strip()]
                    elif config_key.endswith('.initial_capital') or \
                         config_key.endswith('.daily_loss_limit') or \
                         config_key.endswith('.max_position_size') or \
                         config_key.endswith('.stop_loss_pct') or \
                         config_key.endswith('.take_profit_pct') or \
                         config_key.endswith('.max_drawdown') or \
                         config_key.endswith('.min_trade_amount') or \
                         config_key.endswith('.performance_threshold'):
                        try:
                            value = float(value)
                        except ValueError:
                            messagebox.showerror("输入错误", f"{config_key} 必须是数字")
                            return False
                    elif config_key.endswith('.max_trades_per_day') or \
                         config_key.endswith('.max_file_size') or \
                         config_key.endswith('.backup_count') or \
                         config_key.endswith('.check_interval') or \
                         config_key.endswith('.hint_display_duration') or \
                         config_key.endswith('.max_optimization_iterations') or \
                         config_key.endswith('.timeout') or \
                         config_key.endswith('.rateLimit'):
                        try:
                            value = int(value)
                        except ValueError:
                            messagebox.showerror("输入错误", f"{config_key} 必须是整数")
                            return False
                
                # 设置配置值
                self.set_config_value(config_key, value)
        
        return True
    
    def save_config(self):
        """保存配置"""
        if self.save_config_from_ui():
            try:
                self.config_manager.save_config()
                messagebox.showinfo("保存成功", "配置已成功保存")
                self.dialog.destroy()
            except Exception as e:
                messagebox.showerror("保存失败", f"保存配置时出错: {e}")
    
    def export_config(self):
        """导出配置"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
            title="导出配置"
        )
        
        if file_path:
            try:
                # 先保存当前界面的配置
                if self.save_config_from_ui():
                    # 导出配置
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.config_manager.config, f, indent=2, ensure_ascii=False)
                    messagebox.showinfo("导出成功", f"配置已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出配置时出错: {e}")
    
    def import_config(self):
        """导入配置"""
        file_path = filedialog.askopenfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
            title="导入配置"
        )
        
        if file_path:
            try:
                # 导入配置
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
                
                # 更新配置
                self.config_manager.update_config(**imported_config)
                
                # 重新加载配置到界面
                self.load_config_to_ui()
                
                messagebox.showinfo("导入成功", "配置已成功导入")
            except Exception as e:
                messagebox.showerror("导入失败", f"导入配置时出错: {e}")
    
    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认重置", "确定要重置所有配置为默认值吗？"):
            try:
                # 创建新的默认配置
                self.config = self.config_manager._create_default_config()
                
                # 重新加载配置到界面
                self.load_config_to_ui()
                
                messagebox.showinfo("重置成功", "配置已重置为默认值")
            except Exception as e:
                messagebox.showerror("重置失败", f"重置配置时出错: {e}")
    
    def cancel(self):
        """取消操作"""
        self.dialog.destroy()
    
    def show(self):
        """显示对话框"""
        self.dialog.wait_window()

def show_config_dialog(parent):
    """显示配置对话框"""
    dialog = ConfigUI(parent)
    dialog.show()

if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.title("配置测试")
    root.geometry("300x200")
    
    tk.Button(root, text="打开配置", command=lambda: show_config_dialog(root)).pack(pady=20)
    
    root.mainloop()
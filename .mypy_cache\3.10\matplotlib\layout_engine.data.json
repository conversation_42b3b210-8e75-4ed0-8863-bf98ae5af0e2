{".class": "MypyFile", "_fullname": "matplotlib.layout_engine", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedLayoutEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.layout_engine.LayoutEngine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine", "name": "ConstrainedLayoutEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.layout_engine", "mro": ["matplotlib.layout_engine.ConstrainedLayoutEngine", "matplotlib.layout_engine.LayoutEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "h_pad", "w_pad", "hspace", "wspace", "rect", "compress", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "h_pad", "w_pad", "hspace", "wspace", "rect", "compress", "kwargs"], "arg_types": ["matplotlib.layout_engine.ConstrainedLayoutEngine", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConstrainedLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.layout_engine.ConstrainedLayoutEngine", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of ConstrainedLayoutEngine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "h_pad", "w_pad", "hspace", "wspace", "rect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "h_pad", "w_pad", "hspace", "wspace", "rect"], "arg_types": ["matplotlib.layout_engine.ConstrainedLayoutEngine", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of ConstrainedLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.layout_engine.ConstrainedLayoutEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.layout_engine.ConstrainedLayoutEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Figure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.Figure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LayoutEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.layout_engine.LayoutEngine", "name": "LayoutEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.LayoutEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.layout_engine", "mro": ["matplotlib.layout_engine.LayoutEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.LayoutEngine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["matplotlib.layout_engine.LayoutEngine", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjust_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.layout_engine.LayoutEngine.adjust_compatible", "name": "adjust_compatible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjust_compatible of LayoutEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.layout_engine.LayoutEngine.adjust_compatible", "name": "adjust_compatible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjust_compatible of LayoutEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "colorbar_gridspec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.layout_engine.LayoutEngine.colorbar_gridspec", "name": "colorbar_gridspec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorbar_gridspec of LayoutEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.layout_engine.LayoutEngine.colorbar_gridspec", "name": "colorbar_gridspec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorbar_gridspec of LayoutEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.LayoutEngine.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.layout_engine.LayoutEngine", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of LayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.LayoutEngine.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of LayoutEngine", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.LayoutEngine.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.layout_engine.LayoutEngine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of LayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.layout_engine.LayoutEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.layout_engine.LayoutEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PlaceHolderLayoutEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.layout_engine.LayoutEngine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.layout_engine.PlaceHolderLayoutEngine", "name": "PlaceHolderLayoutEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.PlaceHolderLayoutEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.layout_engine", "mro": ["matplotlib.layout_engine.PlaceHolderLayoutEngine", "matplotlib.layout_engine.LayoutEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "adjust_compatible", "colorbar_gridspec", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.PlaceHolderLayoutEngine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "adjust_compatible", "colorbar_gridspec", "kwargs"], "arg_types": ["matplotlib.layout_engine.PlaceHolderLayoutEngine", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PlaceHolderLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.PlaceHolderLayoutEngine.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.layout_engine.PlaceHolderLayoutEngine", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of PlaceHolderLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.layout_engine.PlaceHolderLayoutEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.layout_engine.PlaceHolderLayoutEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TightLayoutEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.layout_engine.LayoutEngine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.layout_engine.TightLayoutEngine", "name": "TightLayoutEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.TightLayoutEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.layout_engine", "mro": ["matplotlib.layout_engine.TightLayoutEngine", "matplotlib.layout_engine.LayoutEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "pad", "h_pad", "w_pad", "rect", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.TightLayoutEngine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "pad", "h_pad", "w_pad", "rect", "kwargs"], "arg_types": ["matplotlib.layout_engine.TightLayoutEngine", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TightLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.TightLayoutEngine.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["matplotlib.layout_engine.TightLayoutEngine", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of TightLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "pad", "w_pad", "h_pad", "rect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.layout_engine.TightLayoutEngine.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "pad", "w_pad", "h_pad", "rect"], "arg_types": ["matplotlib.layout_engine.TightLayoutEngine", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of TightLayoutEngine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.layout_engine.TightLayoutEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.layout_engine.TightLayoutEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.layout_engine.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\layout_engine.pyi"}
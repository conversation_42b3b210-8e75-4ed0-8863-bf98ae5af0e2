{".class": "MypyFile", "_fullname": "websockets.server", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CONNECTING": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.CONNECTING", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConnectionOption": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ConnectionOption", "kind": "Gdef", "module_public": false}, "Extension": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.Extension", "kind": "Gdef", "module_public": false}, "ExtensionHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionHeader", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef", "module_public": false}, "InvalidHandshake": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHandshake", "kind": "Gdef", "module_public": false}, "InvalidHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeader", "kind": "Gdef", "module_public": false}, "InvalidHeaderValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderValue", "kind": "Gdef", "module_public": false}, "InvalidMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidMessage", "kind": "Gdef", "module_public": false}, "InvalidOrigin": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidOrigin", "kind": "Gdef", "module_public": false}, "InvalidUpgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidUpgrade", "kind": "Gdef", "module_public": false}, "LoggerLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.LoggerLike", "kind": "Gdef", "module_public": false}, "MultipleValuesError": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.MultipleValuesError", "kind": "Gdef", "module_public": false}, "NegotiationError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.NegotiationError", "kind": "Gdef", "module_public": false}, "OPEN": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.OPEN", "kind": "Gdef", "module_public": false}, "Origin": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Origin", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.Protocol", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Response", "kind": "Gdef", "module_public": false}, "SERVER": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.SERVER", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "ServerConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.server.ServerProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.server.ServerConnection", "name": "ServerConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.server.ServerConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.server", "mro": ["websockets.server.ServerConnection", "websockets.server.ServerProtocol", "websockets.protocol.Protocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["websockets.server.ServerConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ServerConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.server.ServerConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.server.ServerConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServerExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ServerExtensionFactory", "kind": "Gdef", "module_public": false}, "ServerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.protocol.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.server.ServerProtocol", "name": "ServerProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.server", "mro": ["websockets.server.ServerProtocol", "websockets.protocol.Protocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "origins", "extensions", "subprotocols", "select_subprotocol", "state", "max_size", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "origins", "extensions", "subprotocols", "select_subprotocol", "state", "max_size", "logger"], "arg_types": ["websockets.server.ServerProtocol", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["websockets.typing.Origin", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ServerExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["websockets.server.ServerProtocol", {".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "websockets.protocol.State", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.LoggerLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ServerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["websockets.server.ServerProtocol", "websockets.http11.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept of ServerProtocol", "ret_type": "websockets.http11.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "available_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.server.ServerProtocol.available_extensions", "name": "available_extensions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ServerExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "available_subprotocols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.server.ServerProtocol.available_subprotocols", "name": "available_subprotocols", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "origins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.server.ServerProtocol.origins", "name": "origins", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["websockets.typing.Origin", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "websockets.server.ServerProtocol.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.server.ServerProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of ServerProtocol", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.process_extensions", "name": "process_extensions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["websockets.server.ServerProtocol", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_extensions of ServerProtocol", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.process_origin", "name": "process_origin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["websockets.server.ServerProtocol", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_origin of ServerProtocol", "ret_type": {".class": "UnionType", "items": ["websockets.typing.Origin", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.process_request", "name": "process_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["websockets.server.ServerProtocol", "websockets.http11.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_request of ServerProtocol", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_subprotocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.process_subprotocol", "name": "process_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["websockets.server.ServerProtocol", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_subprotocol of ServerProtocol", "ret_type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "status", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.reject", "name": "reject", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "status", "text"], "arg_types": ["websockets.server.ServerProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.StatusLike"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reject of ServerProtocol", "ret_type": "websockets.http11.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_subprotocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subprotocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.select_subprotocol", "name": "select_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subprotocols"], "arg_types": ["websockets.server.ServerProtocol", {".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_subprotocol of ServerProtocol", "ret_type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.server.ServerProtocol.send_response", "name": "send_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["websockets.server.ServerProtocol", "websockets.http11.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_response of ServerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.server.ServerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.server.ServerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "State": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.State", "kind": "Gdef", "module_public": false}, "StatusLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.StatusLike", "kind": "Gdef", "module_public": false}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef", "module_public": false}, "UpgradeProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.UpgradeProtocol", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.server.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.server.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "accept_key": {".class": "SymbolTableNode", "cross_ref": "websockets.utils.accept_key", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_public": false}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "build_extension": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_extension", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "email": {".class": "SymbolTableNode", "cross_ref": "email", "kind": "Gdef", "module_public": false}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef", "module_public": false}, "lazy_import": {".class": "SymbolTableNode", "cross_ref": "websockets.imports.lazy_import", "kind": "Gdef", "module_public": false}, "parse_connection": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_connection", "kind": "Gdef", "module_public": false}, "parse_extension": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_extension", "kind": "Gdef", "module_public": false}, "parse_subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_subprotocol", "kind": "Gdef", "module_public": false}, "parse_upgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_upgrade", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\server.py"}
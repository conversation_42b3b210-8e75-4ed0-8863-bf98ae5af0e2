{"data_mtime": 1748490882, "dep_lines": [1, 7, 8, 10, 11, 12, 14, 15, 16, 2, 3, 4, 6, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "numpy.typing", "PIL.Image", "matplotlib.axes", "matplotlib.colorizer", "matplotlib.backend_bases", "matplotlib.colors", "matplotlib.figure", "matplotlib.transforms", "os", "pathlib", "typing", "numpy", "PIL", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "re", "pprint", "copy", "inspect", "warnings", "operator", "html", "sys", "collections", "string", "itertools", "contextlib", "types", "traceback", "_frozen_importlib", "abc", "matplotlib.artist", "matplotlib.axes._axes", "matplotlib.axes._base", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence"], "hash": "b3877b8fe15e3c639d07709266280c712433c17a", "id": "matplotlib.image", "ignore_all": true, "interface_hash": "52c95f4f8e25302098f27dd135593f48309b1dd2", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\image.pyi", "plugin_data": null, "size": 7066, "suppressed": [], "version_id": "1.15.0"}
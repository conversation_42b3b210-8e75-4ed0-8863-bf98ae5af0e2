#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API登录对话框
API Login Dialog

提供安全的API凭证输入和连接界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from pathlib import Path
from gate_api_connector import APICredentials, gate_api

class APILoginDialog:
    """API登录对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        self.credentials = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔐 GATE.IO API登录")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.configure(bg='#2d2d2d')
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 加载保存的凭证
        self.load_saved_credentials()
    
    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.dialog, bg='#2d2d2d')
        title_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(title_frame, text="🔐 GATE.IO API连接",
                font=('Arial', 16, 'bold'), bg='#2d2d2d', fg='white').pack()
        
        tk.Label(title_frame, text="连接真实GATE.IO数据进行实战演练",
                font=('Arial', 10), bg='#2d2d2d', fg='#cccccc').pack(pady=(5, 0))
        
        # 环境选择
        env_frame = tk.LabelFrame(self.dialog, text="🌐 交易环境",
                                 font=('Arial', 12, 'bold'),
                                 bg='#2d2d2d', fg='white', padx=15, pady=15)
        env_frame.pack(fill='x', padx=20, pady=10)
        
        self.env_var = tk.StringVar(value="sandbox")
        
        tk.Radiobutton(env_frame, text="🧪 测试环境 (推荐)",
                      variable=self.env_var, value="sandbox",
                      bg='#2d2d2d', fg='white', selectcolor='#4CAF50',
                      font=('Arial', 10)).pack(anchor='w', pady=2)
        
        tk.Radiobutton(env_frame, text="🏦 生产环境 (真实交易)",
                      variable=self.env_var, value="production",
                      bg='#2d2d2d', fg='white', selectcolor='#f44336',
                      font=('Arial', 10)).pack(anchor='w', pady=2)
        
        # API凭证输入
        cred_frame = tk.LabelFrame(self.dialog, text="🔑 API凭证",
                                  font=('Arial', 12, 'bold'),
                                  bg='#2d2d2d', fg='white', padx=15, pady=15)
        cred_frame.pack(fill='x', padx=20, pady=10)
        
        # API Key
        tk.Label(cred_frame, text="API Key:", bg='#2d2d2d', fg='white',
                font=('Arial', 10)).pack(anchor='w', pady=(0, 5))
        
        self.api_key_var = tk.StringVar()
        api_key_entry = tk.Entry(cred_frame, textvariable=self.api_key_var,
                               font=('Arial', 10), bg='#3d3d3d', fg='white',
                               insertbackground='white', width=50)
        api_key_entry.pack(fill='x', pady=(0, 10))
        
        # Secret Key
        tk.Label(cred_frame, text="Secret Key:", bg='#2d2d2d', fg='white',
                font=('Arial', 10)).pack(anchor='w', pady=(0, 5))
        
        self.secret_key_var = tk.StringVar()
        secret_key_entry = tk.Entry(cred_frame, textvariable=self.secret_key_var,
                                  font=('Arial', 10), bg='#3d3d3d', fg='white',
                                  insertbackground='white', show='*', width=50)
        secret_key_entry.pack(fill='x', pady=(0, 10))
        
        # Passphrase (可选)
        tk.Label(cred_frame, text="Passphrase (可选):", bg='#2d2d2d', fg='white',
                font=('Arial', 10)).pack(anchor='w', pady=(0, 5))
        
        self.passphrase_var = tk.StringVar()
        passphrase_entry = tk.Entry(cred_frame, textvariable=self.passphrase_var,
                                  font=('Arial', 10), bg='#3d3d3d', fg='white',
                                  insertbackground='white', show='*', width=50)
        passphrase_entry.pack(fill='x', pady=(0, 10))
        
        # 保存凭证选项
        self.save_cred_var = tk.BooleanVar(value=True)
        tk.Checkbutton(cred_frame, text="💾 保存凭证 (加密存储)",
                      variable=self.save_cred_var,
                      bg='#2d2d2d', fg='white', selectcolor='#4CAF50',
                      font=('Arial', 10)).pack(anchor='w', pady=5)
        
        # 安全提示
        security_frame = tk.LabelFrame(self.dialog, text="🛡️ 安全提示",
                                     font=('Arial', 12, 'bold'),
                                     bg='#2d2d2d', fg='white', padx=15, pady=15)
        security_frame.pack(fill='x', padx=20, pady=10)
        
        security_tips = [
            "• 建议使用测试环境进行实战演练",
            "• API Key仅用于读取数据，不会执行真实交易",
            "• 凭证将加密保存在本地，不会上传到服务器",
            "• 可随时在GATE.IO官网撤销API权限"
        ]
        
        for tip in security_tips:
            tk.Label(security_frame, text=tip, bg='#2d2d2d', fg='#cccccc',
                    font=('Arial', 9), justify='left').pack(anchor='w', pady=1)
        
        # 获取API Key指导
        guide_frame = tk.LabelFrame(self.dialog, text="📖 获取API Key",
                                  font=('Arial', 12, 'bold'),
                                  bg='#2d2d2d', fg='white', padx=15, pady=15)
        guide_frame.pack(fill='x', padx=20, pady=10)
        
        guide_steps = [
            "1. 访问 gate.io 官网并登录",
            "2. 进入 账户设置 → API管理",
            "3. 创建新的API Key",
            "4. 权限设置：只勾选 '现货交易' 和 '查看'",
            "5. 复制API Key和Secret Key到上方"
        ]
        
        for step in guide_steps:
            tk.Label(guide_frame, text=step, bg='#2d2d2d', fg='#cccccc',
                    font=('Arial', 9), justify='left').pack(anchor='w', pady=1)
        
        # 按钮
        button_frame = tk.Frame(self.dialog, bg='#2d2d2d')
        button_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Button(button_frame, text="🌐 打开GATE.IO官网",
                 command=self.open_gate_website,
                 bg='#607D8B', fg='white', font=('Arial', 10),
                 relief='flat', padx=20, pady=8).pack(side='left')
        
        tk.Button(button_frame, text="❌ 取消",
                 command=self.cancel,
                 bg='#f44336', fg='white', font=('Arial', 10),
                 relief='flat', padx=20, pady=8).pack(side='right', padx=(10, 0))
        
        tk.Button(button_frame, text="🔗 连接",
                 command=self.connect,
                 bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=30, pady=8).pack(side='right')
    
    def open_gate_website(self):
        """打开GATE.IO官网"""
        import webbrowser
        webbrowser.open("https://www.gate.io/myaccount/apiv4keys")
    
    def load_saved_credentials(self):
        """加载保存的凭证"""
        try:
            cred_file = Path("config/api_credentials.json")
            if cred_file.exists():
                with open(cred_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 简单解密（实际应用中应使用更强的加密）
                self.api_key_var.set(self.simple_decrypt(data.get('api_key', '')))
                self.secret_key_var.set(self.simple_decrypt(data.get('secret_key', '')))
                self.passphrase_var.set(self.simple_decrypt(data.get('passphrase', '')))
                self.env_var.set(data.get('environment', 'sandbox'))
                
        except Exception as e:
            print(f"加载凭证失败: {e}")
    
    def save_credentials(self):
        """保存凭证"""
        try:
            if not self.save_cred_var.get():
                return
            
            # 确保config目录存在
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            
            # 简单加密保存
            data = {
                'api_key': self.simple_encrypt(self.api_key_var.get()),
                'secret_key': self.simple_encrypt(self.secret_key_var.get()),
                'passphrase': self.simple_encrypt(self.passphrase_var.get()),
                'environment': self.env_var.get()
            }
            
            with open(config_dir / "api_credentials.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"保存凭证失败: {e}")
    
    def simple_encrypt(self, text: str) -> str:
        """使用更安全的加密方法"""
        if not text:
            return ""
        try:
            import base64
            import hashlib
            from cryptography.fernet import Fernet
            
            # 创建一个基于固定密钥的加密器
            # 在实际应用中，应该使用系统密钥库或环境变量存储密钥
            key = hashlib.sha256("trading_system_security_key".encode('utf-8')).digest()
            f = Fernet(base64.urlsafe_b64encode(key))
            
            # 加密文本
            encrypted = f.encrypt(text.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        except ImportError:
            # 如果没有安装cryptography库，回退到简单加密
            print("警告: 未安装cryptography库，使用简单加密")
            return ''.join(chr(ord(c) + 1) for c in text)
    
    def simple_decrypt(self, text: str) -> str:
        """使用更安全的解密方法"""
        if not text:
            return ""
        try:
            import base64
            import hashlib
            from cryptography.fernet import Fernet
            
            # 创建一个基于固定密钥的解密器
            key = hashlib.sha256("trading_system_security_key".encode('utf-8')).digest()
            f = Fernet(base64.urlsafe_b64encode(key))
            
            # 解密文本
            try:
                encrypted = base64.urlsafe_b64decode(text)
                decrypted = f.decrypt(encrypted)
                return decrypted.decode('utf-8')
            except Exception:
                # 如果解密失败，可能是旧格式的加密，尝试旧方法
                return ''.join(chr(ord(c) - 1) for c in text)
        except ImportError:
            # 如果没有安装cryptography库，回退到简单解密
            print("警告: 未安装cryptography库，使用简单解密")
            return ''.join(chr(ord(c) - 1) for c in text)
    
    def validate_inputs(self) -> bool:
        """验证输入"""
        if not self.api_key_var.get().strip():
            messagebox.showerror("输入错误", "请输入API Key")
            return False
        
        if not self.secret_key_var.get().strip():
            messagebox.showerror("输入错误", "请输入Secret Key")
            return False
        
        return True
    
    def connect(self):
        """连接API"""
        if not self.validate_inputs():
            return
        
        try:
            # 创建凭证对象
            self.credentials = APICredentials(
                api_key=self.api_key_var.get().strip(),
                secret_key=self.secret_key_var.get().strip(),
                passphrase=self.passphrase_var.get().strip(),
                sandbox=(self.env_var.get() == "sandbox")
            )
            
            # 显示连接进度
            progress_dialog = self.show_progress_dialog()
            
            # 尝试连接
            success, message = gate_api.connect(self.credentials)
            
            # 关闭进度对话框
            progress_dialog.destroy()
            
            if success:
                # 保存凭证
                self.save_credentials()
                
                # 显示成功信息
                env_text = "测试环境" if self.credentials.sandbox else "生产环境"
                messagebox.showinfo("连接成功", 
                                  f"✅ 成功连接到GATE.IO {env_text}\n\n"
                                  f"现在可以使用真实市场数据进行实战演练")
                
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("连接失败", f"❌ {message}\n\n请检查API凭证是否正确")
                
        except Exception as e:
            messagebox.showerror("连接错误", f"连接过程中出现错误:\n{str(e)}")
    
    def show_progress_dialog(self):
        """显示连接进度对话框"""
        progress = tk.Toplevel(self.dialog)
        progress.title("连接中...")
        progress.geometry("300x150")
        progress.resizable(False, False)
        progress.configure(bg='#2d2d2d')
        progress.transient(self.dialog)
        progress.grab_set()
        
        # 居中显示
        x = (progress.winfo_screenwidth() // 2) - (300 // 2)
        y = (progress.winfo_screenheight() // 2) - (150 // 2)
        progress.geometry(f"300x150+{x}+{y}")
        
        tk.Label(progress, text="🔗 正在连接GATE.IO...",
                font=('Arial', 12), bg='#2d2d2d', fg='white').pack(pady=20)
        
        # 进度条
        progress_bar = ttk.Progressbar(progress, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()
        
        tk.Label(progress, text="请稍候，正在验证API凭证",
                font=('Arial', 10), bg='#2d2d2d', fg='#cccccc').pack()
        
        progress.update()
        return progress
    
    def cancel(self):
        """取消连接"""
        self.result = False
        self.dialog.destroy()
    
    def show(self) -> bool:
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result or False

def show_api_login_dialog(parent) -> bool:
    """显示API登录对话框"""
    dialog = APILoginDialog(parent)
    return dialog.show()

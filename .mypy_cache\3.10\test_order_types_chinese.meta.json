{"data_mtime": 1748499815, "dep_lines": [25, 67, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["core.constants.chinese_ui_constants", "core.professional_trading_gui", "sys", "os", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "core", "core.constants", "typing", "typing_extensions"], "hash": "9c2129146cd18f68e1ab95544baf9c629ff2c6d5", "id": "test_order_types_chinese", "ignore_all": false, "interface_hash": "fd1c78aa8a80814b50709c5c7922fa04732ea152", "mtime": 1748499813, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\test_order_types_chinese.py", "plugin_data": null, "size": 10349, "suppressed": [], "version_id": "1.15.0"}
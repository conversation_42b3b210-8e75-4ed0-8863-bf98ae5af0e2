#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代码质量检查工具
Code Quality Checker

检测硬编码和代码质量问题
"""

import ast
import re
import os
from pathlib import Path
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CodeIssue:
    """代码问题"""
    file_path: str
    line_number: int
    issue_type: str
    description: str
    severity: str  # HIGH, MEDIUM, LOW
    suggestion: str


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root: str):
        """
        初始化代码质量检查器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = Path(project_root)
        self.issues: List[CodeIssue] = []
        
        # 硬编码检测规则
        self.hardcode_patterns = {
            # 数字硬编码 (排除常见的0, 1, -1等)
            'magic_numbers': re.compile(r'\b(?<![\w.])[2-9]\d*(?:\.\d+)?\b(?![\w.])'),
            
            # 字符串硬编码 (排除空字符串和单字符)
            'hardcoded_strings': re.compile(r'["\'][^"\']{2,}["\']'),
            
            # 颜色值硬编码
            'color_codes': re.compile(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}'),
            
            # 文件路径硬编码
            'file_paths': re.compile(r'["\'][^"\']*[/\\][^"\']*["\']'),
            
            # URL硬编码
            'urls': re.compile(r'https?://[^\s"\']+'),
            
            # 尺寸硬编码
            'dimensions': re.compile(r'\b\d+x\d+\b'),
        }
        
        # 排除的文件和目录
        self.exclude_patterns = {
            '__pycache__',
            '.git',
            '.pytest_cache',
            'node_modules',
            '.venv',
            'venv',
            '.idea',
            '.vscode'
        }
        
        # 排除的文件扩展名
        self.exclude_extensions = {
            '.pyc', '.pyo', '.pyd', '.so', '.dll',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp',
            '.mp3', '.mp4', '.avi', '.mov',
            '.zip', '.tar', '.gz', '.rar'
        }
    
    def check_project(self) -> List[CodeIssue]:
        """检查整个项目"""
        self.issues.clear()
        
        # 遍历项目文件
        for file_path in self._get_python_files():
            self._check_file(file_path)
        
        return self.issues
    
    def _get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除指定目录
            dirs[:] = [d for d in dirs if d not in self.exclude_patterns]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    if file_path.suffix not in self.exclude_extensions:
                        python_files.append(file_path)
        
        return python_files
    
    def _check_file(self, file_path: Path):
        """检查单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # 检查硬编码
            self._check_hardcoded_values(file_path, lines)
            
            # 检查AST
            try:
                tree = ast.parse(content)
                self._check_ast(file_path, tree, lines)
            except SyntaxError as e:
                self.issues.append(CodeIssue(
                    file_path=str(file_path),
                    line_number=e.lineno or 0,
                    issue_type="SYNTAX_ERROR",
                    description=f"语法错误: {e.msg}",
                    severity="HIGH",
                    suggestion="修复语法错误"
                ))
        
        except Exception as e:
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=0,
                issue_type="FILE_ERROR",
                description=f"文件读取错误: {e}",
                severity="MEDIUM",
                suggestion="检查文件编码和权限"
            ))
    
    def _check_hardcoded_values(self, file_path: Path, lines: List[str]):
        """检查硬编码值"""
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 跳过注释和空行
            if not line_stripped or line_stripped.startswith('#'):
                continue
            
            # 检查魔法数字
            self._check_magic_numbers(file_path, line_num, line)
            
            # 检查硬编码字符串
            self._check_hardcoded_strings(file_path, line_num, line)
            
            # 检查颜色代码
            self._check_color_codes(file_path, line_num, line)
            
            # 检查文件路径
            self._check_file_paths(file_path, line_num, line)
            
            # 检查URL
            self._check_urls(file_path, line_num, line)
            
            # 检查尺寸
            self._check_dimensions(file_path, line_num, line)
    
    def _check_magic_numbers(self, file_path: Path, line_num: int, line: str):
        """检查魔法数字"""
        # 排除常见的安全数字
        safe_numbers = {'0', '1', '-1', '2', '10', '100', '1000'}
        
        # 排除在特定上下文中的数字
        if any(keyword in line for keyword in ['range(', 'sleep(', 'time.sleep(']):
            return
        
        matches = self.hardcode_patterns['magic_numbers'].findall(line)
        for match in matches:
            if match not in safe_numbers:
                self.issues.append(CodeIssue(
                    file_path=str(file_path),
                    line_number=line_num,
                    issue_type="MAGIC_NUMBER",
                    description=f"魔法数字: {match}",
                    severity="MEDIUM",
                    suggestion=f"将数字 {match} 定义为常量"
                ))
    
    def _check_hardcoded_strings(self, file_path: Path, line_num: int, line: str):
        """检查硬编码字符串"""
        # 排除导入语句、文档字符串等
        if any(keyword in line for keyword in ['import ', 'from ', '"""', "'''"]):
            return
        
        matches = self.hardcode_patterns['hardcoded_strings'].findall(line)
        for match in matches:
            # 排除短字符串和特殊字符串
            content = match[1:-1]  # 去掉引号
            if len(content) < 3 or content in ['', ' ', '\n', '\t']:
                continue
            
            # 排除格式字符串
            if '{' in content and '}' in content:
                continue
            
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=line_num,
                issue_type="HARDCODED_STRING",
                description=f"硬编码字符串: {match}",
                severity="LOW",
                suggestion="将字符串移到配置文件或常量中"
            ))
    
    def _check_color_codes(self, file_path: Path, line_num: int, line: str):
        """检查颜色代码"""
        matches = self.hardcode_patterns['color_codes'].findall(line)
        for match in matches:
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=line_num,
                issue_type="HARDCODED_COLOR",
                description=f"硬编码颜色: {match}",
                severity="MEDIUM",
                suggestion="将颜色定义移到主题配置中"
            ))
    
    def _check_file_paths(self, file_path: Path, line_num: int, line: str):
        """检查文件路径"""
        matches = self.hardcode_patterns['file_paths'].findall(line)
        for match in matches:
            # 排除相对路径和模块导入
            content = match[1:-1]
            if content.startswith('.') or '/' not in content and '\\' not in content:
                continue
            
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=line_num,
                issue_type="HARDCODED_PATH",
                description=f"硬编码路径: {match}",
                severity="HIGH",
                suggestion="使用路径常量或配置文件"
            ))
    
    def _check_urls(self, file_path: Path, line_num: int, line: str):
        """检查URL"""
        matches = self.hardcode_patterns['urls'].findall(line)
        for match in matches:
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=line_num,
                issue_type="HARDCODED_URL",
                description=f"硬编码URL: {match}",
                severity="MEDIUM",
                suggestion="将URL移到配置文件中"
            ))
    
    def _check_dimensions(self, file_path: Path, line_num: int, line: str):
        """检查尺寸"""
        matches = self.hardcode_patterns['dimensions'].findall(line)
        for match in matches:
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=line_num,
                issue_type="HARDCODED_DIMENSION",
                description=f"硬编码尺寸: {match}",
                severity="MEDIUM",
                suggestion="将尺寸定义为常量"
            ))
    
    def _check_ast(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查AST节点"""
        for node in ast.walk(tree):
            # 检查函数长度
            if isinstance(node, ast.FunctionDef):
                self._check_function_length(file_path, node, lines)
            
            # 检查类长度
            elif isinstance(node, ast.ClassDef):
                self._check_class_length(file_path, node, lines)
            
            # 检查复杂的条件表达式
            elif isinstance(node, ast.If):
                self._check_complex_conditions(file_path, node, lines)
    
    def _check_function_length(self, file_path: Path, node: ast.FunctionDef, lines: List[str]):
        """检查函数长度"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            length = node.end_lineno - node.lineno + 1
            if length > 50:  # 函数超过50行
                self.issues.append(CodeIssue(
                    file_path=str(file_path),
                    line_number=node.lineno,
                    issue_type="LONG_FUNCTION",
                    description=f"函数 {node.name} 过长 ({length} 行)",
                    severity="MEDIUM",
                    suggestion="考虑将函数拆分为更小的函数"
                ))
    
    def _check_class_length(self, file_path: Path, node: ast.ClassDef, lines: List[str]):
        """检查类长度"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            length = node.end_lineno - node.lineno + 1
            if length > 200:  # 类超过200行
                self.issues.append(CodeIssue(
                    file_path=str(file_path),
                    line_number=node.lineno,
                    issue_type="LONG_CLASS",
                    description=f"类 {node.name} 过长 ({length} 行)",
                    severity="MEDIUM",
                    suggestion="考虑将类拆分或重构"
                ))
    
    def _check_complex_conditions(self, file_path: Path, node: ast.If, lines: List[str]):
        """检查复杂条件"""
        # 简单检查：如果条件包含多个 and/or
        condition_str = lines[node.lineno - 1] if node.lineno <= len(lines) else ""
        and_count = condition_str.count(' and ')
        or_count = condition_str.count(' or ')
        
        if and_count + or_count > 3:  # 超过3个逻辑操作符
            self.issues.append(CodeIssue(
                file_path=str(file_path),
                line_number=node.lineno,
                issue_type="COMPLEX_CONDITION",
                description="条件表达式过于复杂",
                severity="LOW",
                suggestion="考虑将复杂条件拆分为多个变量"
            ))
    
    def generate_report(self) -> str:
        """生成检查报告"""
        if not self.issues:
            return "🎉 未发现代码质量问题！"
        
        # 按严重程度分组
        high_issues = [i for i in self.issues if i.severity == "HIGH"]
        medium_issues = [i for i in self.issues if i.severity == "MEDIUM"]
        low_issues = [i for i in self.issues if i.severity == "LOW"]
        
        report = f"""
📊 代码质量检查报告
{'='*60}

⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📁 项目路径: {self.project_root}

📈 问题统计:
• 🔴 严重问题: {len(high_issues)}
• 🟡 中等问题: {len(medium_issues)}
• 🟢 轻微问题: {len(low_issues)}
• 📊 总计问题: {len(self.issues)}

"""
        
        # 详细问题列表
        for severity, issues, icon in [
            ("HIGH", high_issues, "🔴"),
            ("MEDIUM", medium_issues, "🟡"),
            ("LOW", low_issues, "🟢")
        ]:
            if issues:
                report += f"\n{icon} {severity} 级别问题:\n"
                report += "-" * 40 + "\n"
                
                for issue in issues:
                    report += f"""
文件: {issue.file_path}
行号: {issue.line_number}
类型: {issue.issue_type}
描述: {issue.description}
建议: {issue.suggestion}
"""
        
        # 统计信息
        issue_types = {}
        for issue in self.issues:
            issue_types[issue.issue_type] = issue_types.get(issue.issue_type, 0) + 1
        
        report += f"\n📊 问题类型统计:\n"
        report += "-" * 40 + "\n"
        for issue_type, count in sorted(issue_types.items()):
            report += f"• {issue_type}: {count}\n"
        
        return report
    
    def get_summary(self) -> Dict[str, int]:
        """获取问题摘要"""
        summary = {
            "total": len(self.issues),
            "high": len([i for i in self.issues if i.severity == "HIGH"]),
            "medium": len([i for i in self.issues if i.severity == "MEDIUM"]),
            "low": len([i for i in self.issues if i.severity == "LOW"])
        }
        
        # 按类型统计
        for issue in self.issues:
            summary[issue.issue_type] = summary.get(issue.issue_type, 0) + 1
        
        return summary


def main():
    """主函数"""
    import sys
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    print("🔍 开始代码质量检查...")
    print(f"📁 项目路径: {project_root}")
    
    # 创建检查器
    checker = CodeQualityChecker(project_root)
    
    # 执行检查
    issues = checker.check_project()
    
    # 生成报告
    report = checker.generate_report()
    print(report)
    
    # 保存报告
    report_file = Path(project_root) / "code_quality_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 报告已保存到: {report_file}")
    
    # 返回退出码
    summary = checker.get_summary()
    if summary["high"] > 0:
        sys.exit(2)  # 有严重问题
    elif summary["medium"] > 0:
        sys.exit(1)  # 有中等问题
    else:
        sys.exit(0)  # 无问题或仅有轻微问题


if __name__ == "__main__":
    main()

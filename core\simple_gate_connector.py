#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版Gate.io数据连接器
Simple Gate.io Data Connector

专为终极版交易系统GUI设计的轻量级连接器
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional

import requests

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleGateConnector:
    """简化版Gate.io数据连接器"""

    def __init__(self):
        """初始化连接器"""
        self.base_url = "https://api.gateio.ws/api/v4"
        self.is_connected = False

        # 数据缓存
        self.tickers = {}
        self.last_update = None

        # 支持的交易对
        self.supported_pairs = ["BTC_USDT", "ETH_USDT", "GT_USDT"]

        logger.info("简化版Gate.io连接器初始化完成")

    def connect(self) -> bool:
        """连接Gate.io API"""
        try:
            # 测试连接
            response = requests.get(
                f"{self.base_url}/spot/currencies", timeout=10
            )

            if response.status_code == 200:
                self.is_connected = True
                logger.info("Gate.io API连接成功")
                return True
            else:
                logger.error(f"Gate.io API连接失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"连接Gate.io API异常: {e}")
            return False

    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """获取实时价格"""
        try:
            # 转换交易对格式
            gate_symbol = symbol.replace("/", "_")

            url = f"{self.base_url}/spot/tickers"
            params = {"currency_pair": gate_symbol}

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data:
                    ticker_data = data[0]

                    # 标准化数据格式
                    ticker = {
                        "symbol": symbol,
                        "timestamp": int(time.time() * 1000),
                        "datetime": datetime.now().isoformat(),
                        "last": float(ticker_data.get("last", 0)),
                        "bid": float(ticker_data.get("highest_bid", 0)),
                        "ask": float(ticker_data.get("lowest_ask", 0)),
                        "high": float(ticker_data.get("high_24h", 0)),
                        "low": float(ticker_data.get("low_24h", 0)),
                        "volume": float(ticker_data.get("base_volume", 0)),
                        "change": float(
                            ticker_data.get("change_percentage", 0)
                        ),
                        "percentage": float(
                            ticker_data.get("change_percentage", 0)
                        ),
                    }

                    self.tickers[symbol] = ticker
                    self.last_update = datetime.now()

                    return ticker

            return None

        except Exception as e:
            logger.error(f"获取{symbol}价格失败: {e}")
            return None

    def get_multiple_tickers(self, symbols: List[str]) -> Dict[str, Dict]:
        """批量获取价格"""
        try:
            url = f"{self.base_url}/spot/tickers"
            response = requests.get(url, timeout=15)

            if response.status_code == 200:
                all_tickers = response.json()
                result = {}

                for symbol in symbols:
                    gate_symbol = symbol.replace("/", "_")

                    # 查找对应的ticker
                    for ticker_data in all_tickers:
                        if ticker_data.get("currency_pair") == gate_symbol:
                            ticker = {
                                "symbol": symbol,
                                "timestamp": int(time.time() * 1000),
                                "datetime": datetime.now().isoformat(),
                                "last": float(ticker_data.get("last", 0)),
                                "bid": float(
                                    ticker_data.get("highest_bid", 0)
                                ),
                                "ask": float(ticker_data.get("lowest_ask", 0)),
                                "high": float(ticker_data.get("high_24h", 0)),
                                "low": float(ticker_data.get("low_24h", 0)),
                                "volume": float(
                                    ticker_data.get("base_volume", 0)
                                ),
                                "change": float(
                                    ticker_data.get("change_percentage", 0)
                                ),
                                "percentage": float(
                                    ticker_data.get("change_percentage", 0)
                                ),
                            }

                            result[symbol] = ticker
                            self.tickers[symbol] = ticker
                            break

                self.last_update = datetime.now()
                return result

            return {}

        except Exception as e:
            logger.error(f"批量获取价格失败: {e}")
            return {}

    def get_klines(
        self, symbol: str, interval: str = "15m", limit: int = 100
    ) -> Optional[List[List]]:
        """获取K线数据"""
        try:
            gate_symbol = symbol.replace("/", "_")

            # 时间间隔映射
            interval_map = {
                "1m": "1m",
                "5m": "5m",
                "15m": "15m",
                "30m": "30m",
                "1h": "1h",
                "4h": "4h",
                "1d": "1d",
            }

            gate_interval = interval_map.get(interval, "15m")

            url = f"{self.base_url}/spot/candlesticks"
            params = {
                "currency_pair": gate_symbol,
                "interval": gate_interval,
                "limit": limit,
            }

            response = requests.get(url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()

                # 转换数据格式 [timestamp, open, high, low, close, volume]
                klines = []
                for item in data:
                    kline = [
                        int(item[0]) * 1000,  # timestamp (转换为毫秒)
                        float(item[5]),  # open
                        float(item[3]),  # high
                        float(item[4]),  # low
                        float(item[2]),  # close
                        float(item[1]),  # volume
                    ]
                    klines.append(kline)

                return klines

            return None

        except Exception as e:
            logger.error(f"获取{symbol} K线数据失败: {e}")
            return None

    def calculate_simple_signals(self, symbol: str) -> Dict:
        """计算简单交易信号"""
        try:
            # 获取K线数据
            klines = self.get_klines(symbol, "15m", 50)
            if not klines or len(klines) < 20:
                return {"signal": 0, "reason": "数据不足"}

            # 计算简单移动平均
            closes = [k[4] for k in klines[-20:]]  # 最近20个收盘价
            ma_5 = sum(closes[-5:]) / 5
            ma_10 = sum(closes[-10:]) / 10
            ma_20 = sum(closes) / 20

            current_price = closes[-1]
            prev_price = closes[-2]

            # 简单信号逻辑
            signals = []
            reasons = []

            # 金叉信号
            if ma_5 > ma_10 > ma_20 and current_price > ma_5:
                signals.append(1)
                reasons.append("均线金叉")

            # 突破信号
            if current_price > max(closes[-10:-1]):
                signals.append(1)
                reasons.append("价格突破")

            # 死叉信号
            if ma_5 < ma_10 and current_price < ma_5:
                signals.append(-1)
                reasons.append("均线死叉")

            # 综合信号
            if signals:
                final_signal = 1 if sum(signals) > 0 else -1
                return {
                    "signal": final_signal,
                    "strength": abs(sum(signals)),
                    "reasons": reasons,
                    "price": current_price,
                    "ma_5": ma_5,
                    "ma_10": ma_10,
                    "ma_20": ma_20,
                }
            else:
                return {"signal": 0, "reason": "无明确信号"}

        except Exception as e:
            logger.error(f"计算{symbol}交易信号失败: {e}")
            return {"signal": 0, "reason": f"错误: {e}"}

    def get_market_summary(self) -> Dict:
        """获取市场概况"""
        try:
            symbols = ["BTC/USDT", "ETH/USDT", "GT/USDT"]
            tickers = self.get_multiple_tickers(symbols)

            summary = {
                "timestamp": datetime.now(),
                "exchange": "Gate.io",
                "total_pairs": len(symbols),
                "active_pairs": len(tickers),
                "market_data": {},
            }

            for symbol, ticker in tickers.items():
                summary["market_data"][symbol] = {
                    "price": ticker["last"],
                    "change": ticker["change"],
                    "percentage": ticker["percentage"],
                    "volume": ticker["volume"],
                }

            return summary

        except Exception as e:
            logger.error(f"获取市场概况失败: {e}")
            return {}

    def get_status(self) -> Dict:
        """获取连接状态"""
        return {
            "connected": self.is_connected,
            "exchange": "Gate.io (Simple)",
            "supported_pairs": len(self.supported_pairs),
            "active_tickers": len(self.tickers),
            "last_update": self.last_update,
            "api_url": self.base_url,
        }


def test_simple_connector():
    """测试简化版连接器"""
    print("🔄 测试简化版Gate.io连接器")
    print("=" * 50)

    connector = SimpleGateConnector()

    # 测试连接
    print("步骤1: 连接API...")
    if connector.connect():
        print("✅ API连接成功")
    else:
        print("❌ API连接失败")
        return

    # 测试获取单个价格
    print("\n步骤2: 获取BTC价格...")
    ticker = connector.get_ticker("BTC/USDT")
    if ticker:
        print(
            f"✅ BTC/USDT: {ticker['last']:.2f} USDT ({ticker['percentage']:+.2f}%)"
        )

    # 测试批量获取价格
    print("\n步骤3: 批量获取价格...")
    symbols = ["BTC/USDT", "ETH/USDT", "GT/USDT"]
    tickers = connector.get_multiple_tickers(symbols)
    for symbol, ticker in tickers.items():
        print(
            f"✅ {symbol}: {ticker['last']:.2f} ({ticker['percentage']:+.2f}%)"
        )

    # 测试交易信号
    print("\n步骤4: 获取交易信号...")
    signals = connector.calculate_simple_signals("BTC/USDT")
    print(f"✅ BTC交易信号: {signals}")

    # 测试市场概况
    print("\n步骤5: 获取市场概况...")
    summary = connector.get_market_summary()
    print(f"✅ 活跃交易对: {summary.get('active_pairs', 0)}")

    print("\n" + "=" * 50)
    print("🎉 简化版Gate.io连接器测试完成！")


if __name__ == "__main__":
    test_simple_connector()

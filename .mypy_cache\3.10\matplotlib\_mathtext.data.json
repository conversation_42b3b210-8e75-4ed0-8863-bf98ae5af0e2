{".class": "MypyFile", "_fullname": "matplotlib._mathtext", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Accent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Char"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Accent", "name": "Accent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Accent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Accent", "matplotlib._mathtext.Char", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "_update_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Accent._update_metrics", "name": "_update_metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Accent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_metrics of Accent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Accent.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "arg_types": ["matplotlib._mathtext.Accent", "matplotlib._mathtext.Output", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Accent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Accent.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Accent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of Accent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Accent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Accent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoHeightChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Hlist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.AutoHeightChar", "name": "AutoHeightChar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.AutoHeightChar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.AutoHeightChar", "matplotlib._mathtext.Hlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "c", "height", "depth", "state", "always", "factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.AutoHeightChar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "c", "height", "depth", "state", "always", "factor"], "arg_types": ["matplotlib._mathtext.AutoHeightChar", "builtins.str", "builtins.float", "builtins.float", "matplotlib._mathtext.ParserState", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoHeightChar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.AutoHeightChar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.AutoHeightChar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoWidthChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Hlist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.AutoWidthChar", "name": "AutoWidthChar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.AutoWidthChar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.AutoWidthChar", "matplotlib._mathtext.Hlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "c", "width", "state", "always", "char_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.AutoWidthChar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "c", "width", "state", "always", "char_class"], "arg_types": ["matplotlib._mathtext.AutoWidthChar", "builtins.str", "builtins.float", "matplotlib._mathtext.ParserState", "builtins.bool", {".class": "TypeType", "item": "matplotlib._mathtext.Char"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoWidthChar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.AutoWidthChar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.AutoWidthChar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BakomaFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.TruetypeFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.BakomaFonts", "name": "BakomaFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.BakomaFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.BakomaFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.BakomaFonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.BakomaFonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BakomaFonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fontmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.BakomaFonts._fontmap", "name": "_fontmap", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_get_glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.BakomaFonts._get_glyph", "name": "_get_glyph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "arg_types": ["matplotlib._mathtext.BakomaFonts", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_glyph of BakomaFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_size_alternatives": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.BakomaFonts._size_alternatives", "name": "_size_alternatives", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_slanted_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.BakomaFonts._slanted_symbols", "name": "_slanted_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_stix_fallback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.BakomaFonts._stix_fallback", "name": "_stix_fallback", "type": "matplotlib._mathtext.StixFonts"}}, "alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.BakomaFonts.alias", "name": "alias", "type": "builtins.str"}}, "get_sized_alternatives_for_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.BakomaFonts.get_sized_alternatives_for_symbol", "name": "get_sized_alternatives_for_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "arg_types": ["matplotlib._mathtext.BakomaFonts", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sized_alternatives_for_symbol of BakomaFonts", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.BakomaFonts.target", "name": "target", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.BakomaFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.BakomaFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Box": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Box", "name": "Box", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Box", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "width", "height", "depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Box.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "width", "height", "depth"], "arg_types": ["matplotlib._mathtext.Box", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Box", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Box.depth", "name": "depth", "type": "builtins.float"}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Box.height", "name": "height", "type": "builtins.float"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x1", "y1", "x2", "y2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Box.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x1", "y1", "x2", "y2"], "arg_types": ["matplotlib._mathtext.Box", "matplotlib._mathtext.Output", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Box", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Box.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Box"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of Box", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Box.width", "name": "width", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Box.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Box", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Char": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Char", "name": "Char", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Char", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "state"], "arg_types": ["matplotlib._mathtext.Char", "builtins.str", "matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>r", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib._mathtext.Char"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of <PERSON>r", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metrics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char._metrics", "name": "_metrics", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": "matplotlib._mathtext.FontMetrics"}}}, "_update_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char._update_metrics", "name": "_update_metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Char"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_metrics of Char", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "c": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.c", "name": "c", "type": "builtins.str"}}, "depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.depth", "name": "depth", "type": "builtins.float"}}, "dpi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.dpi", "name": "dpi", "type": "builtins.float"}}, "font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.font", "name": "font", "type": "builtins.str"}}, "font_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.font_class", "name": "font_class", "type": "builtins.str"}}, "fontset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.fontset", "name": "fontset", "type": "matplotlib._mathtext.Fonts"}}, "fontsize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.fontsize", "name": "fontsize", "type": "builtins.float"}}, "get_kerning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "next"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.get_kerning", "name": "get_kerning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "next"], "arg_types": ["matplotlib._mathtext.Char", {".class": "UnionType", "items": ["matplotlib._mathtext.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kerning of Char", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.height", "name": "height", "type": "builtins.float"}}, "is_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.is_slanted", "name": "is_slanted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Char"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_slanted of Char", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "arg_types": ["matplotlib._mathtext.Char", "matplotlib._mathtext.Output", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Char", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Char.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Char"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of <PERSON>r", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Char.width", "name": "width", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Char.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Char", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComputerModernFontConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.FontConstantsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.ComputerModernFontConstants", "name": "ComputerModernFontConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ComputerModernFontConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.ComputerModernFontConstants", "matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable", "delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.delta", "name": "delta", "type": "builtins.float"}}, "delta_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.delta_integral", "name": "delta_integral", "type": "builtins.float"}}, "delta_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.delta_slanted", "name": "delta_slanted", "type": "builtins.float"}}, "script_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.script_space", "name": "script_space", "type": "builtins.float"}}, "sub1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.sub1", "name": "sub1", "type": "builtins.float"}}, "sub2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.sub2", "name": "sub2", "type": "builtins.float"}}, "subdrop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.subdrop", "name": "subdrop", "type": "builtins.float"}}, "sup1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.ComputerModernFontConstants.sup1", "name": "sup1", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.ComputerModernFontConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.ComputerModernFontConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DejaVuFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.UnicodeFonts"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.DejaVuFonts", "name": "DejaVuFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.DejaVuFonts", "matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuFonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.DejaVuFonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DejaVuFonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fontmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.DejaVuFonts._fontmap", "name": "_fontmap", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_get_glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuFonts._get_glyph", "name": "_get_glyph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "arg_types": ["matplotlib._mathtext.DejaVuFonts", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_glyph of DejaVuFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bakoma": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.DejaVuFonts.bakoma", "name": "bakoma", "type": "matplotlib._mathtext.BakomaFonts"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.DejaVuFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.DejaVuFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DejaVuSansFontConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.FontConstantsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.DejaVuSansFontConstants", "name": "DejaVuSansFontConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuSansFontConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.DejaVuSansFontConstants", "matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.DejaVuSansFontConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.DejaVuSansFontConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DejaVuSansFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.DejaVuFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.DejaVuSansFonts", "name": "DejaVuSansFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuSansFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.DejaVuSansFonts", "matplotlib._mathtext.DejaVuFonts", "matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_fontmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.DejaVuSansFonts._fontmap", "name": "_fontmap", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.DejaVuSansFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.DejaVuSansFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DejaVuSerifFontConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.FontConstantsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.DejaVuSerifFontConstants", "name": "DejaVuSerifFontConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuSerifFontConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.DejaVuSerifFontConstants", "matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.DejaVuSerifFontConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.DejaVuSerifFontConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DejaVuSerifFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.DejaVuFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.DejaVuSerifFonts", "name": "DejaVuSerifFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.DejaVuSerifFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.DejaVuSerifFonts", "matplotlib._mathtext.DejaVuFonts", "matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_fontmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.DejaVuSerifFonts._fontmap", "name": "_fontmap", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.DejaVuSerifFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.DejaVuSerifFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Empty": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Empty", "kind": "Gdef"}, "Error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Error", "name": "Error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["msg"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Error", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FT2Font": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.FT2Font", "kind": "Gdef"}, "FT2Image": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.FT2Image", "kind": "Gdef"}, "FontConstantsBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.FontConstantsBase", "name": "FontConstantsBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.FontConstantsBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable", "delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.delta", "name": "delta", "type": "builtins.float"}}, "delta_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.delta_integral", "name": "delta_integral", "type": "builtins.float"}}, "delta_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.delta_slanted", "name": "delta_slanted", "type": "builtins.float"}}, "script_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.script_space", "name": "script_space", "type": "builtins.float"}}, "sub1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.sub1", "name": "sub1", "type": "builtins.float"}}, "sub2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.sub2", "name": "sub2", "type": "builtins.float"}}, "subdrop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.subdrop", "name": "subdrop", "type": "builtins.float"}}, "sup1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.FontConstantsBase.sup1", "name": "sup1", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontConstantsBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.FontConstantsBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FontInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.FontInfo", "name": "FontInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib._mathtext.FontInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["font", "fontsize", "postscript_name", "metrics", "num", "glyph", "offset"]}}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.FontInfo", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "font"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fontsize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "postscript_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "glyph"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "offset"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "font", "fontsize", "postscript_name", "metrics", "num", "glyph", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib._mathtext.FontInfo.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "font", "fontsize", "postscript_name", "metrics", "num", "glyph", "offset"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of FontInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.FontInfo._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of FontInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib._mathtext.FontInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FontInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FontInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "font", "fontsize", "postscript_name", "metrics", "num", "glyph", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.FontInfo._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "font", "fontsize", "postscript_name", "metrics", "num", "glyph", "offset"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of FontInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo._source", "name": "_source", "type": "builtins.str"}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.font", "name": "font", "type": "matplotlib.ft2font.FT2Font"}}, "font-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.font", "kind": "<PERSON><PERSON><PERSON>"}, "fontsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.fontsize", "name": "fontsize", "type": "builtins.float"}}, "fontsize-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.fontsize", "kind": "<PERSON><PERSON><PERSON>"}, "glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.glyph", "name": "glyph", "type": "matplotlib.ft2font.Glyph"}}, "glyph-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.glyph", "kind": "<PERSON><PERSON><PERSON>"}, "metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.metrics", "name": "metrics", "type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}}}, "metrics-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.metrics", "kind": "<PERSON><PERSON><PERSON>"}, "num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.num", "name": "num", "type": "builtins.int"}}, "num-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.num", "kind": "<PERSON><PERSON><PERSON>"}, "offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.offset", "name": "offset", "type": "builtins.float"}}, "offset-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.offset", "kind": "<PERSON><PERSON><PERSON>"}, "postscript_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontInfo.postscript_name", "name": "postscript_name", "type": "builtins.str"}}, "postscript_name-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontInfo.postscript_name", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": "matplotlib._mathtext.FontInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "builtins.int", "matplotlib.ft2font.Glyph"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "FontMetrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.FontMetrics", "name": "FontMetrics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib._mathtext.FontMetrics", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["advance", "height", "width", "xmin", "xmax", "ymin", "ymax", "iceberg", "slanted"]}}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.FontMetrics", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "advance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "xmin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "xmax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ymin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ymax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "iceberg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "slanted"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "advance", "height", "width", "xmin", "xmax", "ymin", "ymax", "iceberg", "slanted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib._mathtext.FontMetrics.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "advance", "height", "width", "xmin", "xmax", "ymin", "ymax", "iceberg", "slanted"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of FontMetrics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.FontMetrics._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of FontMetrics", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib._mathtext.FontMetrics._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FontMetrics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FontMetrics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "advance", "height", "width", "xmin", "xmax", "ymin", "ymax", "iceberg", "slanted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.FontMetrics._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "advance", "height", "width", "xmin", "xmax", "ymin", "ymax", "iceberg", "slanted"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of FontMetrics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.FontMetrics._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics._source", "name": "_source", "type": "builtins.str"}}, "advance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.advance", "name": "advance", "type": "builtins.float"}}, "advance-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.advance", "kind": "<PERSON><PERSON><PERSON>"}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.height", "name": "height", "type": "builtins.float"}}, "height-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.height", "kind": "<PERSON><PERSON><PERSON>"}, "iceberg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.iceberg", "name": "iceberg", "type": "builtins.float"}}, "iceberg-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.iceberg", "kind": "<PERSON><PERSON><PERSON>"}, "slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.slanted", "name": "slanted", "type": "builtins.bool"}}, "slanted-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.slanted", "kind": "<PERSON><PERSON><PERSON>"}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.width", "name": "width", "type": "builtins.float"}}, "width-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.width", "kind": "<PERSON><PERSON><PERSON>"}, "xmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.xmax", "name": "xmax", "type": "builtins.float"}}, "xmax-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.xmax", "kind": "<PERSON><PERSON><PERSON>"}, "xmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.xmin", "name": "xmin", "type": "builtins.float"}}, "xmin-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.xmin", "kind": "<PERSON><PERSON><PERSON>"}, "ymax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.ymax", "name": "ymax", "type": "builtins.float"}}, "ymax-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.ymax", "kind": "<PERSON><PERSON><PERSON>"}, "ymin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.FontMetrics.ymin", "name": "ymin", "type": "builtins.float"}}, "ymin-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.FontMetrics.ymin", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.FontMetrics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": "matplotlib._mathtext.FontMetrics"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "FontProperties": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.FontProperties", "kind": "Gdef"}, "Fonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Fonts", "name": "Fonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.Fonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Fonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "font"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts._get_font", "name": "_get_font", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "font"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_font of Fonts", "ret_type": "matplotlib.ft2font.FT2Font", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "font", "font_class", "sym", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts._get_info", "name": "_get_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "font", "font_class", "sym", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_info of Fonts", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_font_prop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Fonts.default_font_prop", "name": "default_font_prop", "type": "matplotlib.font_manager.FontProperties"}}, "get_kern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "font1", "fontclass1", "sym1", "fontsize1", "font2", "fontclass2", "sym2", "fontsize2", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.get_kern", "name": "get_kern", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "font1", "fontclass1", "sym1", "fontsize1", "font2", "fontclass2", "sym2", "fontsize2", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kern of Fonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "font", "font_class", "sym", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.get_metrics", "name": "get_metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "font", "font_class", "sym", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metrics of Fonts", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontMetrics"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sized_alternatives_for_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.get_sized_alternatives_for_symbol", "name": "get_sized_alternatives_for_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sized_alternatives_for_symbol of Fonts", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_underline_thickness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.get_underline_thickness", "name": "get_underline_thickness", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_underline_thickness of Fonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xheight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.get_xheight", "name": "get_xheight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xheight of Fonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_glyph_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Fonts.load_glyph_flags", "name": "load_glyph_flags", "type": "matplotlib.ft2font.LoadFlags"}}, "render_glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "ox", "oy", "font", "font_class", "sym", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.render_glyph", "name": "render_glyph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "ox", "oy", "font", "font_class", "sym", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Fonts", "matplotlib._mathtext.Output", "builtins.float", "builtins.float", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_glyph of Fonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_rect_filled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x1", "y1", "x2", "y2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Fonts.render_rect_filled", "name": "render_rect_filled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x1", "y1", "x2", "y2"], "arg_types": ["matplotlib._mathtext.Fonts", "matplotlib._mathtext.Output", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_rect_filled of Fonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Fonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Fonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Forward": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Forward", "kind": "Gdef"}, "Glue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Glue", "name": "Glue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Glue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Glue", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "glue_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Glue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "glue_type"], "arg_types": ["matplotlib._mathtext.Glue", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext._GlueSpec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fil"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "filll"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "neg_fil"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "neg_fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "neg_filll"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "empty"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ss"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Glue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "glue_spec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Glue.glue_spec", "name": "glue_spec", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Glue.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Glue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of Glue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Glue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Glue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Glyph": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.Glyph", "kind": "Gdef"}, "Group": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Group", "kind": "Gdef"}, "HCentered": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Hlist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.HCentered", "name": "HCentered", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.HCentered", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.HCentered", "matplotlib._mathtext.Hlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.HCentered.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "arg_types": ["matplotlib._mathtext.HCentered", {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HCentered", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.HCentered.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.HCentered", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Box"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Hbox", "name": "Hbox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hbox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Hbox", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hbox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib._mathtext.Hbox", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Hbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Hbox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Hbox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hlist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.List"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Hlist", "name": "Hlist", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hlist", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Hlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "elements", "w", "m", "do_kern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hlist.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "elements", "w", "m", "do_kern"], "arg_types": ["matplotlib._mathtext.Hlist", {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "additional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exactly"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Hlist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "w", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hlist.hpack", "name": "hpack", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "w", "m"], "arg_types": ["matplotlib._mathtext.Hlist", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "additional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exactly"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hpack of Hlist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hlist.kern", "name": "kern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Hlist"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kern of Hlist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Hlist.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Hlist", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hrule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Rule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Hrule", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hrule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Hrule", "matplotlib._mathtext.Rule", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Hrule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "thickness"], "arg_types": ["matplotlib._mathtext.Hrule", "matplotlib._mathtext.ParserState", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Hrule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Hrule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Kern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Kern", "name": "<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Kern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Kern", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Kern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib._mathtext.Kern", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Kern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Kern.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib._mathtext.Kern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of <PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Kern.depth", "name": "depth", "type": "builtins.int"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Kern.height", "name": "height", "type": "builtins.int"}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Kern.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Kern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of Kern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Kern.width", "name": "width", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Kern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Kern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Kerning": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.Kerning", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Box"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.List", "name": "List", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.List", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.List.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "arg_types": ["matplotlib._mathtext.List", {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of List", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.List.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib._mathtext.List"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of List", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_glue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x", "sign", "totals", "error_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.List._set_glue", "name": "_set_glue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x", "sign", "totals", "error_type"], "arg_types": ["matplotlib._mathtext.List", "builtins.float", "builtins.int", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_glue of List", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.children", "name": "children", "type": {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "glue_order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.glue_order", "name": "glue_order", "type": "builtins.int"}}, "glue_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.glue_ratio", "name": "glue_ratio", "type": "builtins.float"}}, "glue_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.glue_set", "name": "glue_set", "type": "builtins.float"}}, "glue_sign": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.glue_sign", "name": "glue_sign", "type": "builtins.int"}}, "shift_amount": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.List.shift_amount", "name": "shift_amount", "type": "builtins.float"}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.List.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.List"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of List", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.List.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.List", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Literal", "kind": "Gdef"}, "LoadFlags": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.LoadFlags", "kind": "Gdef"}, "NUM_SIZE_LEVELS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.NUM_SIZE_LEVELS", "name": "NUM_SIZE_LEVELS", "type": "builtins.int"}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Node", "name": "Node", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_kerning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "next"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node.get_kerning", "name": "get_kerning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "next"], "arg_types": ["matplotlib._mathtext.Node", {".class": "UnionType", "items": ["matplotlib._mathtext.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kerning of Node", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output", "x", "y"], "arg_types": ["matplotlib._mathtext.Node", "matplotlib._mathtext.Output", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Node.shrink", "name": "shrink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shrink of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Node.size", "name": "size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Node", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotAny": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.NotAny", "kind": "Gdef"}, "OneOrMore": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.OneOrMore", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Optional", "kind": "Gdef"}, "Output": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Output", "name": "Output", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Output", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Output", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "box"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Output.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "box"], "arg_types": ["matplotlib._mathtext.Output", "matplotlib._mathtext.Box"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Output", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "box": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Output.box", "name": "box", "type": "matplotlib._mathtext.Box"}}, "glyphs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.Output.glyphs", "name": "glyphs", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontInfo"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.Output.rects", "name": "rects", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_raster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "antialiased"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Output.to_raster", "name": "to_raster", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "antialiased"], "arg_types": ["matplotlib._mathtext.Output", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_raster of Output", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Output.to_vector", "name": "to_vector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Output"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_vector of Output", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Output.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Output", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParseBaseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseBaseException", "kind": "Gdef"}, "ParseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseException", "kind": "Gdef"}, "ParseExpression": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseExpression", "kind": "Gdef"}, "ParseFatalException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseFatalException", "kind": "Gdef"}, "ParseResults": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results.ParseResults", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Parser", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Parser", "builtins.object"], "names": {".class": "SymbolTable", "_MathStyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Parser._MathStyle", "name": "_MathStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "matplotlib._mathtext.Parser._MathStyle", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Parser._MathStyle", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DISPLAYSTYLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._MathStyle.DISPLAYSTYLE", "name": "DISPLAYSTYLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "SCRIPTSCRIPTSTYLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._MathStyle.SCRIPTSCRIPTSTYLE", "name": "SCRIPTSCRIPTSTYLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SCRIPTSTYLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._MathStyle.SCRIPTSTYLE", "name": "SCRIPTSTYLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "TEXTSTYLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._MathStyle.TEXTSTYLE", "name": "TEXTSTYLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Parser._MathStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Parser._MathStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Parse<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accent_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._accent_map", "name": "_accent_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_ambi_delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._ambi_delims", "name": "_ambi_delims", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_arrow_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._arrow_symbols", "name": "_arrow_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_auto_sized_delimiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "front", "middle", "back"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser._auto_sized_delimiter", "name": "_auto_sized_delimiter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "front", "middle", "back"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib._mathtext.Box", "matplotlib._mathtext.Char", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_auto_sized_delimiter of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_binary_operators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._binary_operators", "name": "_binary_operators", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._delims", "name": "_delims", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_dropsub_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._dropsub_symbols", "name": "_dropsub_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_em_width_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.Parser._em_width_cache", "name": "_em_width_cache", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_expression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Parser._expression", "name": "_expression", "type": "pyparsing.core.ParserElement"}}, "_fontnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._fontnames", "name": "_fontnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_function_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._function_names", "name": "_function_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_genfrac": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "l<PERSON>im", "r<PERSON><PERSON>", "rule", "style", "num", "den"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser._genfrac", "name": "_genfrac", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "l<PERSON>im", "r<PERSON><PERSON>", "rule", "style", "num", "den"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "matplotlib._mathtext.Parser._MathStyle", "matplotlib._mathtext.Hlist", "matplotlib._mathtext.Hlist"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_genfrac of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_genset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser._genset", "name": "_genset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_genset of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_in_subscript_or_superscript": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Parser._in_subscript_or_superscript", "name": "_in_subscript_or_superscript", "type": "builtins.bool"}}, "_latin_alphabets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._latin_alphabets", "name": "_latin_alphabets", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_left_delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._left_delims", "name": "_left_delims", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_make_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "percentage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser._make_space", "name": "_make_space", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "percentage"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_space of Parser", "ret_type": "matplotlib._mathtext.Kern", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_math_expression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Parser._math_expression", "name": "_math_expression", "type": "pyparsing.core.OneOrMore"}}, "_overunder_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._overunder_functions", "name": "_overunder_functions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_overunder_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._overunder_symbols", "name": "_overunder_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_punctuation_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._punctuation_symbols", "name": "_punctuation_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_relation_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._relation_symbols", "name": "_relation_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_right_delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._right_delims", "name": "_right_delims", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_small_greek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._small_greek", "name": "_small_greek", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_space_widths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._space_widths", "name": "_space_widths", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_spaced_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._spaced_symbols", "name": "_spaced_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_state_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Parser._state_stack", "name": "_state_stack", "type": {".class": "Instance", "args": ["matplotlib._mathtext.ParserState"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_wide_accents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser._wide_accents", "name": "_wide_accents", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "accent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.accent", "name": "accent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accent of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_delim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.auto_delim", "name": "auto_delim", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto_delim of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "binom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.binom", "name": "binom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "binom of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boldsymbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.boldsymbol", "name": "boldsymbol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bold<PERSON><PERSON><PERSON> of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "customspace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.customspace", "name": "customspace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "customspace of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dfrac": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.dfrac", "name": "dfrac", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dfrac of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.end_group", "name": "end_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_group of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "float_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser.float_literal", "name": "float_literal", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.font", "name": "font", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frac": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.frac", "name": "frac", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frac of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.function", "name": "function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "genfrac": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.genfrac", "name": "genfrac", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gen<PERSON><PERSON> of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.get_state", "name": "get_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_state of Parser", "ret_type": "matplotlib._mathtext.ParserState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_dropsub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.is_dropsub", "name": "is_dropsub", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "arg_types": ["matplotlib._mathtext.Parser", "matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dropsub of Parser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_overunder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.is_overunder", "name": "is_overunder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "arg_types": ["matplotlib._mathtext.Parser", "matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_overunder of Parser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.is_slanted", "name": "is_slanted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nucleus"], "arg_types": ["matplotlib._mathtext.Parser", "matplotlib._mathtext.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_slanted of <PERSON><PERSON>r", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "main of Parser", "ret_type": {".class": "Instance", "args": ["matplotlib._mathtext.Hlist"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "math": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.math", "name": "math", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "math of <PERSON>rser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "math_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.math_string", "name": "math_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "math_string of Parser", "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "non_math": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.non_math", "name": "non_math", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "non_math of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operatorname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.operatorname", "name": "operatorname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "operatorname of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "optional_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser.optional_group", "name": "optional_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.overline", "name": "overline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "overline of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser.overset", "name": "overset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "s", "fonts_object", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "s", "fonts_object", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "matplotlib._mathtext.Fonts", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Parser", "ret_type": "matplotlib._mathtext.Hlist", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.pop_state", "name": "pop_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_state of Parser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.push_state", "name": "push_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_state of Parser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "required_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.required_group", "name": "required_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "required_group of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.space", "name": "space", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "space of <PERSON>rser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqrt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.sqrt", "name": "sqrt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqrt of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.start_group", "name": "start_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_group of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.style_literal", "name": "style_literal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "style_literal of <PERSON><PERSON>r", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "substack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.substack", "name": "substack", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "substack of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subsuper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.subsuper", "name": "subsuper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subsuper of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.symbol", "name": "symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["pyparsing.results.ParseResults", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbol of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unclosed_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.unclosed_group", "name": "unclosed_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unclosed_group of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "underset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.Parser.underset", "name": "underset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unknown_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Parser.unknown_symbol", "name": "unknown_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "loc", "toks"], "arg_types": ["matplotlib._mathtext.Parser", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unknown_symbol of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Parser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Parser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParserElement": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement", "kind": "Gdef"}, "ParserState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.ParserState", "name": "ParserState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ParserState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.ParserState", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fontset", "font", "font_class", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ParserState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fontset", "font", "font_class", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.ParserState", "matplotlib._mathtext.Fonts", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParserState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.ParserState._font", "name": "_font", "type": "builtins.str"}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ParserState.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ParserState", "ret_type": "matplotlib._mathtext.ParserState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dpi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.ParserState.dpi", "name": "dpi", "type": "builtins.float"}}, "font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib._mathtext.ParserState.font", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib._mathtext.ParserState.font", "name": "font", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font of ParserState", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.ParserState.font", "name": "font", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font of ParserState", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib._mathtext.ParserState.font", "name": "font", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["matplotlib._mathtext.ParserState", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font of ParserState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "font", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "font of ParserState", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "font_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.ParserState.font_class", "name": "font_class", "type": "builtins.str"}}, "fontset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.ParserState.fontset", "name": "fontset", "type": "matplotlib._mathtext.Fonts"}}, "fontsize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.ParserState.fontsize", "name": "fontsize", "type": "builtins.float"}}, "get_current_underline_thickness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ParserState.get_current_underline_thickness", "name": "get_current_underline_thickness", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_underline_thickness of ParserState", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.ParserState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.ParserState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuotedString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.QuotedString", "kind": "Gdef"}, "RasterParse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.RasterParse", "name": "RasterParse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib._mathtext.RasterParse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["ox", "oy", "width", "height", "depth", "image"]}}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.RasterParse", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ox"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "depth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "ox", "oy", "width", "height", "depth", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib._mathtext.RasterParse.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "ox", "oy", "width", "height", "depth", "image"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of RasterParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.RasterParse._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of RasterParse", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib._mathtext.RasterParse._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RasterParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RasterParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "ox", "oy", "width", "height", "depth", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.RasterParse._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "ox", "oy", "width", "height", "depth", "image"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.RasterParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse._source", "name": "_source", "type": "builtins.str"}}, "depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.depth", "name": "depth", "type": "builtins.float"}}, "depth-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.depth", "kind": "<PERSON><PERSON><PERSON>"}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.height", "name": "height", "type": "builtins.float"}}, "height-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.height", "kind": "<PERSON><PERSON><PERSON>"}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.image", "name": "image", "type": "matplotlib.ft2font.FT2Image"}}, "image-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.image", "kind": "<PERSON><PERSON><PERSON>"}, "ox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.ox", "name": "ox", "type": "builtins.float"}}, "ox-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.ox", "kind": "<PERSON><PERSON><PERSON>"}, "oy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.oy", "name": "oy", "type": "builtins.float"}}, "oy-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.oy", "kind": "<PERSON><PERSON><PERSON>"}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.RasterParse.width", "name": "width", "type": "builtins.float"}}, "width-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse.width", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.RasterParse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": "matplotlib._mathtext.RasterParse"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.ft2font.FT2Image"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "matplotlib.ft2font.FT2Image"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Regex": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Regex", "kind": "Gdef"}, "Rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Box"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Rule", "name": "Rule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Rule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Rule", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "depth", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Rule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "height", "depth", "state"], "arg_types": ["matplotlib._mathtext.Rule", "builtins.float", "builtins.float", "builtins.float", "matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fontset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.Rule.fontset", "name": "fontset", "type": "matplotlib._mathtext.Fonts"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x", "y", "w", "h"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Rule.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "x", "y", "w", "h"], "arg_types": ["matplotlib._mathtext.Rule", "matplotlib._mathtext.Output", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Rule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Rule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Rule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SHRINK_FACTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.SHRINK_FACTOR", "name": "SHRINK_FACTOR", "type": "builtins.float"}}, "STIXFontConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.FontConstantsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.STIXFontConstants", "name": "STIXFontConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.STIXFontConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.STIXFontConstants", "matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable", "delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.delta", "name": "delta", "type": "builtins.float"}}, "delta_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.delta_integral", "name": "delta_integral", "type": "builtins.float"}}, "delta_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.delta_slanted", "name": "delta_slanted", "type": "builtins.float"}}, "script_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.script_space", "name": "script_space", "type": "builtins.float"}}, "sub2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.sub2", "name": "sub2", "type": "builtins.float"}}, "sup1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXFontConstants.sup1", "name": "sup1", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.STIXFontConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.STIXFontConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STIXSansFontConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.FontConstantsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.STIXSansFontConstants", "name": "STIXSansFontConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.STIXSansFontConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.STIXSansFontConstants", "matplotlib._mathtext.FontConstantsBase", "builtins.object"], "names": {".class": "SymbolTable", "delta_integral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXSansFontConstants.delta_integral", "name": "delta_integral", "type": "builtins.float"}}, "delta_slanted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXSansFontConstants.delta_slanted", "name": "delta_slanted", "type": "builtins.float"}}, "script_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXSansFontConstants.script_space", "name": "script_space", "type": "builtins.float"}}, "sup1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.STIXSansFontConstants.sup1", "name": "sup1", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.STIXSansFontConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.STIXSansFontConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StixFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.UnicodeFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.StixFonts", "name": "StixFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.StixFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.StixFonts", "matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.StixFonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.StixFonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StixFonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fallback_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.StixFonts._fallback_font", "name": "_fallback_font", "type": {".class": "NoneType"}}}, "_fontmap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext.StixFonts._fontmap", "name": "_fontmap", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_map_virtual_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "uniindex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.StixFonts._map_virtual_font", "name": "_map_virtual_font", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "uniindex"], "arg_types": ["matplotlib._mathtext.StixFonts", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_virtual_font of StixFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.StixFonts._sans", "name": "_sans", "type": "builtins.bool"}}, "get_sized_alternatives_for_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib._mathtext.StixFonts.get_sized_alternatives_for_symbol", "name": "get_sized_alternatives_for_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "arg_types": ["matplotlib._mathtext.StixFonts", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sized_alternatives_for_symbol of StixFonts", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.StixFonts.get_sized_alternatives_for_symbol", "name": "get_sized_alternatives_for_symbol", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.StixFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.StixFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StixSansFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.StixFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.StixSansFonts", "name": "StixSansFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.StixSansFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.StixSansFonts", "matplotlib._mathtext.StixFonts", "matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_sans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.StixSansFonts._sans", "name": "_sans", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.StixSansFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.StixSansFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.StringEnd", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "TruetypeFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Fonts"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.TruetypeFonts", "name": "TruetypeFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TruetypeFonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fonts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.TruetypeFonts._fonts", "name": "_fonts", "type": {".class": "Instance", "args": ["builtins.str", "matplotlib.ft2font.FT2Font"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_get_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "font"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts._get_font", "name": "_get_font", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "font"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_font of TruetypeFonts", "ret_type": "matplotlib.ft2font.FT2Font", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts._get_glyph", "name": "_get_glyph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_glyph of TruetypeFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts._get_info", "name": "_get_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_info of TruetypeFonts", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.FontInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "font", "glyph", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts._get_offset", "name": "_get_offset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "font", "glyph", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "matplotlib.ft2font.FT2Font", "matplotlib.ft2font.Glyph", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_offset of TruetypeFonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fontmap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "matplotlib._mathtext.TruetypeFonts.fontmap", "name": "fontmap", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_kern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "font1", "fontclass1", "sym1", "fontsize1", "font2", "fontclass2", "sym2", "fontsize2", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts.get_kern", "name": "get_kern", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "font1", "fontclass1", "sym1", "fontsize1", "font2", "fontclass2", "sym2", "fontsize2", "dpi"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kern of TruetypeFonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_underline_thickness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts.get_underline_thickness", "name": "get_underline_thickness", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "font", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_underline_thickness of TruetypeFonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xheight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "fontsize", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.TruetypeFonts.get_xheight", "name": "get_xheight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "fontsize", "dpi"], "arg_types": ["matplotlib._mathtext.TruetypeFonts", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xheight of TruetypeFonts", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.TruetypeFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.TruetypeFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnicodeFonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.TruetypeFonts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.UnicodeFonts", "name": "UnicodeFonts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.UnicodeFonts", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.UnicodeFonts", "matplotlib._mathtext.TruetypeFonts", "matplotlib._mathtext.Fonts", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.UnicodeFonts.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "default_font_prop", "load_glyph_flags"], "arg_types": ["matplotlib._mathtext.UnicodeFonts", "matplotlib.font_manager.FontProperties", "matplotlib.ft2font.LoadFlags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnicodeFonts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cmr10_substitutions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.UnicodeFonts._cmr10_substitutions", "name": "_cmr10_substitutions", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fallback_font": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib._mathtext.UnicodeFonts._fallback_font", "name": "_fallback_font", "type": {".class": "UnionType", "items": ["matplotlib._mathtext.TruetypeFonts", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_glyph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.UnicodeFonts._get_glyph", "name": "_get_glyph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "sym"], "arg_types": ["matplotlib._mathtext.UnicodeFonts", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_glyph of UnicodeFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_map_virtual_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "uniindex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.UnicodeFonts._map_virtual_font", "name": "_map_virtual_font", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fontname", "font_class", "uniindex"], "arg_types": ["matplotlib._mathtext.UnicodeFonts", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_virtual_font of UnicodeFonts", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_slanted_symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext.UnicodeFonts._slanted_symbols", "name": "_slanted_symbols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "get_sized_alternatives_for_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.UnicodeFonts.get_sized_alternatives_for_symbol", "name": "get_sized_alternatives_for_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fontname", "sym"], "arg_types": ["matplotlib._mathtext.UnicodeFonts", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sized_alternatives_for_symbol of UnicodeFonts", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.UnicodeFonts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.UnicodeFonts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VCentered": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Vlist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.VCentered", "name": "VCentered", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.VCentered", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.VCentered", "matplotlib._mathtext.Vlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.VCentered.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "arg_types": ["matplotlib._mathtext.VCentered", {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of VCentered", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VCentered.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.VCentered", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Vbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Box"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Vbox", "name": "Vbox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vbox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Vbox", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "height", "depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vbox.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "height", "depth"], "arg_types": ["matplotlib._mathtext.Vbox", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Vbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Vbox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Vbox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VectorParse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.VectorParse", "name": "VectorParse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib._mathtext.VectorParse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["width", "height", "depth", "glyphs", "rects"]}}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.VectorParse", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "depth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "glyphs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rects"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "width", "height", "depth", "glyphs", "rects"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib._mathtext.VectorParse.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "width", "height", "depth", "glyphs", "rects"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of VectorParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.VectorParse._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of VectorParse", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib._mathtext.VectorParse._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of VectorParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of VectorParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "width", "height", "depth", "glyphs", "rects"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.VectorParse._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "width", "height", "depth", "glyphs", "rects"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of VectorParse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext.VectorParse._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse._source", "name": "_source", "type": "builtins.str"}}, "depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.depth", "name": "depth", "type": "builtins.float"}}, "depth-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse.depth", "kind": "<PERSON><PERSON><PERSON>"}, "glyphs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.glyphs", "name": "glyphs", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "glyphs-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse.glyphs", "kind": "<PERSON><PERSON><PERSON>"}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.height", "name": "height", "type": "builtins.float"}}, "height-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse.height", "kind": "<PERSON><PERSON><PERSON>"}, "rects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.rects", "name": "rects", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rects-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse.rects", "kind": "<PERSON><PERSON><PERSON>"}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext.VectorParse.width", "name": "width", "type": "builtins.float"}}, "width-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse.width", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.VectorParse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": "matplotlib._mathtext.VectorParse"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.ft2font.FT2Font", "builtins.float", "builtins.int", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Vlist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.List"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Vlist", "name": "Vlist", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vlist", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Vlist", "matplotlib._mathtext.List", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "elements", "h", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vlist.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "elements", "h", "m"], "arg_types": ["matplotlib._mathtext.Vlist", {".class": "Instance", "args": ["matplotlib._mathtext.Node"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "additional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exactly"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Vlist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "h", "m", "l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vlist.vpack", "name": "vpack", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "h", "m", "l"], "arg_types": ["matplotlib._mathtext.Vlist", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "additional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exactly"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vpack of Vlist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Vlist.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Vlist", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Vrule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib._mathtext.Rule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext.Vrule", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vrule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext.Vrule", "matplotlib._mathtext.Rule", "matplotlib._mathtext.Box", "matplotlib._mathtext.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.Vrule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["matplotlib._mathtext.Vrule", "matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of V<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext.Vrule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._mathtext.Vrule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroOrMore": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ZeroOrMore", "kind": "Gdef"}, "_GlueSpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._mathtext._GlueSpec", "name": "_GlueSpec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "matplotlib._mathtext._GlueSpec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["width", "stretch", "stretch_order", "shrink", "shrink_order"]}}, "module_name": "matplotlib._mathtext", "mro": ["matplotlib._mathtext._GlueSpec", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stretch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stretch_order"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shrink"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shrink_order"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "width", "stretch", "stretch_order", "shrink", "shrink_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib._mathtext._GlueSpec.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "width", "stretch", "stretch_order", "shrink", "shrink_order"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _GlueSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext._GlueSpec._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _GlueSpec", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib._mathtext._GlueSpec._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _GlueSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _GlueSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "width", "stretch", "stretch_order", "shrink", "shrink_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext._GlueSpec._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "width", "stretch", "stretch_order", "shrink", "shrink_order"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _GlueSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec._NT", "id": -1, "name": "_NT", "namespace": "matplotlib._mathtext._GlueSpec._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec._source", "name": "_source", "type": "builtins.str"}}, "shrink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.shrink", "name": "shrink", "type": "builtins.float"}}, "shrink-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext._GlueSpec.shrink", "kind": "<PERSON><PERSON><PERSON>"}, "shrink_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.shrink_order", "name": "shrink_order", "type": "builtins.int"}}, "shrink_order-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext._GlueSpec.shrink_order", "kind": "<PERSON><PERSON><PERSON>"}, "stretch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.stretch", "name": "stretch", "type": "builtins.float"}}, "stretch-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext._GlueSpec.stretch", "kind": "<PERSON><PERSON><PERSON>"}, "stretch_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.stretch_order", "name": "stretch_order", "type": "builtins.int"}}, "stretch_order-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext._GlueSpec.stretch_order", "kind": "<PERSON><PERSON><PERSON>"}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "matplotlib._mathtext._GlueSpec.width", "name": "width", "type": "builtins.float"}}, "width-redefinition": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext._GlueSpec.width", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._mathtext._GlueSpec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": "matplotlib._mathtext._GlueSpec"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_font_constant_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext._font_constant_mapping", "name": "_font_constant_mapping", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["matplotlib._mathtext.DejaVuSansFontConstants"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib._mathtext.FontConstantsBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_get_font_constant_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext._get_font_constant_set", "name": "_get_font_constant_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": ["matplotlib._mathtext.ParserState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_font_constant_set", "ret_type": {".class": "TypeType", "item": "matplotlib._mathtext.FontConstantsBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext._log", "name": "_log", "type": "logging.Logger"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef"}, "cmd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expr", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.cmd", "name": "cmd", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expr", "args"], "arg_types": ["builtins.str", "pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmd", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "findfont": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.findfont", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_font": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.get_font", "kind": "Gdef"}, "get_unicode_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.get_unicode_index", "name": "get_unicode_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["symbol"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unicode_index", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "latex_to_bakoma": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext_data.latex_to_bakoma", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mpl": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "nested_expr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.nestedExpr", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "oneOf": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.oneOf", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.parse", "kind": "Gdef"}, "pyparsing_common": {".class": "SymbolTableNode", "cross_ref": "pyparsing.pyparsing_common", "kind": "Gdef"}, "pyparsing_version": {".class": "SymbolTableNode", "cross_ref": "pyparsing.__version__", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "ship": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["box", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext.ship", "name": "ship", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["box", "xy"], "arg_types": ["matplotlib._mathtext.Box", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ship", "ret_type": "matplotlib._mathtext.Output", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stix_glyph_fixes": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext_data.stix_glyph_fixes", "kind": "Gdef"}, "stix_virtual_fonts": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext_data.stix_virtual_fonts", "kind": "Gdef"}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}, "tex2uni": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext_data.tex2uni", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\_mathtext.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极版现货交易系统启动脚本
Ultimate Spot Trading System Launcher
"""

import subprocess
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动终极版现货交易系统...")
    print("=" * 50)
    
    # 检查GUI文件是否存在
    gui_file = Path("core/ultimate_spot_trading_gui.py")
    if not gui_file.exists():
        print("❌ 错误: 找不到GUI文件")
        print(f"   期望位置: {gui_file}")
        return False
    
    try:
        # 启动GUI
        print("📱 正在启动GUI界面...")
        subprocess.Popen([sys.executable, str(gui_file)])
        print("✅ GUI界面已启动")
        print("💡 如果界面没有出现，请检查Python环境和依赖库")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    main()

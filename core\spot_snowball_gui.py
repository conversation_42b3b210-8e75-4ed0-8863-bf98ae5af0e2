#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现货滚雪球交易GUI
Spot Snowball Trading GUI

专门为现货滚雪球策略设计的交易界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import asyncio
import json
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd

class SpotSnowballGUI:
    """现货滚雪球交易GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("现货滚雪球交易系统 - Spot Snowball Trading")
        self.root.geometry("1400x900")
        
        # 导入滚雪球策略
        try:
            from spot_snowball_strategy import SpotSnowballStrategy
            self.strategy = SpotSnowballStrategy(initial_capital=10000)
            self.strategy_loaded = True
        except ImportError as e:
            self.strategy = None
            self.strategy_loaded = False
            print(f"策略加载失败: {e}")
        
        # 交易引擎（需要在实际使用时初始化）
        self.trading_engine = None
        self.is_trading = False
        
        # 数据存储
        self.performance_data = []
        self.trade_history = []
        self.signal_info = {}
        
        self.setup_ui()
        self.setup_styles()
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Warning.TLabel', foreground='orange')
        style.configure('Error.TLabel', foreground='red')
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(title_frame, text="🚀 现货滚雪球交易系统", 
                 style='Title.TLabel').pack(side='left')
        
        # 状态指示器
        self.status_label = ttk.Label(title_frame, text="● 未连接", 
                                     foreground='red')
        self.status_label.pack(side='right')
        
        # 创建主要区域
        main_paned = ttk.PanedWindow(self.root, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧面板
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # 右侧面板
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        # 连接控制
        connection_frame = ttk.LabelFrame(parent, text="连接控制")
        connection_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(connection_frame, text="测试连接", 
                  command=self.test_connection).pack(fill='x', padx=5, pady=2)
        ttk.Button(connection_frame, text="开始交易", 
                  command=self.start_trading).pack(fill='x', padx=5, pady=2)
        ttk.Button(connection_frame, text="停止交易", 
                  command=self.stop_trading).pack(fill='x', padx=5, pady=2)
        
        # 策略参数
        strategy_frame = ttk.LabelFrame(parent, text="滚雪球策略参数")
        strategy_frame.pack(fill='x', padx=5, pady=5)
        
        # 初始资金
        ttk.Label(strategy_frame, text="初始资金 (USDT):").pack(anchor='w', padx=5)
        self.initial_capital_var = tk.StringVar(value="10000")
        ttk.Entry(strategy_frame, textvariable=self.initial_capital_var).pack(fill='x', padx=5, pady=2)
        
        # 风险参数
        ttk.Label(strategy_frame, text="每笔风险 (%):").pack(anchor='w', padx=5)
        self.risk_per_trade_var = tk.StringVar(value="2.0")
        ttk.Entry(strategy_frame, textvariable=self.risk_per_trade_var).pack(fill='x', padx=5, pady=2)
        
        # 最小盈亏比
        ttk.Label(strategy_frame, text="最小盈亏比:").pack(anchor='w', padx=5)
        self.min_risk_reward_var = tk.StringVar(value="2.0")
        ttk.Entry(strategy_frame, textvariable=self.min_risk_reward_var).pack(fill='x', padx=5, pady=2)
        
        # 最小胜率
        ttk.Label(strategy_frame, text="最小胜率 (%):").pack(anchor='w', padx=5)
        self.min_win_rate_var = tk.StringVar(value="60")
        ttk.Entry(strategy_frame, textvariable=self.min_win_rate_var).pack(fill='x', padx=5, pady=2)
        
        # 最小信号强度
        ttk.Label(strategy_frame, text="最小信号强度 (%):").pack(anchor='w', padx=5)
        self.min_signal_strength_var = tk.StringVar(value="70")
        ttk.Entry(strategy_frame, textvariable=self.min_signal_strength_var).pack(fill='x', padx=5, pady=2)
        
        # 应用参数按钮
        ttk.Button(strategy_frame, text="应用参数", 
                  command=self.apply_parameters).pack(fill='x', padx=5, pady=5)
        
        # 账户信息
        account_frame = ttk.LabelFrame(parent, text="账户信息")
        account_frame.pack(fill='x', padx=5, pady=5)
        
        self.account_info = {
            'capital': ttk.Label(account_frame, text="当前资金: --"),
            'daily_pnl': ttk.Label(account_frame, text="日盈亏: --"),
            'total_return': ttk.Label(account_frame, text="总收益率: --"),
            'drawdown': ttk.Label(account_frame, text="当前回撤: --"),
            'positions': ttk.Label(account_frame, text="持仓数量: --")
        }
        
        for label in self.account_info.values():
            label.pack(anchor='w', padx=5, pady=1)
        
        # 风险监控
        risk_frame = ttk.LabelFrame(parent, text="风险监控")
        risk_frame.pack(fill='x', padx=5, pady=5)
        
        self.risk_info = {
            'risk_level': ttk.Label(risk_frame, text="风险等级: --"),
            'max_drawdown': ttk.Label(risk_frame, text="最大回撤: --"),
            'win_rate': ttk.Label(risk_frame, text="胜率: --"),
            'profit_factor': ttk.Label(risk_frame, text="盈利因子: --")
        }
        
        for label in self.risk_info.values():
            label.pack(anchor='w', padx=5, pady=1)
        
    def setup_right_panel(self, parent):
        """设置右侧显示面板"""
        # 创建标签页
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # 性能图表标签页
        chart_frame = ttk.Frame(notebook)
        notebook.add(chart_frame, text="性能图表")
        self.setup_performance_chart(chart_frame)
        
        # 持仓信息标签页
        positions_frame = ttk.Frame(notebook)
        notebook.add(positions_frame, text="持仓信息")
        self.setup_positions_table(positions_frame)
        
        # 交易历史标签页
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text="交易历史")
        self.setup_trade_history(history_frame)
        
        # 信号分析标签页
        signals_frame = ttk.Frame(notebook)
        notebook.add(signals_frame, text="信号分析")
        self.setup_signals_analysis(signals_frame)
        
    def setup_performance_chart(self, parent):
        """设置性能图表"""
        # 创建matplotlib图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 8))
        self.fig.suptitle('现货滚雪球策略表现')
        
        # 资金曲线
        self.ax1.set_title('资金增长曲线')
        self.ax1.set_ylabel('资金 (USDT)')
        self.ax1.grid(True)
        
        # 回撤曲线
        self.ax2.set_title('回撤曲线')
        self.ax2.set_ylabel('回撤 (%)')
        self.ax2.set_xlabel('时间')
        self.ax2.grid(True)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def setup_positions_table(self, parent):
        """设置持仓表格"""
        # 创建表格
        columns = ('交易对', '方向', '数量', '入场价', '当前价', '盈亏', '盈亏率')
        self.positions_tree = ttk.Treeview(parent, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100)
        
        # 添加滚动条
        positions_scroll = ttk.Scrollbar(parent, orient='vertical', 
                                       command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scroll.set)
        
        # 布局
        self.positions_tree.pack(side='left', fill='both', expand=True)
        positions_scroll.pack(side='right', fill='y')
        
    def setup_trade_history(self, parent):
        """设置交易历史"""
        # 创建表格
        columns = ('时间', '交易对', '方向', '数量', '价格', '盈亏', '原因')
        self.history_tree = ttk.Treeview(parent, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100)
        
        # 添加滚动条
        history_scroll = ttk.Scrollbar(parent, orient='vertical', 
                                     command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scroll.set)
        
        # 布局
        self.history_tree.pack(side='left', fill='both', expand=True)
        history_scroll.pack(side='right', fill='y')
        
    def setup_signals_analysis(self, parent):
        """设置信号分析"""
        # 信号强度显示
        signals_frame = ttk.LabelFrame(parent, text="当前信号分析")
        signals_frame.pack(fill='x', padx=5, pady=5)
        
        self.signal_info = {}
        pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
        
        for pair in pairs:
            pair_frame = ttk.LabelFrame(signals_frame, text=pair)
            pair_frame.pack(fill='x', padx=5, pady=2)
            
            self.signal_info[pair] = {
                'direction': ttk.Label(pair_frame, text="方向: --"),
                'strength': ttk.Label(pair_frame, text="强度: --"),
                'confidence': ttk.Label(pair_frame, text="置信度: --"),
                'risk_reward': ttk.Label(pair_frame, text="盈亏比: --"),
                'win_probability': ttk.Label(pair_frame, text="胜率预估: --")
            }
            
            for label in self.signal_info[pair].values():
                label.pack(side='left', padx=10)
        
        # 刷新按钮
        ttk.Button(signals_frame, text="刷新信号", 
                  command=self.refresh_signals).pack(pady=5)
        
    def test_connection(self):
        """测试连接"""
        try:
            # 这里应该测试实际的API连接
            # 暂时模拟成功
            self.status_label.config(text="● 已连接", foreground='green')
            messagebox.showinfo("连接测试", "API连接测试成功！")
        except Exception as e:
            self.status_label.config(text="● 连接失败", foreground='red')
            messagebox.showerror("连接错误", f"连接失败: {str(e)}")
    
    def start_trading(self):
        """开始交易"""
        if self.is_trading:
            messagebox.showwarning("警告", "交易已在运行中")
            return
        
        if not self.strategy_loaded:
            messagebox.showerror("错误", "策略未加载，无法开始交易")
            return
            
        try:
            # 应用参数
            self.apply_parameters()
            
            # 初始化交易对
            self.initialize_trading_pairs()
            
            # 刷新信号
            self.refresh_signals()
            
            # 启动交易
            self.is_trading = True
            self.status_label.config(text="● 交易中", foreground='green')
            
            # 启动数据更新线程
            self.start_data_update_thread()
            
            # 添加初始资金记录到性能数据
            self.performance_data.append({
                'timestamp': datetime.now(),
                'capital': self.strategy.current_capital,
                'profit': 0,
                'growth_rate': 0
            })
            
            # 更新图表
            self.update_performance_chart()
            
            messagebox.showinfo("交易启动", "滚雪球策略已启动！")
            
        except Exception as e:
            messagebox.showerror("启动错误", f"启动失败: {str(e)}")
            
    def initialize_trading_pairs(self):
        """初始化交易对"""
        # 设置默认交易对
        default_pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT']
        
        # 初始化信号信息字典
        self.signal_info = {pair: {} for pair in default_pairs}
    
    def stop_trading(self):
        """停止交易"""
        if not self.is_trading:
            messagebox.showwarning("警告", "交易未在运行")
            return
        
        self.is_trading = False
        self.status_label.config(text="● 已停止", foreground='orange')
        messagebox.showinfo("交易停止", "交易已停止")
    
    def apply_parameters(self):
        """应用策略参数"""
        if not self.strategy_loaded:
            messagebox.showerror("错误", "策略未加载，无法应用参数")
            return
            
        try:
            # 获取参数值
            initial_capital = float(self.initial_capital_var.get())
            risk_per_trade = float(self.risk_per_trade_var.get()) / 100
            min_risk_reward = float(self.min_risk_reward_var.get())
            min_win_rate = float(self.min_win_rate_var.get()) / 100
            min_signal_strength = float(self.min_signal_strength_var.get()) / 100
            max_positions = int(self.max_positions_var.get())
            position_size_pct = float(self.position_size_var.get()) / 100
            
            # 更新策略参数
            self.strategy.initial_capital = initial_capital
            self.strategy.risk_per_trade = risk_per_trade
            self.strategy.min_risk_reward = min_risk_reward
            self.strategy.min_win_rate = min_win_rate
            self.strategy.min_signal_strength = min_signal_strength
            self.strategy.max_positions = max_positions
            self.strategy.position_size_pct = position_size_pct
            
            # 如果当前资金为初始值，也更新当前资金
            if self.strategy.current_capital == self.strategy.initial_capital:
                self.strategy.current_capital = initial_capital
            
            messagebox.showinfo("参数更新", "策略参数已更新")
            
            # 更新显示
            self.update_display_data()
            
        except ValueError as e:
            messagebox.showerror("参数错误", "请输入有效的数值")
    
    def refresh_signals(self):
        """刷新信号"""
        if not self.strategy_loaded:
            messagebox.showerror("错误", "策略未加载，无法生成信号")
            return
            
        try:
            # 获取市场数据
            market_data = self.get_market_data()
            
            if not market_data:
                # 如果没有真实数据，使用模拟数据
                import random
                
                for pair in self.signal_info:
                    direction = random.choice(['买入', '卖出', '观望'])
                    strength = random.uniform(0.5, 1.0)
                    risk_reward = random.uniform(1.0, 3.0)
                    win_rate = random.uniform(0.5, 0.8)
                    
                    self.signal_info[pair] = {
                        'direction': direction,
                        'strength': strength,
                        'risk_reward': risk_reward,
                        'win_rate': win_rate
                    }
            else:
                # 使用真实策略生成信号
                for pair, df in market_data.items():
                    if pair not in self.signal_info:
                        self.signal_info[pair] = {}
                        
                    # 使用策略计算信号质量
                    signal_quality = self.strategy.calculate_signal_strength(df)
                    
                    # 转换方向为中文
                    direction_map = {
                        'buy': '买入',
                        'sell': '卖出',
                        'hold': '观望'
                    }
                    
                    self.signal_info[pair] = {
                        'direction': direction_map.get(signal_quality.direction, '观望'),
                        'strength': signal_quality.strength,
                        'risk_reward': signal_quality.risk_reward,
                        'win_rate': signal_quality.win_probability
                    }
            
            # 更新信号显示
            self.update_signals_display()
            
        except Exception as e:
            print(f"刷新信号错误: {e}")
            # 出错时使用模拟数据
            import random
            
            for pair in self.signal_info:
                direction = random.choice(['买入', '卖出', '观望'])
                strength = random.uniform(0.5, 1.0)
                risk_reward = random.uniform(1.0, 3.0)
                win_rate = random.uniform(0.5, 0.8)
                
                self.signal_info[pair] = {
                    'direction': direction,
                    'strength': strength,
                    'risk_reward': risk_reward,
                    'win_rate': win_rate
                }
                
            # 更新信号显示
            self.update_signals_display()
    
    def get_market_data(self):
        """获取市场数据"""
        # 这里应该从交易所API获取实际数据
        # 如果没有实际数据，返回None
        
        try:
            # 尝试导入pandas和numpy
            import pandas as pd
            import numpy as np
            
            # 创建模拟数据
            market_data = {}
            pairs = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT']
            
            for pair in pairs:
                # 创建模拟价格数据
                np.random.seed(42)  # 使结果可重现
                dates = pd.date_range(end=pd.Timestamp.now(), periods=100, freq='1D')
                
                close_prices = np.random.normal(loc=100, scale=10, size=100)
                close_prices = np.exp(np.cumsum(np.random.normal(loc=0.0001, scale=0.02, size=100)))
                
                # 确保价格是正数且有一定的趋势
                close_prices = np.abs(close_prices) * 100
                
                # 创建OHLCV数据
                high_prices = close_prices * np.random.uniform(1.01, 1.05, size=100)
                low_prices = close_prices * np.random.uniform(0.95, 0.99, size=100)
                open_prices = close_prices * np.random.uniform(0.98, 1.02, size=100)
                volumes = np.random.uniform(1000, 10000, size=100)
                
                # 创建DataFrame
                df = pd.DataFrame({
                    'open': open_prices,
                    'high': high_prices,
                    'low': low_prices,
                    'close': close_prices,
                    'volume': volumes
                }, index=dates)
                
                market_data[pair] = df
                
            return market_data
            
        except Exception as e:
            print(f"获取市场数据错误: {e}")
            return None
    
    def update_signals_display(self):
        """更新信号显示"""
        # 清空信号表格
        for item in self.signals_tree.get_children():
            self.signals_tree.delete(item)
            
        # 添加新的信号数据
        for pair, info in self.signal_info.items():
            direction = info.get('direction', '观望')
            strength = info.get('strength', 0.0)
            risk_reward = info.get('risk_reward', 0.0)
            win_rate = info.get('win_rate', 0.0)
            
            # 设置颜色标签
            if direction == '买入':
                tag = 'buy'
            elif direction == '卖出':
                tag = 'sell'
            else:
                tag = 'hold'
                
            # 添加到表格
            self.signals_tree.insert('', 'end', values=(
                pair,
                direction,
                f"{strength:.2f}",
                f"{risk_reward:.2f}",
                f"{win_rate:.2%}"
            ), tags=(tag,))
            confidence = random.uniform(0.6, 0.9)
            risk_reward = random.uniform(1.5, 3.0)
            win_prob = random.uniform(0.55, 0.75)
            
            self.signal_info[pair]['direction'].config(text=f"方向: {direction}")
            self.signal_info[pair]['strength'].config(text=f"强度: {strength:.2f}")
            self.signal_info[pair]['confidence'].config(text=f"置信度: {confidence:.2f}")
            self.signal_info[pair]['risk_reward'].config(text=f"盈亏比: {risk_reward:.2f}")
            self.signal_info[pair]['win_probability'].config(text=f"胜率预估: {win_prob:.1%}")
    
    def start_data_update_thread(self):
        """启动数据更新线程"""
        def update_loop():
            while self.is_trading:
                self.update_display_data()
                threading.Event().wait(5)  # 每5秒更新一次
        
        thread = threading.Thread(target=update_loop, daemon=True)
        thread.start()
    
    def update_display_data(self):
        """更新显示数据"""
        if not self.strategy_loaded:
            # 使用模拟数据
            import random
            
            # 更新账户信息
            current_capital = 10000 + random.uniform(-500, 1000)
            daily_pnl = random.uniform(-100, 200)
            total_return = (current_capital - 10000) / 10000
            drawdown = random.uniform(0, 0.05)
        else:
            # 使用策略数据
            current_capital = self.strategy.current_capital
            
            # 计算每日盈亏
            if self.performance_data:
                last_record = self.performance_data[-1]
                daily_pnl = current_capital - last_record['capital']
            else:
                daily_pnl = 0
                
            # 计算总回报率
            total_return = (current_capital - self.strategy.initial_capital) / self.strategy.initial_capital
            
            # 计算回撤
            if self.performance_data:
                max_capital = max(record['capital'] for record in self.performance_data)
                drawdown = (max_capital - current_capital) / max_capital if max_capital > 0 else 0
            else:
                drawdown = 0
                
            # 添加新的性能记录
            self.performance_data.append({
                'timestamp': datetime.now(),
                'capital': current_capital,
                'profit': daily_pnl,
                'growth_rate': total_return
            })
            
            # 如果性能数据过多，保留最近的100条
            if len(self.performance_data) > 100:
                self.performance_data = self.performance_data[-100:]
        positions_count = random.randint(0, 3)
        
        self.account_info['capital'].config(text=f"当前资金: {current_capital:.2f} USDT")
        self.account_info['daily_pnl'].config(text=f"日盈亏: {daily_pnl:+.2f} USDT")
        self.account_info['total_return'].config(text=f"总收益率: {total_return:+.2%}")
        self.account_info['drawdown'].config(text=f"当前回撤: {drawdown:.2%}")
        self.account_info['positions'].config(text=f"持仓数量: {positions_count}")
        
        # 更新风险信息
        risk_level = "低" if drawdown < 0.02 else "中" if drawdown < 0.05 else "高"
        max_drawdown = random.uniform(0.02, 0.08)
        win_rate = random.uniform(0.6, 0.8)
        profit_factor = random.uniform(1.2, 2.5)
        
        self.risk_info['risk_level'].config(text=f"风险等级: {risk_level}")
        self.risk_info['max_drawdown'].config(text=f"最大回撤: {max_drawdown:.2%}")
        self.risk_info['win_rate'].config(text=f"胜率: {win_rate:.1%}")
        self.risk_info['profit_factor'].config(text=f"盈利因子: {profit_factor:.2f}")
        
        # 更新图表
        self.update_performance_chart()
    
    def update_performance_chart(self):
        """更新性能图表"""
        import numpy as np
        import matplotlib.pyplot as plt
        from matplotlib.figure import Figure
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        
        # 清空图表框架
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
            
        # 创建新图表
        fig = Figure(figsize=(8, 4), dpi=100)
        fig.patch.set_facecolor('#f0f0f0')
        
        # 如果没有性能数据，使用模拟数据
        if not self.performance_data:
            # 生成模拟数据
            dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
            capital = [10000 * (1 + np.random.normal(0.001, 0.01) * i) for i in range(30)]
            
            # 创建资金曲线子图
            ax1 = fig.add_subplot(111)
            ax1.plot(dates, capital, 'b-', label='资金曲线')
            ax1.set_title('滚雪球策略性能 (模拟数据)', fontproperties='SimHei')
            ax1.set_ylabel('资金 (USDT)', fontproperties='SimHei')
            ax1.grid(True, linestyle='--', alpha=0.7)
            
            # 格式化x轴日期
            fig.autofmt_xdate()
            
            # 添加图例
            ax1.legend(loc='upper left', prop={'family': 'SimHei'})
            
        else:
            # 使用实际性能数据
            dates = [record['timestamp'] for record in self.performance_data]
            capital = [record['capital'] for record in self.performance_data]
            
            # 计算回撤
            drawdowns = []
            peak = capital[0]
            for value in capital:
                peak = max(peak, value)
                drawdown = (peak - value) / peak if peak > 0 else 0
                drawdowns.append(drawdown)
            
            # 创建资金曲线子图
            ax1 = fig.add_subplot(211)
            ax1.plot(dates, capital, 'b-', label='资金曲线')
            ax1.set_title('滚雪球策略性能', fontproperties='SimHei')
            ax1.set_ylabel('资金 (USDT)', fontproperties='SimHei')
            ax1.grid(True, linestyle='--', alpha=0.7)
            
            # 创建回撤子图
            ax2 = fig.add_subplot(212, sharex=ax1)
            ax2.fill_between(dates, 0, [d * 100 for d in drawdowns], color='r', alpha=0.3, label='回撤 (%)')
            ax2.set_ylabel('回撤 (%)', fontproperties='SimHei')
            ax2.set_ylim(0, max(drawdowns) * 100 * 1.5 if drawdowns else 10)
            ax2.grid(True, linestyle='--', alpha=0.7)
            
            # 格式化x轴日期
            fig.autofmt_xdate()
            
            # 添加图例
            ax1.legend(loc='upper left', prop={'family': 'SimHei'})
            ax2.legend(loc='upper left', prop={'family': 'SimHei'})
            
        # 调整布局
        fig.tight_layout()
        
        # 将图表添加到GUI
        canvas = FigureCanvasTkAgg(fig, master=self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.chart_data['capital'].append(new_capital)
        
        # 计算回撤
        peak = max(self.chart_data['capital'])
        drawdown = (peak - new_capital) / peak
        self.chart_data['drawdown'].append(drawdown)
        
        # 限制数据点数量
        if len(self.chart_data['capital']) > 100:
            self.chart_data['capital'] = self.chart_data['capital'][-100:]
            self.chart_data['drawdown'] = self.chart_data['drawdown'][-100:]
        
        # 更新图表
        self.ax1.clear()
        self.ax1.plot(self.chart_data['capital'], 'b-', linewidth=2)
        self.ax1.set_title('资金增长曲线')
        self.ax1.set_ylabel('资金 (USDT)')
        self.ax1.grid(True)
        
        self.ax2.clear()
        self.ax2.fill_between(range(len(self.chart_data['drawdown'])), 
                             self.chart_data['drawdown'], 0, 
                             color='red', alpha=0.3)
        self.ax2.set_title('回撤曲线')
        self.ax2.set_ylabel('回撤 (%)')
        self.ax2.grid(True)
        
        self.canvas.draw()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    app = SpotSnowballGUI()
    app.run()

if __name__ == "__main__":
    main()

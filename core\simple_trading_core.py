#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化交易系统核心模块
提供基本的交易功能和状态管理
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

# 设置日志
logger = logging.getLogger(__name__)

class SimpleTradingCore:
    """简化交易系统核心"""
    
    def __init__(self, api_connector=None, config: Dict[str, Any] = None):
        """初始化简化交易系统核心"""
        self.api_connector = api_connector
        self.config = config or {}
        
        # 交易状态
        self.is_running = False
        self.positions = {}
        self.orders = {}
        self.daily_pnl = 0.0
        self.trade_count = 0
        
        # 线程控制
        self.trading_thread = None
        self.stop_trading_flag = False
        
        # 风险参数
        self.max_position_size = self.config.get('max_position_size', 1000.0)
        self.max_daily_loss = self.config.get('max_daily_loss', 500.0)
        self.risk_per_trade = self.config.get('risk_per_trade', 0.02)
        
        logger.info("✅ 简化交易系统核心初始化完成")
    
    def start(self) -> bool:
        """启动交易系统"""
        try:
            if self.is_running:
                logger.warning("交易系统已在运行")
                return False
            
            if not self.api_connector:
                logger.warning("API连接器未设置，使用模拟模式")
            
            self.is_running = True
            self.stop_trading_flag = False
            
            # 启动交易线程
            self.trading_thread = threading.Thread(
                target=self._trading_loop, 
                daemon=True
            )
            self.trading_thread.start()
            
            logger.info("✅ 简化交易系统已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动交易系统失败: {e}")
            return False
    
    def stop(self) -> bool:
        """停止交易系统"""
        try:
            self.is_running = False
            self.stop_trading_flag = True
            
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=5)
            
            logger.info("✅ 简化交易系统已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止交易系统失败: {e}")
            return False
    
    def _trading_loop(self):
        """交易主循环"""
        logger.info("交易主循环已启动")
        
        while not self.stop_trading_flag and self.is_running:
            try:
                # 处理交易信号
                self._process_trading_signals()
                
                # 检查风险管理
                self._check_risk_management()
                
                # 更新持仓状态
                self._update_positions()
                
                # 等待下一个周期
                time.sleep(5)  # 5秒检查一次
                
            except Exception as e:
                logger.error(f"交易循环错误: {e}")
                time.sleep(10)
        
        logger.info("交易主循环已结束")
    
    def _process_trading_signals(self):
        """处理交易信号"""
        try:
            # 获取市场数据
            if (hasattr(self.api_connector, 'market_data_cache') and 
                self.api_connector.market_data_cache):
                
                market_data = self.api_connector.market_data_cache
                
                for symbol, data in market_data.items():
                    # 简单的交易信号生成
                    signal = self._generate_simple_signal(symbol, data)
                    
                    if signal and signal.get('action') != 'HOLD':
                        self._execute_signal(signal)
            
        except Exception as e:
            logger.error(f"处理交易信号失败: {e}")
    
    def _generate_simple_signal(self, symbol: str, market_data) -> Optional[Dict]:
        """生成简单的交易信号"""
        try:
            price = market_data.price
            change_24h = market_data.change_24h
            
            # 简单的趋势跟踪策略
            if change_24h > 5.0:  # 上涨超过5%
                return {
                    'symbol': symbol,
                    'action': 'BUY',
                    'confidence': 0.6,
                    'price': price,
                    'timestamp': datetime.now(),
                    'reason': f"24h涨幅 {change_24h:.2f}%"
                }
            elif change_24h < -5.0:  # 下跌超过5%
                return {
                    'symbol': symbol,
                    'action': 'SELL',
                    'confidence': 0.6,
                    'price': price,
                    'timestamp': datetime.now(),
                    'reason': f"24h跌幅 {change_24h:.2f}%"
                }
            
            return {
                'symbol': symbol,
                'action': 'HOLD',
                'confidence': 0.5,
                'price': price,
                'timestamp': datetime.now(),
                'reason': "无明确趋势"
            }
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return None
    
    def _execute_signal(self, signal: Dict):
        """执行交易信号"""
        try:
            # 检查风险限制
            if not self._check_trade_risk(signal):
                return
            
            # 计算交易数量
            quantity = self._calculate_position_size(signal)
            
            if quantity <= 0:
                return
            
            # 记录交易信号（模拟执行）
            logger.info(f"模拟执行: {signal['action']} {quantity:.4f} {signal['symbol']} @ ${signal['price']:.4f}")
            
            # 更新交易计数
            self.trade_count += 1
            
            # 模拟订单记录
            order_id = f"ORDER_{int(time.time())}_{self.trade_count}"
            self.orders[order_id] = {
                'symbol': signal['symbol'],
                'action': signal['action'],
                'quantity': quantity,
                'price': signal['price'],
                'timestamp': signal['timestamp'],
                'status': 'FILLED'  # 模拟成交
            }
            
        except Exception as e:
            logger.error(f"执行交易信号失败: {e}")
    
    def _check_trade_risk(self, signal: Dict) -> bool:
        """检查交易风险"""
        try:
            # 检查最大并发交易数
            active_positions = len([p for p in self.positions.values() if p.get('status') == 'ACTIVE'])
            if active_positions >= 5:  # 最大5个并发交易
                logger.warning("达到最大并发交易数限制")
                return False
            
            # 检查日损失限制
            if self.daily_pnl < -self.max_daily_loss:
                logger.warning(f"达到日损失限制: {self.max_daily_loss}")
                return False
            
            # 检查信号置信度
            if signal.get('confidence', 0) < 0.5:
                logger.debug(f"信号置信度过低: {signal.get('confidence')}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return False
    
    def _calculate_position_size(self, signal: Dict) -> float:
        """计算持仓大小"""
        try:
            # 基于风险比例计算持仓大小
            risk_amount = self.max_position_size * self.risk_per_trade
            
            # 基于价格计算数量
            price = signal.get('price', 0)
            if price > 0:
                quantity = risk_amount / price
                return min(quantity, self.max_position_size / price)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"计算持仓大小失败: {e}")
            return 0.0
    
    def _check_risk_management(self):
        """检查风险管理"""
        try:
            # 检查持仓风险
            total_exposure = 0.0
            for symbol, position in self.positions.items():
                if position.get('status') == 'ACTIVE':
                    exposure = position.get('quantity', 0) * position.get('current_price', 0)
                    total_exposure += exposure
            
            # 风险警告
            if total_exposure > self.max_position_size:
                logger.warning(f"总持仓超过限制: {total_exposure:.2f} > {self.max_position_size}")
            
        except Exception as e:
            logger.error(f"风险管理检查失败: {e}")
    
    def _update_positions(self):
        """更新持仓状态"""
        try:
            # 更新持仓的当前价格
            if (hasattr(self.api_connector, 'market_data_cache') and 
                self.api_connector.market_data_cache):
                
                for symbol, position in self.positions.items():
                    if symbol in self.api_connector.market_data_cache:
                        current_price = self.api_connector.market_data_cache[symbol].price
                        position['current_price'] = current_price
                        
                        # 计算盈亏
                        entry_price = position.get('entry_price', 0)
                        quantity = position.get('quantity', 0)
                        if entry_price > 0:
                            pnl = (current_price - entry_price) * quantity
                            position['pnl'] = pnl
            
        except Exception as e:
            logger.error(f"更新持仓状态失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取交易系统状态"""
        return {
            "is_running": self.is_running,
            "api_connected": hasattr(self.api_connector, 'is_connected') and self.api_connector.is_connected,
            "positions_count": len(self.positions),
            "orders_count": len(self.orders),
            "daily_pnl": self.daily_pnl,
            "trade_count": self.trade_count,
            "max_position_size": self.max_position_size,
            "max_daily_loss": self.max_daily_loss,
            "timestamp": datetime.now()
        }
    
    def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """下单接口（简化版本）"""
        try:
            # 模拟下单
            order_id = f"ORDER_{int(time.time())}_{len(self.orders)}"
            
            order = {
                'id': order_id,
                'symbol': order_data.get('symbol'),
                'side': order_data.get('side'),
                'amount': order_data.get('amount'),
                'price': order_data.get('price'),
                'status': 'PENDING',
                'timestamp': datetime.now()
            }
            
            self.orders[order_id] = order
            
            logger.info(f"模拟下单成功: {order_id}")
            
            return {
                'success': True,
                'message': '模拟下单成功',
                'order_id': order_id,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            error_msg = f"下单失败: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'order_id': None,
                'timestamp': datetime.now()
            }

# 全局实例
_simple_trading_core_instance = None

def get_trading_system_core(api_connector=None, config: Dict[str, Any] = None):
    """获取简化交易系统核心实例"""
    global _simple_trading_core_instance
    if _simple_trading_core_instance is None:
        _simple_trading_core_instance = SimpleTradingCore(api_connector, config)
    return _simple_trading_core_instance

if __name__ == "__main__":
    print("简化交易系统核心模块加载完成")
    core = get_trading_system_core()
    print(f"交易系统状态: {core.get_status()}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gate.io交易所数据连接器
Gate.io Exchange Data Connector

为终极版交易系统提供Gate.io实时数据
"""

import ccxt
import pandas as pd
import numpy as np
import websocket
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GateDataConnector:
    """Gate.io数据连接器"""

    def __init__(self, api_key: str = "", secret: str = "", sandbox: bool = True):
        """初始化Gate.io连接器"""
        self.api_key = api_key
        self.secret = secret
        self.sandbox = sandbox

        # 初始化交易所
        self.exchange = None
        self.ws = None
        self.is_connected = False

        # 数据存储
        self.tickers = {}
        self.orderbooks = {}
        self.trades = {}
        self.klines = {}

        # 支持的交易对 (Gate.io格式)
        self.supported_pairs = [
            'BTC/USDT', 'ETH/USDT', 'GT/USDT',
            'ADA/USDT', 'SOL/USDT', 'MATIC/USDT',
            'DOT/USDT', 'LINK/USDT', 'UNI/USDT'
        ]

        # Gate.io实际交易对映射
        self.gate_pairs = {}

        logger.info("Gate.io数据连接器初始化完成")

    def _find_actual_pairs(self, markets: Dict):
        """查找实际的交易对"""
        try:
            # 查找BTC/USDT相关的交易对
            btc_pairs = [symbol for symbol in markets.keys() if 'BTC' in symbol and 'USDT' in symbol]
            eth_pairs = [symbol for symbol in markets.keys() if 'ETH' in symbol and 'USDT' in symbol]

            if btc_pairs:
                self.gate_pairs['BTC/USDT'] = btc_pairs[0]
                logger.info(f"找到BTC交易对: {btc_pairs[0]}")

            if eth_pairs:
                self.gate_pairs['ETH/USDT'] = eth_pairs[0]
                logger.info(f"找到ETH交易对: {eth_pairs[0]}")

            # 更新支持的交易对为实际找到的
            self.supported_pairs = list(self.gate_pairs.values())[:5]  # 取前5个

        except Exception as e:
            logger.error(f"查找实际交易对失败: {e}")
            # 使用默认的Gate.io格式
            self.supported_pairs = ['BTC_USDT', 'ETH_USDT', 'GT_USDT']

    def connect_rest_api(self) -> bool:
        """连接REST API"""
        try:
            self.exchange = ccxt.gate({
                'apiKey': self.api_key,
                'secret': self.secret,
                'sandbox': self.sandbox,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot'
                }
            })

            # 测试连接
            markets = self.exchange.load_markets()
            logger.info(f"成功连接Gate.io REST API，支持 {len(markets)} 个交易对")

            # 查找实际的交易对
            self._find_actual_pairs(markets)

            self.is_connected = True
            return True

        except Exception as e:
            logger.error(f"连接Gate.io REST API失败: {e}")
            return False

    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """获取实时价格"""
        try:
            if not self.exchange:
                return None

            # 使用实际的交易对
            actual_symbol = self.gate_pairs.get(symbol, symbol)
            if actual_symbol not in self.exchange.markets:
                # 如果还是找不到，尝试直接使用
                if symbol not in self.exchange.markets:
                    logger.warning(f"交易对 {symbol} 不存在")
                    return None
                actual_symbol = symbol

            ticker = self.exchange.fetch_ticker(actual_symbol)

            # 标准化数据格式
            standardized_ticker = {
                'symbol': symbol,
                'timestamp': ticker['timestamp'],
                'datetime': ticker['datetime'],
                'last': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'high': ticker['high'],
                'low': ticker['low'],
                'volume': ticker['baseVolume'],
                'change': ticker['change'],
                'percentage': ticker['percentage']
            }

            self.tickers[symbol] = standardized_ticker
            return standardized_ticker

        except Exception as e:
            logger.error(f"获取{symbol}价格失败: {e}")
            return None

    def get_orderbook(self, symbol: str, limit: int = 20) -> Optional[Dict]:
        """获取订单簿"""
        try:
            if not self.exchange:
                return None

            orderbook = self.exchange.fetch_order_book(symbol, limit)

            # 标准化数据格式
            standardized_orderbook = {
                'symbol': symbol,
                'timestamp': orderbook['timestamp'],
                'datetime': orderbook['datetime'],
                'bids': orderbook['bids'][:limit],
                'asks': orderbook['asks'][:limit],
                'bid_price': orderbook['bids'][0][0] if orderbook['bids'] else 0,
                'ask_price': orderbook['asks'][0][0] if orderbook['asks'] else 0,
                'spread': 0
            }

            # 计算价差
            if standardized_orderbook['bid_price'] and standardized_orderbook['ask_price']:
                spread = standardized_orderbook['ask_price'] - standardized_orderbook['bid_price']
                standardized_orderbook['spread'] = spread / standardized_orderbook['bid_price']

            self.orderbooks[symbol] = standardized_orderbook
            return standardized_orderbook

        except Exception as e:
            logger.error(f"获取{symbol}订单簿失败: {e}")
            return None

    def get_klines(self, symbol: str, timeframe: str = '15m', limit: int = 100) -> Optional[pd.DataFrame]:
        """获取K线数据"""
        try:
            if not self.exchange:
                return None

            # 获取OHLCV数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

            if not ohlcv:
                return None

            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['symbol'] = symbol
            df['timeframe'] = timeframe

            # 计算技术指标
            df = self._calculate_indicators(df)

            self.klines[f"{symbol}_{timeframe}"] = df
            return df

        except Exception as e:
            logger.error(f"获取{symbol} K线数据失败: {e}")
            return None

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            # 移动平均线
            df['ma_5'] = df['close'].rolling(window=5).mean()
            df['ma_10'] = df['close'].rolling(window=10).mean()
            df['ma_20'] = df['close'].rolling(window=20).mean()
            df['ma_30'] = df['close'].rolling(window=30).mean()

            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # 成交量移动平均
            df['volume_ma'] = df['volume'].rolling(window=20).mean()

            # 价格变化率
            df['price_change'] = df['close'].pct_change()
            df['price_change_5'] = df['close'].pct_change(5)

            return df

        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return df

    def get_recent_trades(self, symbol: str, limit: int = 50) -> Optional[List[Dict]]:
        """获取最近交易"""
        try:
            if not self.exchange:
                return None

            trades = self.exchange.fetch_trades(symbol, limit=limit)

            # 标准化交易数据
            standardized_trades = []
            for trade in trades:
                standardized_trade = {
                    'id': trade['id'],
                    'timestamp': trade['timestamp'],
                    'datetime': trade['datetime'],
                    'symbol': trade['symbol'],
                    'side': trade['side'],
                    'amount': trade['amount'],
                    'price': trade['price'],
                    'cost': trade['cost']
                }
                standardized_trades.append(standardized_trade)

            self.trades[symbol] = standardized_trades
            return standardized_trades

        except Exception as e:
            logger.error(f"获取{symbol}交易数据失败: {e}")
            return None

    def get_market_summary(self) -> Dict:
        """获取市场概况"""
        try:
            summary = {
                'timestamp': datetime.now(),
                'exchange': 'Gate.io',
                'total_pairs': len(self.supported_pairs),
                'active_pairs': 0,
                'market_data': {}
            }

            for symbol in self.supported_pairs:
                ticker = self.get_ticker(symbol)
                if ticker:
                    summary['active_pairs'] += 1
                    summary['market_data'][symbol] = {
                        'price': ticker['last'],
                        'change': ticker['change'],
                        'percentage': ticker['percentage'],
                        'volume': ticker['volume']
                    }

            return summary

        except Exception as e:
            logger.error(f"获取市场概况失败: {e}")
            return {}

    def start_websocket(self, symbols: List[str] = None):
        """启动WebSocket连接"""
        if symbols is None:
            symbols = self.supported_pairs[:3]  # 默认监控前3个交易对

        def on_message(ws, message):
            try:
                data = json.loads(message)
                self._process_websocket_data(data)
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")

        def on_error(ws, error):
            logger.error(f"WebSocket错误: {error}")

        def on_close(ws, close_status_code, close_msg):
            logger.info("WebSocket连接已关闭")

        def on_open(ws):
            logger.info("WebSocket连接已建立")
            # 订阅价格数据
            for symbol in symbols:
                subscribe_msg = {
                    "method": "ticker.subscribe",
                    "params": [symbol.replace('/', '_').lower()],
                    "id": 1
                }
                ws.send(json.dumps(subscribe_msg))

        # 启动WebSocket
        websocket.enableTrace(False)
        ws_url = "wss://api.gateio.ws/ws/v4/"

        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        # 在新线程中运行
        ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
        ws_thread.start()

        logger.info(f"WebSocket已启动，监控 {len(symbols)} 个交易对")

    def _process_websocket_data(self, data: Dict):
        """处理WebSocket数据"""
        try:
            if 'method' in data and data['method'] == 'ticker.update':
                params = data.get('params', [])
                if len(params) >= 2:
                    symbol = params[0].replace('_', '/').upper()
                    ticker_data = params[1]

                    # 更新实时价格
                    self.tickers[symbol] = {
                        'symbol': symbol,
                        'timestamp': int(time.time() * 1000),
                        'last': float(ticker_data.get('last', 0)),
                        'bid': float(ticker_data.get('bid', 0)),
                        'ask': float(ticker_data.get('ask', 0)),
                        'high': float(ticker_data.get('high', 0)),
                        'low': float(ticker_data.get('low', 0)),
                        'volume': float(ticker_data.get('base_volume', 0)),
                        'change': float(ticker_data.get('change_percentage', 0))
                    }

        except Exception as e:
            logger.error(f"处理WebSocket数据失败: {e}")

    def get_trading_signals(self, symbol: str) -> Dict:
        """获取交易信号"""
        try:
            # 获取K线数据
            df = self.get_klines(symbol, '15m', 50)
            if df is None or len(df) < 30:
                return {'signal': 0, 'reason': '数据不足'}

            latest = df.iloc[-1]
            prev = df.iloc[-2]

            signals = []
            reasons = []

            # 突破信号
            if (latest['close'] > latest['ma_30'] * 1.02 and
                latest['ma_10'] > latest['ma_30'] and
                latest['rsi'] < 70 and
                latest['volume'] > latest['volume_ma'] * 1.5):
                signals.append(1)
                reasons.append('突破信号')

            # 超卖反弹信号
            if (latest['rsi'] < 30 and prev['rsi'] < latest['rsi'] and
                latest['close'] > latest['ma_5']):
                signals.append(1)
                reasons.append('超卖反弹')

            # 卖出信号
            if (latest['close'] < latest['ma_10'] * 0.98 or
                latest['rsi'] > 80):
                signals.append(-1)
                reasons.append('卖出信号')

            # 综合信号
            if signals:
                final_signal = 1 if sum(signals) > 0 else -1
                return {
                    'signal': final_signal,
                    'strength': abs(sum(signals)),
                    'reasons': reasons,
                    'price': latest['close'],
                    'rsi': latest['rsi'],
                    'volume_ratio': latest['volume'] / latest['volume_ma'] if latest['volume_ma'] > 0 else 1
                }
            else:
                return {'signal': 0, 'reason': '无明确信号'}

        except Exception as e:
            logger.error(f"获取{symbol}交易信号失败: {e}")
            return {'signal': 0, 'reason': f'错误: {e}'}

    def get_status(self) -> Dict:
        """获取连接状态"""
        return {
            'connected': self.is_connected,
            'exchange': 'Gate.io',
            'supported_pairs': len(self.supported_pairs),
            'active_tickers': len(self.tickers),
            'active_orderbooks': len(self.orderbooks),
            'active_klines': len(self.klines),
            'websocket_active': self.ws is not None,
            'last_update': datetime.now()
        }

def test_gate_connector():
    """测试Gate.io连接器"""
    print("🔄 测试Gate.io数据连接器")
    print("=" * 50)

    # 创建连接器（无需API密钥即可获取市场数据）
    connector = GateDataConnector()

    # 连接REST API
    print("步骤1: 连接REST API...")
    if connector.connect_rest_api():
        print("✅ REST API连接成功")
    else:
        print("❌ REST API连接失败")
        return

    # 测试获取价格
    print("\n步骤2: 获取实时价格...")
    ticker = connector.get_ticker('BTC/USDT')
    if ticker:
        print(f"✅ BTC/USDT价格: {ticker['last']:.2f} USDT")
        print(f"   24h变化: {ticker['percentage']:.2f}%")
        print(f"   成交量: {ticker['volume']:.2f}")

    # 测试获取K线
    print("\n步骤3: 获取K线数据...")
    klines = connector.get_klines('BTC/USDT', '15m', 20)
    if klines is not None:
        print(f"✅ 获取到 {len(klines)} 根K线")
        latest = klines.iloc[-1]
        print(f"   最新价格: {latest['close']:.2f}")
        print(f"   RSI: {latest['rsi']:.2f}")
        print(f"   MA20: {latest['ma_20']:.2f}")

    # 测试交易信号
    print("\n步骤4: 获取交易信号...")
    signals = connector.get_trading_signals('BTC/USDT')
    print(f"✅ 交易信号: {signals}")

    # 测试市场概况
    print("\n步骤5: 获取市场概况...")
    summary = connector.get_market_summary()
    print(f"✅ 活跃交易对: {summary.get('active_pairs', 0)}")

    # 显示状态
    print("\n步骤6: 连接状态...")
    status = connector.get_status()
    for key, value in status.items():
        print(f"   {key}: {value}")

    print("\n" + "=" * 50)
    print("🎉 Gate.io数据连接器测试完成！")

if __name__ == "__main__":
    test_gate_connector()

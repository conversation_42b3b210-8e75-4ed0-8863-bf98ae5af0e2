{"data_mtime": 1748493664, "dep_lines": [13, 14, 11, 13, 15, 16, 17, 18, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 10, 10, 20, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["matplotlib.pyplot", "matplotlib.dates", "logging", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datetime", "typing", "pathlib", "json", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "types"], "hash": "e0a763a40877c758ce0dfd56b9ed2d235bbd5d1f", "id": "core.monitoring.report_generator", "ignore_all": true, "interface_hash": "75eb94eb59fdbedb3dda23e645bcdd49d0ce49df", "mtime": 1748490862, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\monitoring\\report_generator.py", "plugin_data": null, "size": 17238, "suppressed": ["pandas"], "version_id": "1.15.0"}
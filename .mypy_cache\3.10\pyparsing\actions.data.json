{".class": "MypyFile", "_fullname": "pyparsing.actions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "OnlyOnce": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.actions.OnlyOnce", "name": "OnlyOnce", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.actions.OnlyOnce", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.actions", "mro": ["pyparsing.actions.OnlyOnce", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "l", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.OnlyOnce.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "l", "t"], "arg_types": ["pyparsing.actions.OnlyOnce", "builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OnlyOnce", "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.OnlyOnce.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method_call"], "arg_types": ["pyparsing.actions.OnlyOnce", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnlyOnce", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.actions.OnlyOnce.callable", "name": "callable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "called": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.actions.OnlyOnce.called", "name": "called", "type": "builtins.bool"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.OnlyOnce.reset", "name": "reset", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.actions.OnlyOnce.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.actions.OnlyOnce", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParseAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.actions.ParseAction", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "ParseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseException", "kind": "Gdef"}, "ParseResults": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results.ParseResults", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.actions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "col": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.col", "kind": "Gdef"}, "matchOnlyAtCol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.actions.matchOnlyAtCol", "name": "matchOnlyAtCol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_only_at_col": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.match_only_at_col", "name": "match_only_at_col", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_only_at_col", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "removeQuotes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.actions.removeQuotes", "name": "removeQuotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["s", "l", "t"], "arg_types": ["builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_quotes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["s", "l", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.remove_quotes", "name": "remove_quotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["s", "l", "t"], "arg_types": ["builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_quotes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replaceWith": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.actions.replaceWith", "name": "replaceWith", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["repl_str"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_with": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["repl_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.replace_with", "name": "replace_with", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["repl_str"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_with", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replaced_by_pep8": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.replaced_by_pep8", "kind": "Gdef"}, "withAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.actions.withAttribute", "name": "withAttribute", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "attr_dict"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "withClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.actions.withClass", "name": "with<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["classname", "namespace"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "attr_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.with_attribute", "name": "with_attribute", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "attr_dict"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_attribute", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["classname", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.actions.with_class", "name": "with_class", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["classname", "namespace"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_class", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\actions.py"}
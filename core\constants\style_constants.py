#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
样式常量
Style Constants

定义界面样式相关的常量
"""

from typing import Dict, Tuple


class StyleConstants:
    """样式常量"""
    
    # ==================== 布局常量 ====================
    # 内边距 (padding)
    PADDING_NONE = 0
    PADDING_TINY = 2
    PADDING_SMALL = 5
    PADDING_MEDIUM = 10
    PADDING_LARGE = 15
    PADDING_XLARGE = 20
    PADDING_XXLARGE = 25
    
    # 外边距 (margin)
    MARGIN_NONE = 0
    MARGIN_TINY = 2
    MARGIN_SMALL = 5
    MARGIN_MEDIUM = 10
    MARGIN_LARGE = 15
    MARGIN_XLARGE = 20
    
    # 间距 (spacing)
    SPACING_NONE = 0
    SPACING_TINY = 1
    SPACING_SMALL = 2
    SPACING_MEDIUM = 5
    SPACING_LARGE = 10
    SPACING_XLARGE = 15
    
    # ==================== 组件尺寸 ====================
    # 按钮尺寸
    BUTTON_HEIGHT_SMALL = 25
    BUTTON_HEIGHT_MEDIUM = 30
    BUTTON_HEIGHT_LARGE = 35
    BUTTON_HEIGHT_XLARGE = 40
    
    BUTTON_WIDTH_SMALL = 60
    BUTTON_WIDTH_MEDIUM = 80
    BUTTON_WIDTH_LARGE = 100
    BUTTON_WIDTH_XLARGE = 120
    
    # 输入框尺寸
    ENTRY_HEIGHT_SMALL = 20
    ENTRY_HEIGHT_MEDIUM = 25
    ENTRY_HEIGHT_LARGE = 30
    
    ENTRY_WIDTH_SMALL = 80
    ENTRY_WIDTH_MEDIUM = 120
    ENTRY_WIDTH_LARGE = 160
    ENTRY_WIDTH_XLARGE = 200
    
    # 框架尺寸
    FRAME_BORDER_WIDTH_THIN = 1
    FRAME_BORDER_WIDTH_MEDIUM = 2
    FRAME_BORDER_WIDTH_THICK = 3
    
    FRAME_MIN_HEIGHT = 50
    FRAME_MIN_WIDTH = 100
    
    # 滚动条尺寸
    SCROLLBAR_WIDTH = 16
    SCROLLBAR_MIN_HEIGHT = 20
    
    # ==================== 字体常量 ====================
    # 字体大小
    FONT_SIZE_TINY = 6
    FONT_SIZE_SMALL = 8
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    FONT_SIZE_XXLARGE = 18
    FONT_SIZE_HUGE = 20
    
    # 字体族
    FONT_FAMILY_SYSTEM = "System"
    FONT_FAMILY_SANS = "Arial"
    FONT_FAMILY_SERIF = "Times New Roman"
    FONT_FAMILY_MONO = "Courier New"
    FONT_FAMILY_UI = "Segoe UI"
    FONT_FAMILY_CODE = "Consolas"
    
    # 字体权重
    FONT_WEIGHT_NORMAL = "normal"
    FONT_WEIGHT_BOLD = "bold"
    
    # 字体样式
    FONT_STYLE_NORMAL = "normal"
    FONT_STYLE_ITALIC = "italic"
    
    # ==================== 颜色常量 ====================
    # 基础颜色
    COLOR_WHITE = "#FFFFFF"
    COLOR_BLACK = "#000000"
    COLOR_TRANSPARENT = "transparent"
    
    # 灰度颜色
    COLOR_GRAY_100 = "#F7F7F7"
    COLOR_GRAY_200 = "#E5E5E5"
    COLOR_GRAY_300 = "#D4D4D4"
    COLOR_GRAY_400 = "#A3A3A3"
    COLOR_GRAY_500 = "#737373"
    COLOR_GRAY_600 = "#525252"
    COLOR_GRAY_700 = "#404040"
    COLOR_GRAY_800 = "#262626"
    COLOR_GRAY_900 = "#171717"
    
    # 功能颜色
    COLOR_PRIMARY = "#2563EB"
    COLOR_SECONDARY = "#64748B"
    COLOR_SUCCESS = "#16A34A"
    COLOR_WARNING = "#D97706"
    COLOR_ERROR = "#DC2626"
    COLOR_INFO = "#0891B2"
    
    # 交易颜色
    COLOR_PROFIT = "#10B981"
    COLOR_LOSS = "#EF4444"
    COLOR_NEUTRAL = "#6B7280"
    COLOR_BUY = "#16A34A"
    COLOR_SELL = "#DC2626"
    
    # ==================== 透明度常量 ====================
    ALPHA_TRANSPARENT = 0.0
    ALPHA_VERY_LOW = 0.1
    ALPHA_LOW = 0.3
    ALPHA_MEDIUM = 0.5
    ALPHA_HIGH = 0.7
    ALPHA_VERY_HIGH = 0.9
    ALPHA_OPAQUE = 1.0
    
    # 状态透明度
    ALPHA_DISABLED = 0.4
    ALPHA_HOVER = 0.8
    ALPHA_ACTIVE = 1.0
    ALPHA_FOCUS = 1.0
    
    # ==================== 边框常量 ====================
    # 边框样式
    BORDER_STYLE_NONE = "none"
    BORDER_STYLE_SOLID = "solid"
    BORDER_STYLE_DASHED = "dashed"
    BORDER_STYLE_DOTTED = "dotted"
    
    # 边框半径
    BORDER_RADIUS_NONE = 0
    BORDER_RADIUS_SMALL = 2
    BORDER_RADIUS_MEDIUM = 4
    BORDER_RADIUS_LARGE = 6
    BORDER_RADIUS_XLARGE = 8
    BORDER_RADIUS_ROUND = 50  # 百分比
    
    # ==================== 阴影常量 ====================
    # 阴影级别
    SHADOW_NONE = "none"
    SHADOW_SMALL = "0 1px 2px rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "0 2px 4px rgba(0, 0, 0, 0.1)"
    SHADOW_LARGE = "0 4px 8px rgba(0, 0, 0, 0.1)"
    SHADOW_XLARGE = "0 8px 16px rgba(0, 0, 0, 0.1)"
    
    # ==================== 动画常量 ====================
    # 动画持续时间 (毫秒)
    ANIMATION_DURATION_INSTANT = 0
    ANIMATION_DURATION_FAST = 150
    ANIMATION_DURATION_NORMAL = 300
    ANIMATION_DURATION_SLOW = 500
    ANIMATION_DURATION_VERY_SLOW = 1000
    
    # 缓动函数
    EASING_LINEAR = "linear"
    EASING_EASE = "ease"
    EASING_EASE_IN = "ease-in"
    EASING_EASE_OUT = "ease-out"
    EASING_EASE_IN_OUT = "ease-in-out"
    
    # ==================== 层级常量 ====================
    Z_INDEX_BACKGROUND = -1
    Z_INDEX_NORMAL = 0
    Z_INDEX_ELEVATED = 10
    Z_INDEX_DROPDOWN = 100
    Z_INDEX_MODAL = 1000
    Z_INDEX_TOOLTIP = 10000
    
    # ==================== 响应式断点 ====================
    BREAKPOINT_SMALL = 768
    BREAKPOINT_MEDIUM = 1024
    BREAKPOINT_LARGE = 1280
    BREAKPOINT_XLARGE = 1536
    
    # ==================== 网格系统 ====================
    GRID_COLUMNS = 12
    GRID_GUTTER = 16
    GRID_MARGIN = 16
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def get_font_tuple(cls, family: str = None, size: int = None, weight: str = None) -> Tuple[str, int, str]:
        """获取字体元组"""
        family = family or cls.FONT_FAMILY_SANS
        size = size or cls.FONT_SIZE_NORMAL
        weight = weight or cls.FONT_WEIGHT_NORMAL
        return (family, size, weight)
    
    @classmethod
    def get_padding_dict(cls, padding: int) -> Dict[str, int]:
        """获取内边距字典"""
        return {
            "padx": padding,
            "pady": padding
        }
    
    @classmethod
    def get_margin_dict(cls, margin: int) -> Dict[str, int]:
        """获取外边距字典"""
        return {
            "padx": margin,
            "pady": margin
        }
    
    @classmethod
    def rgba_to_hex(cls, r: int, g: int, b: int, a: float = 1.0) -> str:
        """RGBA转十六进制颜色"""
        if a < 1.0:
            # 如果有透明度，返回带透明度的格式
            alpha = int(a * 255)
            return f"#{r:02x}{g:02x}{b:02x}{alpha:02x}"
        else:
            return f"#{r:02x}{g:02x}{b:02x}"
    
    @classmethod
    def hex_to_rgba(cls, hex_color: str) -> Tuple[int, int, int, float]:
        """十六进制颜色转RGBA"""
        hex_color = hex_color.lstrip('#')
        if len(hex_color) == 6:
            r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            a = 1.0
        elif len(hex_color) == 8:
            r, g, b, alpha = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4, 6))
            a = alpha / 255.0
        else:
            raise ValueError(f"Invalid hex color: {hex_color}")
        return (r, g, b, a)
    
    @classmethod
    def get_responsive_size(cls, base_size: int, screen_width: int) -> int:
        """根据屏幕宽度获取响应式尺寸"""
        if screen_width < cls.BREAKPOINT_SMALL:
            return int(base_size * 0.8)
        elif screen_width < cls.BREAKPOINT_MEDIUM:
            return base_size
        elif screen_width < cls.BREAKPOINT_LARGE:
            return int(base_size * 1.1)
        else:
            return int(base_size * 1.2)
    
    @classmethod
    def get_button_style(cls, size: str = "medium") -> Dict[str, any]:
        """获取按钮样式"""
        size_map = {
            "small": {
                "height": cls.BUTTON_HEIGHT_SMALL,
                "width": cls.BUTTON_WIDTH_SMALL,
                "font_size": cls.FONT_SIZE_SMALL,
                "padding": cls.PADDING_SMALL
            },
            "medium": {
                "height": cls.BUTTON_HEIGHT_MEDIUM,
                "width": cls.BUTTON_WIDTH_MEDIUM,
                "font_size": cls.FONT_SIZE_NORMAL,
                "padding": cls.PADDING_MEDIUM
            },
            "large": {
                "height": cls.BUTTON_HEIGHT_LARGE,
                "width": cls.BUTTON_WIDTH_LARGE,
                "font_size": cls.FONT_SIZE_MEDIUM,
                "padding": cls.PADDING_LARGE
            }
        }
        return size_map.get(size, size_map["medium"])


# 创建全局样式常量实例
STYLE_CONSTANTS = StyleConstants()


if __name__ == "__main__":
    # 测试样式常量
    print("🎨 样式常量测试")
    print(f"默认字体: {StyleConstants.get_font_tuple()}")
    print(f"按钮样式(中): {StyleConstants.get_button_style('medium')}")
    print(f"颜色转换: {StyleConstants.hex_to_rgba('#FF0080')}")
    print(f"响应式尺寸: {StyleConstants.get_responsive_size(16, 1920)}")
    print("✅ 样式常量测试完成")

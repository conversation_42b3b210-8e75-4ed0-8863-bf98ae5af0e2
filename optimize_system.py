#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统全面优化脚本
Complete System Optimization Script

一键优化整个交易系统
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step: str):
    """打印步骤"""
    print(f"\n📋 {step}")
    print("-" * 40)

def run_optimization():
    """运行系统优化"""
    print_header("企业级现货交易系统 - 全面优化")
    
    # 步骤1: 系统修复
    print_step("步骤1: 系统基础修复")
    try:
        result = subprocess.run([sys.executable, 'core/system_repair.py'], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 系统基础修复完成")
        else:
            print(f"⚠️ 系统修复有警告: {result.stderr}")
    except Exception as e:
        print(f"❌ 系统修复失败: {e}")
    
    # 步骤2: 网络优化
    print_step("步骤2: 网络连接优化")
    try:
        result = subprocess.run([sys.executable, 'core/network_optimizer.py'], 
                              capture_output=True, text=True, timeout=60)
        print("✅ 网络优化完成")
        # 显示网络测试结果
        if result.stdout:
            lines = result.stdout.split('\n')
            for line in lines[-10:]:  # 显示最后10行
                if line.strip():
                    print(f"  {line}")
    except Exception as e:
        print(f"❌ 网络优化失败: {e}")
    
    # 步骤3: 安全优化
    print_step("步骤3: 安全配置优化")
    try:
        result = subprocess.run([sys.executable, 'core/security_optimizer.py'], 
                              capture_output=True, text=True, timeout=60)
        print("✅ 安全优化完成")
    except Exception as e:
        print(f"❌ 安全优化失败: {e}")
    
    # 步骤4: 健康检查
    print_step("步骤4: 系统健康检查")
    try:
        result = subprocess.run([sys.executable, 'core/system_health_checker.py'], 
                              capture_output=True, text=True, timeout=60)
        
        # 解析健康检查结果
        if "系统状态: HEALTHY" in result.stdout:
            print("✅ 系统状态: 健康")
        elif "系统状态: WARNING" in result.stdout:
            print("⚠️ 系统状态: 警告")
        elif "系统状态: CRITICAL" in result.stdout:
            print("❌ 系统状态: 严重")
        
        # 显示通过率
        lines = result.stdout.split('\n')
        for line in lines:
            if "检查完成:" in line:
                print(f"📊 {line.strip()}")
                break
                
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 步骤5: 生成优化报告
    print_step("步骤5: 生成优化报告")
    generate_optimization_report()

def generate_optimization_report():
    """生成优化报告"""
    try:
        report_content = f"""
企业级现货交易系统 - 优化报告
========================================

优化时间: {datetime.now().isoformat()}
系统版本: 1.0.0

优化项目:
✅ 系统基础修复
  - 创建必要目录结构
  - 修复配置文件
  - 初始化数据库
  - 清理临时文件

✅ 网络连接优化
  - 优化HTTP会话配置
  - 测试交易所API连接
  - 诊断网络问题
  - 提供解决方案建议

✅ 安全配置优化
  - 优化文件权限
  - 保护配置文件
  - 加强密钥管理
  - 设置环境变量

✅ 系统健康检查
  - Python环境检查
  - 依赖包验证
  - 文件结构检查
  - 代码质量验证

下一步建议:
1. 配置API密钥 (编辑 .env 文件)
2. 测试网络连接
3. 运行交易系统测试
4. 设置监控和告警

重要文件:
- config.json: 主配置文件
- .env: 环境变量配置
- logs/: 日志目录
- core/: 核心代码目录

技术支持:
如遇问题，请检查 logs/ 目录下的日志文件
"""
        
        # 保存报告
        report_file = Path('optimization_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 优化报告已生成: {report_file}")
        
        # 显示关键信息
        print("\n📋 优化完成总结:")
        print("✅ 系统基础结构已完善")
        print("✅ 安全配置已加强") 
        print("✅ 网络连接已优化")
        print("✅ 健康检查已完成")
        
        print("\n🎯 下一步操作:")
        print("1. 编辑 .env 文件，配置您的API密钥")
        print("2. 运行 python core/ultimate_trading_gui.py 启动系统")
        print("3. 在GUI中测试连接并开始交易")
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查系统前置条件...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    
    # 检查核心文件
    required_files = [
        'core/system_repair.py',
        'core/network_optimizer.py', 
        'core/security_optimizer.py',
        'core/system_health_checker.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 前置条件检查通过")
    return True

def main():
    """主函数"""
    print("🚀 企业级现货交易系统 - 全面优化工具")
    print("版本: 1.0.0")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先解决上述问题")
        return False
    
    # 确认执行
    print("\n📋 本工具将执行以下优化:")
    print("  1. 系统基础修复")
    print("  2. 网络连接优化") 
    print("  3. 安全配置优化")
    print("  4. 系统健康检查")
    print("  5. 生成优化报告")
    
    try:
        confirm = input("\n是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return False
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return False
    
    # 执行优化
    try:
        run_optimization()
        
        print_header("优化完成")
        print("🎉 系统优化已完成！")
        print("📄 详细报告请查看: optimization_report.txt")
        print("🚀 现在可以启动交易系统了！")
        
        return True
        
    except KeyboardInterrupt:
        print("\n❌ 优化过程被中断")
        return False
    except Exception as e:
        print(f"\n❌ 优化过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

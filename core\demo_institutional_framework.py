#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级量化交易框架演示
Demonstration of Institutional Quantitative Trading Framework

展示如何使用机构级框架对策略进行验证、风险分析和监控
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from institutional_manager import InstitutionalFrameworkManager, create_institutional_framework
from statistical_validation import generate_validation_report
from risk_factor_analysis import generate_risk_factor_report
from performance_monitoring import MonitoringConfig

def create_sample_strategy_data():
    """创建示例策略数据"""
    # 创建时间序列
    dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
    n_days = len(dates)
    
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    # 策略1：优秀的趋势跟踪策略
    trend_strategy_returns = []
    for i in range(n_days):
        # 模拟趋势跟踪策略：有一定的序列相关性
        if i == 0:
            daily_return = np.random.normal(0.001, 0.015)
        else:
            # 添加动量效应
            momentum = trend_strategy_returns[i-1] * 0.1
            daily_return = np.random.normal(0.0008 + momentum, 0.012)
        trend_strategy_returns.append(daily_return)
    
    # 策略2：均值回归策略
    mean_reversion_returns = []
    price_level = 100
    for i in range(n_days):
        # 模拟均值回归：价格偏离均值时有回归倾向
        deviation = (price_level - 100) / 100
        expected_return = -deviation * 0.1 + np.random.normal(0.0005, 0.010)
        mean_reversion_returns.append(expected_return)
        price_level *= (1 + expected_return)
    
    # 策略3：高波动率策略（风险较高）
    high_vol_returns = np.random.normal(0.0003, 0.025, n_days)
    
    # 策略4：衰减策略（前期好，后期差）
    decay_returns = []
    for i in range(n_days):
        # 线性衰减：从好到差
        decay_factor = 1 - (i / n_days) * 0.8
        daily_return = np.random.normal(0.001 * decay_factor, 0.015)
        decay_returns.append(daily_return)
    
    return {
        'trend_following': pd.Series(trend_strategy_returns, index=dates),
        'mean_reversion': pd.Series(mean_reversion_returns, index=dates),
        'high_volatility': pd.Series(high_vol_returns, index=dates),
        'alpha_decay': pd.Series(decay_returns, index=dates)
    }

def create_benchmark_data():
    """创建基准数据（模拟沪深300）"""
    dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
    n_days = len(dates)
    
    # 模拟市场收益：有一定的波动聚类效应
    np.random.seed(123)
    market_returns = []
    volatility = 0.015
    
    for i in range(n_days):
        # GARCH效应：波动率聚类
        if i > 0:
            volatility = 0.9 * volatility + 0.1 * abs(market_returns[i-1])
        
        daily_return = np.random.normal(0.0003, volatility)
        market_returns.append(daily_return)
    
    return pd.Series(market_returns, index=dates)

def demonstrate_institutional_framework():
    """演示机构级框架的完整流程"""
    print("=" * 60)
    print("机构级量化交易框架演示")
    print("=" * 60)
    
    # 1. 创建示例数据
    print("\n1. 创建示例数据...")
    strategies_data = create_sample_strategy_data()
    benchmark_data = create_benchmark_data()
    
    print(f"创建了 {len(strategies_data)} 个策略的示例数据")
    print(f"数据时间范围: {strategies_data['trend_following'].index[0]} 到 {strategies_data['trend_following'].index[-1]}")
    
    # 2. 初始化机构级框架
    print("\n2. 初始化机构级框架...")
    
    # 配置监控参数
    monitoring_config = MonitoringConfig(
        min_sharpe_ratio=1.0,
        max_drawdown_limit=0.15,
        max_volatility=0.25,
        lookback_window=60,
        decay_threshold=0.2
    )
    
    framework = InstitutionalFrameworkManager(
        benchmark_data=benchmark_data,
        monitoring_config=monitoring_config
    )
    
    print("机构级框架初始化完成")
    
    # 3. 对每个策略进行全面分析
    print("\n3. 开始策略分析...")
    
    for strategy_name, returns in strategies_data.items():
        print(f"\n--- 分析策略: {strategy_name} ---")
        
        # 3.1 统计验证
        print(f"执行统计验证...")
        validation_result = framework.validate_strategy(
            strategy_name=strategy_name,
            returns=returns,
            turnover=0.5 if 'high' in strategy_name else 0.2  # 高波动策略假设换手率更高
        )
        
        print(f"验证评分: {validation_result.validation_score:.1f}/100")
        print(f"夏普比率: {validation_result.sharpe_ratio:.3f}")
        print(f"最大回撤: {validation_result.max_drawdown:.2%}")
        print(f"建议: {validation_result.recommendation}")
        
        # 3.2 风险因子分析
        print(f"执行风险因子分析...")
        risk_result = framework.analyze_risk_factors(
            strategy_name=strategy_name,
            returns=returns
        )
        
        print(f"模型拟合度(R²): {risk_result.r_squared:.3f}")
        print(f"跟踪误差: {risk_result.tracking_error:.2%}")
        print(f"Alpha贡献: {risk_result.idiosyncratic_return:.2%}")
        
        # 显示主要因子暴露
        significant_factors = [f for f in risk_result.factor_exposures if f.p_value < 0.05]
        if significant_factors:
            print("显著因子暴露:")
            for factor in significant_factors[:3]:  # 显示前3个
                print(f"  {factor.factor_name}: {factor.exposure:.3f} (p={factor.p_value:.3f})")
        
        # 3.3 部署到监控系统
        print(f"部署到监控系统...")
        framework.deploy_strategy(strategy_name, returns)
        
        # 模拟一些新的收益数据更新
        recent_returns = returns.iloc[-10:]  # 最近10天
        framework.update_strategy_performance(strategy_name, recent_returns)
        
        print(f"策略 {strategy_name} 分析完成")
    
    # 4. 生成综合报告
    print("\n4. 生成综合报告...")
    
    # 框架仪表板
    dashboard = framework.get_framework_dashboard()
    print(f"\n=== 框架仪表板 ===")
    print(f"总策略数: {dashboard['total_strategies']}")
    print(f"已验证策略: {dashboard['validated_strategies']}")
    print(f"已部署策略: {dashboard['deployed_strategies']}")
    print(f"平均验证评分: {dashboard['avg_validation_score']:.1f}")
    print(f"平均夏普比率: {dashboard['avg_sharpe_ratio']:.3f}")
    print(f"总警报数: {dashboard['total_alerts']}")
    print(f"高危警报数: {dashboard['high_severity_alerts']}")
    
    # 5. 详细策略报告
    print("\n5. 生成详细策略报告...")
    
    for strategy_name in strategies_data.keys():
        print(f"\n{'='*50}")
        print(f"策略 {strategy_name} 详细报告")
        print(f"{'='*50}")
        
        comprehensive_report = framework.get_strategy_comprehensive_report(strategy_name)
        print(comprehensive_report)
        
        # 生成专业报告
        if strategy_name in framework.validation_results:
            validation_report = generate_validation_report(framework.validation_results[strategy_name])
            print(f"\n--- 统计验证详细报告 ---")
            print(validation_report)
        
        if strategy_name in framework.risk_analysis_results:
            risk_report = generate_risk_factor_report(framework.risk_analysis_results[strategy_name])
            print(f"\n--- 风险因子详细报告 ---")
            print(risk_report)
    
    # 6. 导出结果
    print("\n6. 导出分析结果...")
    framework.export_results("demo_results")
    print("所有分析结果已导出到 demo_results 目录")
    
    # 7. 监控演示
    print("\n7. 监控系统演示...")
    monitoring_report = framework.monitor.generate_monitoring_report()
    print(monitoring_report)
    
    print("\n" + "="*60)
    print("机构级量化交易框架演示完成")
    print("="*60)
    
    return framework

def analyze_existing_strategy():
    """分析现有策略的示例"""
    print("\n" + "="*60)
    print("分析现有策略示例")
    print("="*60)
    
    # 这里可以加载您现有的策略数据
    # 例如：从strategies目录中的某个策略获取收益数据
    
    # 示例：分析RSI策略
    try:
        # 这里应该是实际的策略收益数据
        # 为演示目的，我们创建一个示例
        dates = pd.date_range('2023-01-01', '2024-12-31', freq='D')
        rsi_returns = np.random.normal(0.0008, 0.018, len(dates))
        rsi_strategy_returns = pd.Series(rsi_returns, index=dates)
        
        # 创建框架
        framework = create_institutional_framework()
        
        # 分析策略
        print("分析RSI策略...")
        validation_result = framework.validate_strategy(
            strategy_name="RSI_Strategy",
            returns=rsi_strategy_returns,
            turnover=0.3
        )
        
        risk_result = framework.analyze_risk_factors(
            strategy_name="RSI_Strategy",
            returns=rsi_strategy_returns
        )
        
        # 生成报告
        report = framework.get_strategy_comprehensive_report("RSI_Strategy")
        print(report)
        
    except Exception as e:
        print(f"分析现有策略时出错: {e}")
        print("请确保策略数据格式正确")

if __name__ == "__main__":
    # 运行完整演示
    framework = demonstrate_institutional_framework()
    
    # 可选：分析现有策略
    # analyze_existing_strategy()
    
    print("\n提示：")
    print("1. 查看 demo_results 目录中的导出文件")
    print("2. 可以修改 MonitoringConfig 来调整监控参数")
    print("3. 可以添加自定义因子数据来改进风险分析")
    print("4. 在实际使用中，请替换示例数据为真实的策略收益数据")

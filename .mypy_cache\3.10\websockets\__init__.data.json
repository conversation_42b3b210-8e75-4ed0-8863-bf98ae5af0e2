{".class": "MypyFile", "_fullname": "websockets", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClientConnection": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.client.ClientConnection", "kind": "Gdef"}, "ClientProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.client.ClientProtocol", "kind": "Gdef"}, "Close": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Close", "kind": "Gdef"}, "CloseCode": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.CloseCode", "kind": "Gdef"}, "ConcurrencyError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ConcurrencyError", "kind": "Gdef"}, "ConnectionClosed": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ConnectionClosed", "kind": "Gdef"}, "ConnectionClosedError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ConnectionClosedError", "kind": "Gdef"}, "ConnectionClosedOK": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ConnectionClosedOK", "kind": "Gdef"}, "Data": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Data", "kind": "Gdef"}, "DuplicateParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.DuplicateParameter", "kind": "Gdef"}, "ExtensionName": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionName", "kind": "Gdef"}, "ExtensionParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionParameter", "kind": "Gdef"}, "Frame": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Frame", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef"}, "HeadersLike": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.HeadersLike", "kind": "Gdef"}, "InvalidHandshake": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHandshake", "kind": "Gdef"}, "InvalidHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeader", "kind": "Gdef"}, "InvalidHeaderFormat": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderFormat", "kind": "Gdef"}, "InvalidHeaderValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderValue", "kind": "Gdef"}, "InvalidMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidMessage", "kind": "Gdef"}, "InvalidOrigin": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidOrigin", "kind": "Gdef"}, "InvalidParameterName": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidParameterName", "kind": "Gdef"}, "InvalidParameterValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidParameterValue", "kind": "Gdef"}, "InvalidProxy": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidProxy", "kind": "Gdef"}, "InvalidProxyMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidProxyMessage", "kind": "Gdef"}, "InvalidProxyStatus": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidProxyStatus", "kind": "Gdef"}, "InvalidState": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidState", "kind": "Gdef"}, "InvalidStatus": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidStatus", "kind": "Gdef"}, "InvalidURI": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidURI", "kind": "Gdef"}, "InvalidUpgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidUpgrade", "kind": "Gdef"}, "LoggerLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.LoggerLike", "kind": "Gdef"}, "MultipleValuesError": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.MultipleValuesError", "kind": "Gdef"}, "NegotiationError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.NegotiationError", "kind": "Gdef"}, "Opcode": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Opcode", "kind": "Gdef"}, "Origin": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Origin", "kind": "Gdef"}, "PayloadTooBig": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.PayloadTooBig", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.Protocol", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProtocolError", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProxyError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Response", "kind": "Gdef"}, "Router": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.router.Router", "kind": "Gdef"}, "SecurityError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.SecurityError", "kind": "Gdef"}, "Server": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.Server", "kind": "Gdef"}, "ServerConnection": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.ServerConnection", "kind": "Gdef"}, "ServerProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.server.ServerProtocol", "kind": "Gdef"}, "Side": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.Side", "kind": "Gdef"}, "State": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.State", "kind": "Gdef"}, "StatusLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.StatusLike", "kind": "Gdef"}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "WebSocketException": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.WebSocketException", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "websockets.version.version", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "basic_auth": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.basic_auth", "kind": "Gdef"}, "broadcast": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.connection.broadcast", "kind": "Gdef"}, "connect": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.client.connect", "kind": "Gdef"}, "lazy_import": {".class": "SymbolTableNode", "cross_ref": "websockets.imports.lazy_import", "kind": "Gdef", "module_public": false}, "route": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.router.route", "kind": "Gdef"}, "serve": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.serve", "kind": "Gdef"}, "unix_connect": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.client.unix_connect", "kind": "Gdef"}, "unix_route": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.router.unix_route", "kind": "Gdef"}, "unix_serve": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.unix_serve", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\__init__.py"}
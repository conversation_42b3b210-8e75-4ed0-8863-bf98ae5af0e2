{"data_mtime": 1748490238, "dep_lines": [4, 1, 3, 5, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30], "dependencies": ["collections.abc", "__future__", "warnings", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "5f62708e729847c23510f0bb83d0b9a3f6222e8c", "id": "websockets.imports", "ignore_all": true, "interface_hash": "b649218a3a9610aa999e54e58b513955a12439e3", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\imports.py", "plugin_data": null, "size": 2895, "suppressed": [], "version_id": "1.15.0"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全配置优化器
Security Configuration Optimizer

优化系统安全配置，加强数据保护
"""

import os
import json
import stat
import hashlib
import secrets
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

class SecurityOptimizer:
    """安全配置优化器"""
    
    def __init__(self):
        self.security_issues = []
        self.fixed_issues = []
        
    def run_security_optimization(self) -> Dict[str, Any]:
        """运行安全优化"""
        print("🔒 开始安全配置优化...")
        print("=" * 40)
        
        optimizations = [
            ('文件权限优化', self._optimize_file_permissions),
            ('配置文件安全', self._secure_config_files),
            ('密钥管理优化', self._optimize_key_management),
            ('日志安全配置', self._secure_logging),
            ('数据库安全', self._secure_database),
            ('环境变量配置', self._setup_environment_variables)
        ]
        
        for name, func in optimizations:
            try:
                result = func()
                if result:
                    print(f"✅ {name}: 优化完成")
                    self.fixed_issues.append(name)
                else:
                    print(f"ℹ️ {name}: 无需优化")
            except Exception as e:
                print(f"❌ {name}: 优化失败 - {str(e)}")
                self.security_issues.append(f"{name}: {str(e)}")
        
        return {
            'timestamp': datetime.now().isoformat(),
            'fixed_issues': self.fixed_issues,
            'remaining_issues': self.security_issues
        }
    
    def _optimize_file_permissions(self) -> bool:
        """优化文件权限"""
        sensitive_files = [
            'config.json',
            'core/gui_config.json',
            'core/secure_vault.db',
            'core/encrypted_keys.json'
        ]
        
        optimized = False
        
        for file_path in sensitive_files:
            path = Path(file_path)
            if path.exists():
                try:
                    # 在Windows上设置文件为只读（对当前用户）
                    current_stat = path.stat()
                    
                    # 检查文件是否可被其他用户访问
                    if os.name == 'nt':  # Windows
                        # Windows文件权限处理
                        path.chmod(stat.S_IREAD | stat.S_IWRITE)
                    else:  # Unix/Linux
                        # 设置为600权限（只有所有者可读写）
                        path.chmod(0o600)
                    
                    print(f"  🔒 优化文件权限: {file_path}")
                    optimized = True
                    
                except Exception as e:
                    print(f"  ⚠️ 无法优化权限: {file_path} - {e}")
        
        return optimized
    
    def _secure_config_files(self) -> bool:
        """保护配置文件"""
        config_files = ['config.json', 'core/gui_config.json']
        secured = False
        
        for config_file in config_files:
            path = Path(config_file)
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查是否有明文密钥
                    sensitive_keys = ['api_key', 'api_secret', 'secret', 'password']
                    has_sensitive = False
                    
                    def check_sensitive(obj, prefix=""):
                        nonlocal has_sensitive
                        if isinstance(obj, dict):
                            for key, value in obj.items():
                                if any(sk in key.lower() for sk in sensitive_keys):
                                    if value and len(str(value)) > 10:
                                        has_sensitive = True
                                        print(f"  ⚠️ 发现明文敏感信息: {prefix}{key}")
                                check_sensitive(value, f"{prefix}{key}.")
                        elif isinstance(obj, list):
                            for i, item in enumerate(obj):
                                check_sensitive(item, f"{prefix}[{i}].")
                    
                    check_sensitive(config)
                    
                    if has_sensitive:
                        # 创建安全配置模板
                        secure_config = self._create_secure_config_template(config)
                        
                        # 备份原文件
                        backup_path = path.with_suffix('.backup')
                        path.rename(backup_path)
                        
                        # 保存安全配置
                        with open(path, 'w', encoding='utf-8') as f:
                            json.dump(secure_config, f, indent=2, ensure_ascii=False)
                        
                        print(f"  🔒 配置文件已安全化: {config_file}")
                        print(f"  📄 原文件备份为: {backup_path}")
                        secured = True
                
                except Exception as e:
                    print(f"  ❌ 配置文件安全化失败: {config_file} - {e}")
        
        return secured
    
    def _create_secure_config_template(self, config: Dict) -> Dict:
        """创建安全配置模板"""
        secure_config = config.copy()
        
        # 移除敏感信息，使用环境变量引用
        sensitive_mappings = {
            'api_key': '${EXCHANGE_API_KEY}',
            'api_secret': '${EXCHANGE_API_SECRET}',
            'secret': '${EXCHANGE_SECRET}',
            'password': '${DATABASE_PASSWORD}'
        }
        
        def secure_dict(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if any(sk in key.lower() for sk in sensitive_mappings.keys()):
                        if value and len(str(value)) > 10:
                            # 替换为环境变量引用
                            for sk, env_var in sensitive_mappings.items():
                                if sk in key.lower():
                                    obj[key] = env_var
                                    break
                    else:
                        secure_dict(value)
            elif isinstance(obj, list):
                for item in obj:
                    secure_dict(item)
        
        secure_dict(secure_config)
        return secure_config
    
    def _optimize_key_management(self) -> bool:
        """优化密钥管理"""
        try:
            from secure_key_management import get_key_manager
            
            # 检查密钥管理器状态
            km = get_key_manager("temp_password")
            status = km.get_security_status()
            
            optimized = False
            
            # 检查加密状态
            if not status.get('encryption_enabled', False):
                print("  ⚠️ 密钥加密未启用")
                # 这里可以添加启用加密的逻辑
            
            # 检查文件安全性
            if not status.get('file_secure', True):
                print("  🔒 优化密钥文件安全性")
                optimized = True
            
            return optimized
            
        except Exception as e:
            print(f"  ❌ 密钥管理优化失败: {e}")
            return False
    
    def _secure_logging(self) -> bool:
        """保护日志安全"""
        log_dir = Path('logs')
        if not log_dir.exists():
            return False
        
        secured = False
        
        # 设置日志目录权限
        try:
            if os.name != 'nt':  # Unix/Linux
                log_dir.chmod(0o750)  # 所有者读写执行，组读执行
            
            # 检查日志文件中的敏感信息
            for log_file in log_dir.glob('*.log'):
                if log_file.stat().st_size > 0:
                    try:
                        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        # 检查敏感信息模式
                        sensitive_patterns = [
                            'api_key', 'secret', 'password', 'token'
                        ]
                        
                        for pattern in sensitive_patterns:
                            if pattern in content.lower():
                                print(f"  ⚠️ 日志文件可能包含敏感信息: {log_file.name}")
                                break
                    
                    except Exception:
                        pass  # 忽略读取错误
            
            secured = True
            
        except Exception as e:
            print(f"  ❌ 日志安全配置失败: {e}")
        
        return secured
    
    def _secure_database(self) -> bool:
        """保护数据库安全"""
        db_files = [
            'core/monitoring.db',
            'core/secure_vault.db'
        ]
        
        secured = False
        
        for db_file in db_files:
            path = Path(db_file)
            if path.exists():
                try:
                    # 设置数据库文件权限
                    if os.name != 'nt':  # Unix/Linux
                        path.chmod(0o600)  # 只有所有者可读写
                    
                    print(f"  🔒 数据库文件权限已优化: {db_file}")
                    secured = True
                    
                except Exception as e:
                    print(f"  ❌ 数据库安全配置失败: {db_file} - {e}")
        
        return secured
    
    def _setup_environment_variables(self) -> bool:
        """设置环境变量配置"""
        env_file = Path('.env')
        
        if env_file.exists():
            return False  # 已存在
        
        # 创建环境变量模板
        env_template = """# 交易系统环境变量配置
# 请根据实际情况填写以下变量

# 交易所API配置
EXCHANGE_API_KEY=your_api_key_here
EXCHANGE_API_SECRET=your_api_secret_here
EXCHANGE_SANDBOX=true

# 系统配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 安全配置
TRADING_MASTER_PASSWORD=your_master_password_here

# 数据库配置（如果使用外部数据库）
DATABASE_PASSWORD=your_db_password_here

# 监控配置
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=your_webhook_url_here
"""
        
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_template)
            
            # 设置文件权限
            if os.name != 'nt':  # Unix/Linux
                env_file.chmod(0o600)
            
            print(f"  📄 环境变量模板已创建: {env_file}")
            print(f"  ⚠️ 请编辑 {env_file} 文件并填写实际配置")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 环境变量配置失败: {e}")
            return False
    
    def generate_security_report(self) -> str:
        """生成安全报告"""
        report = f"""
安全配置优化报告
生成时间: {datetime.now().isoformat()}

已修复的安全问题:
{chr(10).join(f"✅ {issue}" for issue in self.fixed_issues)}

仍需关注的问题:
{chr(10).join(f"⚠️ {issue}" for issue in self.security_issues)}

安全建议:
1. 定期更换API密钥
2. 使用强密码保护主密码
3. 定期备份重要数据
4. 监控系统访问日志
5. 保持系统和依赖包更新
"""
        return report

def main():
    """主函数"""
    optimizer = SecurityOptimizer()
    results = optimizer.run_security_optimization()
    
    print("\n" + "=" * 40)
    print("📊 安全优化完成")
    print(f"✅ 已修复: {len(results['fixed_issues'])} 个问题")
    print(f"⚠️ 仍需关注: {len(results['remaining_issues'])} 个问题")
    
    # 生成安全报告
    report = optimizer.generate_security_report()
    
    # 保存报告
    report_file = Path('logs/security_report.txt')
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 安全报告已保存: {report_file}")

if __name__ == "__main__":
    main()

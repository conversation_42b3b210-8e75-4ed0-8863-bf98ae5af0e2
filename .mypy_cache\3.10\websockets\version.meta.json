{"data_mtime": 1748490238, "dep_lines": [3, 1, 3, 27, 28, 29, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["importlib.metadata", "__future__", "importlib", "pathlib", "re", "subprocess", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "21a098bd81f728b18125cbd775608ed6f6809389", "id": "websockets.version", "ignore_all": true, "interface_hash": "70a212d62c971266ead30c33b0fe925de80585b3", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\version.py", "plugin_data": null, "size": 3296, "suppressed": [], "version_id": "1.15.0"}
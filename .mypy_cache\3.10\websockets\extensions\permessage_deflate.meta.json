{"data_mtime": 1748490240, "dep_lines": [17, 4, 7, 8, 16, 1, 3, 5, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["websockets.extensions.base", "collections.abc", "websockets.frames", "websockets.exceptions", "websockets.typing", "__future__", "zlib", "typing", "websockets", "builtins", "_frozen_importlib", "abc", "enum", "typing_extensions"], "hash": "d720e39c3a9fbd757ef7a9dcfda94646be64489c", "id": "websockets.extensions.permessage_deflate", "ignore_all": true, "interface_hash": "8b9cc0db8deae8a4a71927696a3cdb32e6b01e25", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py", "plugin_data": null, "size": 26408, "suppressed": [], "version_id": "1.15.0"}
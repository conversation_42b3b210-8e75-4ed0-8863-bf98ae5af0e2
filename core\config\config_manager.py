#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器
Configuration Manager

统一管理系统配置参数
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 交易参数
        "trading": {
            "initial_capital": 10000.0,
            "risk_per_trade": 0.02,
            "stop_loss_pct": 0.05,
            "take_profit_pct": 0.10,
            "min_profit_rate": 0.01,
            "win_rate_threshold": 0.6,
            "signal_strength_threshold": 0.7,
            "max_positions": 5,
            "position_size_pct": 0.1
        },
        
        # 倍增策略参数
        "doubling_strategy": {
            "target_multiplier": 2.0,
            "compound_mode": True,
            "reinvest_profits": True,
            "max_drawdown": 0.2,
            "growth_target": 0.5,
            "risk_adjustment": True
        },
        
        # 界面参数
        "ui": {
            "theme": "dark",
            "update_interval": 5,  # 秒
            "auto_refresh": True,
            "show_notifications": True,
            "log_level": "INFO",
            "window_width": 1400,
            "window_height": 900,
            "font_size": 10
        },
        
        # 性能参数
        "performance": {
            "max_cpu_usage": 70.0,
            "max_memory_usage": 80.0,
            "execution_timeout": 30.0,
            "retry_attempts": 3,
            "cache_size": 1000,
            "thread_pool_size": 5
        },
        
        # 风险管理参数
        "risk": {
            "max_daily_loss": 0.05,
            "max_position_size": 0.2,
            "correlation_limit": 0.7,
            "volatility_threshold": 0.3,
            "drawdown_limit": 0.15,
            "var_confidence": 0.95
        },
        
        # 数据参数
        "data": {
            "history_days": 30,
            "cache_duration": 300,  # 秒
            "backup_interval": 3600,  # 秒
            "max_records": 10000,
            "compression": True
        },
        
        # API参数
        "api": {
            "timeout": 10.0,
            "retry_delay": 1.0,
            "rate_limit": 100,  # 每分钟请求数
            "sandbox_mode": True,
            "debug_mode": False
        }
    }
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件名
        """
        self.config_file = Path(__file__).parent / config_file
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            是否加载成功
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 递归合并配置
                self._merge_config(self.config, user_config)
                logger.info(f"配置文件加载成功: {self.config_file}")
                return True
            else:
                # 创建默认配置文件
                self.save_config()
                logger.info(f"创建默认配置文件: {self.config_file}")
                return True
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            keys = key.split('.')
            config = self.config
            
            # 导航到最后一级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            logger.error(f"设置配置值失败: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置段
        
        Args:
            section: 配置段名
            
        Returns:
            配置段字典
        """
        return self.config.get(section, {})
    
    def update_section(self, section: str, values: Dict[str, Any]) -> bool:
        """
        更新配置段
        
        Args:
            section: 配置段名
            values: 新的配置值
            
        Returns:
            是否更新成功
        """
        try:
            if section not in self.config:
                self.config[section] = {}
            
            self.config[section].update(values)
            return True
            
        except Exception as e:
            logger.error(f"更新配置段失败: {e}")
            return False
    
    def reset_to_default(self, section: Optional[str] = None) -> bool:
        """
        重置为默认配置
        
        Args:
            section: 要重置的配置段，None表示重置全部
            
        Returns:
            是否重置成功
        """
        try:
            if section is None:
                self.config = self.DEFAULT_CONFIG.copy()
            else:
                if section in self.DEFAULT_CONFIG:
                    self.config[section] = self.DEFAULT_CONFIG[section].copy()
            
            return True
            
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, str]:
        """
        验证配置有效性
        
        Returns:
            验证错误字典
        """
        errors = {}
        
        try:
            # 验证交易参数
            trading = self.get_section('trading')
            if trading.get('initial_capital', 0) <= 0:
                errors['trading.initial_capital'] = "初始资金必须大于0"
            
            if not 0 < trading.get('risk_per_trade', 0) <= 1:
                errors['trading.risk_per_trade'] = "每笔风险比例必须在0-1之间"
            
            # 验证界面参数
            ui = self.get_section('ui')
            if ui.get('update_interval', 0) <= 0:
                errors['ui.update_interval'] = "更新间隔必须大于0"
            
            # 验证性能参数
            performance = self.get_section('performance')
            if not 0 < performance.get('max_cpu_usage', 0) <= 100:
                errors['performance.max_cpu_usage'] = "CPU使用率限制必须在0-100之间"
            
        except Exception as e:
            errors['general'] = f"配置验证失败: {e}"
        
        return errors
    
    def _merge_config(self, base: Dict, update: Dict) -> None:
        """
        递归合并配置字典
        
        Args:
            base: 基础配置
            update: 更新配置
        """
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def export_config(self, file_path: str) -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置导出成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 导入文件路径
            
        Returns:
            是否导入成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证导入的配置
            temp_config = self.config.copy()
            self._merge_config(temp_config, imported_config)
            
            # 如果验证通过，应用配置
            self.config = temp_config
            logger.info(f"配置导入成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            return False


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """快捷方式：获取配置值"""
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any) -> bool:
    """快捷方式：设置配置值"""
    return get_config_manager().set(key, value)


if __name__ == "__main__":
    # 测试代码
    print("🔧 配置管理器测试")
    
    config = ConfigManager()
    
    # 测试获取配置
    print(f"初始资金: {config.get('trading.initial_capital')}")
    print(f"更新间隔: {config.get('ui.update_interval')}")
    
    # 测试设置配置
    config.set('trading.initial_capital', 20000)
    print(f"修改后初始资金: {config.get('trading.initial_capital')}")
    
    # 测试验证
    errors = config.validate_config()
    if errors:
        print(f"配置错误: {errors}")
    else:
        print("✅ 配置验证通过")
    
    print("🎉 配置管理器测试完成")

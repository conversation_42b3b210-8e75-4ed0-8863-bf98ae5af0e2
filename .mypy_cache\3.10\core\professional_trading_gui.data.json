{".class": "MypyFile", "_fullname": "core.professional_trading_gui", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "API_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.professional_trading_gui.API_AVAILABLE", "name": "API_AVAILABLE", "type": "builtins.bool"}}, "AppConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.app_constants.AppConstants", "kind": "Gdef"}, "CHART_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.professional_trading_gui.CHART_SYSTEM_AVAILABLE", "name": "CHART_SYSTEM_AVAILABLE", "type": "builtins.bool"}}, "CandlestickChart": {".class": "SymbolTableNode", "cross_ref": "core.ui.charts.candlestick_chart.CandlestickChart", "kind": "Gdef"}, "ChineseUIConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.chinese_ui_constants.ChineseUIConstants", "kind": "Gdef"}, "MessageConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.message_constants.MessageConstants", "kind": "Gdef"}, "PathConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.path_constants.PathConstants", "kind": "Gdef"}, "ProfessionalTradingGUI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.professional_trading_gui.ProfessionalTradingGUI", "name": "ProfessionalTradingGUI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.professional_trading_gui", "mro": ["core.professional_trading_gui.ProfessionalTradingGUI", "builtins.object"], "names": {".class": "SymbolTable", "PROFESSIONAL_COLORS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.PROFESSIONAL_COLORS", "name": "PROFESSIONAL_COLORS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PROFESSIONAL_FONTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.PROFESSIONAL_FONTS", "name": "PROFESSIONAL_FONTS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.__init__", "name": "__init__", "type": null}}, "account_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.account_data", "name": "account_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "account_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.account_labels", "name": "account_labels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "analyze_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.analyze_positions", "name": "analyze_positions", "type": null}}, "api_connector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.api_connector", "name": "api_connector", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cancel_all_orders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.cancel_all_orders", "name": "cancel_all_orders", "type": null}}, "cancel_selected_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.cancel_selected_order", "name": "cancel_selected_order", "type": null}}, "chart": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.chart", "name": "chart", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "chart_display_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.chart_display_frame", "name": "chart_display_frame", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "chart_symbol_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.chart_symbol_var", "name": "chart_symbol_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "chart_timeframe_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.chart_timeframe_var", "name": "chart_timeframe_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close_all_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.close_all_positions", "name": "close_all_positions", "type": null}}, "connect_exchange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.connect_exchange", "name": "connect_exchange", "type": null}}, "create_account_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_account_info", "name": "create_account_info", "type": null}}, "create_chart_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_chart_tab", "name": "create_chart_tab", "type": null}}, "create_control_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_control_buttons", "name": "create_control_buttons", "type": null}}, "create_history_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_history_tab", "name": "create_history_tab", "type": null}}, "create_main_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_main_content", "name": "create_main_content", "type": null}}, "create_market_data_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_market_data_tab", "name": "create_market_data_tab", "type": null}}, "create_monitor_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_monitor_tab", "name": "create_monitor_tab", "type": null}}, "create_orders_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_orders_tab", "name": "create_orders_tab", "type": null}}, "create_positions_tab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_positions_tab", "name": "create_positions_tab", "type": null}}, "create_quick_trading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_quick_trading", "name": "create_quick_trading", "type": null}}, "create_risk_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_risk_management", "name": "create_risk_management", "type": null}}, "create_risk_warning_banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_risk_warning_banner", "name": "create_risk_warning_banner", "type": null}}, "create_status_bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_status_bar", "name": "create_status_bar", "type": null}}, "create_title_bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_title_bar", "name": "create_title_bar", "type": null}}, "create_trading_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_trading_buttons", "name": "create_trading_buttons", "type": null}}, "create_trading_control_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.create_trading_control_center", "name": "create_trading_control_center", "type": null}}, "emergency_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.emergency_stop", "name": "emergency_stop", "type": null}}, "export_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.export_history", "name": "export_history", "type": null}}, "generate_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.generate_report", "name": "generate_report", "type": null}}, "history_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.history_tree", "name": "history_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_trading_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.init_trading_system", "name": "init_trading_system", "type": null}}, "is_connected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.is_connected", "name": "is_connected", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_trading": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.is_trading", "name": "is_trading", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.log_message", "name": "log_message", "type": null}}, "market_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.market_data", "name": "market_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "market_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.market_tree", "name": "market_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mode_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.mode_var", "name": "mode_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "monitor_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.monitor_text", "name": "monitor_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "notebook": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.notebook", "name": "notebook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "order_type_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.order_type_var", "name": "order_type_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "orders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.orders", "name": "orders", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "orders_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.orders_tree", "name": "orders_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pause_trading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.pause_trading", "name": "pause_trading", "type": null}}, "pause_trading_btn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.pause_trading_btn", "name": "pause_trading_btn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "place_buy_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.place_buy_order", "name": "place_buy_order", "type": null}}, "place_sell_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.place_sell_order", "name": "place_sell_order", "type": null}}, "positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.positions", "name": "positions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "positions_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.positions_tree", "name": "positions_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "price_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.price_var", "name": "price_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quantity_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.quantity_var", "name": "quantity_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "refresh_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.refresh_history", "name": "refresh_history", "type": null}}, "refresh_market_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.refresh_market_data", "name": "refresh_market_data", "type": null}}, "refresh_orders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.refresh_orders", "name": "refresh_orders", "type": null}}, "refresh_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.refresh_positions", "name": "refresh_positions", "type": null}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.root", "name": "root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.run", "name": "run", "type": null}}, "setup_left_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.setup_left_panel", "name": "setup_left_panel", "type": null}}, "setup_right_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.setup_right_panel", "name": "setup_right_panel", "type": null}}, "setup_ui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.setup_ui", "name": "setup_ui", "type": null}}, "setup_variables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.setup_variables", "name": "setup_variables", "type": null}}, "setup_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.setup_window", "name": "setup_window", "type": null}}, "show_monitor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.show_monitor", "name": "show_monitor", "type": null}}, "show_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.show_settings", "name": "show_settings", "type": null}}, "start_trading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.start_trading", "name": "start_trading", "type": null}}, "start_trading_btn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.start_trading_btn", "name": "start_trading_btn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start_trading_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.start_trading_thread", "name": "start_trading_thread", "type": null}}, "status_indicator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.status_indicator", "name": "status_indicator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "status_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.status_label", "name": "status_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stop_trading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.stop_trading", "name": "stop_trading", "type": null}}, "stop_trading_btn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.stop_trading_btn", "name": "stop_trading_btn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "symbol_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.symbol_var", "name": "symbol_var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "time_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.time_label", "name": "time_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "trading_core": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.trading_core", "name": "trading_core", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "trading_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.trading_mode", "name": "trading_mode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_account_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_account_data", "name": "update_account_data", "type": null}}, "update_chart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_chart", "name": "update_chart", "type": null}}, "update_market_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_market_data", "name": "update_market_data", "type": null}}, "update_monitor_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_monitor_display", "name": "update_monitor_display", "type": null}}, "update_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_time", "name": "update_time", "type": null}}, "update_trading_buttons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.update_trading_buttons", "name": "update_trading_buttons", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.professional_trading_gui.ProfessionalTradingGUI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.professional_trading_gui.ProfessionalTradingGUI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StyleConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.style_constants.StyleConstants", "kind": "Gdef"}, "TRADING_CORE_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.professional_trading_gui.TRADING_CORE_AVAILABLE", "name": "TRADING_CORE_AVAILABLE", "type": "builtins.bool"}}, "TradingConstants": {".class": "SymbolTableNode", "cross_ref": "core.constants.trading_constants.TradingConstants", "kind": "Gdef"}, "_": {".class": "SymbolTableNode", "cross_ref": "core.i18n.language_manager._", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.professional_trading_gui.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "gate_api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.professional_trading_gui.gate_api", "name": "gate_api", "type": {".class": "AnyType", "missing_import_name": "core.professional_trading_gui.gate_api", "source_any": null, "type_of_any": 3}}}, "get_color": {".class": "SymbolTableNode", "cross_ref": "core.ui.theme_manager.get_color", "kind": "Gdef"}, "get_config": {".class": "SymbolTableNode", "cross_ref": "core.config.config_manager.get_config", "kind": "Gdef"}, "get_config_manager": {".class": "SymbolTableNode", "cross_ref": "core.config.config_manager.get_config_manager", "kind": "Gdef"}, "get_font": {".class": "SymbolTableNode", "cross_ref": "core.ui.theme_manager.get_font", "kind": "Gdef"}, "get_language_manager": {".class": "SymbolTableNode", "cross_ref": "core.i18n.language_manager.get_language_manager", "kind": "Gdef"}, "get_theme_manager": {".class": "SymbolTableNode", "cross_ref": "core.ui.theme_manager.get_theme_manager", "kind": "Gdef"}, "get_trading_system_core": {".class": "SymbolTableNode", "cross_ref": "core.trading_system_core.get_trading_system_core", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.professional_trading_gui.main", "name": "main", "type": null}}, "messagebox": {".class": "SymbolTableNode", "cross_ref": "tkinter.messagebox", "kind": "Gdef"}, "set_config": {".class": "SymbolTableNode", "cross_ref": "core.config.config_manager.set_config", "kind": "Gdef"}, "set_language": {".class": "SymbolTableNode", "cross_ref": "core.i18n.language_manager.set_language", "kind": "Gdef"}, "show_api_login_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.professional_trading_gui.show_api_login_dialog", "name": "show_api_login_dialog", "type": {".class": "AnyType", "missing_import_name": "core.professional_trading_gui.show_api_login_dialog", "source_any": null, "type_of_any": 3}}}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "tk": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef"}, "ttk": {".class": "SymbolTableNode", "cross_ref": "tkinter.ttk", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\professional_trading_gui.py"}
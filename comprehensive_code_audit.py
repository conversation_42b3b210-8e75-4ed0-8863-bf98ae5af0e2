#!/usr/bin/env python3
"""
企业级现货交易系统 - 深度文件审查工具
Comprehensive Code Audit Tool for Enterprise Spot Trading System

此工具对整个项目进行深度代码质量分析和问题识别
"""

import os
import sys
import ast
import re
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple
import traceback

class ComprehensiveCodeAuditor:
    """全面代码审查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.audit_results = {
            'summary': {},
            'critical_issues': [],
            'warnings': [],
            'recommendations': [],
            'code_metrics': {},
            'file_analysis': {},
            'dependencies': {},
            'security_issues': [],
            'performance_issues': [],
            'maintainability_issues': []
        }
        
        # 代码质量检查规则
        self.quality_rules = {
            'max_function_length': 50,
            'max_class_length': 300,
            'max_line_length': 120,
            'max_cyclomatic_complexity': 10,
            'min_documentation_ratio': 0.2
        }
        
        # 安全敏感模式
        self.security_patterns = {
            'hardcoded_secrets': [
                r'api_key\s*=\s*["\']([^"\']+)["\']',
                r'secret\s*=\s*["\']([^"\']+)["\']',
                r'password\s*=\s*["\']([^"\']+)["\']',
                r'token\s*=\s*["\']([^"\']+)["\']'
            ],
            'sql_injection': [
                r'execute\s*\(\s*["\'].*%.*["\']',
                r'query\s*\(\s*["\'].*\+.*["\']',
                r'SELECT.*\+.*FROM'
            ],
            'eval_usage': [
                r'\beval\s*\(',
                r'\bexec\s*\(',
                r'__import__\s*\('
            ]
        }
        
        # 性能问题模式
        self.performance_patterns = [
            r'time\.sleep\s*\(\s*[^)]*\)',  # 同步睡眠
            r'\.append\s*\(.*\)\s*#.*loop',  # 循环中的列表追加
            r'for.*in.*\.keys\(\)',  # 低效的字典遍历
            r'len\s*\(.*\)\s*==\s*0',  # 低效的空检查
        ]
        
    def run_comprehensive_audit(self) -> Dict[str, Any]:
        """运行全面代码审查"""
        print("🔍 开始企业级代码审查...")
        
        try:
            # 1. 文件结构分析
            self._analyze_file_structure()
            
            # 2. Python代码质量分析
            self._analyze_python_files()
            
            # 3. 配置文件分析
            self._analyze_config_files()
            
            # 4. 依赖关系分析
            self._analyze_dependencies()
            
            # 5. 安全审查
            self._security_audit()
            
            # 6. 性能分析
            self._performance_audit()
            
            # 7. 可维护性分析
            self._maintainability_audit()
            
            # 8. 重复代码检测
            self._duplicate_code_detection()
            
            # 9. 生成审查报告
            self._generate_audit_report()
            
            print("✅ 代码审查完成")
            return self.audit_results
            
        except Exception as e:
            print(f"❌ 审查过程中出现错误: {e}")
            traceback.print_exc()
            return self.audit_results
    
    def _analyze_file_structure(self):
        """分析文件结构"""
        print("📂 分析文件结构...")
        
        structure_info = {
            'total_files': 0,
            'python_files': 0,
            'config_files': 0,
            'doc_files': 0,
            'large_files': [],
            'empty_files': [],
            'duplicate_names': []
        }
        
        file_names = {}
        
        for root, dirs, files in os.walk(self.project_root):
            for file in files:
                file_path = Path(root) / file
                structure_info['total_files'] += 1
                
                # 统计文件类型
                if file.endswith('.py'):
                    structure_info['python_files'] += 1
                elif file.endswith(('.json', '.yaml', '.yml', '.toml', '.ini')):
                    structure_info['config_files'] += 1
                elif file.endswith(('.md', '.txt', '.rst')):
                    structure_info['doc_files'] += 1
                
                # 检查文件大小
                try:
                    file_size = file_path.stat().st_size
                    if file_size > 100000:  # 100KB以上
                        structure_info['large_files'].append({
                            'path': str(file_path),
                            'size': file_size
                        })
                    elif file_size == 0:
                        structure_info['empty_files'].append(str(file_path))
                except OSError:
                    pass
                
                # 检查重复文件名
                if file in file_names:
                    if file not in [item['name'] for item in structure_info['duplicate_names']]:
                        structure_info['duplicate_names'].append({
                            'name': file,
                            'paths': [file_names[file], str(file_path)]
                        })
                    else:
                        # 添加到现有重复项
                        for item in structure_info['duplicate_names']:
                            if item['name'] == file:
                                item['paths'].append(str(file_path))
                                break
                else:
                    file_names[file] = str(file_path)
        
        self.audit_results['code_metrics']['file_structure'] = structure_info
        
        # 检查潜在问题
        if len(structure_info['empty_files']) > 0:
            self.audit_results['warnings'].append({
                'type': 'Empty Files',
                'severity': 'low',
                'description': f"发现 {len(structure_info['empty_files'])} 个空文件",
                'files': structure_info['empty_files']
            })
        
        if len(structure_info['duplicate_names']) > 0:
            self.audit_results['warnings'].append({
                'type': 'Duplicate File Names',
                'severity': 'medium',
                'description': f"发现 {len(structure_info['duplicate_names'])} 个重复文件名",
                'details': structure_info['duplicate_names']
            })
    
    def _analyze_python_files(self):
        """分析Python文件"""
        print("🐍 分析Python代码质量...")
        
        python_files = list(self.project_root.rglob("*.py"))
        code_metrics = {
            'total_lines': 0,
            'total_functions': 0,
            'total_classes': 0,
            'complex_functions': [],
            'long_functions': [],
            'large_classes': [],
            'import_issues': [],
            'syntax_errors': []
        }
        
        for py_file in python_files:
            try:
                file_analysis = self._analyze_single_python_file(py_file)
                self.audit_results['file_analysis'][str(py_file)] = file_analysis
                
                # 累计统计
                code_metrics['total_lines'] += file_analysis.get('line_count', 0)
                code_metrics['total_functions'] += len(file_analysis.get('functions', []))
                code_metrics['total_classes'] += len(file_analysis.get('classes', []))
                
                # 检查问题
                for func in file_analysis.get('functions', []):
                    if func.get('length', 0) > self.quality_rules['max_function_length']:
                        code_metrics['long_functions'].append({
                            'file': str(py_file),
                            'function': func['name'],
                            'length': func['length']
                        })
                
                for cls in file_analysis.get('classes', []):
                    if cls.get('length', 0) > self.quality_rules['max_class_length']:
                        code_metrics['large_classes'].append({
                            'file': str(py_file),
                            'class': cls['name'],
                            'length': cls['length']
                        })
                
                # 检查导入问题
                if file_analysis.get('import_issues'):
                    code_metrics['import_issues'].extend(file_analysis['import_issues'])
                
            except SyntaxError as e:
                code_metrics['syntax_errors'].append({
                    'file': str(py_file),
                    'error': str(e),
                    'line': getattr(e, 'lineno', 'unknown')
                })
            except Exception as e:
                self.audit_results['warnings'].append({
                    'type': 'File Analysis Error',
                    'severity': 'medium',
                    'description': f"无法分析文件 {py_file}: {e}"
                })
        
        self.audit_results['code_metrics']['python_analysis'] = code_metrics
        
        # 生成问题报告
        if code_metrics['syntax_errors']:
            self.audit_results['critical_issues'].extend([
                {
                    'type': 'Syntax Error',
                    'severity': 'critical',
                    'description': f"语法错误在 {error['file']}",
                    'details': error
                } for error in code_metrics['syntax_errors']
            ])
        
        if code_metrics['long_functions']:
            self.audit_results['maintainability_issues'].append({
                'type': 'Long Functions',
                'severity': 'medium',
                'description': f"发现 {len(code_metrics['long_functions'])} 个过长函数",
                'functions': code_metrics['long_functions']
            })
    
    def _analyze_single_python_file(self, file_path: Path) -> Dict[str, Any]:
        """分析单个Python文件"""
        analysis = {
            'line_count': 0,
            'functions': [],
            'classes': [],
            'imports': [],
            'import_issues': [],
            'docstring_coverage': 0,
            'complexity_score': 0
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                analysis['line_count'] = len(lines)
            
            # 解析AST
            try:
                tree = ast.parse(content)
                
                # 分析函数
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        func_info = {
                            'name': node.name,
                            'line': node.lineno,
                            'length': getattr(node, 'end_lineno', node.lineno) - node.lineno + 1,
                            'has_docstring': ast.get_docstring(node) is not None,
                            'args_count': len(node.args.args)
                        }
                        analysis['functions'].append(func_info)
                    
                    elif isinstance(node, ast.ClassDef):
                        class_info = {
                            'name': node.name,
                            'line': node.lineno,
                            'length': getattr(node, 'end_lineno', node.lineno) - node.lineno + 1,
                            'has_docstring': ast.get_docstring(node) is not None,
                            'methods_count': len([n for n in node.body if isinstance(n, ast.FunctionDef)])
                        }
                        analysis['classes'].append(class_info)
                    
                    elif isinstance(node, (ast.Import, ast.ImportFrom)):
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis['imports'].append(alias.name)
                        else:
                            module = node.module if node.module else ''
                            for alias in node.names:
                                analysis['imports'].append(f"{module}.{alias.name}")
                
                # 计算文档字符串覆盖率
                documented_items = sum(1 for item in analysis['functions'] + analysis['classes'] if item['has_docstring'])
                total_items = len(analysis['functions']) + len(analysis['classes'])
                if total_items > 0:
                    analysis['docstring_coverage'] = documented_items / total_items
                
            except SyntaxError:
                analysis['import_issues'].append("语法错误，无法解析AST")
                
        except Exception as e:
            analysis['import_issues'].append(f"文件读取错误: {e}")
        
        return analysis
    
    def _analyze_config_files(self):
        """分析配置文件"""
        print("⚙️ 分析配置文件...")
        
        config_files = []
        config_files.extend(self.project_root.rglob("*.json"))
        config_files.extend(self.project_root.rglob("*.yaml"))
        config_files.extend(self.project_root.rglob("*.yml"))
        config_files.extend(self.project_root.rglob("*.toml"))
        config_files.extend(self.project_root.rglob("*.ini"))
        
        config_analysis = {
            'total_configs': len(config_files),
            'json_files': 0,
            'yaml_files': 0,
            'invalid_configs': [],
            'sensitive_data': []
        }
        
        for config_file in config_files:
            try:
                if config_file.suffix == '.json':
                    config_analysis['json_files'] += 1
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self._check_sensitive_config_data(config_file, data, config_analysis)
                
                elif config_file.suffix in ['.yaml', '.yml']:
                    config_analysis['yaml_files'] += 1
                    # 这里可以添加YAML解析，如果需要的话
                
            except json.JSONDecodeError as e:
                config_analysis['invalid_configs'].append({
                    'file': str(config_file),
                    'error': f"JSON解析错误: {e}"
                })
            except Exception as e:
                config_analysis['invalid_configs'].append({
                    'file': str(config_file),
                    'error': f"配置文件读取错误: {e}"
                })
        
        self.audit_results['code_metrics']['config_analysis'] = config_analysis
        
        if config_analysis['invalid_configs']:
            for invalid_config in config_analysis['invalid_configs']:
                self.audit_results['critical_issues'].append({
                    'type': 'Invalid Config',
                    'severity': 'high',
                    'description': f"无效配置文件: {invalid_config['file']}",
                    'details': invalid_config['error']
                })
    
    def _check_sensitive_config_data(self, file_path: Path, data: Dict, analysis: Dict):
        """检查配置文件中的敏感数据"""
        sensitive_keys = ['api_key', 'secret', 'password', 'token', 'private_key']
        
        def check_dict(d, path=""):
            if isinstance(d, dict):
                for key, value in d.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        if isinstance(value, str) and len(value) > 10:  # 可能是真实密钥
                            analysis['sensitive_data'].append({
                                'file': str(file_path),
                                'path': current_path,
                                'type': 'potential_secret'
                            })
                    check_dict(value, current_path)
            elif isinstance(d, list):
                for i, item in enumerate(d):
                    check_dict(item, f"{path}[{i}]")
        
        check_dict(data)
    
    def _analyze_dependencies(self):
        """分析依赖关系"""
        print("📦 分析依赖关系...")
        
        dependencies = {
            'requirements_files': [],
            'total_dependencies': 0,
            'outdated_patterns': [],
            'security_vulnerabilities': []
        }
        
        # 分析requirements.txt
        req_files = list(self.project_root.rglob("requirements*.txt"))
        for req_file in req_files:
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    deps = [line.strip() for line in lines if line.strip() and not line.startswith('#')]
                    dependencies['requirements_files'].append({
                        'file': str(req_file),
                        'dependencies': deps,
                        'count': len(deps)
                    })
                    dependencies['total_dependencies'] += len(deps)
                    
                    # 检查过时的依赖模式
                    for dep in deps:
                        if '==' in dep and any(old in dep.lower() for old in ['2.7', 'python2']):
                            dependencies['outdated_patterns'].append({
                                'file': str(req_file),
                                'dependency': dep,
                                'issue': 'Python 2.x dependency'
                            })
                            
            except Exception as e:
                self.audit_results['warnings'].append({
                    'type': 'Dependency Analysis Error',
                    'severity': 'low',
                    'description': f"无法分析依赖文件 {req_file}: {e}"
                })
        
        self.audit_results['dependencies'] = dependencies
    
    def _security_audit(self):
        """安全审查"""
        print("🔒 进行安全审查...")
        
        python_files = list(self.project_root.rglob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 检查硬编码密钥
                    for pattern_name, patterns in self.security_patterns.items():
                        for pattern in patterns:
                            matches = re.finditer(pattern, content, re.IGNORECASE)
                            for match in matches:
                                line_num = content[:match.start()].count('\n') + 1
                                self.audit_results['security_issues'].append({
                                    'type': pattern_name,
                                    'severity': 'high' if pattern_name == 'hardcoded_secrets' else 'medium',
                                    'file': str(py_file),
                                    'line': line_num,
                                    'match': match.group(0)[:50] + '...' if len(match.group(0)) > 50 else match.group(0)
                                })
                    
            except Exception as e:
                self.audit_results['warnings'].append({
                    'type': 'Security Scan Error',
                    'severity': 'low',
                    'description': f"无法扫描文件 {py_file}: {e}"
                })
    
    def _performance_audit(self):
        """性能审查"""
        print("⚡ 进行性能审查...")
        
        python_files = list(self.project_root.rglob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    for pattern in self.performance_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            self.audit_results['performance_issues'].append({
                                'type': 'Performance Pattern',
                                'severity': 'medium',
                                'file': str(py_file),
                                'line': line_num,
                                'pattern': pattern,
                                'match': match.group(0)
                            })
                            
            except Exception as e:
                self.audit_results['warnings'].append({
                    'type': 'Performance Scan Error',
                    'severity': 'low',
                    'description': f"无法扫描文件 {py_file}: {e}"
                })
    
    def _maintainability_audit(self):
        """可维护性审查"""
        print("🔧 进行可维护性审查...")
        
        # 检查代码重复
        python_files = list(self.project_root.rglob("*.py"))
        
        # 统计代码行数分布
        line_counts = []
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    line_counts.append({'file': str(py_file), 'lines': lines})
            except:
                pass
        
        # 找出异常大的文件
        if line_counts:
            avg_lines = sum(item['lines'] for item in line_counts) / len(line_counts)
            large_files = [item for item in line_counts if item['lines'] > avg_lines * 3]
            
            if large_files:
                self.audit_results['maintainability_issues'].append({
                    'type': 'Large Files',
                    'severity': 'medium',
                    'description': f"发现 {len(large_files)} 个异常大的文件",
                    'files': large_files,
                    'average_lines': avg_lines
                })
    
    def _duplicate_code_detection(self):
        """重复代码检测"""
        print("🔍 检测重复代码...")
        
        # 简单的重复行检测
        python_files = list(self.project_root.rglob("*.py"))
        line_hashes = {}
        duplicates = []
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for i, line in enumerate(lines):
                        line_content = line.strip()
                        if len(line_content) > 20 and not line_content.startswith('#'):  # 忽略短行和注释
                            line_hash = hash(line_content)
                            if line_hash in line_hashes:
                                duplicates.append({
                                    'content': line_content[:80] + '...' if len(line_content) > 80 else line_content,
                                    'files': [line_hashes[line_hash], {'file': str(py_file), 'line': i+1}]
                                })
                            else:
                                line_hashes[line_hash] = {'file': str(py_file), 'line': i+1}
            except:
                pass
        
        if duplicates:
            self.audit_results['maintainability_issues'].append({
                'type': 'Duplicate Code Lines',
                'severity': 'low',
                'description': f"发现 {len(duplicates)} 行重复代码",
                'duplicates': duplicates[:10]  # 只显示前10个
            })
    
    def _generate_audit_report(self):
        """生成审查报告"""
        print("📊 生成审查报告...")
        
        # 计算总体得分
        total_issues = (
            len(self.audit_results['critical_issues']) * 3 +
            len(self.audit_results['security_issues']) * 2 +
            len(self.audit_results['performance_issues']) +
            len(self.audit_results['maintainability_issues'])
        )
        
        total_files = self.audit_results['code_metrics'].get('file_structure', {}).get('total_files', 1)
        quality_score = max(0, 100 - (total_issues * 2))
        
        self.audit_results['summary'] = {
            'audit_date': datetime.now().isoformat(),
            'total_files_analyzed': total_files,
            'python_files': self.audit_results['code_metrics'].get('file_structure', {}).get('python_files', 0),
            'total_issues': total_issues,
            'critical_issues_count': len(self.audit_results['critical_issues']),
            'security_issues_count': len(self.audit_results['security_issues']),
            'performance_issues_count': len(self.audit_results['performance_issues']),
            'maintainability_issues_count': len(self.audit_results['maintainability_issues']),
            'quality_score': quality_score,
            'overall_status': 'EXCELLENT' if quality_score >= 90 else 'GOOD' if quality_score >= 70 else 'NEEDS_IMPROVEMENT'
        }
        
        # 生成建议
        recommendations = []
        
        if self.audit_results['critical_issues']:
            recommendations.append("🚨 立即修复所有严重问题，特别是语法错误和配置问题")
        
        if self.audit_results['security_issues']:
            recommendations.append("🔒 审查并修复安全漏洞，移除硬编码密钥")
        
        if len(self.audit_results['performance_issues']) > 5:
            recommendations.append("⚡ 优化性能问题，特别关注循环和同步操作")
        
        if self.audit_results['code_metrics'].get('python_analysis', {}).get('long_functions'):
            recommendations.append("🔧 重构过长的函数，提高代码可读性")
        
        if not recommendations:
            recommendations.append("✅ 代码质量良好，继续保持最佳实践")
        
        self.audit_results['recommendations'] = recommendations

def main():
    """主函数"""
    print("🔍 企业级现货交易系统 - 深度代码审查")
    print("=" * 60)
    
    auditor = ComprehensiveCodeAuditor()
    results = auditor.run_comprehensive_audit()
    
    # 保存详细报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"comprehensive_audit_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印摘要报告
    print("\n" + "=" * 60)
    print("📊 审查结果摘要")
    print("=" * 60)
    
    summary = results['summary']
    print(f"📁 分析文件总数: {summary['total_files_analyzed']}")
    print(f"🐍 Python文件: {summary['python_files']}")
    print(f"🎯 代码质量得分: {summary['quality_score']}/100 ({summary['overall_status']})")
    print(f"⚠️  发现问题总数: {summary['total_issues']}")
    
    if summary['critical_issues_count'] > 0:
        print(f"🚨 严重问题: {summary['critical_issues_count']}")
    if summary['security_issues_count'] > 0:
        print(f"🔒 安全问题: {summary['security_issues_count']}")
    if summary['performance_issues_count'] > 0:
        print(f"⚡ 性能问题: {summary['performance_issues_count']}")
    if summary['maintainability_issues_count'] > 0:
        print(f"🔧 可维护性问题: {summary['maintainability_issues_count']}")
    
    print("\n📋 主要建议:")
    for i, rec in enumerate(results['recommendations'], 1):
        print(f"{i}. {rec}")
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    print("=" * 60)
    
    return results

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能重连管理系统
Smart Reconnection Management System
"""

import asyncio
import json
import logging
import random
import threading
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional


class ConnectionState(Enum):
    """连接状态"""

    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


@dataclass
class ConnectionAttempt:
    """连接尝试记录"""

    timestamp: datetime
    attempt_number: int
    success: bool
    error_message: str = ""
    response_time_ms: float = 0.0


@dataclass
class ReconnectionConfig:
    """重连配置"""

    max_retries: int = 10
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    health_check_interval: int = 30
    connection_timeout: float = 10.0


class SmartReconnectManager:
    """智能重连管理器"""

    def __init__(self, config: Optional[ReconnectionConfig] = None):
        self.config = config or ReconnectionConfig()
        self.state = ConnectionState.DISCONNECTED
        self.connection_attempts: List[ConnectionAttempt] = []
        self.health_check_callbacks: List[Callable] = []
        self.reconnect_callbacks: List[Callable] = []
        self.state_change_callbacks: List[Callable] = []

        # 内部状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.last_successful_connection: Optional[datetime] = None
        self.consecutive_failures = 0

        # 设置日志
        self.logger = self._setup_logger()

        # 统计信息
        self.stats = {
            "total_attempts": 0,
            "successful_connections": 0,
            "failed_connections": 0,
            "total_downtime_seconds": 0,
            "average_response_time_ms": 0.0,
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger("SmartReconnectManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # 创建文件处理器
            log_file = Path("logs/reconnection.log")
            log_file.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.INFO)

            # 创建格式器
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def set_state(self, new_state: ConnectionState):
        """设置连接状态"""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state

            self.logger.info(
                f"连接状态变更: {old_state.value} -> {new_state.value}"
            )

            # 通知状态变更回调
            for callback in self.state_change_callbacks:
                try:
                    callback(old_state, new_state)
                except Exception as e:
                    self.logger.error(f"状态变更回调执行失败: {e}")

    async def execute_with_retry(
        self, func: Callable, *args, operation_name: str = "操作", **kwargs
    ) -> Optional[Any]:
        """带重试机制的函数执行"""

        for attempt in range(self.config.max_retries):
            attempt_start = time.time()

            try:
                self.logger.debug(
                    f"尝试执行 {operation_name} (第 {attempt + 1} 次)"
                )

                # 设置连接状态
                if attempt == 0:
                    self.set_state(ConnectionState.CONNECTING)
                else:
                    self.set_state(ConnectionState.RECONNECTING)

                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=self.config.connection_timeout,
                    )
                else:
                    result = func(*args, **kwargs)

                # 记录成功尝试
                response_time = (time.time() - attempt_start) * 1000
                self._record_attempt(
                    attempt + 1, True, response_time=response_time
                )

                # 更新状态
                self.set_state(ConnectionState.CONNECTED)
                self.last_successful_connection = datetime.now()
                self.consecutive_failures = 0

                self.logger.info(
                    f"✅ {operation_name} 执行成功 (第 {attempt + 1} 次尝试)"
                )
                return result

            except asyncio.TimeoutError as e:
                error_msg = f"超时: {e}"
                self._record_attempt(attempt + 1, False, error_msg)
                self.consecutive_failures += 1

                if attempt == self.config.max_retries - 1:
                    self.logger.error(
                        f"❌ {operation_name} 最终超时失败，已重试 {self.config.max_retries} 次"
                    )
                    self.set_state(ConnectionState.FAILED)
                    raise

                delay = self._calculate_delay(attempt)
                self.logger.warning(
                    f"⏳ {operation_name} 超时，{delay:.1f}秒后重试 (第 {attempt + 1}/{self.config.max_retries} 次): {error_msg}"
                )
                await asyncio.sleep(delay)

            except Exception as e:
                error_msg = str(e)
                self._record_attempt(attempt + 1, False, error_msg)
                self.consecutive_failures += 1

                if attempt == self.config.max_retries - 1:
                    self.logger.error(
                        f"❌ {operation_name} 最终失败，已重试 {self.config.max_retries} 次: {error_msg}"
                    )
                    self.set_state(ConnectionState.FAILED)
                    raise

                delay = self._calculate_delay(attempt)
                self.logger.warning(
                    f"⏳ {operation_name} 失败，{delay:.1f}秒后重试 (第 {attempt + 1}/{self.config.max_retries} 次): {error_msg}"
                )
                await asyncio.sleep(delay)

    def _calculate_delay(self, attempt: int) -> float:
        """计算重试延迟时间"""
        delay = min(
            self.config.base_delay * (self.config.backoff_factor**attempt),
            self.config.max_delay,
        )

        if self.config.jitter:
            # 添加随机抖动 (±10%)
            jitter_range = delay * 0.1
            delay += random.uniform(-jitter_range, jitter_range)

        return max(delay, 0.1)  # 最小延迟0.1秒

    def _record_attempt(
        self,
        attempt_number: int,
        success: bool,
        error_message: str = "",
        response_time: float = 0.0,
    ):
        """记录连接尝试"""
        attempt = ConnectionAttempt(
            timestamp=datetime.now(),
            attempt_number=attempt_number,
            success=success,
            error_message=error_message,
            response_time_ms=response_time,
        )

        self.connection_attempts.append(attempt)

        # 更新统计信息
        self.stats["total_attempts"] += 1
        if success:
            self.stats["successful_connections"] += 1

            # 更新平均响应时间
            if response_time > 0:
                total_response_time = (
                    self.stats["average_response_time_ms"]
                    * (self.stats["successful_connections"] - 1)
                    + response_time
                )
                self.stats["average_response_time_ms"] = (
                    total_response_time / self.stats["successful_connections"]
                )
        else:
            self.stats["failed_connections"] += 1

        # 保持最近1000次尝试记录
        if len(self.connection_attempts) > 1000:
            self.connection_attempts = self.connection_attempts[-1000:]

    def start_health_monitoring(self):
        """启动健康检查监控"""
        if self.is_monitoring:
            self.logger.warning("健康检查监控已在运行中")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._health_monitor_loop, daemon=True
        )
        self.monitor_thread.start()

        self.logger.info("🔍 健康检查监控已启动")

    def stop_health_monitoring(self):
        """停止健康检查监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        self.logger.info("⏹️ 健康检查监控已停止")

    def _health_monitor_loop(self):
        """健康检查监控循环"""
        while self.is_monitoring:
            try:
                # 执行健康检查
                if self.health_check_callbacks:
                    for health_check in self.health_check_callbacks:
                        try:
                            if asyncio.iscoroutinefunction(health_check):
                                # 在新的事件循环中运行异步健康检查
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                try:
                                    result = loop.run_until_complete(
                                        health_check()
                                    )
                                    if result:
                                        self.logger.debug("✅ 健康检查通过")
                                        if (
                                            self.state
                                            == ConnectionState.FAILED
                                        ):
                                            self.set_state(
                                                ConnectionState.CONNECTED
                                            )
                                    else:
                                        self.logger.warning("⚠️ 健康检查失败")
                                        self._trigger_reconnect()
                                finally:
                                    loop.close()
                            else:
                                result = health_check()
                                if result:
                                    self.logger.debug("✅ 健康检查通过")
                                    if self.state == ConnectionState.FAILED:
                                        self.set_state(
                                            ConnectionState.CONNECTED
                                        )
                                else:
                                    self.logger.warning("⚠️ 健康检查失败")
                                    self._trigger_reconnect()
                        except Exception as e:
                            self.logger.error(f"健康检查异常: {e}")
                            self._trigger_reconnect()

                # 等待下一次检查
                time.sleep(self.config.health_check_interval)

            except Exception as e:
                self.logger.error(f"健康监控循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再重试

    def _trigger_reconnect(self):
        """触发重连"""
        if self.state in [
            ConnectionState.CONNECTING,
            ConnectionState.RECONNECTING,
        ]:
            return  # 已在重连中

        self.logger.info("🔄 触发重连流程...")
        self.set_state(ConnectionState.RECONNECTING)

        # 通知重连回调
        for callback in self.reconnect_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"重连回调执行失败: {e}")

    def add_health_check_callback(self, callback: Callable):
        """添加健康检查回调"""
        self.health_check_callbacks.append(callback)

    def add_reconnect_callback(self, callback: Callable):
        """添加重连回调"""
        self.reconnect_callbacks.append(callback)

    def add_state_change_callback(self, callback: Callable):
        """添加状态变更回调"""
        self.state_change_callbacks.append(callback)

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        now = datetime.now()

        # 计算正常运行时间
        if self.last_successful_connection:
            uptime_seconds = (
                now - self.last_successful_connection
            ).total_seconds()
        else:
            uptime_seconds = 0

        # 计算最近成功率 (最近100次尝试)
        recent_attempts = self.connection_attempts[-100:]
        if recent_attempts:
            recent_success_rate = len(
                [a for a in recent_attempts if a.success]
            ) / len(recent_attempts)
        else:
            recent_success_rate = 0.0

        return {
            "current_state": self.state.value,
            "consecutive_failures": self.consecutive_failures,
            "last_successful_connection": (
                self.last_successful_connection.isoformat()
                if self.last_successful_connection
                else None
            ),
            "uptime_seconds": uptime_seconds,
            "total_attempts": self.stats["total_attempts"],
            "successful_connections": self.stats["successful_connections"],
            "failed_connections": self.stats["failed_connections"],
            "success_rate": self.stats["successful_connections"]
            / max(self.stats["total_attempts"], 1),
            "recent_success_rate": recent_success_rate,
            "average_response_time_ms": self.stats["average_response_time_ms"],
            "monitoring_active": self.is_monitoring,
        }

    def export_connection_history(self) -> str:
        """导出连接历史"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/connection_history_{timestamp}.json"

            data = {
                "export_timestamp": datetime.now().isoformat(),
                "config": {
                    "max_retries": self.config.max_retries,
                    "base_delay": self.config.base_delay,
                    "max_delay": self.config.max_delay,
                    "backoff_factor": self.config.backoff_factor,
                    "health_check_interval": self.config.health_check_interval,
                },
                "stats": self.get_connection_stats(),
                "recent_attempts": [
                    {
                        "timestamp": attempt.timestamp.isoformat(),
                        "attempt_number": attempt.attempt_number,
                        "success": attempt.success,
                        "error_message": attempt.error_message,
                        "response_time_ms": attempt.response_time_ms,
                    }
                    for attempt in self.connection_attempts[
                        -200:
                    ]  # 最近200次尝试
                ],
            }

            Path(filename).parent.mkdir(parents=True, exist_ok=True)
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"✅ 连接历史已导出: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"导出连接历史失败: {e}")
            return ""


class ConnectionManager:
    """连接管理器 - 管理多个连接的重连"""

    def __init__(self):
        self.connections: Dict[str, SmartReconnectManager] = {}
        self.logger = logging.getLogger("ConnectionManager")

    def register_connection(
        self, name: str, config: Optional[ReconnectionConfig] = None
    ) -> SmartReconnectManager:
        """注册一个连接"""
        if name in self.connections:
            self.logger.warning(f"连接 '{name}' 已存在，将被覆盖")

        reconnect_manager = SmartReconnectManager(config)
        self.connections[name] = reconnect_manager

        self.logger.info(f"✅ 连接 '{name}' 已注册")
        return reconnect_manager

    def get_connection(self, name: str) -> Optional[SmartReconnectManager]:
        """获取连接管理器"""
        return self.connections.get(name)

    def start_all_monitoring(self):
        """启动所有连接的健康监控"""
        for name, manager in self.connections.items():
            manager.start_health_monitoring()
            self.logger.info(f"🔍 已启动 '{name}' 的健康监控")

    def stop_all_monitoring(self):
        """停止所有连接的健康监控"""
        for name, manager in self.connections.items():
            manager.stop_health_monitoring()
            self.logger.info(f"⏹️ 已停止 '{name}' 的健康监控")

    def get_overall_status(self) -> Dict[str, Any]:
        """获取整体连接状态"""
        status = {
            "total_connections": len(self.connections),
            "connected_count": 0,
            "failed_count": 0,
            "reconnecting_count": 0,
            "connections": {},
        }

        for name, manager in self.connections.items():
            conn_stats = manager.get_connection_stats()
            status["connections"][name] = conn_stats

            if conn_stats["current_state"] == ConnectionState.CONNECTED.value:
                status["connected_count"] += 1
            elif conn_stats["current_state"] == ConnectionState.FAILED.value:
                status["failed_count"] += 1
            elif conn_stats["current_state"] in [
                ConnectionState.CONNECTING.value,
                ConnectionState.RECONNECTING.value,
            ]:
                status["reconnecting_count"] += 1

        status["overall_health"] = (
            "healthy" if status["failed_count"] == 0 else "degraded"
        )

        return status


# 全局连接管理器实例
connection_manager = ConnectionManager()


def get_connection_manager() -> ConnectionManager:
    """获取连接管理器实例"""
    return connection_manager


if __name__ == "__main__":
    # 演示智能重连系统
    print("🚀 启动智能重连系统演示")

    async def demo_api_call(should_fail: bool = False):
        """模拟API调用"""
        await asyncio.sleep(0.1)  # 模拟网络延迟

        if should_fail:
            raise Exception("模拟API调用失败")

        return {"status": "success", "data": "mock_data"}

    async def demo_health_check():
        """模拟健康检查"""
        # 随机返回健康状态
        return random.random() > 0.3  # 70%概率健康

    async def main():
        # 创建重连管理器
        config = ReconnectionConfig(
            max_retries=5,
            base_delay=1.0,
            max_delay=10.0,
            health_check_interval=5,
        )

        manager = SmartReconnectManager(config)

        # 添加健康检查
        manager.add_health_check_callback(demo_health_check)

        # 添加状态变更回调
        def on_state_change(old_state, new_state):
            print(f"🔄 状态变更: {old_state.value} -> {new_state.value}")

        manager.add_state_change_callback(on_state_change)

        # 启动健康监控
        manager.start_health_monitoring()

        try:
            # 测试成功连接
            print("\n📡 测试成功连接:")
            result = await manager.execute_with_retry(
                demo_api_call, False, operation_name="API调用"
            )
            print(f"✅ 结果: {result}")

            # 测试失败重连
            print("\n📡 测试失败重连:")
            try:
                await manager.execute_with_retry(
                    demo_api_call, True, operation_name="失败API调用"
                )
            except Exception as e:
                print(f"❌ 最终失败: {e}")

            # 显示统计信息
            print("\n📊 连接统计:")
            stats = manager.get_connection_stats()
            for key, value in stats.items():
                print(f"  {key}: {value}")

            # 等待健康检查运行
            print("\n⏳ 健康检查运行中，10秒后停止...")
            await asyncio.sleep(10)

        finally:
            manager.stop_health_monitoring()

            # 导出连接历史
            history_file = manager.export_connection_history()
            if history_file:
                print(f"📁 连接历史已导出: {history_file}")

    asyncio.run(main())
    print("✅ 演示完成")

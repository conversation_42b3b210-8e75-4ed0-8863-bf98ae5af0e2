{".class": "MypyFile", "_fullname": "core.ui.theme_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ThemeManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.ui.theme_manager.ThemeManager", "name": "ThemeManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.ui.theme_manager", "mro": ["core.ui.theme_manager.ThemeManager", "builtins.object"], "names": {".class": "SymbolTable", "THEMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.ui.theme_manager.ThemeManager.THEMES", "name": "THEMES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "theme_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "theme_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThemeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_to_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "style_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.apply_to_widget", "name": "apply_to_widget", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "style_type"], "arg_types": ["core.ui.theme_manager.ThemeManager", "tkinter.Widget", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_to_widget of ThemeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_gradient_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "parent", "gradient_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.create_gradient_frame", "name": "create_gradient_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "parent", "gradient_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "tkinter.Widget", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_gradient_frame of ThemeManager", "ret_type": "tkinter.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ui.theme_manager.ThemeManager.current_theme", "name": "current_theme", "type": "builtins.str"}}, "export_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theme_name", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.export_theme", "name": "export_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "theme_name", "file_path"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_theme of ThemeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_available_themes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.get_available_themes", "name": "get_available_themes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["core.ui.theme_manager.ThemeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_themes of ThemeManager", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.get_color", "name": "get_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_color of ThemeManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "font_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.get_font", "name": "get_font", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "font_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_font of ThemeManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_gradient": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gradient_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.get_gradient", "name": "get_gradient", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gradient_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_gradient of ThemeManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_style_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "component_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.get_style_config", "name": "get_style_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "component_type"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_config of ThemeManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theme_name", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.import_theme", "name": "import_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "theme_name", "file_path"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_theme of ThemeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theme_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.ThemeManager.set_theme", "name": "set_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theme_name"], "arg_types": ["core.ui.theme_manager.ThemeManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theme of ThemeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theme_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ui.theme_manager.ThemeManager.theme_data", "name": "theme_data", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.ui.theme_manager.ThemeManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.ui.theme_manager.ThemeManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ui.theme_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_theme_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.ui.theme_manager._theme_manager", "name": "_theme_manager", "type": {".class": "NoneType"}}}, "get_color": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["color_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.get_color", "name": "get_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["color_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_color", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_font": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["font_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.get_font", "name": "get_font", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["font_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_font", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_theme_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ui.theme_manager.get_theme_manager", "name": "get_theme_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_theme_manager", "ret_type": "core.ui.theme_manager.ThemeManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.ui.theme_manager.theme", "name": "theme", "type": "core.ui.theme_manager.ThemeManager"}}, "themes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.ui.theme_manager.themes", "name": "themes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tk": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef"}, "ttk": {".class": "SymbolTableNode", "cross_ref": "tkinter.ttk", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\ui\\theme_manager.py"}
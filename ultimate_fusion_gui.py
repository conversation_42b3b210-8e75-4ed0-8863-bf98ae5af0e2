#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
融合GUI启动器 - 终极版
Ultimate Fusion GUI Launcher
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
core_dir = os.path.join(current_dir, 'core')
sys.path.insert(0, core_dir)
sys.path.insert(0, current_dir)

class UltimateSpotTradingGUI:
    """终极现货交易融合GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_main_interface()
        self.start_monitoring()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 融合现货交易系统 - Enterprise v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        
        # 居中显示
        self.center_window()
        
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_main_interface(self):
        """设置主界面"""
        # 顶部标题栏
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, 
                               text="🚀 Enterprise Spot Trading System v2.0 - 融合版",
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 系统状态指示器
        self.status_indicator = ttk.Label(title_frame, text="🟢 运行中", 
                                         font=("Arial", 12, "bold"), 
                                         foreground="green")
        self.status_indicator.pack(side=tk.RIGHT)
        
        # 主容器 - 水平分割
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 左侧控制面板
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # 右侧信息面板
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        self.setup_bottom_status()
        
    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        # 快速交易区
        trade_frame = ttk.LabelFrame(parent, text="⚡ 快速交易", padding="10")
        trade_frame.pack(fill=tk.X, pady=5)
        
        # 交易对和金额
        ttk.Label(trade_frame, text="交易对:").grid(row=0, column=0, sticky="w", pady=2)
        self.pair_var = tk.StringVar(value="BTC/USDT")
        pair_combo = ttk.Combobox(trade_frame, textvariable=self.pair_var, width=12)
        pair_combo['values'] = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "SOL/USDT", "ADA/USDT"]
        pair_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(trade_frame, text="金额:").grid(row=1, column=0, sticky="w", pady=2)
        self.amount_var = tk.StringVar(value="100")
        amount_entry = ttk.Entry(trade_frame, textvariable=self.amount_var, width=12)
        amount_entry.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        
        # 交易按钮
        btn_frame = ttk.Frame(trade_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        buy_btn = ttk.Button(btn_frame, text="💰 买入", command=self.execute_buy)
        buy_btn.pack(side=tk.LEFT, padx=5)
        
        sell_btn = ttk.Button(btn_frame, text="💸 卖出", command=self.execute_sell)
        sell_btn.pack(side=tk.LEFT, padx=5)
        
        trade_frame.columnconfigure(1, weight=1)
        
        # 账户监控区
        account_frame = ttk.LabelFrame(parent, text="💼 账户状态", padding="10")
        account_frame.pack(fill=tk.X, pady=5)
        
        self.balance_var = tk.StringVar(value="10,156.78 USDT")
        self.pnl_var = tk.StringVar(value="+156.78 USDT (+1.55%)")
        self.orders_var = tk.StringVar(value="3 个活跃订单")
        
        ttk.Label(account_frame, text="💰 余额:", font=("Arial", 10, "bold")).pack(anchor="w")
        balance_label = ttk.Label(account_frame, textvariable=self.balance_var, 
                                 font=("Consolas", 12))
        balance_label.pack(anchor="w", pady=2)
        
        ttk.Label(account_frame, text="📈 今日盈亏:", font=("Arial", 10, "bold")).pack(anchor="w")
        pnl_label = ttk.Label(account_frame, textvariable=self.pnl_var, 
                             font=("Consolas", 12), foreground="green")
        pnl_label.pack(anchor="w", pady=2)
        
        ttk.Label(account_frame, text="📋 订单状态:", font=("Arial", 10, "bold")).pack(anchor="w")
        orders_label = ttk.Label(account_frame, textvariable=self.orders_var, 
                                font=("Consolas", 12))
        orders_label.pack(anchor="w", pady=2)
        
        # 策略控制区
        strategy_frame = ttk.LabelFrame(parent, text="🎯 策略控制", padding="10")
        strategy_frame.pack(fill=tk.X, pady=5)
        
        self.strategy_var = tk.StringVar(value="现货滚雪球")
        ttk.Label(strategy_frame, text="当前策略:").pack(anchor="w")
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.strategy_var)
        strategy_combo['values'] = ["现货滚雪球", "多币种监控", "智能套利", "趋势跟踪"]
        strategy_combo.pack(fill=tk.X, pady=5)
        
        self.strategy_status = ttk.Label(strategy_frame, text="🟢 运行中", 
                                        font=("Arial", 10, "bold"), foreground="green")
        self.strategy_status.pack(anchor="w", pady=5)
        
        control_frame = ttk.Frame(strategy_frame)
        control_frame.pack(fill=tk.X)
        
        ttk.Button(control_frame, text="▶️ 启动", command=self.start_strategy).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="⏸️ 暂停", command=self.pause_strategy).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="⚙️ 设置", command=self.strategy_settings).pack(side=tk.LEFT, padx=2)
        
    def setup_right_panel(self, parent):
        """设置右侧信息面板"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 实时监控标签页
        self.setup_monitoring_tab(notebook)
        
        # 交易历史标签页
        self.setup_history_tab(notebook)
        
        # 数据分析标签页
        self.setup_analysis_tab(notebook)
        
        # 系统设置标签页
        self.setup_settings_tab(notebook)
        
    def setup_monitoring_tab(self, notebook):
        """设置监控标签页"""
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="📊 实时监控")
        
        # 价格显示区域
        price_frame = ttk.LabelFrame(monitor_frame, text="💹 实时价格", padding="10")
        price_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建价格表格
        price_columns = ("交易对", "当前价格", "24h变动", "24h成交量")
        self.price_tree = ttk.Treeview(price_frame, columns=price_columns, show="headings", height=6)
        
        for col in price_columns:
            self.price_tree.heading(col, text=col)
            self.price_tree.column(col, width=120, anchor="center")
            
        # 添加示例数据
        price_data = [
            ("BTC/USDT", "43,256.78", "+2.34%", "1.2B"),
            ("ETH/USDT", "2,587.45", "+1.87%", "890M"),
            ("BNB/USDT", "315.67", "+0.95%", "234M"),
            ("SOL/USDT", "98.34", "+3.21%", "156M"),
            ("ADA/USDT", "0.487", "-0.56%", "78M")
        ]
        
        for data in price_data:
            self.price_tree.insert("", "end", values=data)
            
        self.price_tree.pack(fill=tk.X, pady=5)
        
        # 系统性能区域
        perf_frame = ttk.LabelFrame(monitor_frame, text="⚡ 系统性能", padding="10")
        perf_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.perf_text = tk.Text(perf_frame, height=8, font=("Consolas", 10))
        perf_scrollbar = ttk.Scrollbar(perf_frame, orient="vertical", command=self.perf_text.yview)
        self.perf_text.configure(yscrollcommand=perf_scrollbar.set)
        
        self.perf_text.pack(side="left", fill="both", expand=True)
        perf_scrollbar.pack(side="right", fill="y")
        
        self.update_performance_display()
        
    def setup_history_tab(self, notebook):
        """设置历史标签页"""
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text="📜 交易历史")
        
        # 历史记录表格
        history_columns = ("时间", "交易对", "类型", "数量", "价格", "状态")
        history_tree = ttk.Treeview(history_frame, columns=history_columns, show="headings")
        
        for col in history_columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=120, anchor="center")
            
        # 添加示例数据
        history_data = [
            ("15:30:25", "BTC/USDT", "买入", "0.01", "43,250", "✅ 已完成"),
            ("14:45:12", "ETH/USDT", "卖出", "0.5", "2,580", "✅ 已完成"),
            ("13:20:33", "BNB/USDT", "买入", "10", "315", "✅ 已完成"),
            ("12:15:45", "ADA/USDT", "买入", "1000", "0.48", "🔄 进行中"),
            ("11:30:22", "SOL/USDT", "卖出", "5", "98.5", "✅ 已完成")
        ]
        
        for data in history_data:
            history_tree.insert("", "end", values=data)
            
        # 滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=history_tree.yview)
        history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        history_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        history_scrollbar.pack(side="right", fill="y", pady=10)
        
    def setup_analysis_tab(self, notebook):
        """设置分析标签页"""
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="📈 数据分析")
        
        # 分析控制
        control_frame = ttk.Frame(analysis_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(control_frame, text="分析周期:").pack(side=tk.LEFT, padx=5)
        period_var = tk.StringVar(value="24小时")
        period_combo = ttk.Combobox(control_frame, textvariable=period_var, width=10)
        period_combo['values'] = ["1小时", "24小时", "7天", "30天"]
        period_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="🔍 分析", command=self.run_analysis).pack(side=tk.LEFT, padx=10)
        
        # 分析结果显示
        self.analysis_text = tk.Text(analysis_frame, wrap=tk.WORD, font=("Consolas", 10))
        analysis_scrollbar = ttk.Scrollbar(analysis_frame, orient="vertical", command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scrollbar.set)
        
        self.analysis_text.pack(side="left", fill="both", expand=True, padx=10, pady=5)
        analysis_scrollbar.pack(side="right", fill="y", pady=5)
        
        self.show_sample_analysis()
        
    def setup_settings_tab(self, notebook):
        """设置设置标签页"""
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="⚙️ 设置")
        
        # 滚动框架
        canvas = tk.Canvas(settings_frame)
        scrollbar = ttk.Scrollbar(settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基础设置
        basic_frame = ttk.LabelFrame(scrollable_frame, text="基础配置", padding="10")
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        settings_data = [
            ("初始资金:", "10000", "USDT"),
            ("日亏损限制:", "300", "USDT"),
            ("止损比例:", "3", "%"),
            ("止盈比例:", "6", "%"),
            ("最大仓位:", "10", "%")
        ]
        
        for i, (label, value, unit) in enumerate(settings_data):
            ttk.Label(basic_frame, text=label).grid(row=i, column=0, sticky="w", padx=5, pady=2)
            entry = ttk.Entry(basic_frame, width=10)
            entry.insert(0, value)
            entry.grid(row=i, column=1, padx=5, pady=2)
            ttk.Label(basic_frame, text=unit).grid(row=i, column=2, sticky="w", padx=5, pady=2)
            
        ttk.Button(scrollable_frame, text="💾 保存设置", command=self.save_settings).pack(pady=20)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def setup_bottom_status(self):
        """设置底部状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.main_status = ttk.Label(status_frame, text="✅ 系统运行正常", 
                                    relief=tk.SUNKEN, anchor=tk.W)
        self.main_status.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)
        
        self.time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT, padx=2, pady=2)
        
        self.update_time()
        
    def start_monitoring(self):
        """启动监控"""
        def monitor_worker():
            while True:
                try:
                    # 模拟数据更新
                    import random
                    
                    # 更新余额
                    balance = 10000 + random.uniform(-200, 300)
                    self.balance_var.set(f"{balance:,.2f} USDT")
                    
                    # 更新盈亏
                    pnl = random.uniform(-50, 200)
                    pnl_pct = (pnl / 10000) * 100
                    color = "+" if pnl >= 0 else ""
                    self.pnl_var.set(f"{color}{pnl:.2f} USDT ({color}{pnl_pct:.2f}%)")
                    
                    # 更新订单数量
                    orders = random.randint(1, 8)
                    self.orders_var.set(f"{orders} 个活跃订单")
                    
                    time.sleep(3)  # 每3秒更新一次
                    
                except Exception as e:
                    print(f"监控更新错误: {e}")
                    break
                    
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("🕒 %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def update_performance_display(self):
        """更新性能显示"""
        perf_text = f"""
🚀 系统性能监控 - {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 交易统计:
• 总交易次数: 156
• 成功交易: 118 (75.6%)
• 失败交易: 38 (24.4%)
• 当日交易: 12

💰 盈利数据:
• 总盈利: +1,456.78 USDT
• 当日盈利: +156.78 USDT
• 最大盈利: +234.56 USDT
• 平均盈利: +9.34 USDT

⚡ 系统状态:
• CPU使用率: 15.3%
• 内存使用: 234MB
• 网络延迟: 45ms
• API响应: 正常

🎯 策略表现:
• 现货滚雪球: 运行中
• 胜率: 75.6%
• 夏普比率: 1.85
• 最大回撤: 2.3%
"""
        
        self.perf_text.delete(1.0, tk.END)
        self.perf_text.insert(tk.END, perf_text)
        
        # 每30秒更新一次
        self.root.after(30000, self.update_performance_display)
        
    def show_sample_analysis(self):
        """显示示例分析"""
        analysis_text = f"""
📊 交易分析报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

=== 24小时交易概览 ===
📈 总交易次数: 15
✅ 成功交易: 11 (73.3%)
❌ 失败交易: 4 (26.7%)
💰 净盈利: +245.67 USDT
📊 胜率: 73.33%

=== 盈亏分析 ===
💎 最大单笔盈利: +67.89 USDT (BTC/USDT)
📉 最大单笔亏损: -23.45 USDT (ETH/USDT)
⚖️ 盈亏比: 2.89:1
📈 收益率: +2.46%

=== 交易对表现 ===
🥇 BTC/USDT: +89.23 USDT (5笔交易)
🥈 ETH/USDT: +67.45 USDT (4笔交易)
🥉 BNB/USDT: +45.67 USDT (3笔交易)
📊 SOL/USDT: +23.12 USDT (2笔交易)
⚠️ ADA/USDT: +20.20 USDT (1笔交易)

=== 策略分析 ===
🎯 现货滚雪球策略: 表现优秀
• 执行成功率: 95.2%
• 风险控制: 良好
• 资金利用率: 68.3%

=== 建议与优化 ===
✅ 当前策略表现良好，建议继续执行
🔄 可适当增加BTC/USDT交易频率
⚠️ 注意ADA/USDT波动风险
📈 建议保持当前仓位配置

更新时间: {datetime.now().strftime('%H:%M:%S')}
"""
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_text)
        
    def execute_buy(self):
        """执行买入"""
        pair = self.pair_var.get()
        amount = self.amount_var.get()
        messagebox.showinfo("买入确认", 
                          f"🛒 买入订单已提交\n\n"
                          f"交易对: {pair}\n"
                          f"金额: {amount} USDT\n"
                          f"类型: 市价买入\n"
                          f"状态: 等待执行")
        self.main_status.config(text=f"📝 提交买入订单: {pair} {amount}USDT")
        
    def execute_sell(self):
        """执行卖出"""
        pair = self.pair_var.get()
        amount = self.amount_var.get()
        messagebox.showinfo("卖出确认", 
                          f"💰 卖出订单已提交\n\n"
                          f"交易对: {pair}\n"
                          f"金额: {amount} USDT\n"
                          f"类型: 市价卖出\n"
                          f"状态: 等待执行")
        self.main_status.config(text=f"📝 提交卖出订单: {pair} {amount}USDT")
        
    def start_strategy(self):
        """启动策略"""
        strategy = self.strategy_var.get()
        self.strategy_status.config(text="🟢 运行中", foreground="green")
        messagebox.showinfo("策略启动", f"✅ {strategy} 策略已启动")
        self.main_status.config(text=f"🚀 已启动策略: {strategy}")
        
    def pause_strategy(self):
        """暂停策略"""
        self.strategy_status.config(text="🟡 已暂停", foreground="orange")
        messagebox.showinfo("策略暂停", "⏸️ 策略已暂停")
        self.main_status.config(text="⏸️ 策略已暂停")
        
    def strategy_settings(self):
        """策略设置"""
        messagebox.showinfo("策略设置", "⚙️ 策略设置面板（开发中）")
        
    def run_analysis(self):
        """运行分析"""
        self.main_status.config(text="🔍 正在分析数据...")
        self.root.after(2000, lambda: self.main_status.config(text="✅ 分析完成"))
        self.show_sample_analysis()
        
    def save_settings(self):
        """保存设置"""
        messagebox.showinfo("设置保存", "💾 设置已成功保存")
        self.main_status.config(text="💾 设置已保存")
        
    def run(self):
        """运行应用"""
        self.main_status.config(text="🚀 融合交易系统启动完成")
        self.root.mainloop()

def main():
    """主函数"""
    print("🚀 启动融合现货交易系统...")
    print("=" * 60)
    print("📋 系统信息:")
    print("   • 版本: Enterprise v2.0 融合版")
    print("   • 类型: 现货交易系统")
    print("   • 特性: 融合9个GUI界面的最佳功能")
    print("=" * 60)
    
    try:
        app = UltimateSpotTradingGUI()
        print("✅ GUI界面创建成功")
        print("🎯 启动交易界面...")
        app.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        messagebox.showerror("启动失败", f"融合交易系统启动失败:\n{e}")

if __name__ == "__main__":
    main()

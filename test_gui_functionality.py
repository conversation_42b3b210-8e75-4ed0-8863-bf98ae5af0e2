#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI功能全面测试
GUI Functionality Comprehensive Test

验证所有GUI功能的实现状态
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_gui_import():
    """测试GUI导入"""
    print("🔧 测试GUI导入...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        print("  ✅ 专业交易GUI导入成功")
        return True
    except Exception as e:
        print(f"  ❌ GUI导入失败: {e}")
        return False


def test_gui_initialization():
    """测试GUI初始化"""
    print("\n🔧 测试GUI初始化...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        print("  ✅ GUI实例创建成功")

        # 检查基本属性
        checks = {
            "root窗口": hasattr(gui, 'root'),
            "专业配色": hasattr(gui, 'PROFESSIONAL_COLORS'),
            "专业字体": hasattr(gui, 'PROFESSIONAL_FONTS'),
            "连接状态": hasattr(gui, 'is_connected'),
            "交易状态": hasattr(gui, 'is_trading'),
            "账户数据": hasattr(gui, 'account_data'),
        }

        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"    {status} {check_name}")

        # 销毁GUI
        gui.root.destroy()

        passed = sum(checks.values())
        total = len(checks)
        print(f"  📊 初始化检查: {passed}/{total} 通过")

        return passed == total

    except Exception as e:
        print(f"  ❌ GUI初始化失败: {e}")
        return False


def test_gui_components():
    """测试GUI组件"""
    print("\n🔧 测试GUI组件...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        gui = ProfessionalTradingGUI()

        # 检查主要组件
        components = {
            "notebook标签页": hasattr(gui, 'notebook'),
            "市场数据表格": hasattr(gui, 'market_tree'),
            "持仓数据表格": hasattr(gui, 'positions_tree'),
            "订单数据表格": hasattr(gui, 'orders_tree'),
            "历史数据表格": hasattr(gui, 'history_tree'),
            "监控文本": hasattr(gui, 'monitor_text'),
            "状态指示器": hasattr(gui, 'status_indicator'),
            "账户标签": hasattr(gui, 'account_labels'),
            "交易按钮": hasattr(gui, 'start_trading_btn'),
            "模式选择": hasattr(gui, 'mode_var'),
        }

        for comp_name, result in components.items():
            status = "✅" if result else "❌"
            print(f"    {status} {comp_name}")

        gui.root.destroy()

        passed = sum(components.values())
        total = len(components)
        print(f"  📊 组件检查: {passed}/{total} 通过")

        return passed >= total * 0.8  # 80%通过即可

    except Exception as e:
        print(f"  ❌ GUI组件测试失败: {e}")
        return False


def test_gui_methods():
    """测试GUI方法"""
    print("\n🔧 测试GUI方法...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        gui = ProfessionalTradingGUI()

        # 检查关键方法
        methods = {
            "连接交易所": "connect_exchange",
            "开始交易": "start_trading",
            "暂停交易": "pause_trading",
            "停止交易": "stop_trading",
            "紧急停止": "emergency_stop",
            "刷新市场数据": "refresh_market_data",
            "刷新持仓": "refresh_positions",
            "刷新订单": "refresh_orders",
            "刷新历史": "refresh_history",
            "更新账户数据": "update_account_data",
            "下买单": "place_buy_order",
            "下卖单": "place_sell_order",
            "持仓分析": "analyze_positions",
            "生成报告": "generate_report",
            "导出历史": "export_history",
            "日志记录": "log_message",
        }

        method_results = {}
        for method_name, method_attr in methods.items():
            try:
                method = getattr(gui, method_attr)
                if callable(method):
                    method_results[method_name] = True
                    print(f"    ✅ {method_name}")
                else:
                    method_results[method_name] = False
                    print(f"    ❌ {method_name} (不可调用)")
            except AttributeError:
                method_results[method_name] = False
                print(f"    ❌ {method_name} (不存在)")

        gui.root.destroy()

        passed = sum(method_results.values())
        total = len(method_results)
        print(f"  📊 方法检查: {passed}/{total} 通过")

        return passed >= total * 0.9  # 90%通过

    except Exception as e:
        print(f"  ❌ GUI方法测试失败: {e}")
        return False


def test_trading_functionality():
    """测试交易功能"""
    print("\n🔧 测试交易功能...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        gui = ProfessionalTradingGUI()

        # 测试交易功能
        trading_tests = {}

        # 测试日志功能
        try:
            gui.log_message("测试日志消息")
            trading_tests["日志功能"] = True
        except:
            trading_tests["日志功能"] = False

        # 测试数据刷新
        try:
            gui.refresh_market_data()
            trading_tests["市场数据刷新"] = True
        except:
            trading_tests["市场数据刷新"] = False

        try:
            gui.refresh_positions()
            trading_tests["持仓数据刷新"] = True
        except:
            trading_tests["持仓数据刷新"] = False

        try:
            gui.refresh_orders()
            trading_tests["订单数据刷新"] = True
        except:
            trading_tests["订单数据刷新"] = False

        try:
            gui.refresh_history()
            trading_tests["历史数据刷新"] = True
        except:
            trading_tests["历史数据刷新"] = False

        # 测试分析功能
        try:
            gui.analyze_positions()
            trading_tests["持仓分析"] = True
        except:
            trading_tests["持仓分析"] = False

        try:
            gui.generate_report()
            trading_tests["报告生成"] = True
        except:
            trading_tests["报告生成"] = False

        # 输出结果
        for test_name, result in trading_tests.items():
            status = "✅" if result else "❌"
            print(f"    {status} {test_name}")

        gui.root.destroy()

        passed = sum(trading_tests.values())
        total = len(trading_tests)
        print(f"  📊 交易功能: {passed}/{total} 通过")

        return passed >= total * 0.8  # 80%通过

    except Exception as e:
        print(f"  ❌ 交易功能测试失败: {e}")
        return False


def test_data_management():
    """测试数据管理"""
    print("\n🔧 测试数据管理...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        gui = ProfessionalTradingGUI()

        # 测试数据管理功能
        data_tests = {}

        # 测试账户数据更新
        try:
            gui.update_account_data()
            data_tests["账户数据更新"] = True
        except:
            data_tests["账户数据更新"] = False

        # 测试监控显示更新
        try:
            gui.update_monitor_display()
            data_tests["监控显示更新"] = True
        except:
            data_tests["监控显示更新"] = False

        # 测试设置显示
        try:
            # 不实际显示，只测试方法存在
            if hasattr(gui, 'show_settings'):
                data_tests["设置显示"] = True
            else:
                data_tests["设置显示"] = False
        except:
            data_tests["设置显示"] = False

        # 输出结果
        for test_name, result in data_tests.items():
            status = "✅" if result else "❌"
            print(f"    {status} {test_name}")

        gui.root.destroy()

        passed = sum(data_tests.values())
        total = len(data_tests)
        print(f"  📊 数据管理: {passed}/{total} 通过")

        return passed >= total * 0.8

    except Exception as e:
        print(f"  ❌ 数据管理测试失败: {e}")
        return False


def test_user_interface():
    """测试用户界面"""
    print("\n🔧 测试用户界面...")

    try:
        from core.professional_trading_gui import ProfessionalTradingGUI

        gui = ProfessionalTradingGUI()

        # 测试界面功能
        ui_tests = {}

        # 检查窗口属性
        try:
            title = gui.root.title()
            if "终极现货交易终端" in title:
                ui_tests["窗口标题"] = True
            else:
                ui_tests["窗口标题"] = False
        except:
            ui_tests["窗口标题"] = False

        # 检查配色方案
        try:
            if gui.PROFESSIONAL_COLORS and len(gui.PROFESSIONAL_COLORS) > 10:
                ui_tests["专业配色"] = True
            else:
                ui_tests["专业配色"] = False
        except:
            ui_tests["专业配色"] = False

        # 检查字体配置
        try:
            if gui.PROFESSIONAL_FONTS and len(gui.PROFESSIONAL_FONTS) > 5:
                ui_tests["专业字体"] = True
            else:
                ui_tests["专业字体"] = False
        except:
            ui_tests["专业字体"] = False

        # 输出结果
        for test_name, result in ui_tests.items():
            status = "✅" if result else "❌"
            print(f"    {status} {test_name}")

        gui.root.destroy()

        passed = sum(ui_tests.values())
        total = len(ui_tests)
        print(f"  📊 用户界面: {passed}/{total} 通过")

        return passed == total

    except Exception as e:
        print(f"  ❌ 用户界面测试失败: {e}")
        return False


def calculate_functionality_score(results):
    """计算功能完整性分数"""
    passed = sum(results)
    total = len(results)
    score = (passed / total) * 100

    if score >= 95:
        grade = "A+"
        status = "优秀"
    elif score >= 90:
        grade = "A"
        status = "良好"
    elif score >= 80:
        grade = "B+"
        status = "合格"
    elif score >= 70:
        grade = "B"
        status = "基本合格"
    else:
        grade = "C"
        status = "需要改进"

    return score, grade, status


def main():
    """主测试函数"""
    print("🚀 开始GUI功能全面测试")
    print("=" * 60)

    # 执行测试
    tests = [
        ("GUI导入", test_gui_import),
        ("GUI初始化", test_gui_initialization),
        ("GUI组件", test_gui_components),
        ("GUI方法", test_gui_methods),
        ("交易功能", test_trading_functionality),
        ("数据管理", test_data_management),
        ("用户界面", test_user_interface),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append(False)

    # 计算功能完整性分数
    score, grade, status = calculate_functionality_score(results)

    # 输出结果
    print("\n" + "=" * 60)
    print("📊 GUI功能测试结果总结:")

    for i, (test_name, _) in enumerate(tests):
        status_icon = "✅" if results[i] else "❌"
        print(f"  {status_icon} {test_name}")

    print(f"\n🏆 功能完整性评估:")
    print(f"  📊 通过率: {score:.1f}%")
    print(f"  🎯 功能等级: {grade}")
    print(f"  📈 状态: {status}")

    # 详细分析
    if score >= 90:
        print("\n🎉 GUI功能测试优秀通过！")
        print("💎 所有核心功能已完整实现")
        print("🚀 系统已达到专业交易软件标准")
    elif score >= 80:
        print("\n👍 GUI功能测试良好通过")
        print("🔧 大部分功能已完整实现")
        print("⚡ 系统基本满足专业交易需求")
    else:
        print("\n⚠️ GUI功能需要进一步完善")
        print("🛠️ 请检查失败的测试项目")

    # 功能建议
    print(f"\n📋 功能实现建议:")
    if score >= 95:
        print("  🎯 功能已基本完善，可考虑性能优化")
    elif score >= 90:
        print("  🔧 完善少数未通过的功能")
    elif score >= 80:
        print("  📈 重点完善核心交易功能")
    else:
        print("  🛠️ 需要全面检查和修复功能")

    return score >= 80


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

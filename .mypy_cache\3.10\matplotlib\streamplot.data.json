{".class": "MypyFile", "_fullname": "matplotlib.streamplot", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrowStyle": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.ArrowStyle", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Colormap": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Colormap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DomainMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.DomainMap", "name": "DomainMap", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.DomainMap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "grid", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "grid", "mask"], "arg_types": ["matplotlib.streamplot.DomainMap", "matplotlib.streamplot.Grid", "matplotlib.streamplot.StreamMask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DomainMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xd", "yd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.data2grid", "name": "data2grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xd", "yd"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data2grid of DomainMap", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.grid", "name": "grid", "type": "matplotlib.streamplot.Grid"}}, "grid2data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xg", "yg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.grid2data", "name": "grid2data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xg", "yg"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grid2data of DomainMap", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grid2mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xi", "yi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.grid2mask", "name": "grid2mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xi", "yi"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grid2mask of DomainMap", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.mask", "name": "mask", "type": "matplotlib.streamplot.StreamMask"}}, "mask2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xm", "ym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.mask2grid", "name": "mask2grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xm", "ym"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask2grid of DomainMap", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_start_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xg", "yg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.reset_start_point", "name": "reset_start_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xg", "yg"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_start_point of DomainMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_trajectory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xg", "yg", "broken_streamlines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.start_trajectory", "name": "start_trajectory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xg", "yg", "broken_streamlines"], "arg_types": ["matplotlib.streamplot.DomainMap", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_trajectory of DomainMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "undo_trajectory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.undo_trajectory", "name": "undo_trajectory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.streamplot.DomainMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undo_trajectory of DomainMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_trajectory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xg", "yg", "broken_streamlines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.DomainMap.update_trajectory", "name": "update_trajectory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "xg", "yg", "broken_streamlines"], "arg_types": ["matplotlib.streamplot.DomainMap", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_trajectory of DomainMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "x_data2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.x_data2grid", "name": "x_data2grid", "type": "builtins.float"}}, "x_grid2mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.x_grid2mask", "name": "x_grid2mask", "type": "builtins.float"}}, "x_mask2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.x_mask2grid", "name": "x_mask2grid", "type": "builtins.float"}}, "y_data2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.y_data2grid", "name": "y_data2grid", "type": "builtins.float"}}, "y_grid2mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.y_grid2mask", "name": "y_grid2mask", "type": "builtins.float"}}, "y_mask2grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.DomainMap.y_mask2grid", "name": "y_mask2grid", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.DomainMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.DomainMap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Grid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.Grid", "name": "Grid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.Grid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.Grid", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.Grid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "arg_types": ["matplotlib.streamplot.Grid", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Grid", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.dx", "name": "dx", "type": "builtins.float"}}, "dy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.dy", "name": "dy", "type": "builtins.float"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.height", "name": "height", "type": "builtins.float"}}, "nx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.nx", "name": "nx", "type": "builtins.int"}}, "ny": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.ny", "name": "ny", "type": "builtins.int"}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.streamplot.Grid.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.streamplot.Grid"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of Grid", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.streamplot.Grid.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.streamplot.Grid"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of Grid", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.width", "name": "width", "type": "builtins.float"}}, "within_grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "xi", "yi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.Grid.within_grid", "name": "within_grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "xi", "yi"], "arg_types": ["matplotlib.streamplot.Grid", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "within_grid of Grid", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "x_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.x_origin", "name": "x_origin", "type": "builtins.float"}}, "y_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.Grid.y_origin", "name": "y_origin", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.Grid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.Grid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidIndexError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.InvalidIndexError", "name": "InvalidIndexError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.InvalidIndexError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.InvalidIndexError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.InvalidIndexError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.InvalidIndexError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineCollection": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.LineCollection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Normalize": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Normalize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OutOfBounds": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.IndexError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.OutOfBounds", "name": "OutOfBounds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.OutOfBounds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.OutOfBounds", "builtins.IndexError", "builtins.LookupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.OutOfBounds.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.OutOfBounds", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PatchCollection": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.PatchCollection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StreamMask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.StreamMask", "name": "StreamMask", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.StreamMask", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.StreamMask", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.StreamMask.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "density"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.StreamMask.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "density"], "arg_types": ["matplotlib.streamplot.StreamMask", {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamMask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.StreamMask.nx", "name": "nx", "type": "builtins.int"}}, "ny": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.StreamMask.ny", "name": "ny", "type": "builtins.int"}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.StreamMask.shape", "name": "shape", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.StreamMask.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.StreamMask", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamplotSet": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.StreamplotSet", "name": "StreamplotSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.StreamplotSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.StreamplotSet", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines", "arrows"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.StreamplotSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines", "arrows"], "arg_types": ["matplotlib.streamplot.StreamplotSet", "matplotlib.collections.LineCollection", "matplotlib.collections.PatchCollection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamplotSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.StreamplotSet.arrows", "name": "arrows", "type": "matplotlib.collections.PatchCollection"}}, "lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.streamplot.StreamplotSet.lines", "name": "lines", "type": "matplotlib.collections.LineCollection"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.StreamplotSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.StreamplotSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TerminateTrajectory": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.streamplot.TerminateTrajectory", "name": "TerminateTrajectory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.TerminateTrajectory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.streamplot", "mro": ["matplotlib.streamplot.TerminateTrajectory", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.streamplot.TerminateTrajectory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.streamplot.TerminateTrajectory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.streamplot.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.streamplot.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "streamplot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["axes", "x", "y", "u", "v", "density", "linewidth", "color", "cmap", "norm", "arrowsize", "arrowstyle", "minlength", "transform", "zorder", "start_points", "maxlength", "integration_direction", "broken_streamlines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.streamplot.streamplot", "name": "streamplot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["axes", "x", "y", "u", "v", "density", "linewidth", "color", "cmap", "norm", "arrowsize", "arrowstyle", "minlength", "transform", "zorder", "start_points", "maxlength", "integration_direction", "broken_streamlines"], "arg_types": ["matplotlib.axes._axes.Axes", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.colors.Colormap", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.colors.Normalize", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ArrowStyle"], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "forward"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "backward"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "both"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamplot", "ret_type": "matplotlib.streamplot.StreamplotSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\streamplot.pyi"}
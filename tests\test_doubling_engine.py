#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
倍增引擎测试
Doubling Engine Tests

全面测试倍增引擎功能
"""

import unittest
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.doubling_growth_engine import DoublingGrowthEngine, DoublingSimulator

class TestDoublingEngine(unittest.TestCase):
    """倍增引擎测试类"""

    def setUp(self):
        """测试前准备"""
        self.initial_capital = 1000.0
        self.engine = DoublingGrowthEngine(self.initial_capital)
        self.simulator = DoublingSimulator(self.initial_capital)

    def test_initialization(self):
        """测试初始化"""
        # 验证引擎初始化
        self.assertEqual(self.engine.initial_capital, self.initial_capital)
        self.assertEqual(self.engine.current_capital, self.initial_capital)
        self.assertEqual(self.engine.target_capital, self.initial_capital * 2.0)
        self.assertEqual(self.engine.profit_reinvest_ratio, 0.8)
        self.assertEqual(self.engine.total_reinvested, 0.0)
        self.assertEqual(self.engine.doubling_progress, 0.0)
        self.assertEqual(self.engine.growth_stage, 1)
        
        # 验证模拟器初始化
        self.assertEqual(self.simulator.engine.initial_capital, self.initial_capital)
        self.assertFalse(self.simulator.is_running)

    def test_growth_step(self):
        """测试增长步骤"""
        # 记录初始资金
        initial_capital = self.engine.current_capital
        
        # 执行增长步骤
        self.engine._simulate_growth_step()
        
        # 验证资金增长
        self.assertNotEqual(self.engine.current_capital, initial_capital)
        
        # 验证倍增进度更新
        self.assertGreater(self.engine.doubling_progress, 0.0)
        
        # 验证不会低于初始资金的95%
        self.assertGreaterEqual(self.engine.current_capital, self.initial_capital * 0.95)

    def test_stage_multiplier(self):
        """测试阶段倍数"""
        # 测试不同进度的阶段倍数
        test_cases = [
            (0.1, 0.8),   # 0-20%: 起步阶段
            (0.3, 1.0),   # 20-40%: 加速阶段
            (0.5, 1.2),   # 40-60%: 快速增长阶段
            (0.7, 1.4),   # 60-80%: 冲刺阶段
            (0.9, 1.6)    # 80-100%: 最终阶段
        ]
        
        for progress, expected_multiplier in test_cases:
            self.engine.doubling_progress = progress
            multiplier = self.engine._get_stage_multiplier()
            self.assertEqual(multiplier, expected_multiplier, 
                            f"进度 {progress} 的阶段倍数应为 {expected_multiplier}")

    def test_update_growth_stage(self):
        """测试更新增长阶段"""
        # 测试不同进度的增长阶段
        test_cases = [
            (0.1, 1),   # 0-20%: 阶段1
            (0.3, 2),   # 20-40%: 阶段2
            (0.5, 3),   # 40-60%: 阶段3
            (0.7, 4),   # 60-80%: 阶段4
            (0.9, 5)    # 80-100%: 阶段5
        ]
        
        for progress, expected_stage in test_cases:
            self.engine.doubling_progress = progress
            self.engine._update_growth_stage()
            self.assertEqual(self.engine.growth_stage, expected_stage, 
                            f"进度 {progress} 的增长阶段应为 {expected_stage}")

    def test_get_current_status(self):
        """测试获取当前状态"""
        # 设置一些测试数据
        self.engine.current_capital = 1200.0
        self.engine.doubling_progress = 0.2
        self.engine.growth_stage = 2
        self.engine.days_elapsed = 10
        self.engine.total_trades = 20
        self.engine.winning_trades = 15
        self.engine.total_profit = 200.0
        
        # 获取状态
        status = self.engine.get_current_status()
        
        # 验证状态数据
        self.assertEqual(status['current_capital'], 1200.0)
        self.assertEqual(status['doubling_progress'], 0.2)
        self.assertEqual(status['growth_stage'], 2)
        self.assertEqual(status['days_elapsed'], 10)
        self.assertEqual(status['total_trades'], 20)
        self.assertEqual(status['winning_trades'], 15)
        self.assertEqual(status['win_rate'], 75.0)
        self.assertEqual(status['total_profit'], 200.0)
        self.assertFalse(status['is_doubling_achieved'])
        
        # 测试倍增达成
        self.engine.current_capital = 2000.0
        status = self.engine.get_current_status()
        self.assertTrue(status['is_doubling_achieved'])

    def test_simulator_operations(self):
        """测试模拟器操作"""
        # 测试启动
        self.simulator.start()
        self.assertTrue(self.simulator.is_running)
        
        # 测试获取模拟数据
        data = self.simulator.get_simulated_data()
        self.assertIsInstance(data, dict)
        self.assertIn('current_balance', data)
        self.assertIn('total_value', data)
        self.assertIn('doubling_progress', data)
        
        # 测试获取增长信息
        info = self.simulator.get_growth_info()
        self.assertIsInstance(info, str)
        self.assertIn("倍增增长状态报告", info)
        
        # 测试停止
        self.simulator.stop()
        self.assertFalse(self.simulator.is_running)

    def test_calculate_daily_return(self):
        """测试计算日收益率"""
        # 设置测试数据
        self.engine.current_capital = 1100.0  # 10%增长
        self.engine.days_elapsed = 10
        
        # 计算日收益率
        daily_return = self.engine._calculate_daily_return()
        
        # 验证结果 (10%增长在10天内，约等于0.96%每天)
        self.assertAlmostEqual(daily_return, 0.96, delta=0.1)
        
        # 测试天数为0的情况
        self.engine.days_elapsed = 0
        daily_return = self.engine._calculate_daily_return()
        self.assertEqual(daily_return, 0.0)

if __name__ == '__main__':
    unittest.main()
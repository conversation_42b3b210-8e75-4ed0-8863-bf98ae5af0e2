#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TradingView图表集成
TradingView Chart Integration for Advanced Technical Analysis
"""

import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from flask import Flask, render_template_string, jsonify, request
import threading
import webbrowser


class TradingViewDataProvider:
    """TradingView数据提供者"""
    
    def __init__(self):
        """初始化数据提供者"""
        self.symbols = {}
        self.kline_data = {}
        self.indicators = {}
        
        # 支持的时间周期
        self.supported_intervals = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
    
    def add_symbol(self, symbol: str, name: str, exchange: str = "CRYPTO"):
        """添加交易对"""
        self.symbols[symbol] = {
            'name': name,
            'exchange': exchange,
            'type': 'crypto',
            'session': '24x7',
            'timezone': 'UTC',
            'minmov': 1,
            'pricescale': 100,
            'has_intraday': True,
            'has_daily': True,
            'has_weekly_and_monthly': True,
            'supported_resolutions': list(self.supported_intervals.keys())
        }
        print(f"✅ 已添加交易对: {symbol} ({name})")
    
    def generate_sample_data(self, symbol: str, interval: str = '1h', 
                           bars: int = 1000, start_price: float = 50000.0):
        """生成示例K线数据"""
        try:
            if interval not in self.supported_intervals:
                print(f"❌ 不支持的时间周期: {interval}")
                return
            
            # 生成时间序列
            interval_minutes = self.supported_intervals[interval]
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=interval_minutes * bars)
            
            timestamps = []
            current_time = start_time
            while current_time <= end_time:
                timestamps.append(int(current_time.timestamp()))
                current_time += timedelta(minutes=interval_minutes)
            
            # 生成价格数据
            np.random.seed(42)
            returns = np.random.normal(0, 0.02, len(timestamps))
            
            klines = []
            current_price = start_price
            
            for i, timestamp in enumerate(timestamps):
                # 计算OHLC
                open_price = current_price
                
                # 生成价格变化
                price_change = current_price * returns[i]
                close_price = max(open_price + price_change, start_price * 0.1)
                
                # 生成高低价
                volatility = abs(returns[i]) * current_price * 2
                high_price = max(open_price, close_price) + volatility * np.random.uniform(0, 0.5)
                low_price = min(open_price, close_price) - volatility * np.random.uniform(0, 0.5)
                
                # 确保价格逻辑正确
                high_price = max(high_price, open_price, close_price)
                low_price = min(low_price, open_price, close_price)
                low_price = max(low_price, start_price * 0.1)  # 防止价格过低
                
                # 生成成交量
                volume = np.random.uniform(1000, 10000)
                
                klines.append({
                    'time': timestamp,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': round(volume, 2)
                })
                
                current_price = close_price
            
            # 存储数据
            key = f"{symbol}_{interval}"
            self.kline_data[key] = klines
            
            print(f"✅ 已生成 {symbol} {interval} K线数据: {len(klines)} 条")
            
        except Exception as e:
            print(f"❌ 生成K线数据失败: {e}")
    
    def get_klines(self, symbol: str, interval: str, from_time: int, to_time: int) -> List[Dict]:
        """获取K线数据"""
        try:
            key = f"{symbol}_{interval}"
            
            if key not in self.kline_data:
                print(f"⚠️ 未找到 {symbol} {interval} 数据，生成示例数据")
                self.generate_sample_data(symbol, interval)
            
            if key not in self.kline_data:
                return []
            
            # 过滤时间范围
            filtered_data = []
            for kline in self.kline_data[key]:
                if from_time <= kline['time'] <= to_time:
                    filtered_data.append(kline)
            
            return filtered_data
            
        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return []
    
    def calculate_indicators(self, symbol: str, interval: str) -> Dict:
        """计算技术指标"""
        try:
            key = f"{symbol}_{interval}"
            
            if key not in self.kline_data:
                return {}
            
            klines = self.kline_data[key]
            if len(klines) < 50:
                return {}
            
            # 转换为DataFrame
            df = pd.DataFrame(klines)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            indicators = {}
            
            # 移动平均线
            indicators['MA5'] = df['close'].rolling(window=5).mean().round(2).tolist()
            indicators['MA10'] = df['close'].rolling(window=10).mean().round(2).tolist()
            indicators['MA20'] = df['close'].rolling(window=20).mean().round(2).tolist()
            indicators['MA50'] = df['close'].rolling(window=50).mean().round(2).tolist()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            indicators['RSI'] = (100 - (100 / (1 + rs))).round(2).tolist()
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            macd_line = exp1 - exp2
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line
            
            indicators['MACD'] = macd_line.round(2).tolist()
            indicators['MACD_Signal'] = signal_line.round(2).tolist()
            indicators['MACD_Histogram'] = histogram.round(2).tolist()
            
            # 布林带
            ma20 = df['close'].rolling(window=20).mean()
            std20 = df['close'].rolling(window=20).std()
            indicators['BB_Upper'] = (ma20 + (std20 * 2)).round(2).tolist()
            indicators['BB_Lower'] = (ma20 - (std20 * 2)).round(2).tolist()
            indicators['BB_Middle'] = ma20.round(2).tolist()
            
            # 成交量移动平均
            indicators['Volume_MA'] = df['volume'].rolling(window=20).mean().round(2).tolist()
            
            self.indicators[key] = indicators
            return indicators
            
        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")
            return {}


class TradingViewServer:
    """TradingView图表服务器"""
    
    def __init__(self, data_provider: TradingViewDataProvider, port: int = 5000):
        """初始化图表服务器"""
        self.data_provider = data_provider
        self.port = port
        self.app = Flask(__name__)
        self.server_thread = None
        
        # 设置路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            """主页面"""
            return render_template_string(self._get_chart_template())
        
        @self.app.route('/config')
        def config():
            """图表配置"""
            return jsonify({
                'supports_search': True,
                'supports_group_request': False,
                'supports_marks': False,
                'supports_timescale_marks': False,
                'supports_time': True,
                'exchanges': [
                    {'value': 'CRYPTO', 'name': 'Crypto', 'desc': 'Cryptocurrency'}
                ],
                'symbols_types': [
                    {'name': 'crypto', 'value': 'crypto'}
                ],
                'supported_resolutions': ['1', '5', '15', '30', '60', '240', '1D']
            })
        
        @self.app.route('/symbols')
        def symbols():
            """交易对搜索"""
            query = request.args.get('symbol', '').upper()
            
            results = []
            for symbol, info in self.data_provider.symbols.items():
                if query in symbol or query in info['name']:
                    results.append({
                        'symbol': symbol,
                        'full_name': f"{info['exchange']}:{symbol}",
                        'description': info['name'],
                        'exchange': info['exchange'],
                        'type': info['type']
                    })
            
            return jsonify(results)
        
        @self.app.route('/symbol_info')
        def symbol_info():
            """交易对信息"""
            symbol = request.args.get('symbol', '')
            
            if symbol in self.data_provider.symbols:
                info = self.data_provider.symbols[symbol]
                return jsonify({
                    'name': symbol,
                    'exchange-traded': info['exchange'],
                    'exchange-listed': info['exchange'],
                    'timezone': info['timezone'],
                    'minmov': info['minmov'],
                    'minmov2': 0,
                    'pointvalue': 1,
                    'session': info['session'],
                    'has_intraday': info['has_intraday'],
                    'has_no_volume': False,
                    'description': info['name'],
                    'type': info['type'],
                    'supported_resolutions': info['supported_resolutions'],
                    'pricescale': info['pricescale'],
                    'ticker': symbol
                })
            else:
                return jsonify({'error': 'Symbol not found'}), 404
        
        @self.app.route('/history')
        def history():
            """历史数据"""
            symbol = request.args.get('symbol', '')
            resolution = request.args.get('resolution', '60')
            from_time = int(request.args.get('from', 0))
            to_time = int(request.args.get('to', time.time()))
            
            # 转换分辨率格式
            interval_map = {
                '1': '1m', '5': '5m', '15': '15m', '30': '30m',
                '60': '1h', '240': '4h', '1D': '1d'
            }
            interval = interval_map.get(resolution, '1h')
            
            klines = self.data_provider.get_klines(symbol, interval, from_time, to_time)
            
            if not klines:
                return jsonify({'s': 'no_data'})
            
            # 转换为TradingView格式
            response = {
                's': 'ok',
                't': [k['time'] for k in klines],
                'o': [k['open'] for k in klines],
                'h': [k['high'] for k in klines],
                'l': [k['low'] for k in klines],
                'c': [k['close'] for k in klines],
                'v': [k['volume'] for k in klines]
            }
            
            return jsonify(response)
        
        @self.app.route('/indicators/<symbol>/<interval>')
        def indicators(symbol, interval):
            """技术指标数据"""
            indicators = self.data_provider.calculate_indicators(symbol, interval)
            return jsonify(indicators)
    
    def _get_chart_template(self) -> str:
        """获取图表HTML模板"""
        return '''
<!DOCTYPE html>
<html>
<head>
    <title>终极版交易系统 - TradingView图表</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        .header { background: #1e222d; color: white; padding: 10px 20px; }
        .header h1 { margin: 0; font-size: 24px; }
        .controls { background: #2a2e39; padding: 10px 20px; color: white; }
        .control-group { display: inline-block; margin-right: 20px; }
        .control-group label { margin-right: 5px; }
        .control-group select { padding: 5px; background: #131722; color: white; border: 1px solid #434651; }
        #chartContainer { height: calc(100vh - 120px); }
        .indicators-panel { position: fixed; right: 10px; top: 120px; width: 300px; background: rgba(30, 34, 45, 0.9); color: white; padding: 15px; border-radius: 5px; }
        .indicator-item { margin-bottom: 10px; font-size: 12px; }
    </style>
    <script src="https://unpkg.com/tradingview-charting-library/bundles/charting_library.min.js"></script>
</head>
<body>
    <div class="header">
        <h1>🚀 终极版交易系统 - 高级图表分析</h1>
    </div>
    
    <div class="controls">
        <div class="control-group">
            <label>交易对:</label>
            <select id="symbolSelect">
                <option value="BTCUSDT">BTC/USDT</option>
                <option value="ETHUSDT">ETH/USDT</option>
                <option value="SOLUSDT">SOL/USDT</option>
            </select>
        </div>
        <div class="control-group">
            <label>时间周期:</label>
            <select id="intervalSelect">
                <option value="1">1分钟</option>
                <option value="5">5分钟</option>
                <option value="15">15分钟</option>
                <option value="30">30分钟</option>
                <option value="60" selected>1小时</option>
                <option value="240">4小时</option>
                <option value="1D">1天</option>
            </select>
        </div>
        <div class="control-group">
            <button onclick="refreshChart()" style="padding: 5px 15px; background: #2962ff; color: white; border: none; border-radius: 3px; cursor: pointer;">刷新图表</button>
        </div>
    </div>
    
    <div id="chartContainer"></div>
    
    <div class="indicators-panel" id="indicatorsPanel">
        <h3>📊 技术指标</h3>
        <div id="indicatorsList">
            <div class="indicator-item">MA5: <span id="ma5">--</span></div>
            <div class="indicator-item">MA20: <span id="ma20">--</span></div>
            <div class="indicator-item">RSI: <span id="rsi">--</span></div>
            <div class="indicator-item">MACD: <span id="macd">--</span></div>
        </div>
    </div>

    <script>
        let widget = null;
        
        function initChart() {
            const symbol = document.getElementById('symbolSelect').value;
            const interval = document.getElementById('intervalSelect').value;
            
            if (widget) {
                widget.remove();
            }
            
            widget = new TradingView.widget({
                width: '100%',
                height: '100%',
                symbol: symbol,
                interval: interval,
                container_id: 'chartContainer',
                datafeed: {
                    onReady: function(callback) {
                        fetch('/config').then(response => response.json()).then(callback);
                    },
                    searchSymbols: function(userInput, exchange, symbolType, onResultReadyCallback) {
                        fetch(`/symbols?symbol=${userInput}`)
                            .then(response => response.json())
                            .then(onResultReadyCallback);
                    },
                    resolveSymbol: function(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
                        fetch(`/symbol_info?symbol=${symbolName}`)
                            .then(response => response.json())
                            .then(onSymbolResolvedCallback)
                            .catch(onResolveErrorCallback);
                    },
                    getBars: function(symbolInfo, resolution, from, to, onHistoryCallback, onErrorCallback, firstDataRequest) {
                        fetch(`/history?symbol=${symbolInfo.name}&resolution=${resolution}&from=${from}&to=${to}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.s === 'ok') {
                                    const bars = data.t.map((time, index) => ({
                                        time: time * 1000,
                                        open: data.o[index],
                                        high: data.h[index],
                                        low: data.l[index],
                                        close: data.c[index],
                                        volume: data.v[index]
                                    }));
                                    onHistoryCallback(bars, {noData: false});
                                } else {
                                    onHistoryCallback([], {noData: true});
                                }
                            })
                            .catch(onErrorCallback);
                    },
                    subscribeBars: function() {},
                    unsubscribeBars: function() {}
                },
                library_path: 'https://unpkg.com/tradingview-charting-library/bundles/',
                locale: 'zh',
                disabled_features: ['use_localstorage_for_settings'],
                enabled_features: ['study_templates'],
                charts_storage_url: '',
                charts_storage_api_version: '1.1',
                client_id: 'ultimate_trading_system',
                user_id: 'public_user',
                theme: 'dark',
                overrides: {
                    'paneProperties.background': '#131722',
                    'paneProperties.vertGridProperties.color': '#363c4e',
                    'paneProperties.horzGridProperties.color': '#363c4e',
                    'symbolWatermarkProperties.transparency': 90,
                    'scalesProperties.textColor': '#AAA',
                    'mainSeriesProperties.candleStyle.wickUpColor': '#336854',
                    'mainSeriesProperties.candleStyle.wickDownColor': '#7f323f'
                }
            });
            
            // 更新技术指标
            updateIndicators(symbol, interval);
        }
        
        function updateIndicators(symbol, interval) {
            const intervalMap = {
                '1': '1m', '5': '5m', '15': '15m', '30': '30m',
                '60': '1h', '240': '4h', '1D': '1d'
            };
            const mappedInterval = intervalMap[interval] || '1h';
            
            fetch(`/indicators/${symbol}/${mappedInterval}`)
                .then(response => response.json())
                .then(data => {
                    if (data.MA5 && data.MA5.length > 0) {
                        document.getElementById('ma5').textContent = data.MA5[data.MA5.length - 1] || '--';
                    }
                    if (data.MA20 && data.MA20.length > 0) {
                        document.getElementById('ma20').textContent = data.MA20[data.MA20.length - 1] || '--';
                    }
                    if (data.RSI && data.RSI.length > 0) {
                        document.getElementById('rsi').textContent = data.RSI[data.RSI.length - 1] || '--';
                    }
                    if (data.MACD && data.MACD.length > 0) {
                        document.getElementById('macd').textContent = data.MACD[data.MACD.length - 1] || '--';
                    }
                })
                .catch(console.error);
        }
        
        function refreshChart() {
            initChart();
        }
        
        // 监听选择变化
        document.getElementById('symbolSelect').addEventListener('change', initChart);
        document.getElementById('intervalSelect').addEventListener('change', initChart);
        
        // 初始化图表
        window.addEventListener('load', initChart);
    </script>
</body>
</html>
        '''
    
    def start_server(self):
        """启动图表服务器"""
        try:
            if self.server_thread and self.server_thread.is_alive():
                print("⚠️ 图表服务器已在运行")
                return
            
            print(f"🚀 启动TradingView图表服务器 (端口: {self.port})...")
            
            self.server_thread = threading.Thread(
                target=lambda: self.app.run(host='127.0.0.1', port=self.port, debug=False),
                daemon=True
            )
            self.server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            # 自动打开浏览器
            chart_url = f"http://127.0.0.1:{self.port}"
            print(f"✅ 图表服务器已启动: {chart_url}")
            
            try:
                webbrowser.open(chart_url)
                print("🌐 已自动打开浏览器")
            except:
                print("💡 请手动打开浏览器访问图表")
            
        except Exception as e:
            print(f"❌ 启动图表服务器失败: {e}")
    
    def stop_server(self):
        """停止图表服务器"""
        print("🛑 停止TradingView图表服务器...")
        # Flask服务器会在主程序退出时自动停止
        print("✅ 图表服务器已停止")


class TradingViewIntegration:
    """TradingView集成管理器"""
    
    def __init__(self, port: int = 5000):
        """初始化TradingView集成"""
        self.data_provider = TradingViewDataProvider()
        self.server = TradingViewServer(self.data_provider, port)
        
        # 添加默认交易对
        self._setup_default_symbols()
    
    def _setup_default_symbols(self):
        """设置默认交易对"""
        symbols = [
            ('BTCUSDT', 'Bitcoin / Tether'),
            ('ETHUSDT', 'Ethereum / Tether'),
            ('SOLUSDT', 'Solana / Tether'),
            ('BNBUSDT', 'Binance Coin / Tether'),
            ('ADAUSDT', 'Cardano / Tether'),
            ('DOTUSDT', 'Polkadot / Tether'),
            ('AVAXUSDT', 'Avalanche / Tether'),
            ('MATICUSDT', 'Polygon / Tether')
        ]
        
        for symbol, name in symbols:
            self.data_provider.add_symbol(symbol, name)
            
            # 生成多个时间周期的数据
            for interval in ['1m', '5m', '15m', '1h', '4h', '1d']:
                start_prices = {
                    'BTCUSDT': 50000, 'ETHUSDT': 3000, 'SOLUSDT': 150,
                    'BNBUSDT': 400, 'ADAUSDT': 1.2, 'DOTUSDT': 25,
                    'AVAXUSDT': 80, 'MATICUSDT': 2.5
                }
                start_price = start_prices.get(symbol, 100)
                
                bars = 1000 if interval in ['1m', '5m'] else 2000
                self.data_provider.generate_sample_data(symbol, interval, bars, start_price)
    
    def start_chart_server(self):
        """启动图表服务器"""
        self.server.start_server()
    
    def stop_chart_server(self):
        """停止图表服务器"""
        self.server.stop_server()
    
    def add_custom_symbol(self, symbol: str, name: str, start_price: float = 100.0):
        """添加自定义交易对"""
        self.data_provider.add_symbol(symbol, name)
        
        # 生成数据
        for interval in ['1m', '5m', '15m', '1h', '4h', '1d']:
            bars = 1000 if interval in ['1m', '5m'] else 2000
            self.data_provider.generate_sample_data(symbol, interval, bars, start_price)
        
        print(f"✅ 已添加自定义交易对: {symbol}")
    
    def update_real_time_data(self, symbol: str, price_data: Dict):
        """更新实时数据"""
        # 这里可以集成真实的价格数据
        # 暂时使用示例实现
        pass
    
    def get_chart_url(self) -> str:
        """获取图表URL"""
        return f"http://127.0.0.1:{self.server.port}"


# 全局TradingView集成
tradingview_integration = None

def get_tradingview_integration(port: int = 5000) -> TradingViewIntegration:
    """获取TradingView集成实例"""
    global tradingview_integration
    if tradingview_integration is None:
        tradingview_integration = TradingViewIntegration(port)
    return tradingview_integration

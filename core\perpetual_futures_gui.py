#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
永续合约交易GUI
Perpetual Futures Trading GUI

专门支持永续合约交易的界面
"""

import json
import random
import threading
import time
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, ttk


class PerpetualFuturesGUI:
    """永续合约交易GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 终极版交易系统 v1.4 - 永续合约版")
        self.root.geometry("1200x900")
        self.root.configure(bg="#1e1e1e")

        # 交易配置
        self.trading_config = {
            "initial_capital": 1000.0,  # 初始资金 USDT
            "current_balance": 1000.0,  # 当前余额
            "daily_pnl": 0.0,  # 当日盈亏
            "total_pnl": 0.0,  # 总盈亏
            "active_strategies": ["永续突破", "网格套利", "资金费率套利"],
            "strategy_allocation": {
                "永续突破": 40,
                "网格套利": 35,
                "资金费率套利": 25,
            },
        }

        # 永续合约策略状态 - 清零交易次数
        self.strategies = {
            "永续突破": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "win_rate": 0,
                "leverage": "10x",
                "wins": 0,
            },
            "网格套利": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "win_rate": 0,
                "leverage": "5x",
                "wins": 0,
            },
            "资金费率套利": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "win_rate": 0,
                "leverage": "3x",
                "wins": 0,
            },
        }

        # 永续合约仓位管理
        self.positions = {}  # 存储当前持仓
        self.position_id_counter = 1  # 仓位ID计数器
        self.closed_positions = []  # 已平仓记录

        # 永续合约交易对数据
        self.perpetual_pairs = {
            # 主流永续合约
            "BTCUSDT": {
                "price": 107951.00,
                "change": -0.44,
                "volume": 2845050.5,
                "funding_rate": 0.0125,
                "open_interest": 15420.8,
                "category": "主流",
                "leverage": "1-125x",
            },
            "ETHUSDT": {
                "price": 2508.35,
                "change": -1.77,
                "volume": 1567802.2,
                "funding_rate": 0.0089,
                "open_interest": 89450.7,
                "category": "主流",
                "leverage": "1-100x",
            },
            "BNBUSDT": {
                "price": 692.45,
                "change": 1.23,
                "volume": 452308.8,
                "funding_rate": 0.0156,
                "open_interest": 23450.6,
                "category": "主流",
                "leverage": "1-75x",
            },
            # DeFi永续合约
            "UNIUSDT": {
                "price": 12.85,
                "change": 2.45,
                "volume": 124503.3,
                "funding_rate": 0.0234,
                "open_interest": 8760.1,
                "category": "DeFi",
                "leverage": "1-50x",
            },
            "AAVEUSDT": {
                "price": 285.67,
                "change": -0.89,
                "volume": 87601.1,
                "funding_rate": -0.0045,
                "open_interest": 5670.9,
                "category": "DeFi",
                "leverage": "1-50x",
            },
            "SUSHIUSDT": {
                "price": 1.45,
                "change": 3.21,
                "volume": 156709.9,
                "funding_rate": 0.0178,
                "open_interest": 12340.5,
                "category": "DeFi",
                "leverage": "1-25x",
            },
            # Layer1永续合约
            "SOLUSDT": {
                "price": 245.78,
                "change": 1.56,
                "volume": 678904.4,
                "funding_rate": 0.0098,
                "open_interest": 34560.2,
                "category": "Layer1",
                "leverage": "1-75x",
            },
            "ADAUSDT": {
                "price": 1.23,
                "change": -2.34,
                "volume": 894507.7,
                "funding_rate": -0.0067,
                "open_interest": 78920.3,
                "category": "Layer1",
                "leverage": "1-50x",
            },
            "DOTUSDT": {
                "price": 8.95,
                "change": 0.78,
                "volume": 234506.6,
                "funding_rate": 0.0123,
                "open_interest": 15670.9,
                "category": "Layer1",
                "leverage": "1-50x",
            },
            "AVAXUSDT": {
                "price": 42.67,
                "change": 2.89,
                "volume": 345602.2,
                "funding_rate": 0.0145,
                "open_interest": 23450.7,
                "category": "Layer1",
                "leverage": "1-50x",
            },
            # Layer2永续合约
            "MATICUSDT": {
                "price": 1.15,
                "change": 1.45,
                "volume": 789203.3,
                "funding_rate": 0.0089,
                "open_interest": 45670.1,
                "category": "Layer2",
                "leverage": "1-50x",
            },
            "OPUSDT": {
                "price": 3.45,
                "change": -1.23,
                "volume": 123408.8,
                "funding_rate": -0.0034,
                "open_interest": 8920.3,
                "category": "Layer2",
                "leverage": "1-25x",
            },
            "ARBUSDT": {
                "price": 2.78,
                "change": 0.95,
                "volume": 456701.1,
                "funding_rate": 0.0067,
                "open_interest": 15670.4,
                "category": "Layer2",
                "leverage": "1-25x",
            },
            # AI永续合约
            "FETUSDT": {
                "price": 2.34,
                "change": 4.56,
                "volume": 234507.7,
                "funding_rate": 0.0234,
                "open_interest": 12340.5,
                "category": "AI",
                "leverage": "1-25x",
            },
            "AGIXUSDT": {
                "price": 0.89,
                "change": 2.78,
                "volume": 156704.4,
                "funding_rate": 0.0189,
                "open_interest": 8920.3,
                "category": "AI",
                "leverage": "1-20x",
            },
            "OCEANUSDT": {
                "price": 1.67,
                "change": 1.89,
                "volume": 89203.3,
                "funding_rate": 0.0145,
                "open_interest": 5670.2,
                "category": "AI",
                "leverage": "1-20x",
            },
            # 游戏永续合约
            "AXSUSDT": {
                "price": 8.45,
                "change": -1.56,
                "volume": 123405.5,
                "funding_rate": -0.0078,
                "open_interest": 8920.4,
                "category": "游戏",
                "leverage": "1-25x",
            },
            "SANDUSDT": {
                "price": 0.67,
                "change": 2.34,
                "volume": 345608.8,
                "funding_rate": 0.0156,
                "open_interest": 15670.9,
                "category": "游戏",
                "leverage": "1-25x",
            },
            "MANAUSDT": {
                "price": 0.89,
                "change": 1.23,
                "volume": 234502.2,
                "funding_rate": 0.0123,
                "open_interest": 12340.7,
                "category": "游戏",
                "leverage": "1-20x",
            },
            # 存储永续合约
            "FILUSDT": {
                "price": 6.78,
                "change": -0.45,
                "volume": 156709.9,
                "funding_rate": -0.0023,
                "open_interest": 8920.4,
                "category": "存储",
                "leverage": "1-25x",
            },
            "ARUSDT": {
                "price": 23.45,
                "change": 1.67,
                "volume": 89204.4,
                "funding_rate": 0.0089,
                "open_interest": 5670.1,
                "category": "存储",
                "leverage": "1-20x",
            },
            # 交易所永续合约
            "GTUSDT": {
                "price": 21.28,
                "change": -0.73,
                "volume": 452306.6,
                "funding_rate": 0.0034,
                "open_interest": 12340.8,
                "category": "交易所",
                "leverage": "1-25x",
            },
        }

        # 分类
        self.categories = [
            "全部",
            "主流",
            "DeFi",
            "Layer1",
            "Layer2",
            "AI",
            "游戏",
            "存储",
            "交易所",
        ]
        self.current_category = "全部"

        # 创建界面
        self.create_widgets()

        # 启动数据更新
        self.start_updates()

    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg="#1e1e1e")
        title_frame.pack(fill="x", padx=10, pady=5)

        title_label = tk.Label(
            title_frame,
            text="🚀 终极版交易系统 v1.4 - 永续合约版",
            font=("Arial", 16, "bold"),
            fg="#ff6600",
            bg="#1e1e1e",
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="支持22个永续合约 | 杠杆交易 | 资金费率监控 | 持仓量分析",
            font=("Arial", 10),
            fg="#888888",
            bg="#1e1e1e",
        )
        subtitle_label.pack()

        # 资金状态
        balance_frame = tk.Frame(self.root, bg="#2e2e2e")
        balance_frame.pack(fill="x", padx=10, pady=5)

        self.balance_label = tk.Label(
            balance_frame,
            text=f"💰 总资金: {self.trading_config['current_balance']:,.2f} USDT",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#2e2e2e",
        )
        self.balance_label.pack(side="left", padx=10)

        self.pnl_label = tk.Label(
            balance_frame,
            text=f"📈 当日盈亏: {self.trading_config['daily_pnl']:+.2f} USDT",
            font=("Arial", 12, "bold"),
            fg=(
                "#00ff00"
                if self.trading_config["daily_pnl"] >= 0
                else "#ff4444"
            ),
            bg="#2e2e2e",
        )
        self.pnl_label.pack(side="right", padx=10)

        # 永续合约策略状态
        strategy_frame = tk.LabelFrame(
            self.root,
            text="⚡ 永续合约策略",
            font=("Arial", 12, "bold"),
            fg="#ff6600",
            bg="#1e1e1e",
        )
        strategy_frame.pack(fill="x", padx=10, pady=5)

        self.strategy_display_frame = tk.Frame(strategy_frame, bg="#1e1e1e")
        self.strategy_display_frame.pack(fill="x", padx=5, pady=5)

        self.update_strategy_display()

        # 持仓显示区域
        positions_frame = tk.LabelFrame(
            self.root,
            text="📊 当前持仓",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        positions_frame.pack(fill="x", padx=10, pady=5)

        self.positions_display_frame = tk.Frame(positions_frame, bg="#1e1e1e")
        self.positions_display_frame.pack(fill="x", padx=5, pady=5)

        self.update_positions_display()

        # 状态栏
        status_frame = tk.Frame(self.root, bg="#333333")
        status_frame.pack(fill="x", padx=10, pady=5)

        self.status_label = tk.Label(
            status_frame,
            text=f"📊 永续合约监控: {len(self.perpetual_pairs)}个 | 🔄 实时更新 | ⚡ 杠杆交易",
            font=("Arial", 10),
            fg="#ff6600",
            bg="#333333",
        )
        self.status_label.pack(pady=5)

        # 分类筛选
        filter_frame = tk.Frame(self.root, bg="#2e2e2e")
        filter_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(
            filter_frame,
            text="永续合约分类:",
            font=("Arial", 12, "bold"),
            fg="white",
            bg="#2e2e2e",
        ).pack(side="left", padx=10)

        self.category_var = tk.StringVar(value="全部")
        for category in self.categories:
            btn = tk.Radiobutton(
                filter_frame,
                text=category,
                variable=self.category_var,
                value=category,
                command=self.filter_category,
                font=("Arial", 10),
                fg="white",
                bg="#2e2e2e",
                selectcolor="#4e4e4e",
            )
            btn.pack(side="left", padx=5)

        # 控制按钮
        control_frame = tk.Frame(filter_frame, bg="#2e2e2e")
        control_frame.pack(side="right", padx=10)

        ttk.Button(
            control_frame, text="🔄 刷新数据", command=self.refresh_data
        ).pack(side="left", padx=2)
        ttk.Button(
            control_frame, text="💼 持仓管理", command=self.show_positions
        ).pack(side="left", padx=2)

        # 永续合约表格
        self.create_table()

        # 交易信号
        signal_frame = tk.LabelFrame(
            self.root,
            text="🎯 永续合约交易信号",
            font=("Arial", 12, "bold"),
            fg="white",
            bg="#1e1e1e",
        )
        signal_frame.pack(fill="x", padx=10, pady=5)

        self.signal_text = tk.Text(
            signal_frame,
            height=6,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Consolas", 9),
            wrap="word",
        )
        self.signal_text.pack(fill="x", padx=5, pady=5)

        # 添加初始信号
        self.add_signal("🌐 永续合约监控系统已启动")
        self.add_signal(f"📊 正在监控 {len(self.perpetual_pairs)} 个永续合约")
        self.add_signal("⚡ 杠杆交易信号分析已就绪")
        self.add_signal("💰 资金费率监控已启动")

    def create_table(self):
        """创建永续合约表格"""
        table_frame = tk.Frame(self.root)
        table_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建Treeview - 永续合约专用列
        columns = (
            "合约",
            "价格",
            "24h变化",
            "成交量",
            "资金费率",
            "持仓量",
            "杠杆",
            "分类",
        )
        self.tree = ttk.Treeview(
            table_frame, columns=columns, show="headings", height=15
        )

        # 设置列标题和宽度
        column_widths = {
            "合约": 100,
            "价格": 100,
            "24h变化": 80,
            "成交量": 100,
            "资金费率": 80,
            "持仓量": 100,
            "杠杆": 80,
            "分类": 80,
        }
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(
                col, width=column_widths.get(col, 100), anchor="center"
            )

        # 滚动条
        scrollbar = ttk.Scrollbar(
            table_frame, orient="vertical", command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 初始化数据
        self.update_table()

    def get_filtered_pairs(self):
        """获取筛选后的永续合约"""
        if self.current_category == "全部":
            return self.perpetual_pairs
        else:
            return {
                k: v
                for k, v in self.perpetual_pairs.items()
                if v["category"] == self.current_category
            }

    def update_table(self):
        """更新表格数据"""
        # 清除现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 获取筛选后的数据
        filtered_pairs = self.get_filtered_pairs()

        # 添加数据
        for symbol, data in filtered_pairs.items():
            price = data["price"]
            change = data["change"]
            volume = data["volume"]
            funding_rate = data["funding_rate"]
            open_interest = data["open_interest"]
            leverage = data["leverage"]
            category = data["category"]

            # 格式化价格
            if price >= 1:
                price_text = f"{price:,.2f}"
            else:
                price_text = f"{price:.6f}"

            # 格式化变化
            change_text = f"{change:+.2f}%"

            # 格式化成交量
            volume_text = (
                f"{volume/1000:.1f}K" if volume >= 1000 else f"{volume:.1f}"
            )

            # 格式化资金费率
            funding_text = f"{funding_rate:+.4f}%"

            # 格式化持仓量
            oi_text = f"{open_interest:,.1f}"

            # 插入数据
            item = self.tree.insert(
                "",
                "end",
                values=(
                    symbol,
                    price_text,
                    change_text,
                    volume_text,
                    funding_text,
                    oi_text,
                    leverage,
                    category,
                ),
            )

        # 更新状态
        count = len(filtered_pairs)
        if self.current_category == "全部":
            self.status_label.config(
                text=f"📊 永续合约监控: {count}个 | 🔄 实时更新 | ⚡ 杠杆交易"
            )
        else:
            self.status_label.config(
                text=f"📊 {self.current_category}永续合约: {count}个 | 🔄 实时更新 | ⚡ 杠杆交易"
            )

    def filter_category(self):
        """筛选分类"""
        self.current_category = self.category_var.get()
        self.update_table()
        self.add_signal(f"🔍 切换到 {self.current_category} 永续合约")

    def simulate_price_changes(self):
        """模拟价格变化"""
        for symbol in self.perpetual_pairs:
            # 永续合约波动更大
            change = random.uniform(-1.0, 1.0)
            self.perpetual_pairs[symbol]["change"] += change

            # 限制变化范围
            if self.perpetual_pairs[symbol]["change"] > 15:
                self.perpetual_pairs[symbol]["change"] = 15
            elif self.perpetual_pairs[symbol]["change"] < -15:
                self.perpetual_pairs[symbol]["change"] = -15

            # 更新价格
            base_price = self.perpetual_pairs[symbol]["price"]
            self.perpetual_pairs[symbol]["price"] = base_price * (
                1 + change / 100
            )

            # 更新资金费率
            funding_change = random.uniform(-0.001, 0.001)
            self.perpetual_pairs[symbol]["funding_rate"] += funding_change

            # 限制资金费率范围
            if self.perpetual_pairs[symbol]["funding_rate"] > 0.05:
                self.perpetual_pairs[symbol]["funding_rate"] = 0.05
            elif self.perpetual_pairs[symbol]["funding_rate"] < -0.05:
                self.perpetual_pairs[symbol]["funding_rate"] = -0.05

    def generate_signal(self):
        """生成永续合约交易信号"""
        filtered_pairs = self.get_filtered_pairs()
        if not filtered_pairs:
            return

        symbol = random.choice(list(filtered_pairs.keys()))
        data = filtered_pairs[symbol]
        change = data["change"]
        funding_rate = data["funding_rate"]
        category = data["category"]
        leverage = data["leverage"]

        # 根据价格变化和资金费率生成信号
        if change > 3 and funding_rate < 0:
            signal = "做多"
            strength = "强"
            reason = "价格上涨+负资金费率"
        elif change < -3 and funding_rate > 0.02:
            signal = "做空"
            strength = "强"
            reason = "价格下跌+高资金费率"
        elif change > 2:
            signal = "做多"
            strength = "中"
            reason = "价格突破"
        elif change < -2:
            signal = "做空"
            strength = "中"
            reason = "价格跌破"
        elif abs(funding_rate) > 0.03:
            signal = "套利"
            strength = "中"
            reason = f"资金费率{'过高' if funding_rate > 0 else '过低'}"
        else:
            signal = "观察"
            strength = "弱"
            reason = "震荡整理"

        # 永续合约特定原因
        perp_reasons = {
            "主流": ["机构持仓", "期现套利", "杠杆清算"],
            "DeFi": ["TVL变化", "协议收益", "流动性挖矿"],
            "Layer1": ["生态资金", "质押收益", "开发进展"],
            "Layer2": ["扩容需求", "手续费优势", "资金迁移"],
            "AI": ["AI热度", "概念炒作", "技术突破"],
            "游戏": ["游戏收入", "用户增长", "NFT交易"],
            "存储": ["存储需求", "网络使用", "代币经济"],
            "交易所": ["平台收入", "回购计划", "业务扩展"],
        }

        if signal != "观察":
            extra_reason = random.choice(
                perp_reasons.get(category, ["技术分析"])
            )
            signal_text = f"⚡ {symbol} ({category}): {signal}信号 ({strength}) - {reason} | {extra_reason} | {leverage}"
        else:
            signal_text = f"⚪ {symbol} ({category}): 震荡整理，等待机会 | 资金费率: {funding_rate:+.4f}%"

        self.add_signal(signal_text)

    def add_signal(self, message):
        """添加信号"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.signal_text.insert("end", full_message)
        self.signal_text.see("end")

        # 限制长度
        lines = self.signal_text.get("1.0", "end").split("\n")
        if len(lines) > 20:
            self.signal_text.delete("1.0", "5.0")

    def refresh_data(self):
        """刷新数据"""
        self.simulate_price_changes()
        self.update_table()
        self.add_signal("🔄 永续合约数据已刷新")

    def update_positions_display(self):
        """更新持仓显示"""
        # 清除旧显示
        for widget in self.positions_display_frame.winfo_children():
            widget.destroy()

        if not self.positions:
            tk.Label(
                self.positions_display_frame,
                text="📭 当前无持仓",
                font=("Arial", 10),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=10)
            return

        # 显示持仓
        for position_id, position in self.positions.items():
            pos_frame = tk.Frame(
                self.positions_display_frame,
                bg="#2e2e2e",
                relief="raised",
                bd=1,
            )
            pos_frame.pack(fill="x", padx=5, pady=2)

            # 基本信息
            info_frame = tk.Frame(pos_frame, bg="#2e2e2e")
            info_frame.pack(fill="x", padx=5, pady=2)

            direction_text = (
                "多头" if position["direction"] == "LONG" else "空头"
            )
            direction_color = (
                "#00ff00" if position["direction"] == "LONG" else "#ff4444"
            )

            tk.Label(
                info_frame,
                text=f"📊 {position['symbol']}",
                font=("Arial", 10, "bold"),
                fg="#ffffff",
                bg="#2e2e2e",
            ).pack(side="left")
            tk.Label(
                info_frame,
                text=f"{direction_text}",
                font=("Arial", 9, "bold"),
                fg=direction_color,
                bg="#2e2e2e",
            ).pack(side="left", padx=10)
            tk.Label(
                info_frame,
                text=f"杠杆: {position['leverage']}x",
                font=("Arial", 9),
                fg="#ffaa00",
                bg="#2e2e2e",
            ).pack(side="left", padx=10)

            # 盈亏信息
            unrealized_pnl = position.get("unrealized_pnl", 0)
            pnl_color = "#00ff00" if unrealized_pnl >= 0 else "#ff4444"
            pnl_text = f"{unrealized_pnl:+.2f} USDT"
            tk.Label(
                info_frame,
                text=f"盈亏: {pnl_text}",
                font=("Arial", 9, "bold"),
                fg=pnl_color,
                bg="#2e2e2e",
            ).pack(side="right")

    def show_positions(self):
        """显示持仓管理窗口"""
        pos_window = tk.Toplevel(self.root)
        pos_window.title("💼 持仓管理")
        pos_window.geometry("600x400")
        pos_window.configure(bg="#1e1e1e")

        tk.Label(
            pos_window,
            text="💼 持仓管理",
            font=("Arial", 14, "bold"),
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 持仓列表
        pos_frame = tk.Frame(pos_window, bg="#1e1e1e")
        pos_frame.pack(fill="both", expand=True, padx=20, pady=10)

        if not self.positions:
            tk.Label(
                pos_frame,
                text="📭 当前无持仓",
                font=("Arial", 12),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=50)
        else:
            for position_id, position in self.positions.items():
                p_frame = tk.LabelFrame(
                    pos_frame,
                    text=f"仓位 {position_id}",
                    fg="#ffffff",
                    bg="#2e2e2e",
                )
                p_frame.pack(fill="x", pady=5)

                # 详细信息
                info_text = f"""
交易对: {position['symbol']}
方向: {'多头' if position['direction'] == 'LONG' else '空头'}
开仓价格: {position['entry_price']:.2f}
名义价值: {position['notional_value']:.2f} USDT
杠杆倍数: {position['leverage']}x
保证金: {position['margin']:.2f} USDT
未实现盈亏: {position.get('unrealized_pnl', 0):+.2f} USDT
强制平仓价: {position['liquidation_price']:.2f}
开仓时间: {position['open_time'].strftime('%H:%M:%S')}
                """

                tk.Label(
                    p_frame,
                    text=info_text,
                    font=("Consolas", 9),
                    fg="#ffffff",
                    bg="#2e2e2e",
                    justify="left",
                ).pack(padx=10, pady=5)

        self.add_signal("💼 查看持仓管理")

    def calculate_liquidation_price(self, direction, entry_price, leverage):
        """计算强制平仓价格"""
        # 维持保证金率通常为0.5%
        maintenance_margin_rate = 0.005

        if direction == "LONG":
            # 多头强制平仓价格 = 开仓价格 * (1 - 1/杠杆 + 维持保证金率)
            liquidation_price = entry_price * (
                1 - 1 / leverage + maintenance_margin_rate
            )
        else:  # SHORT
            # 空头强制平仓价格 = 开仓价格 * (1 + 1/杠杆 - 维持保证金率)
            liquidation_price = entry_price * (
                1 + 1 / leverage - maintenance_margin_rate
            )

        return liquidation_price

    def update_strategy_display(self):
        """动态更新永续合约策略显示"""
        # 清除旧显示
        for widget in self.strategy_display_frame.winfo_children():
            widget.destroy()

        # 重新创建策略显示
        for strategy_name, strategy_data in self.strategies.items():
            s_frame = tk.Frame(self.strategy_display_frame, bg="#1e1e1e")
            s_frame.pack(fill="x", padx=5, pady=2)

            allocation = self.trading_config["strategy_allocation"][
                strategy_name
            ]
            pnl = strategy_data["pnl"]
            win_rate = strategy_data["win_rate"]
            trades = strategy_data["trades"]
            status = strategy_data["status"]
            leverage = strategy_data["leverage"]

            # 策略名称
            tk.Label(
                s_frame,
                text=f"⚡ {strategy_name}",
                font=("Arial", 10, "bold"),
                fg="#ff6600",
                bg="#1e1e1e",
            ).pack(side="left")

            # 资金分配
            tk.Label(
                s_frame,
                text=f"资金: {allocation}%",
                font=("Arial", 9),
                fg="#ffffff",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 杠杆倍数
            tk.Label(
                s_frame,
                text=f"杠杆: {leverage}",
                font=("Arial", 9),
                fg="#ffaa00",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 盈亏显示
            pnl_color = "#00ff00" if pnl >= 0 else "#ff4444"
            tk.Label(
                s_frame,
                text=f"盈亏: {pnl:+.2f}",
                font=("Arial", 9),
                fg=pnl_color,
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 交易次数
            tk.Label(
                s_frame,
                text=f"交易: {trades}次",
                font=("Arial", 9),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 胜率显示
            win_rate_color = (
                "#00ff00"
                if win_rate >= 70
                else "#ffaa00" if win_rate >= 60 else "#ff4444"
            )
            tk.Label(
                s_frame,
                text=f"胜率: {win_rate}%",
                font=("Arial", 9),
                fg=win_rate_color,
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 状态显示
            status_color = "#00ff00" if status == "运行中" else "#ff4444"
            tk.Label(
                s_frame,
                text=f"状态: {status}",
                font=("Arial", 9),
                fg=status_color,
                bg="#1e1e1e",
            ).pack(side="right")

    def update_balance_display(self):
        """更新余额显示"""
        self.balance_label.config(
            text=f"💰 总资金: {self.trading_config['current_balance']:,.2f} USDT"
        )
        self.pnl_label.config(
            text=f"📈 当日盈亏: {self.trading_config['daily_pnl']:+.2f} USDT",
            fg=(
                "#00ff00"
                if self.trading_config["daily_pnl"] >= 0
                else "#ff4444"
            ),
        )

    def open_position(
        self, strategy_name, symbol, direction, size, leverage, entry_price
    ):
        """开仓"""
        position_id = f"POS_{self.position_id_counter}"
        self.position_id_counter += 1

        # 修复：正确计算保证金和名义价值
        notional_value = size  # size现在表示名义价值(USDT)
        margin_required = notional_value / leverage  # 所需保证金
        position_size_coins = notional_value / entry_price  # 实际币数量

        position = {
            "id": position_id,
            "strategy": strategy_name,
            "symbol": symbol,
            "direction": direction,  # 'LONG' 或 'SHORT'
            "notional_value": notional_value,  # 名义价值
            "position_size_coins": position_size_coins,  # 币数量
            "leverage": leverage,
            "entry_price": entry_price,
            "open_time": datetime.now(),
            "unrealized_pnl": 0.0,
            "margin": margin_required,  # 修复：正确的保证金计算
            "maintenance_margin_rate": 0.005,  # 维持保证金率0.5%
            "liquidation_price": self.calculate_liquidation_price(
                direction, entry_price, leverage
            ),
        }

        self.positions[position_id] = position
        direction_text = "开多" if direction == "LONG" else "开空"
        self.add_signal(
            f"📈 {strategy_name} {direction_text} {symbol} | 价格: {entry_price:.2f} | 杠杆: {leverage}x | 仓位: {size:.2f} USDT"
        )
        return position_id

    def close_position(self, position_id, exit_price, reason="手动平仓"):
        """平仓"""
        if position_id not in self.positions:
            return

        position = self.positions[position_id]

        # 修复：正确计算永续合约盈亏
        price_change_rate = 0
        if position["direction"] == "LONG":
            price_change_rate = (
                exit_price - position["entry_price"]
            ) / position["entry_price"]
        else:  # SHORT
            price_change_rate = (
                position["entry_price"] - exit_price
            ) / position["entry_price"]

        # 永续合约盈亏 = 价格变化率 × 名义价值 (杠杆已经体现在名义价值中)
        gross_pnl = price_change_rate * position["notional_value"]

        # 修复：计算交易手续费 (开仓+平仓，通常为0.1%)
        trading_fee = position["notional_value"] * 0.001  # 0.1%手续费
        pnl = gross_pnl - trading_fee

        # 更新策略盈亏
        strategy_name = position["strategy"]
        self.strategies[strategy_name]["pnl"] += pnl
        self.trading_config["daily_pnl"] += pnl

        # 修复：更新胜率统计
        if pnl > 0:
            self.strategies[strategy_name]["wins"] += 1

        # 重新计算胜率
        total_trades = self.strategies[strategy_name]["trades"]
        if total_trades > 0:
            wins = self.strategies[strategy_name]["wins"]
            self.strategies[strategy_name]["win_rate"] = int(
                (wins / total_trades) * 100
            )

        # 修复：平仓时释放保证金并加上盈亏
        self.trading_config["current_balance"] += position["margin"] + pnl

        # 记录平仓
        closed_position = position.copy()
        closed_position["exit_price"] = exit_price
        closed_position["close_time"] = datetime.now()
        closed_position["realized_pnl"] = pnl
        closed_position["close_reason"] = reason
        self.closed_positions.append(closed_position)

        # 修复：内存管理 - 限制历史记录数量
        if len(self.closed_positions) > 100:
            self.closed_positions = self.closed_positions[
                -50:
            ]  # 保留最近50条记录

        # 移除持仓
        del self.positions[position_id]

        direction_text = "平多" if position["direction"] == "LONG" else "平空"
        pnl_text = f"+{pnl:.2f}" if pnl >= 0 else f"{pnl:.2f}"
        self.add_signal(
            f"💰 {strategy_name} {direction_text} {position['symbol']} | 价格: {exit_price:.2f} | 盈亏: {pnl_text} USDT | {reason}"
        )

        return pnl

    def update_positions_pnl(self):
        """更新所有持仓的未实现盈亏"""
        for position_id, position in self.positions.items():
            symbol = position["symbol"]
            if symbol in self.perpetual_pairs:
                current_price = self.perpetual_pairs[symbol]["price"]

                # 修复：正确计算未实现盈亏
                price_change_rate = 0
                if position["direction"] == "LONG":
                    price_change_rate = (
                        current_price - position["entry_price"]
                    ) / position["entry_price"]
                else:  # SHORT
                    price_change_rate = (
                        position["entry_price"] - current_price
                    ) / position["entry_price"]

                # 未实现盈亏 = 价格变化率 × 名义价值
                unrealized_pnl = price_change_rate * position["notional_value"]
                position["unrealized_pnl"] = unrealized_pnl

                # 检查是否触及强制平仓价格
                if (
                    position["direction"] == "LONG"
                    and current_price <= position["liquidation_price"]
                ) or (
                    position["direction"] == "SHORT"
                    and current_price >= position["liquidation_price"]
                ):
                    position["force_liquidation"] = True

    def check_position_close_conditions(self):
        """检查平仓条件"""
        positions_to_close = []

        for position_id, position in self.positions.items():
            unrealized_pnl = position["unrealized_pnl"]
            symbol = position["symbol"]
            current_price = self.perpetual_pairs[symbol]["price"]
            notional_value = position["notional_value"]

            # 修复：基于百分比的止盈止损
            pnl_percentage = unrealized_pnl / notional_value  # 盈亏百分比

            # 强制平仓检查
            if position.get("force_liquidation", False):
                positions_to_close.append(
                    (position_id, current_price, "强制平仓")
                )

            # 止盈条件：盈利超过5%
            elif pnl_percentage > 0.05:
                positions_to_close.append((position_id, current_price, "止盈"))

            # 止损条件：亏损超过3%
            elif pnl_percentage < -0.03:
                positions_to_close.append((position_id, current_price, "止损"))

            # 修复：时间止损计算
            elif (
                datetime.now() - position["open_time"]
            ).total_seconds() > 3600 and unrealized_pnl < 0:
                positions_to_close.append(
                    (position_id, current_price, "时间止损")
                )

        # 执行平仓
        for position_id, exit_price, reason in positions_to_close:
            self.close_position(position_id, exit_price, reason)

    def simulate_perpetual_trading(self):
        """模拟永续合约交易（完整版）"""
        # 资金保护：如果余额低于初始资金的20%，停止交易
        if (
            self.trading_config["current_balance"]
            < self.trading_config["initial_capital"] * 0.2
        ):
            self.add_signal("🛡️ 触发资金保护，停止交易")
            return

        # 更新持仓盈亏
        self.update_positions_pnl()

        # 检查平仓条件
        self.check_position_close_conditions()

        # 策略交易逻辑
        for strategy_name, strategy_data in self.strategies.items():
            if strategy_data["status"] != "运行中":
                continue

            # 降低开仓频率
            if random.random() < 0.03:  # 3%概率开仓
                allocation = (
                    self.trading_config["strategy_allocation"][strategy_name]
                    / 100
                )

                # 修复：计算真实可用资金（扣除已占用保证金）
                strategy_positions = [
                    p
                    for p in self.positions.values()
                    if p["strategy"] == strategy_name
                ]
                used_margin = sum(p["margin"] for p in strategy_positions)
                total_allocated = (
                    self.trading_config["current_balance"] * allocation
                )
                available_capital = max(0, total_allocated - used_margin)

                # 检查是否已有该策略的持仓（使用已计算的strategy_positions）
                if len(strategy_positions) >= 2:  # 限制每个策略最多2个持仓
                    continue

                # 获取杠杆倍数
                leverage_str = strategy_data["leverage"]
                leverage = int(leverage_str.replace("x", ""))

                # 修复：智能选择交易对
                suitable_symbols = []
                for symbol, data in self.perpetual_pairs.items():
                    # 根据策略选择合适的交易对
                    if strategy_name == "永续突破":
                        # 突破策略偏好高波动性币种
                        if abs(data["change"]) > 1 and data["volume"] > 500000:
                            suitable_symbols.append(symbol)
                    elif strategy_name == "网格套利":
                        # 网格策略偏好震荡币种
                        if abs(data["change"]) < 2:
                            suitable_symbols.append(symbol)
                    elif strategy_name == "资金费率套利":
                        # 资金费率策略偏好高费率币种
                        if abs(data["funding_rate"]) > 0.01:
                            suitable_symbols.append(symbol)

                # 如果没有合适的交易对，随机选择
                if not suitable_symbols:
                    suitable_symbols = list(self.perpetual_pairs.keys())

                symbol = random.choice(suitable_symbols)
                current_price = self.perpetual_pairs[symbol]["price"]
                funding_rate = self.perpetual_pairs[symbol]["funding_rate"]
                price_change = self.perpetual_pairs[symbol]["change"]

                # 修复：优化策略交易逻辑，降低触发门槛
                direction = None
                if strategy_name == "永续突破":
                    # 突破策略：降低触发门槛
                    volume = self.perpetual_pairs[symbol]["volume"]
                    if price_change > 2 and volume > 300000:  # 降低门槛
                        direction = "LONG"
                    elif price_change < -2 and volume > 300000:  # 降低门槛
                        direction = "SHORT"

                elif strategy_name == "网格套利":
                    # 网格策略：扩大震荡范围，降低触发门槛
                    if -2 <= price_change <= 2:  # 扩大震荡范围
                        if price_change > 0.5:  # 降低触发门槛
                            direction = "SHORT"  # 高点做空
                        elif price_change < -0.5:  # 降低触发门槛
                            direction = "LONG"  # 低点做多

                elif strategy_name == "资金费率套利":
                    # 资金费率套利：降低费率门槛
                    if funding_rate > 0.015:  # 降低门槛
                        direction = "SHORT"  # 做空避免支付费率
                    elif funding_rate < -0.01:  # 降低门槛
                        direction = "LONG"  # 做多获得费率收益

                # 开仓
                if direction:
                    # 修复：正确计算仓位大小
                    max_position_value = (
                        available_capital * 0.2
                    )  # 单笔最多使用20%资金
                    max_notional_value = (
                        max_position_value * leverage
                    )  # 最大名义价值

                    # 根据币种价格调整仓位大小
                    if current_price > 1000:  # 高价币种(如BTC)
                        notional_value = min(max_notional_value, 500)
                    elif current_price > 100:  # 中价币种(如ETH)
                        notional_value = min(max_notional_value, 300)
                    else:  # 低价币种
                        notional_value = min(max_notional_value, 200)

                    # 检查保证金是否足够
                    required_margin = notional_value / leverage
                    if required_margin <= available_capital:
                        self.open_position(
                            strategy_name,
                            symbol,
                            direction,
                            notional_value,
                            leverage,
                            current_price,
                        )

                        # 从可用资金中扣除保证金
                        self.trading_config[
                            "current_balance"
                        ] -= required_margin

                        # 更新交易次数
                        strategy_data["trades"] += 1

        # 更新总盈亏
        self.trading_config["total_pnl"] = (
            self.trading_config["current_balance"]
            - self.trading_config["initial_capital"]
        )

    def start_updates(self):
        """启动更新"""

        def update_loop():
            while True:
                try:
                    # 模拟价格变化
                    self.simulate_price_changes()

                    # 模拟永续合约交易
                    self.simulate_perpetual_trading()

                    # 更新界面
                    self.root.after(0, self.update_table)
                    self.root.after(0, self.update_balance_display)
                    self.root.after(0, self.update_strategy_display)
                    self.root.after(0, self.update_positions_display)

                    # 生成信号
                    if (
                        random.random() < 0.4
                    ):  # 40%概率生成信号（永续合约更活跃）
                        self.root.after(0, self.generate_signal)

                    time.sleep(10)  # 10秒更新一次（永续合约更频繁）

                except Exception as e:
                    print(f"更新错误: {e}")
                    time.sleep(30)

        threading.Thread(target=update_loop, daemon=True).start()

    def run(self):
        """运行GUI"""
        self.add_signal("🚀 永续合约监控系统启动成功")
        self.add_signal("⚡ 支持1-125倍杠杆交易")
        self.add_signal("💰 资金费率实时监控")
        self.root.mainloop()


def main():
    """主函数"""
    app = PerpetualFuturesGUI()
    app.run()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略回测系统
Strategy Backtesting System for Historical Performance Analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
import json
import os


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 10000.0, commission: float = 0.001):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            commission: 手续费率
        """
        self.initial_capital = initial_capital
        self.commission = commission
        
        # 回测状态
        self.current_capital = initial_capital
        self.positions = {}  # 持仓
        self.trades = []     # 交易记录
        self.equity_curve = []  # 资金曲线
        
        # 性能指标
        self.metrics = {}
        
        # 数据
        self.price_data = {}
        self.current_time = None
        self.current_prices = {}
    
    def load_historical_data(self, symbol: str, data: pd.DataFrame):
        """加载历史数据"""
        try:
            # 确保数据格式正确
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in data.columns for col in required_columns):
                raise ValueError(f"数据必须包含列: {required_columns}")
            
            # 按时间排序
            data = data.sort_values('timestamp').reset_index(drop=True)
            
            self.price_data[symbol] = data
            print(f"✅ 已加载 {symbol} 历史数据: {len(data)} 条记录")
            
        except Exception as e:
            print(f"❌ 加载历史数据失败: {e}")
    
    def generate_sample_data(self, symbol: str, days: int = 365, start_price: float = 50000.0):
        """生成示例数据"""
        try:
            # 生成时间序列
            start_time = datetime.now() - timedelta(days=days)
            timestamps = [start_time + timedelta(hours=i) for i in range(days * 24)]
            
            # 生成价格数据（随机游走）
            np.random.seed(42)  # 固定随机种子
            returns = np.random.normal(0, 0.02, len(timestamps))  # 2%波动率
            
            prices = [start_price]
            for ret in returns[1:]:
                new_price = prices[-1] * (1 + ret)
                prices.append(max(new_price, start_price * 0.5))  # 防止价格过低
            
            # 生成OHLCV数据
            data = []
            for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
                if i == 0:
                    open_price = close
                else:
                    open_price = prices[i-1]
                
                # 生成高低价
                volatility = abs(returns[i]) * close
                high = close + volatility * np.random.uniform(0, 1)
                low = close - volatility * np.random.uniform(0, 1)
                
                # 确保价格逻辑正确
                high = max(high, open_price, close)
                low = min(low, open_price, close)
                
                volume = np.random.uniform(1000, 10000)
                
                data.append({
                    'timestamp': timestamp,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })
            
            df = pd.DataFrame(data)
            self.load_historical_data(symbol, df)
            
        except Exception as e:
            print(f"❌ 生成示例数据失败: {e}")
    
    def place_order(self, symbol: str, side: str, amount: float, price: float = None) -> bool:
        """下单"""
        try:
            if symbol not in self.current_prices:
                print(f"❌ 无法获取 {symbol} 当前价格")
                return False
            
            current_price = self.current_prices[symbol]
            execution_price = price if price else current_price
            
            # 计算交易金额
            if side.lower() == 'buy':
                trade_value = amount * execution_price
                commission_cost = trade_value * self.commission
                total_cost = trade_value + commission_cost
                
                if total_cost > self.current_capital:
                    print(f"❌ 资金不足: 需要 {total_cost:.2f}, 可用 {self.current_capital:.2f}")
                    return False
                
                # 执行买入
                self.current_capital -= total_cost
                if symbol not in self.positions:
                    self.positions[symbol] = 0
                self.positions[symbol] += amount
                
            else:  # sell
                if symbol not in self.positions or self.positions[symbol] < amount:
                    print(f"❌ 持仓不足: 需要 {amount}, 持有 {self.positions.get(symbol, 0)}")
                    return False
                
                # 执行卖出
                trade_value = amount * execution_price
                commission_cost = trade_value * self.commission
                net_proceeds = trade_value - commission_cost
                
                self.current_capital += net_proceeds
                self.positions[symbol] -= amount
                
                if self.positions[symbol] == 0:
                    del self.positions[symbol]
            
            # 记录交易
            trade_record = {
                'timestamp': self.current_time,
                'symbol': symbol,
                'side': side,
                'amount': amount,
                'price': execution_price,
                'value': amount * execution_price,
                'commission': trade_value * self.commission,
                'capital_after': self.current_capital
            }
            
            self.trades.append(trade_record)
            
            print(f"📈 {side.upper()} {amount:.6f} {symbol} @ {execution_price:.2f}")
            return True
            
        except Exception as e:
            print(f"❌ 下单失败: {e}")
            return False
    
    def get_portfolio_value(self) -> float:
        """获取投资组合总价值"""
        total_value = self.current_capital
        
        for symbol, amount in self.positions.items():
            if symbol in self.current_prices:
                total_value += amount * self.current_prices[symbol]
        
        return total_value
    
    def run_backtest(self, strategy_func: Callable, symbols: List[str], 
                    start_date: datetime = None, end_date: datetime = None) -> Dict:
        """运行回测"""
        try:
            print("🚀 开始策略回测...")
            
            # 验证数据
            for symbol in symbols:
                if symbol not in self.price_data:
                    print(f"❌ 缺少 {symbol} 历史数据")
                    return {'success': False, 'error': f'缺少 {symbol} 数据'}
            
            # 获取时间范围
            all_timestamps = []
            for symbol in symbols:
                all_timestamps.extend(self.price_data[symbol]['timestamp'].tolist())
            
            all_timestamps = sorted(set(all_timestamps))
            
            if start_date:
                all_timestamps = [t for t in all_timestamps if t >= start_date]
            if end_date:
                all_timestamps = [t for t in all_timestamps if t <= end_date]
            
            print(f"📊 回测时间范围: {len(all_timestamps)} 个时间点")
            
            # 重置状态
            self.current_capital = self.initial_capital
            self.positions = {}
            self.trades = []
            self.equity_curve = []
            
            # 逐时间点回测
            for i, timestamp in enumerate(all_timestamps):
                self.current_time = timestamp
                
                # 更新当前价格
                self.current_prices = {}
                for symbol in symbols:
                    symbol_data = self.price_data[symbol]
                    # 找到最接近的价格数据
                    closest_idx = symbol_data['timestamp'].sub(timestamp).abs().idxmin()
                    self.current_prices[symbol] = symbol_data.loc[closest_idx, 'close']
                
                # 执行策略
                try:
                    strategy_func(self, timestamp, self.current_prices)
                except Exception as e:
                    print(f"⚠️ 策略执行异常 {timestamp}: {e}")
                
                # 记录资金曲线
                portfolio_value = self.get_portfolio_value()
                self.equity_curve.append({
                    'timestamp': timestamp,
                    'portfolio_value': portfolio_value,
                    'capital': self.current_capital,
                    'positions_value': portfolio_value - self.current_capital
                })
                
                # 进度显示
                if i % (len(all_timestamps) // 10) == 0:
                    progress = (i / len(all_timestamps)) * 100
                    print(f"📈 回测进度: {progress:.1f}% - 组合价值: {portfolio_value:.2f}")
            
            # 计算性能指标
            self.calculate_metrics()
            
            print("✅ 回测完成")
            return {
                'success': True,
                'trades': len(self.trades),
                'final_value': self.get_portfolio_value(),
                'metrics': self.metrics
            }
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def calculate_metrics(self):
        """计算性能指标"""
        try:
            if not self.equity_curve:
                return
            
            # 转换为DataFrame便于计算
            df = pd.DataFrame(self.equity_curve)
            df['returns'] = df['portfolio_value'].pct_change().fillna(0)
            
            final_value = df['portfolio_value'].iloc[-1]
            total_return = (final_value - self.initial_capital) / self.initial_capital
            
            # 年化收益率
            days = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).days
            annual_return = (1 + total_return) ** (365 / max(days, 1)) - 1
            
            # 波动率
            volatility = df['returns'].std() * np.sqrt(365 * 24)  # 假设小时数据
            
            # 夏普比率
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            df['cummax'] = df['portfolio_value'].cummax()
            df['drawdown'] = (df['portfolio_value'] - df['cummax']) / df['cummax']
            max_drawdown = df['drawdown'].min()
            
            # 胜率
            winning_trades = len([t for t in self.trades if self._is_profitable_trade(t)])
            total_trades = len(self.trades)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            self.metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'final_value': final_value,
                'profit_loss': final_value - self.initial_capital
            }
            
        except Exception as e:
            print(f"❌ 计算性能指标失败: {e}")
            self.metrics = {}
    
    def _is_profitable_trade(self, trade: Dict) -> bool:
        """判断交易是否盈利（简化版）"""
        # 这里需要更复杂的逻辑来匹配买卖对
        # 暂时简化处理
        return True
    
    def get_performance_report(self) -> str:
        """生成性能报告"""
        if not self.metrics:
            return "无性能数据"
        
        report = f"""
📊 策略回测性能报告
{'='*50}
💰 初始资金: {self.initial_capital:,.2f}
💎 最终价值: {self.metrics['final_value']:,.2f}
📈 总收益: {self.metrics['profit_loss']:,.2f} ({self.metrics['total_return']:.2%})
📅 年化收益率: {self.metrics['annual_return']:.2%}
📊 波动率: {self.metrics['volatility']:.2%}
⚡ 夏普比率: {self.metrics['sharpe_ratio']:.3f}
📉 最大回撤: {self.metrics['max_drawdown']:.2%}
🎯 总交易次数: {self.metrics['total_trades']}
✅ 盈利交易: {self.metrics['winning_trades']}
🏆 胜率: {self.metrics['win_rate']:.2%}
"""
        return report
    
    def save_results(self, filename: str):
        """保存回测结果"""
        try:
            results = {
                'metrics': self.metrics,
                'trades': self.trades,
                'equity_curve': self.equity_curve,
                'initial_capital': self.initial_capital,
                'commission': self.commission
            }
            
            # 转换datetime为字符串
            for trade in results['trades']:
                if isinstance(trade['timestamp'], datetime):
                    trade['timestamp'] = trade['timestamp'].isoformat()
            
            for point in results['equity_curve']:
                if isinstance(point['timestamp'], datetime):
                    point['timestamp'] = point['timestamp'].isoformat()
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 回测结果已保存: {filename}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


class StrategyLibrary:
    """策略库"""
    
    @staticmethod
    def moving_average_strategy(engine: BacktestEngine, timestamp: datetime, prices: Dict):
        """移动平均策略"""
        try:
            symbol = 'BTC_USDT'
            if symbol not in prices:
                return
            
            # 获取历史价格计算移动平均
            symbol_data = engine.price_data[symbol]
            current_idx = symbol_data['timestamp'].sub(timestamp).abs().idxmin()
            
            if current_idx < 20:  # 需要足够的历史数据
                return
            
            # 计算短期和长期移动平均
            short_ma = symbol_data.loc[current_idx-9:current_idx, 'close'].mean()  # 10日均线
            long_ma = symbol_data.loc[current_idx-19:current_idx, 'close'].mean()   # 20日均线
            
            current_price = prices[symbol]
            position = engine.positions.get(symbol, 0)
            
            # 金叉买入，死叉卖出
            if short_ma > long_ma and position == 0:
                # 买入信号
                buy_amount = engine.current_capital * 0.1 / current_price  # 10%资金
                engine.place_order(symbol, 'buy', buy_amount)
                
            elif short_ma < long_ma and position > 0:
                # 卖出信号
                engine.place_order(symbol, 'sell', position)
                
        except Exception as e:
            pass  # 静默处理异常
    
    @staticmethod
    def rsi_strategy(engine: BacktestEngine, timestamp: datetime, prices: Dict):
        """RSI策略"""
        try:
            symbol = 'BTC_USDT'
            if symbol not in prices:
                return
            
            symbol_data = engine.price_data[symbol]
            current_idx = symbol_data['timestamp'].sub(timestamp).abs().idxmin()
            
            if current_idx < 14:  # RSI需要14个数据点
                return
            
            # 计算RSI
            closes = symbol_data.loc[current_idx-13:current_idx, 'close']
            delta = closes.diff()
            gain = (delta.where(delta > 0, 0)).mean()
            loss = (-delta.where(delta < 0, 0)).mean()
            
            if loss == 0:
                rsi = 100
            else:
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
            
            position = engine.positions.get(symbol, 0)
            current_price = prices[symbol]
            
            # RSI超卖买入，超买卖出
            if rsi < 30 and position == 0:
                buy_amount = engine.current_capital * 0.2 / current_price  # 20%资金
                engine.place_order(symbol, 'buy', buy_amount)
                
            elif rsi > 70 and position > 0:
                engine.place_order(symbol, 'sell', position)
                
        except Exception as e:
            pass  # 静默处理异常
    
    @staticmethod
    def bollinger_bands_strategy(engine: BacktestEngine, timestamp: datetime, prices: Dict):
        """布林带策略"""
        try:
            symbol = 'BTC_USDT'
            if symbol not in prices:
                return
            
            symbol_data = engine.price_data[symbol]
            current_idx = symbol_data['timestamp'].sub(timestamp).abs().idxmin()
            
            if current_idx < 20:
                return
            
            # 计算布林带
            closes = symbol_data.loc[current_idx-19:current_idx, 'close']
            sma = closes.mean()
            std = closes.std()
            
            upper_band = sma + (std * 2)
            lower_band = sma - (std * 2)
            
            current_price = prices[symbol]
            position = engine.positions.get(symbol, 0)
            
            # 价格触及下轨买入，触及上轨卖出
            if current_price <= lower_band and position == 0:
                buy_amount = engine.current_capital * 0.15 / current_price  # 15%资金
                engine.place_order(symbol, 'buy', buy_amount)
                
            elif current_price >= upper_band and position > 0:
                engine.place_order(symbol, 'sell', position)
                
        except Exception as e:
            pass  # 静默处理异常

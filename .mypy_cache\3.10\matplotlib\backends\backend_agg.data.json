{".class": "MypyFile", "_fullname": "matplotlib.backends.backend_agg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bbox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Bbox", "kind": "Gdef"}, "BboxBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.BboxBase", "kind": "Gdef"}, "FigureCanvasAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.FigureCanvasBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg", "name": "FigureCanvasAgg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.backend_agg", "mro": ["matplotlib.backends.backend_agg.FigureCanvasAgg", "matplotlib.backend_bases.FigureCanvasBase", "builtins.object"], "names": {".class": "SymbolTable", "_lastKey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg._lastKey", "name": "_last<PERSON>ey", "type": {".class": "NoneType"}}}, "_print_pil": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "filename_or_obj", "fmt", "pil_kwargs", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg._print_pil", "name": "_print_pil", "type": null}}, "buffer_rgba": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.buffer_rgba", "name": "buffer_rgba", "type": null}}, "copy_from_bbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bbox"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.copy_from_bbox", "name": "copy_from_bbox", "type": null}}, "draw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.draw", "name": "draw", "type": null}}, "get_renderer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.get_renderer", "name": "get_renderer", "type": null}}, "print_jpeg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_jpeg", "name": "print_jpeg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "arg_types": ["matplotlib.backends.backend_agg.FigureCanvasAgg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_jpg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_jpg", "name": "print_jpg", "type": null}}, "print_png": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_png", "name": "print_png", "type": null}}, "print_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "filename_or_obj", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_raw", "name": "print_raw", "type": null}}, "print_rgba": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_rgba", "name": "print_rgba", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "filename_or_obj", "metadata"], "arg_types": ["matplotlib.backends.backend_agg.FigureCanvasAgg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_tif": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_tif", "name": "print_tif", "type": null}}, "print_tiff": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_tiff", "name": "print_tiff", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "arg_types": ["matplotlib.backends.backend_agg.FigureCanvasAgg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_to_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_to_buffer", "name": "print_to_buffer", "type": null}}, "print_webp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "filename_or_obj", "metadata", "pil_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.print_webp", "name": "print_webp", "type": null}}, "renderer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.renderer", "name": "renderer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "restore_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "region", "bbox", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.restore_region", "name": "restore_region", "type": null}}, "tostring_argb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.tostring_argb", "name": "tostring_argb", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.backend_agg.FigureCanvasAgg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.backend_agg.FigureCanvasAgg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FigureCanvasBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureCanvasBase", "kind": "Gdef"}, "FigureManagerBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureManagerBase", "kind": "Gdef"}, "LoadFlags": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ft2font.LoadFlags", "kind": "Gdef"}, "MathTextParser": {".class": "SymbolTableNode", "cross_ref": "matplotlib.mathtext.MathTextParser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef"}, "RendererAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.RendererBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.backend_agg.RendererAgg", "name": "RendererAgg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.backend_agg", "mro": ["matplotlib.backends.backend_agg.RendererAgg", "matplotlib.backend_bases.RendererBase", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "width", "height", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.__init__", "name": "__init__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.__setstate__", "name": "__setstate__", "type": null}}, "_filter_renderers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg._filter_renderers", "name": "_filter_renderers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_prepare_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "font_prop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg._prepare_font", "name": "_prepare_font", "type": null}}, "_renderer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg._renderer", "name": "_renderer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_update_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg._update_methods", "name": "_update_methods", "type": null}}, "bbox": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.bbox", "name": "bbox", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "buffer_rgba": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.buffer_rgba", "name": "buffer_rgba", "type": null}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.clear", "name": "clear", "type": null}}, "copy_from_bbox": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.copy_from_bbox", "name": "copy_from_bbox", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dpi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.dpi", "name": "dpi", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "draw_mathtext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.draw_mathtext", "name": "draw_mathtext", "type": null}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "gc", "path", "transform", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.draw_path", "name": "draw_path", "type": null}}, "draw_tex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "mtext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.draw_tex", "name": "draw_tex", "type": null}}, "draw_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "gc", "x", "y", "s", "prop", "angle", "<PERSON><PERSON><PERSON>", "mtext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.draw_text", "name": "draw_text", "type": null}}, "get_canvas_width_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.get_canvas_width_height", "name": "get_canvas_width_height", "type": null}}, "get_text_width_height_descent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "s", "prop", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.get_text_width_height_descent", "name": "get_text_width_height_descent", "type": null}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.height", "name": "height", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mathtext_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.mathtext_parser", "name": "mathtext_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "option_image_nocomposite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.option_image_nocomposite", "name": "option_image_nocomposite", "type": null}}, "option_scale_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.option_scale_image", "name": "option_scale_image", "type": null}}, "points_to_pixels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.points_to_pixels", "name": "points_to_pixels", "type": null}}, "restore_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "region", "bbox", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.restore_region", "name": "restore_region", "type": null}}, "start_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.start_filter", "name": "start_filter", "type": null}}, "stop_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "post_processing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.stop_filter", "name": "stop_filter", "type": null}}, "tostring_argb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.RendererAgg.tostring_argb", "name": "tostring_argb", "type": null}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.backends.backend_agg.RendererAgg.width", "name": "width", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.backend_agg.RendererAgg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.backend_agg.RendererAgg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.RendererBase", "kind": "Gdef"}, "_Backend": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases._Backend", "kind": "Gdef"}, "_BackendAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases._Backend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.backend_agg._BackendAgg", "name": "_BackendAgg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg._BackendAgg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.backend_agg", "mro": ["matplotlib.backends.backend_agg._BackendAgg", "matplotlib.backend_bases._Backend", "builtins.object"], "names": {".class": "SymbolTable", "FigureCanvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg._BackendAgg.FigureCanvas", "name": "FigureCanvas", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["figure"], "arg_types": [{".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": ["matplotlib.backends.backend_agg.FigureCanvasAgg"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib.backends.backend_agg.FigureCanvasAgg", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FigureManager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg._BackendAgg.FigureManager", "name": "FigureManager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["canvas", "num"], "arg_types": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": ["matplotlib.backend_bases.FigureManagerBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib.backend_bases.FigureManagerBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "backend_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_agg._BackendAgg.backend_version", "name": "backend_version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.backend_agg._BackendAgg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.backend_agg._BackendAgg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendererAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.backends.backend_agg._RendererAgg", "name": "_RendererAgg", "type": {".class": "AnyType", "missing_import_name": "matplotlib.backends.backend_agg._RendererAgg", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_agg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_api": {".class": "SymbolTableNode", "cross_ref": "matplotlib._api", "kind": "Gdef"}, "_fontManager": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.fontManager", "kind": "Gdef"}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef"}, "cos": {".class": "SymbolTableNode", "cross_ref": "math.cos", "kind": "Gdef"}, "get_font": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.get_font", "kind": "Gdef"}, "get_hinting_flag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_agg.get_hinting_flag", "name": "get_hinting_flag", "type": null}}, "mpl": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef"}, "radians": {".class": "SymbolTableNode", "cross_ref": "math.radians", "kind": "Gdef"}, "sin": {".class": "SymbolTableNode", "cross_ref": "math.sin", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一日志配置系统
Unified Logging Configuration System

提供统一的日志配置、格式化和管理
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # 颜色代码
    COLORS = {
        "DEBUG": "\033[36m",  # 青色
        "INFO": "\033[32m",  # 绿色
        "WARNING": "\033[33m",  # 黄色
        "ERROR": "\033[31m",  # 红色
        "CRITICAL": "\033[35m",  # 紫色
        "RESET": "\033[0m",  # 重置
    }

    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"

        return super().format(record)


class TradingLogFilter(logging.Filter):
    """交易系统日志过滤器"""

    def __init__(self, component: str = None):
        super().__init__()
        self.component = component

    def filter(self, record):
        # 添加组件信息
        if self.component:
            record.component = self.component

        # 过滤敏感信息
        if hasattr(record, "msg"):
            msg = str(record.msg)
            # 隐藏API密钥
            if "api_key" in msg.lower() or "secret" in msg.lower():
                record.msg = self._mask_sensitive_info(msg)

        return True

    def _mask_sensitive_info(self, message: str) -> str:
        """掩码敏感信息"""
        import re

        # 掩码API密钥模式
        patterns = [
            (
                r'api_key["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{8})[a-zA-Z0-9]*',
                r"api_key=\1****",
            ),
            (
                r'secret["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{8})[a-zA-Z0-9]*',
                r"secret=\1****",
            ),
            (
                r'password["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{4})[a-zA-Z0-9]*',
                r"password=\1****",
            ),
        ]

        for pattern, replacement in patterns:
            message = re.sub(
                pattern, replacement, message, flags=re.IGNORECASE
            )

        return message


class LoggingManager:
    """日志管理器"""

    def __init__(self):
        self.loggers = {}
        self.handlers = {}
        self.configured = False

    def setup_logging(
        self,
        log_level: str = "INFO",
        log_dir: str = "logs",
        log_file: str = "trading.log",
        max_file_size: int = 10485760,  # 10MB
        backup_count: int = 5,
        console_output: bool = True,
        colored_output: bool = True,
    ) -> None:
        """
        设置日志系统

        Args:
            log_level: 日志级别
            log_dir: 日志目录
            log_file: 日志文件名
            max_file_size: 最大文件大小
            backup_count: 备份文件数量
            console_output: 是否输出到控制台
            colored_output: 是否使用彩色输出
        """
        if self.configured:
            return

        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)

        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))

        # 清除现有处理器
        root_logger.handlers.clear()

        # 创建格式化器
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        simple_formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s", datefmt="%H:%M:%S"
        )

        # 文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_path / log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8",
        )
        file_handler.setFormatter(detailed_formatter)
        file_handler.addFilter(TradingLogFilter())
        root_logger.addHandler(file_handler)
        self.handlers["file"] = file_handler

        # 控制台处理器
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)

            if colored_output and sys.stdout.isatty():
                console_formatter = ColoredFormatter(
                    "%(asctime)s - %(levelname)s - %(message)s",
                    datefmt="%H:%M:%S",
                )
            else:
                console_formatter = simple_formatter

            console_handler.setFormatter(console_formatter)
            console_handler.addFilter(TradingLogFilter())
            root_logger.addHandler(console_handler)
            self.handlers["console"] = console_handler

        # 错误文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            log_path / "error.log",
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        error_handler.addFilter(TradingLogFilter())
        root_logger.addHandler(error_handler)
        self.handlers["error"] = error_handler

        # 交易日志处理器
        trading_handler = logging.handlers.RotatingFileHandler(
            log_path / "trading.log",
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8",
        )
        trading_handler.addFilter(TradingLogFilter("trading"))
        trading_handler.setFormatter(detailed_formatter)
        self.handlers["trading"] = trading_handler

        # 设置特定日志器
        self._setup_component_loggers()

        self.configured = True
        logging.info("日志系统初始化完成")

    def _setup_component_loggers(self):
        """设置组件特定的日志器"""
        # 交易日志器
        trading_logger = logging.getLogger("trading")
        trading_logger.addHandler(self.handlers["trading"])
        trading_logger.propagate = False
        self.loggers["trading"] = trading_logger

        # API日志器
        api_logger = logging.getLogger("api")
        api_logger.addFilter(TradingLogFilter("api"))
        self.loggers["api"] = api_logger

        # 策略日志器
        strategy_logger = logging.getLogger("strategy")
        strategy_logger.addFilter(TradingLogFilter("strategy"))
        self.loggers["strategy"] = strategy_logger

        # 风险管理日志器
        risk_logger = logging.getLogger("risk")
        risk_logger.addFilter(TradingLogFilter("risk"))
        self.loggers["risk"] = risk_logger

        # 监控日志器
        monitor_logger = logging.getLogger("monitor")
        monitor_logger.addFilter(TradingLogFilter("monitor"))
        self.loggers["monitor"] = monitor_logger

    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name in self.loggers:
            return self.loggers[name]

        logger = logging.getLogger(name)
        logger.addFilter(TradingLogFilter(name))
        self.loggers[name] = logger
        return logger

    def set_level(self, level: str, component: str = None):
        """设置日志级别"""
        log_level = getattr(logging, level.upper())

        if component and component in self.loggers:
            self.loggers[component].setLevel(log_level)
        else:
            logging.getLogger().setLevel(log_level)

    def add_custom_handler(self, name: str, handler: logging.Handler):
        """添加自定义处理器"""
        handler.addFilter(TradingLogFilter())
        logging.getLogger().addHandler(handler)
        self.handlers[name] = handler

    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            "configured": self.configured,
            "handlers_count": len(self.handlers),
            "loggers_count": len(self.loggers),
            "handlers": list(self.handlers.keys()),
            "loggers": list(self.loggers.keys()),
        }

        # 获取日志文件大小
        for name, handler in self.handlers.items():
            if hasattr(handler, "baseFilename"):
                try:
                    file_size = os.path.getsize(handler.baseFilename)
                    stats[f"{name}_file_size"] = file_size
                except (OSError, AttributeError):
                    pass

        return stats


# 全局日志管理器
_logging_manager = LoggingManager()


def setup_logging(**kwargs):
    """设置日志系统"""
    _logging_manager.setup_logging(**kwargs)


def get_logger(name: str = None) -> logging.Logger:
    """获取日志器"""
    if name:
        return _logging_manager.get_logger(name)
    return logging.getLogger()


def get_trading_logger() -> logging.Logger:
    """获取交易日志器"""
    return _logging_manager.get_logger("trading")


def get_api_logger() -> logging.Logger:
    """获取API日志器"""
    return _logging_manager.get_logger("api")


def get_strategy_logger() -> logging.Logger:
    """获取策略日志器"""
    return _logging_manager.get_logger("strategy")


def get_risk_logger() -> logging.Logger:
    """获取风险管理日志器"""
    return _logging_manager.get_logger("risk")


def get_monitor_logger() -> logging.Logger:
    """获取监控日志器"""
    return _logging_manager.get_logger("monitor")


# 初始化日志系统
def init_logging():
    """初始化日志系统"""
    setup_logging(
        log_level="INFO",
        log_dir="logs",
        console_output=True,
        colored_output=True,
    )

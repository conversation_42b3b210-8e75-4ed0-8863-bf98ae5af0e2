{".class": "MypyFile", "_fullname": "matplotlib.contour", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Artist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist.Artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.Collection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Colorizer": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colorizer.Colorizer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorizingArtist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colorizer.ColorizingArtist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Colormap": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Colormap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ContourLabeler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.contour.ContourLabeler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.contour", "mro": ["matplotlib.contour.ContourLabeler", "builtins.object"], "names": {".class": "SymbolTable", "add_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "rotation", "lev", "cvalue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.add_label", "name": "add_label", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "rotation", "lev", "cvalue"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_label of ContourLabeler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_label_near": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "x", "y", "inline", "inline_spacing", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.add_label_near", "name": "add_label_near", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "x", "y", "inline", "inline_spacing", "transform"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.float", "builtins.float", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_label_near of ContourLabeler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clabel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "levels", "fontsize", "inline", "inline_spacing", "fmt", "colors", "use_clabeltext", "manual", "rightside_up", "zorder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.clabel", "name": "clabel", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "levels", "fontsize", "inline", "inline_spacing", "fmt", "colors", "use_clabeltext", "manual", "rightside_up", "zorder"], "arg_types": ["matplotlib.contour.ContourLabeler", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.str", "matplotlib.ticker.Formatter", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.float", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clabel of ContourLabeler", "ret_type": {".class": "Instance", "args": ["matplotlib.text.Text"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lev", "fmt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.get_text", "name": "get_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lev", "fmt"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.float", {".class": "UnionType", "items": ["builtins.str", "matplotlib.ticker.Formatter", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.float", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text of ContourLabe<PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "labelCValueList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelCValueList", "name": "labelCValueList", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "labelFmt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelFmt", "name": "labelFmt", "type": {".class": "UnionType", "items": ["builtins.str", "matplotlib.ticker.Formatter", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.float", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}}}, "labelIndiceList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelIndiceList", "name": "labelIndiceList", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "labelLevelList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelLevelList", "name": "labelLevelList", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "labelManual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelManual", "name": "labelManual", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}}}, "labelMappable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelMappable", "name": "labelMappable", "type": {".class": "UnionType", "items": ["matplotlib.colorizer._ScalarMappable", "matplotlib.colorizer.ColorizingArtist"], "uses_pep604_syntax": true}}}, "labelXYs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.labelXYs", "name": "labelXYs", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inline", "inline_spacing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.labels", "name": "labels", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inline", "inline_spacing"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "labels of ContourLabeler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "locate_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "linecontour", "labelwidth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.locate_label", "name": "locate_label", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "linecontour", "labelwidth"], "arg_types": ["matplotlib.contour.ContourLabeler", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "locate_label of ContourLabeler", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.pop_label", "name": "pop_label", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "index"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_label of ContourLabeler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "linecontour", "labelwidth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.print_label", "name": "print_label", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "linecontour", "labelwidth"], "arg_types": ["matplotlib.contour.ContourLabeler", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_label of ContourLabeler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourLabeler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rightside_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourLabeler.rightside_up", "name": "rightside_up", "type": "builtins.bool"}}, "too_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "lw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourLabeler.too_close", "name": "too_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "lw"], "arg_types": ["matplotlib.contour.ContourLabeler", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "too_close of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.contour.ContourLabeler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.contour.ContourLabeler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContourSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.contour.ContourLabeler", "matplotlib.collections.Collection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.contour.ContourSet", "name": "ContourSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.contour", "mro": ["matplotlib.contour.ContourSet", "matplotlib.contour.ContourLabeler", "matplotlib.collections.Collection", "matplotlib.colorizer.ColorizingArtist", "matplotlib.colorizer._ScalarMappable", "matplotlib.colorizer._ColorizerInterface", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "ax", "args", "levels", "filled", "linewidths", "linestyles", "hatches", "alpha", "origin", "extent", "cmap", "colors", "norm", "vmin", "vmax", "colorizer", "extend", "antialiased", "nchunk", "locator", "transform", "negative_linestyles", "clip_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "ax", "args", "levels", "filled", "linewidths", "linestyles", "hatches", "alpha", "origin", "extent", "cmap", "colors", "norm", "vmin", "vmax", "colorizer", "extend", "antialiased", "nchunk", "locator", "transform", "negative_linestyles", "clip_path", "kwargs"], "arg_types": ["matplotlib.contour.ContourSet", "matplotlib.axes._axes.Axes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "upper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lower"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.colors.Colormap", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.colors.Normalize", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.colorizer.Colorizer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "neither"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["matplotlib.ticker.Locator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", "matplotlib.path.Path", "matplotlib.transforms.TransformedPath", "matplotlib.transforms.TransformedPatchPath", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ContourSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allkinds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.contour.ContourSet.allkinds", "name": "allkinds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allkinds of ContourSet", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.contour.ContourSet.allkinds", "name": "allkinds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allkinds of ContourSet", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "allsegs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.contour.ContourSet.allsegs", "name": "allsegs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allsegs of ContourSet", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.contour.ContourSet.allsegs", "name": "allsegs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allsegs of ContourSet", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.contour.ContourSet.alpha", "name": "alpha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alpha of ContourSet", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.contour.ContourSet.alpha", "name": "alpha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alpha of ContourSet", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.axes", "name": "axes", "type": "matplotlib.axes._axes.Axes"}}, "clip_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.clip_path", "name": "clip_path", "type": {".class": "UnionType", "items": ["matplotlib.patches.Patch", "matplotlib.path.Path", "matplotlib.transforms.TransformedPath", "matplotlib.transforms.TransformedPatchPath", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "colors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.colors", "name": "colors", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}}}, "extend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.extend", "name": "extend", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "neither"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}], "uses_pep604_syntax": false}}}, "extent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.extent", "name": "extent", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "filled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.filled", "name": "filled", "type": "builtins.bool"}}, "find_nearest_contour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "x", "y", "indices", "pixel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourSet.find_nearest_contour", "name": "find_nearest_contour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "x", "y", "indices", "pixel"], "arg_types": ["matplotlib.contour.ContourSet", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_nearest_contour of ContourSet", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hatches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.hatches", "name": "hatches", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "labelCValues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.labelCValues", "name": "labelCValues", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "labelTexts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.labelTexts", "name": "labelTexts", "type": {".class": "Instance", "args": ["matplotlib.text.Text"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "legend_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "variable_name", "str_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.contour.ContourSet.legend_elements", "name": "legend_elements", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "variable_name", "str_format"], "arg_types": ["matplotlib.contour.ContourSet", "builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "legend_elements of ContourSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "levels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.levels", "name": "levels", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "linestyles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.contour.ContourSet.linestyles", "name": "linestyles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linestyles of ContourSet", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.contour.ContourSet.linestyles", "name": "linestyles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.contour.ContourSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linestyles of ContourSet", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "linewidths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.linewidths", "name": "linewidths", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "locator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.locator", "name": "locator", "type": {".class": "UnionType", "items": ["matplotlib.ticker.Locator", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "logscale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.logscale", "name": "logscale", "type": "builtins.bool"}}, "nchunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.nchunk", "name": "nchunk", "type": "builtins.int"}}, "negative_linestyles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.negative_linestyles", "name": "negative_linestyles", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dashdot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dotted"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}}}, "origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.contour.ContourSet.origin", "name": "origin", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "upper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lower"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.contour.ContourSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.contour.ContourSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Formatter": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker.Formatter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Locator": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker.Locator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Normalize": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Normalize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Patch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.Patch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PathCollection": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.PathCollection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QuadContourSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.contour.ContourSet"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.contour.QuadContourSet", "name": "QuadContourSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.contour.QuadContourSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.contour", "mro": ["matplotlib.contour.QuadContourSet", "matplotlib.contour.ContourSet", "matplotlib.contour.ContourLabeler", "matplotlib.collections.Collection", "matplotlib.colorizer.ColorizingArtist", "matplotlib.colorizer._ScalarMappable", "matplotlib.colorizer._ColorizerInterface", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.contour.QuadContourSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.contour.QuadContourSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Text": {".class": "SymbolTableNode", "cross_ref": "matplotlib.text.Text", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TransformedPatchPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.TransformedPatchPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TransformedPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.TransformedPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.contour.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cm": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\contour.pyi"}
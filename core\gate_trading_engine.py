#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gate.io交易引擎
Gate.io Trading Engine for Real Order Execution
"""

import time
import json
import hmac
import hashlib
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from gate_io_client import GateIOClient


class GateIOTradingEngine:
    """Gate.io交易引擎 - 真实订单执行"""
    
    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True, demo_mode: bool = True):
        """
        初始化交易引擎
        
        Args:
            api_key: Gate.io API密钥
            api_secret: Gate.io API密钥
            testnet: 是否使用测试网络
            demo_mode: 演示模式（不发送真实订单）
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.demo_mode = demo_mode
        
        # 初始化API客户端
        self.client = GateIOClient(api_key, api_secret, testnet)
        
        # 订单管理
        self.active_orders = {}  # 活跃订单
        self.order_history = []  # 订单历史
        self.trade_history = []  # 成交历史
        
        # 账户信息
        self.account_balance = {}
        self.positions = {}
        
        # 风险控制
        self.daily_trade_count = 0
        self.daily_trade_volume = 0.0
        self.last_trade_time = 0
        
        # 配置参数
        self.config = {
            'max_daily_trades': 100,
            'max_daily_volume': 10000.0,
            'min_order_interval': 1.0,  # 最小订单间隔(秒)
            'max_order_size': 1000.0,   # 最大单笔订单金额
            'slippage_tolerance': 0.005, # 滑点容忍度
        }
    
    def set_demo_mode(self, demo_mode: bool):
        """设置演示模式"""
        self.demo_mode = demo_mode
        print(f"🎭 交易模式: {'演示模式' if demo_mode else '真实交易模式'}")
    
    def get_account_balance(self) -> Dict:
        """获取账户余额"""
        try:
            if self.demo_mode:
                # 演示模式返回模拟余额
                return {
                    'USDT': {'available': 1000.0, 'locked': 0.0},
                    'BTC': {'available': 0.0, 'locked': 0.0},
                    'ETH': {'available': 0.0, 'locked': 0.0}
                }
            
            # 真实API调用
            response = self.client._request("GET", "/spot/accounts")
            
            if 'error' in response:
                print(f"❌ 获取余额失败: {response['error']}")
                return {}
            
            # 解析余额数据
            balance = {}
            for account in response:
                currency = account.get('currency', '')
                available = float(account.get('available', 0))
                locked = float(account.get('locked', 0))
                
                if available > 0 or locked > 0:
                    balance[currency] = {
                        'available': available,
                        'locked': locked
                    }
            
            self.account_balance = balance
            return balance
            
        except Exception as e:
            print(f"❌ 获取账户余额异常: {e}")
            return {}
    
    def place_order(self, symbol: str, side: str, amount: float, price: float = None, order_type: str = "market") -> Dict:
        """
        下单
        
        Args:
            symbol: 交易对 (如 BTC_USDT)
            side: 买卖方向 (buy/sell)
            amount: 数量或金额
            price: 价格 (限价单需要)
            order_type: 订单类型 (market/limit)
        
        Returns:
            订单结果
        """
        try:
            # 风险检查
            risk_check = self._risk_check(symbol, side, amount, price)
            if not risk_check['allowed']:
                return {'success': False, 'error': risk_check['reason']}
            
            # 构建订单参数
            order_params = {
                'currency_pair': symbol,
                'side': side,
                'type': order_type
            }
            
            if order_type == "market":
                if side == "buy":
                    order_params['amount'] = str(amount)  # 买入金额(USDT)
                else:
                    order_params['amount'] = str(amount)  # 卖出数量
            else:  # limit order
                order_params['amount'] = str(amount)
                order_params['price'] = str(price)
            
            # 演示模式
            if self.demo_mode:
                return self._simulate_order(symbol, side, amount, price, order_type)
            
            # 真实下单
            response = self.client._request("POST", "/spot/orders", data=order_params)
            
            if 'error' in response:
                return {'success': False, 'error': response['error']}
            
            # 处理订单响应
            order_result = self._process_order_response(response)
            
            # 更新统计
            self._update_trade_stats(amount)
            
            return order_result
            
        except Exception as e:
            print(f"❌ 下单异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _simulate_order(self, symbol: str, side: str, amount: float, price: float, order_type: str) -> Dict:
        """模拟订单执行"""
        try:
            # 获取当前市场价格
            ticker = self.client.get_tickers(symbol)
            if not ticker or len(ticker) == 0:
                return {'success': False, 'error': '无法获取市场价格'}
            
            current_price = float(ticker[0].get('last', 0))
            if current_price <= 0:
                return {'success': False, 'error': '无效的市场价格'}
            
            # 计算执行价格
            if order_type == "market":
                exec_price = current_price
                # 模拟滑点
                if side == "buy":
                    exec_price *= (1 + self.config['slippage_tolerance'])
                else:
                    exec_price *= (1 - self.config['slippage_tolerance'])
            else:
                exec_price = price
            
            # 计算数量和金额
            if side == "buy":
                if order_type == "market":
                    exec_amount = amount / exec_price  # 买入数量
                    exec_value = amount  # 买入金额
                else:
                    exec_amount = amount
                    exec_value = amount * exec_price
            else:
                exec_amount = amount
                exec_value = amount * exec_price
            
            # 生成模拟订单ID
            order_id = f"demo_{int(time.time() * 1000)}"
            
            # 创建订单记录
            order_record = {
                'id': order_id,
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'amount': exec_amount,
                'price': exec_price,
                'value': exec_value,
                'status': 'filled',
                'create_time': datetime.now(),
                'fill_time': datetime.now(),
                'fee': exec_value * 0.0015,  # Gate.io手续费
                'demo': True
            }
            
            # 添加到历史记录
            self.order_history.append(order_record)
            self.trade_history.append(order_record)
            
            print(f"🎭 模拟订单执行: {side} {exec_amount:.6f} {symbol.split('_')[0]} @ {exec_price:.2f}")
            
            return {
                'success': True,
                'order_id': order_id,
                'symbol': symbol,
                'side': side,
                'amount': exec_amount,
                'price': exec_price,
                'value': exec_value,
                'status': 'filled',
                'demo': True
            }
            
        except Exception as e:
            return {'success': False, 'error': f'模拟订单失败: {e}'}
    
    def _risk_check(self, symbol: str, side: str, amount: float, price: float) -> Dict:
        """风险检查"""
        try:
            # 检查交易频率
            current_time = time.time()
            if current_time - self.last_trade_time < self.config['min_order_interval']:
                return {'allowed': False, 'reason': '交易频率过高'}
            
            # 检查日交易次数
            if self.daily_trade_count >= self.config['max_daily_trades']:
                return {'allowed': False, 'reason': '日交易次数超限'}
            
            # 检查订单金额
            order_value = amount if side == "buy" else amount * (price or 0)
            if order_value > self.config['max_order_size']:
                return {'allowed': False, 'reason': f'订单金额超限: {order_value:.2f} > {self.config["max_order_size"]}'}
            
            # 检查日交易量
            if self.daily_trade_volume + order_value > self.config['max_daily_volume']:
                return {'allowed': False, 'reason': '日交易量超限'}
            
            # 检查余额（演示模式跳过）
            if not self.demo_mode:
                balance = self.get_account_balance()
                if side == "buy":
                    usdt_available = balance.get('USDT', {}).get('available', 0)
                    if order_value > usdt_available:
                        return {'allowed': False, 'reason': f'USDT余额不足: {usdt_available:.2f} < {order_value:.2f}'}
                else:
                    base_currency = symbol.split('_')[0]
                    base_available = balance.get(base_currency, {}).get('available', 0)
                    if amount > base_available:
                        return {'allowed': False, 'reason': f'{base_currency}余额不足: {base_available:.6f} < {amount:.6f}'}
            
            return {'allowed': True, 'reason': '风险检查通过'}
            
        except Exception as e:
            return {'allowed': False, 'reason': f'风险检查异常: {e}'}
    
    def _process_order_response(self, response: Dict) -> Dict:
        """处理订单响应"""
        try:
            order_id = response.get('id', '')
            status = response.get('status', '')
            
            order_result = {
                'success': True,
                'order_id': order_id,
                'symbol': response.get('currency_pair', ''),
                'side': response.get('side', ''),
                'amount': float(response.get('amount', 0)),
                'price': float(response.get('price', 0)),
                'status': status,
                'create_time': datetime.now(),
                'demo': False
            }
            
            # 添加到活跃订单
            if status in ['open', 'partial']:
                self.active_orders[order_id] = order_result
            
            # 添加到历史记录
            self.order_history.append(order_result)
            
            return order_result
            
        except Exception as e:
            return {'success': False, 'error': f'处理订单响应失败: {e}'}
    
    def _update_trade_stats(self, amount: float):
        """更新交易统计"""
        self.daily_trade_count += 1
        self.daily_trade_volume += amount
        self.last_trade_time = time.time()
    
    def get_order_status(self, order_id: str) -> Dict:
        """查询订单状态"""
        try:
            if self.demo_mode:
                # 演示模式查找本地记录
                for order in self.order_history:
                    if order['id'] == order_id:
                        return {'success': True, 'order': order}
                return {'success': False, 'error': '订单不存在'}
            
            # 真实API查询
            response = self.client._request("GET", f"/spot/orders/{order_id}")
            
            if 'error' in response:
                return {'success': False, 'error': response['error']}
            
            return {'success': True, 'order': response}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def cancel_order(self, order_id: str) -> Dict:
        """取消订单"""
        try:
            if self.demo_mode:
                # 演示模式模拟取消
                if order_id in self.active_orders:
                    self.active_orders[order_id]['status'] = 'cancelled'
                    del self.active_orders[order_id]
                    return {'success': True, 'message': '模拟订单已取消'}
                return {'success': False, 'error': '订单不存在或已完成'}
            
            # 真实API取消
            response = self.client._request("DELETE", f"/spot/orders/{order_id}")
            
            if 'error' in response:
                return {'success': False, 'error': response['error']}
            
            # 从活跃订单中移除
            if order_id in self.active_orders:
                del self.active_orders[order_id]
            
            return {'success': True, 'order': response}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_trade_history(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """获取交易历史"""
        try:
            if symbol:
                return [trade for trade in self.trade_history if trade.get('symbol') == symbol][-limit:]
            return self.trade_history[-limit:]
            
        except Exception as e:
            print(f"❌ 获取交易历史失败: {e}")
            return []
    
    def get_daily_stats(self) -> Dict:
        """获取日交易统计"""
        return {
            'trade_count': self.daily_trade_count,
            'trade_volume': self.daily_trade_volume,
            'max_trades': self.config['max_daily_trades'],
            'max_volume': self.config['max_daily_volume'],
            'remaining_trades': self.config['max_daily_trades'] - self.daily_trade_count,
            'remaining_volume': self.config['max_daily_volume'] - self.daily_trade_volume
        }
    
    def reset_daily_stats(self):
        """重置日统计（新的一天）"""
        self.daily_trade_count = 0
        self.daily_trade_volume = 0.0
        print("📊 日交易统计已重置")


class SmartOrderRouter:
    """智能订单路由器"""
    
    def __init__(self, trading_engine: GateIOTradingEngine):
        """初始化订单路由器"""
        self.engine = trading_engine
        
    def execute_strategy_order(self, strategy_name: str, symbol: str, side: str, amount: float, 
                             order_type: str = "market", price: float = None) -> Dict:
        """执行策略订单"""
        try:
            print(f"🎯 {strategy_name}策略订单: {side} {amount:.6f} {symbol}")
            
            # 执行订单
            result = self.engine.place_order(symbol, side, amount, price, order_type)
            
            if result['success']:
                print(f"✅ 订单执行成功: {result['order_id']}")
            else:
                print(f"❌ 订单执行失败: {result['error']}")
            
            return result
            
        except Exception as e:
            print(f"❌ 策略订单执行异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def execute_batch_orders(self, orders: List[Dict]) -> List[Dict]:
        """批量执行订单"""
        results = []
        
        for order in orders:
            try:
                result = self.engine.place_order(
                    order['symbol'],
                    order['side'],
                    order['amount'],
                    order.get('price'),
                    order.get('type', 'market')
                )
                results.append(result)
                
                # 订单间隔
                time.sleep(self.engine.config['min_order_interval'])
                
            except Exception as e:
                results.append({'success': False, 'error': str(e)})
        
        return results

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI诊断工具
GUI Diagnostic Tool

快速诊断和解决GUI卡顿问题
"""

import os
import sys
import time
from datetime import datetime

import psutil


def check_system_resources():
    """检查系统资源"""
    print("🔍 系统资源检查")
    print("=" * 50)

    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU使用率: {cpu_percent:.1f}%")

    # 内存使用
    memory = psutil.virtual_memory()
    print(
        f"内存使用: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)"
    )

    # 磁盘使用
    disk = psutil.disk_usage("/")
    print(f"磁盘使用: {disk.percent:.1f}%")

    # 网络状态
    try:
        import requests

        response = requests.get(
            "https://api.gateio.ws/api/v4/spot/currencies", timeout=5
        )
        network_status = (
            "✅ 正常" if response.status_code == 200 else "❌ 异常"
        )
    except:
        network_status = "❌ 异常"

    print(f"网络连接: {network_status}")
    print()


def check_python_environment():
    """检查Python环境"""
    print("🐍 Python环境检查")
    print("=" * 50)

    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

    # 检查关键模块
    modules = ["tkinter", "threading", "queue", "json", "time"]
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}: 已安装")
        except ImportError:
            print(f"❌ {module}: 未安装")

    print()


def check_gui_processes():
    """检查GUI进程"""
    print("🖥️ GUI进程检查")
    print("=" * 50)

    gui_processes = []
    for proc in psutil.process_iter(
        ["pid", "name", "cpu_percent", "memory_percent"]
    ):
        try:
            if "python" in proc.info["name"].lower():
                cmdline = " ".join(proc.cmdline())
                if "gui" in cmdline.lower() or "trading" in cmdline.lower():
                    gui_processes.append(proc.info)
        except:
            continue

    if gui_processes:
        print("发现GUI进程:")
        for proc in gui_processes:
            print(
                f"  PID: {proc['pid']}, CPU: {proc['cpu_percent']:.1f}%, 内存: {proc['memory_percent']:.1f}%"
            )
    else:
        print("未发现GUI进程")

    print()


def performance_test():
    """性能测试"""
    print("⚡ 性能测试")
    print("=" * 50)

    # 测试文件I/O
    start_time = time.time()
    test_file = "performance_test.tmp"

    try:
        with open(test_file, "w") as f:
            for i in range(1000):
                f.write(f"test line {i}\n")

        with open(test_file, "r") as f:
            lines = f.readlines()

        os.remove(test_file)

        io_time = time.time() - start_time
        print(
            f"文件I/O测试: {io_time:.3f}秒 {'✅ 正常' if io_time < 1 else '⚠️ 较慢'}"
        )

    except Exception as e:
        print(f"文件I/O测试: ❌ 失败 ({e})")

    # 测试计算性能
    start_time = time.time()
    result = sum(i * i for i in range(100000))
    calc_time = time.time() - start_time
    print(
        f"计算性能测试: {calc_time:.3f}秒 {'✅ 正常' if calc_time < 0.1 else '⚠️ 较慢'}"
    )

    print()


def diagnose_gui_issues():
    """诊断GUI问题"""
    print("🔧 GUI问题诊断")
    print("=" * 50)

    issues = []
    recommendations = []

    # 检查内存
    memory = psutil.virtual_memory()
    if memory.percent > 80:
        issues.append("内存使用率过高")
        recommendations.append("关闭不必要的程序释放内存")

    # 检查CPU
    cpu_percent = psutil.cpu_percent(interval=1)
    if cpu_percent > 80:
        issues.append("CPU使用率过高")
        recommendations.append("关闭高CPU占用的程序")

    # 检查Python版本
    if sys.version_info < (3, 8):
        issues.append("Python版本过低")
        recommendations.append("升级到Python 3.8或更高版本")

    # 检查GUI进程数量
    gui_count = 0
    for proc in psutil.process_iter(["name"]):
        try:
            if "python" in proc.info["name"].lower():
                cmdline = " ".join(proc.cmdline())
                if "gui" in cmdline.lower():
                    gui_count += 1
        except:
            continue

    if gui_count > 1:
        issues.append("多个GUI进程同时运行")
        recommendations.append("关闭多余的GUI进程")

    if issues:
        print("发现问题:")
        for issue in issues:
            print(f"  ❌ {issue}")

        print("\n建议解决方案:")
        for rec in recommendations:
            print(f"  💡 {rec}")
    else:
        print("✅ 未发现明显问题")

    print()


def generate_report():
    """生成诊断报告"""
    print("📋 生成诊断报告")
    print("=" * 50)

    report_file = (
        f"gui_diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    )

    with open(report_file, "w", encoding="utf-8") as f:
        f.write("GUI诊断报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now()}\n\n")

        # 系统信息
        f.write("系统信息:\n")
        f.write(f"  操作系统: {os.name}\n")
        f.write(f"  Python版本: {sys.version}\n")

        # 资源使用
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent()
        f.write(f"  CPU使用率: {cpu_percent:.1f}%\n")
        f.write(f"  内存使用率: {memory.percent:.1f}%\n")

        # 建议
        f.write("\n优化建议:\n")
        f.write("  1. 使用优化版GUI (optimized_trading_gui.py)\n")
        f.write("  2. 定期重启GUI程序\n")
        f.write("  3. 关闭不必要的后台程序\n")
        f.write("  4. 确保网络连接稳定\n")

    print(f"✅ 诊断报告已保存: {report_file}")


def main():
    """主函数"""
    print("🔧 GUI诊断工具")
    print("=" * 50)
    print("正在诊断GUI卡顿问题...")
    print()

    # 执行各项检查
    check_system_resources()
    check_python_environment()
    check_gui_processes()
    performance_test()
    diagnose_gui_issues()

    # 提供解决方案
    print("🚀 解决方案建议")
    print("=" * 50)
    print("1. 使用优化版GUI:")
    print("   python optimized_trading_gui.py")
    print()
    print("2. 如果仍有问题，尝试:")
    print("   - 重启计算机")
    print("   - 关闭其他程序")
    print("   - 检查网络连接")
    print("   - 更新Python版本")
    print()
    print("3. 紧急情况下:")
    print("   - 使用任务管理器结束Python进程")
    print("   - 重新启动GUI程序")
    print()

    # 生成报告
    generate_report()

    print("🎉 诊断完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main()

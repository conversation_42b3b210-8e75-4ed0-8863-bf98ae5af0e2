#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现货滚雪球交易策略
Spot Trading Snowball Strategy

专门为现货交易设计的复利增长策略系统
正向学习，注重方向、盈亏比、胜率、信号强度、交易执行、止损止盈
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import talib

logger = logging.getLogger(__name__)


@dataclass
class SignalQuality:
    """信号质量评估"""

    strength: float  # 信号强度 (0-1)
    direction: str  # 方向 (buy/sell/hold)
    confidence: float  # 置信度 (0-1)
    risk_reward: float  # 盈亏比
    win_probability: float  # 胜率预估


@dataclass
class TradeSetup:
    """交易设置"""

    pair: str
    direction: str
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    risk_reward_ratio: float
    signal_quality: SignalQuality


class SpotSnowballStrategy:
    """
    现货滚雪球策略

    核心理念：
    1. 只做现货，不用杠杆
    2. 复利增长，滚雪球效应
    3. 严格风控，保护本金
    4. 高胜率，稳定盈利
    """

    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.name = "SpotSnowball"

        # 核心参数
        self.risk_per_trade = 0.02  # 每笔交易风险2%
        self.min_risk_reward = 2.0  # 最小盈亏比2:1
        self.min_win_rate = 0.6  # 最小胜率60%
        self.min_signal_strength = 0.7  # 最小信号强度70%
        self.max_positions = 3  # 最大同时持仓3个
        self.position_size_pct = 0.15  # 单仓位最大15%

        # 滚雪球参数
        self.compound_frequency = "weekly"  # 复利频率
        self.target_monthly_return = 0.08  # 目标月收益8%
        self.max_drawdown_limit = 0.10  # 最大回撤10%

        # 交易记录
        self.trades_history = []
        self.performance_metrics = {}

    def analyze_market_structure(self, df: pd.DataFrame) -> Dict:
        """分析市场结构"""
        # 趋势分析
        df["ema_20"] = talib.EMA(df["close"], timeperiod=20)
        df["ema_50"] = talib.EMA(df["close"], timeperiod=50)
        df["ema_200"] = talib.EMA(df["close"], timeperiod=200)

        # 趋势强度
        trend_strength = 0
        if (
            df["ema_20"].iloc[-1]
            > df["ema_50"].iloc[-1]
            > df["ema_200"].iloc[-1]
        ):
            trend_strength = 1  # 强势上涨
        elif df["ema_20"].iloc[-1] > df["ema_50"].iloc[-1]:
            trend_strength = 0.5  # 温和上涨
        elif (
            df["ema_20"].iloc[-1]
            < df["ema_50"].iloc[-1]
            < df["ema_200"].iloc[-1]
        ):
            trend_strength = -1  # 强势下跌
        elif df["ema_20"].iloc[-1] < df["ema_50"].iloc[-1]:
            trend_strength = -0.5  # 温和下跌

        # 波动率分析
        df["atr"] = talib.ATR(
            df["high"], df["low"], df["close"], timeperiod=14
        )
        volatility = df["atr"].iloc[-1] / df["close"].iloc[-1]

        # 成交量分析
        df["volume_sma"] = talib.SMA(df["volume"], timeperiod=20)
        volume_ratio = df["volume"].iloc[-1] / df["volume_sma"].iloc[-1]

        return {
            "trend_strength": trend_strength,
            "volatility": volatility,
            "volume_ratio": volume_ratio,
            "price_position": self._get_price_position(df),
        }

    def _get_price_position(self, df: pd.DataFrame) -> float:
        """获取价格在区间中的位置"""
        high_20 = df["high"].rolling(20).max().iloc[-1]
        low_20 = df["low"].rolling(20).min().iloc[-1]
        current_price = df["close"].iloc[-1]

        if high_20 == low_20:
            return 0.5

        return (current_price - low_20) / (high_20 - low_20)

    def calculate_signal_strength(self, df: pd.DataFrame) -> SignalQuality:
        """计算信号强度和质量"""
        market_structure = self.analyze_market_structure(df)

        # 技术指标
        df["rsi"] = talib.RSI(df["close"], timeperiod=14)
        df["macd"], df["macd_signal"], df["macd_hist"] = talib.MACD(
            df["close"]
        )
        df["bb_upper"], df["bb_middle"], df["bb_lower"] = talib.BBANDS(
            df["close"]
        )

        # 信号强度计算
        strength_factors = []

        # 1. 趋势一致性 (30%)
        trend_consistency = abs(market_structure["trend_strength"])
        strength_factors.append(("trend", trend_consistency, 0.30))

        # 2. 技术指标确认 (25%)
        rsi_score = self._score_rsi(df["rsi"].iloc[-1])
        macd_score = self._score_macd(
            df["macd"].iloc[-1], df["macd_signal"].iloc[-1]
        )
        bb_score = self._score_bollinger(
            df["close"].iloc[-1],
            df["bb_upper"].iloc[-1],
            df["bb_middle"].iloc[-1],
            df["bb_lower"].iloc[-1],
        )
        tech_score = (rsi_score + macd_score + bb_score) / 3
        strength_factors.append(("technical", tech_score, 0.25))

        # 3. 成交量确认 (20%)
        volume_score = min(market_structure["volume_ratio"] / 1.5, 1.0)
        strength_factors.append(("volume", volume_score, 0.20))

        # 4. 价格位置 (15%)
        price_position = market_structure["price_position"]
        position_score = 1 - abs(price_position - 0.5) * 2  # 中间位置得分最高
        strength_factors.append(("position", position_score, 0.15))

        # 5. 波动率适宜性 (10%)
        volatility = market_structure["volatility"]
        vol_score = 1 - abs(volatility - 0.03) / 0.03  # 3%波动率最佳
        vol_score = max(0, min(1, vol_score))
        strength_factors.append(("volatility", vol_score, 0.10))

        # 计算综合信号强度
        total_strength = sum(
            score * weight for _, score, weight in strength_factors
        )

        # 确定方向
        direction = "hold"
        if market_structure["trend_strength"] > 0.3 and total_strength > 0.7:
            direction = "buy"
        elif (
            market_structure["trend_strength"] < -0.3 and total_strength > 0.7
        ):
            direction = "sell"

        # 计算置信度
        confidence = total_strength * (1 - market_structure["volatility"])

        # 估算胜率
        win_probability = self._estimate_win_rate(
            total_strength, market_structure
        )

        # 计算盈亏比
        risk_reward = self._calculate_risk_reward(df, direction)

        return SignalQuality(
            strength=total_strength,
            direction=direction,
            confidence=confidence,
            risk_reward=risk_reward,
            win_probability=win_probability,
        )

    def _score_rsi(self, rsi: float) -> float:
        """RSI评分"""
        if 40 <= rsi <= 60:
            return 1.0  # 中性区域最佳
        elif 30 <= rsi <= 70:
            return 0.8
        elif 20 <= rsi <= 80:
            return 0.6
        else:
            return 0.2  # 极端区域风险高

    def _score_macd(self, macd: float, signal: float) -> float:
        """MACD评分"""
        if macd > signal:
            return min(1.0, (macd - signal) / abs(signal) * 2)
        else:
            return max(0.0, 1 - (signal - macd) / abs(signal) * 2)

    def _score_bollinger(
        self, price: float, upper: float, middle: float, lower: float
    ) -> float:
        """布林带评分"""
        if lower <= price <= upper:
            # 在布林带内，根据位置评分
            position = (price - lower) / (upper - lower)
            return 1 - abs(position - 0.5) * 2
        else:
            return 0.3  # 在布林带外风险较高

    def _estimate_win_rate(
        self, signal_strength: float, market_structure: Dict
    ) -> float:
        """估算胜率"""
        base_win_rate = 0.5

        # 信号强度加成
        strength_bonus = signal_strength * 0.3

        # 趋势加成
        trend_bonus = abs(market_structure["trend_strength"]) * 0.15

        # 成交量加成
        volume_bonus = min(market_structure["volume_ratio"] / 2, 0.1)

        estimated_win_rate = (
            base_win_rate + strength_bonus + trend_bonus + volume_bonus
        )
        return min(0.85, max(0.3, estimated_win_rate))  # 限制在30%-85%之间

    def _calculate_risk_reward(
        self, df: pd.DataFrame, direction: str
    ) -> float:
        """计算盈亏比"""
        if direction == "hold":
            return 0

        current_price = df["close"].iloc[-1]
        atr = df["atr"].iloc[-1] if "atr" in df.columns else df["close"].std()

        # 动态止损止盈
        if direction == "buy":
            stop_loss = current_price - (atr * 1.5)
            take_profit = current_price + (atr * 3.0)
        else:  # sell
            stop_loss = current_price + (atr * 1.5)
            take_profit = current_price - (atr * 3.0)

        risk = abs(current_price - stop_loss)
        reward = abs(take_profit - current_price)

        return reward / risk if risk > 0 else 0

    def generate_trade_setup(
        self, pair: str, df: pd.DataFrame
    ) -> Optional[TradeSetup]:
        """生成交易设置"""
        signal_quality = self.calculate_signal_strength(df)

        # 信号过滤
        if (
            signal_quality.strength < self.min_signal_strength
            or signal_quality.win_probability < self.min_win_rate
            or signal_quality.risk_reward < self.min_risk_reward
            or signal_quality.direction == "hold"
        ):
            return None

        current_price = df["close"].iloc[-1]
        atr = df["atr"].iloc[-1] if "atr" in df.columns else df["close"].std()

        # 计算止损止盈
        if signal_quality.direction == "buy":
            stop_loss = current_price - (atr * 1.5)
            take_profit = current_price + (atr * 3.0)
        else:
            stop_loss = current_price + (atr * 1.5)
            take_profit = current_price - (atr * 3.0)

        # 计算仓位大小
        risk_amount = self.current_capital * self.risk_per_trade
        price_risk = abs(current_price - stop_loss)
        position_size = min(
            risk_amount / price_risk,
            self.current_capital * self.position_size_pct / current_price,
        )

        return TradeSetup(
            pair=pair,
            direction=signal_quality.direction,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=position_size,
            risk_reward_ratio=signal_quality.risk_reward,
            signal_quality=signal_quality,
        )

    def update_capital(self, pnl: float):
        """更新资金（滚雪球效应）"""
        self.current_capital += pnl

        # 记录复利增长
        growth_rate = (
            self.current_capital - self.initial_capital
        ) / self.initial_capital

        logger.info(
            f"资金更新: {self.current_capital:.2f} USDT, 增长率: {growth_rate:.2%}"
        )

    def get_performance_summary(self) -> Dict:
        """获取表现摘要"""
        if not self.trades_history:
            return {}

        total_trades = len(self.trades_history)
        winning_trades = sum(
            1 for trade in self.trades_history if trade["pnl"] > 0
        )
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        total_pnl = sum(trade["pnl"] for trade in self.trades_history)
        total_return = total_pnl / self.initial_capital

        return {
            "total_trades": total_trades,
            "win_rate": win_rate,
            "total_return": total_return,
            "current_capital": self.current_capital,
            "growth_factor": self.current_capital / self.initial_capital,
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易执行引擎
Trading Execution Engine

负责高效执行交易信号，管理执行队列，优化执行性能
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
from enum import Enum
import statistics

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """执行状态"""
    PENDING = "pending"         # 待执行
    EXECUTING = "executing"     # 执行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 执行失败
    CANCELLED = "cancelled"     # 已取消


@dataclass
class ExecutionTask:
    """执行任务"""
    id: str
    signal: Dict[str, Any]
    priority: int = 1           # 优先级 (1-10, 10最高)
    timestamp: datetime = None
    status: ExecutionStatus = ExecutionStatus.PENDING
    attempts: int = 0
    max_attempts: int = 3
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ExecutionMetrics:
    """执行指标"""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_latency: float = 0.0
    max_latency: float = 0.0
    min_latency: float = float('inf')
    execution_times: List[float] = None
    
    def __post_init__(self):
        if self.execution_times is None:
            self.execution_times = []


class ExecutionEngine:
    """交易执行引擎"""
    
    def __init__(self, order_manager, api_connector, max_concurrent_executions: int = 5):
        """
        初始化执行引擎
        
        Args:
            order_manager: 订单管理器
            api_connector: API连接器
            max_concurrent_executions: 最大并发执行数
        """
        self.order_manager = order_manager
        self.api = api_connector
        self.max_concurrent = max_concurrent_executions
        
        # 执行队列
        self.execution_queue = asyncio.PriorityQueue()
        self.active_executions: Dict[str, ExecutionTask] = {}
        
        # 执行指标
        self.metrics = ExecutionMetrics()
        
        # 执行控制
        self.running = False
        self.workers: List[asyncio.Task] = []
        
        # 回调函数
        self.execution_callbacks: List[Callable] = []
        
        logger.info(f"交易执行引擎初始化完成，最大并发数: {max_concurrent_executions}")
    
    async def start(self):
        """启动执行引擎"""
        if self.running:
            logger.warning("执行引擎已在运行")
            return
        
        self.running = True
        
        # 启动工作线程
        for i in range(self.max_concurrent):
            worker = asyncio.create_task(self._execution_worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"执行引擎已启动，工作线程数: {len(self.workers)}")
    
    async def stop(self):
        """停止执行引擎"""
        if not self.running:
            return
        
        self.running = False
        
        # 取消所有工作线程
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作线程结束
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("执行引擎已停止")
    
    async def execute_signal(self, signal: Dict[str, Any], priority: int = 1) -> str:
        """
        执行交易信号
        
        Args:
            signal: 交易信号
            priority: 优先级 (1-10)
            
        Returns:
            执行任务ID
        """
        try:
            # 创建执行任务
            task_id = f"exec_{int(time.time() * 1000000)}"
            task = ExecutionTask(
                id=task_id,
                signal=signal,
                priority=priority
            )
            
            # 添加到队列（优先级队列，数字越小优先级越高）
            await self.execution_queue.put((-priority, task.timestamp, task))
            
            logger.info(f"交易信号已加入执行队列: {task_id}, 优先级: {priority}")
            return task_id
            
        except Exception as e:
            logger.error(f"添加执行任务失败: {str(e)}")
            raise
    
    async def execute_signals_batch(self, signals: List[Dict[str, Any]], priority: int = 1) -> List[str]:
        """
        批量执行交易信号
        
        Args:
            signals: 交易信号列表
            priority: 优先级
            
        Returns:
            执行任务ID列表
        """
        task_ids = []
        for signal in signals:
            task_id = await self.execute_signal(signal, priority)
            task_ids.append(task_id)
        
        logger.info(f"批量添加执行任务: {len(task_ids)} 个")
        return task_ids
    
    def get_execution_status(self, task_id: str) -> Optional[ExecutionTask]:
        """
        获取执行状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行任务对象
        """
        return self.active_executions.get(task_id)
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.execution_queue.qsize()
    
    def get_active_executions(self) -> Dict[str, ExecutionTask]:
        """获取活跃执行任务"""
        return self.active_executions.copy()
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取执行指标"""
        return {
            'total_executions': self.metrics.total_executions,
            'successful_executions': self.metrics.successful_executions,
            'failed_executions': self.metrics.failed_executions,
            'success_rate': (
                self.metrics.successful_executions / self.metrics.total_executions 
                if self.metrics.total_executions > 0 else 0
            ),
            'average_latency': self.metrics.average_latency,
            'max_latency': self.metrics.max_latency,
            'min_latency': self.metrics.min_latency if self.metrics.min_latency != float('inf') else 0,
            'queue_size': self.get_queue_size(),
            'active_executions': len(self.active_executions)
        }
    
    def add_execution_callback(self, callback: Callable):
        """
        添加执行回调函数
        
        Args:
            callback: 回调函数，接收 (task_id, status, result) 参数
        """
        self.execution_callbacks.append(callback)
    
    async def _execution_worker(self, worker_name: str):
        """执行工作线程"""
        logger.info(f"执行工作线程启动: {worker_name}")
        
        while self.running:
            try:
                # 从队列获取任务（等待最多1秒）
                try:
                    priority, timestamp, task = await asyncio.wait_for(
                        self.execution_queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # 执行任务
                await self._execute_task(task, worker_name)
                
            except Exception as e:
                logger.error(f"执行工作线程错误 {worker_name}: {str(e)}")
                await asyncio.sleep(1)
        
        logger.info(f"执行工作线程停止: {worker_name}")
    
    async def _execute_task(self, task: ExecutionTask, worker_name: str):
        """执行单个任务"""
        start_time = time.time()
        task.status = ExecutionStatus.EXECUTING
        task.attempts += 1
        
        # 添加到活跃执行列表
        self.active_executions[task.id] = task
        
        try:
            logger.info(f"[{worker_name}] 开始执行任务: {task.id}")
            
            # 转换信号为订单
            order_data = self._convert_signal_to_order(task.signal)
            
            # 提交订单
            success, message, order = await self.order_manager.place_order(order_data)
            
            # 计算执行时间
            execution_time = time.time() - start_time
            task.execution_time = execution_time
            
            if success:
                task.status = ExecutionStatus.COMPLETED
                self.metrics.successful_executions += 1
                logger.info(f"[{worker_name}] 任务执行成功: {task.id}, 耗时: {execution_time:.3f}s")
                
                # 调用回调函数
                await self._call_callbacks(task.id, ExecutionStatus.COMPLETED, {
                    'order': order,
                    'execution_time': execution_time
                })
                
            else:
                # 执行失败，检查是否需要重试
                if task.attempts < task.max_attempts:
                    task.status = ExecutionStatus.PENDING
                    task.error_message = message
                    
                    # 重新加入队列（降低优先级）
                    await self.execution_queue.put((-1, datetime.now(), task))
                    logger.warning(f"[{worker_name}] 任务执行失败，将重试: {task.id}, 错误: {message}")
                    
                else:
                    task.status = ExecutionStatus.FAILED
                    task.error_message = message
                    self.metrics.failed_executions += 1
                    logger.error(f"[{worker_name}] 任务执行失败（已达最大重试次数）: {task.id}, 错误: {message}")
                    
                    # 调用回调函数
                    await self._call_callbacks(task.id, ExecutionStatus.FAILED, {
                        'error': message,
                        'attempts': task.attempts
                    })
            
            # 更新指标
            self._update_metrics(execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            task.execution_time = execution_time
            task.status = ExecutionStatus.FAILED
            task.error_message = str(e)
            self.metrics.failed_executions += 1
            
            logger.error(f"[{worker_name}] 任务执行异常: {task.id}, 错误: {str(e)}")
            
            # 调用回调函数
            await self._call_callbacks(task.id, ExecutionStatus.FAILED, {
                'error': str(e),
                'exception': True
            })
        
        finally:
            # 从活跃执行列表移除
            if task.id in self.active_executions:
                del self.active_executions[task.id]
            
            self.metrics.total_executions += 1
    
    def _convert_signal_to_order(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        将交易信号转换为订单数据
        
        Args:
            signal: 交易信号
            
        Returns:
            订单数据
        """
        try:
            # 基本订单数据
            order_data = {
                'symbol': signal['symbol'],
                'side': signal['action'],  # buy/sell
                'type': signal.get('type', 'market'),
                'amount': signal['amount'],
                'strategy': signal.get('strategy', 'unknown')
            }
            
            # 添加价格信息
            if 'price' in signal:
                order_data['price'] = signal['price']
            
            if 'stop_price' in signal:
                order_data['stop_price'] = signal['stop_price']
            
            return order_data
            
        except Exception as e:
            logger.error(f"转换交易信号失败: {str(e)}")
            raise ValueError(f"无效的交易信号: {str(e)}")
    
    def _update_metrics(self, execution_time: float):
        """更新执行指标"""
        self.metrics.execution_times.append(execution_time)
        
        # 保持最近1000次执行记录
        if len(self.metrics.execution_times) > 1000:
            self.metrics.execution_times = self.metrics.execution_times[-1000:]
        
        # 更新延迟统计
        self.metrics.average_latency = statistics.mean(self.metrics.execution_times)
        self.metrics.max_latency = max(self.metrics.max_latency, execution_time)
        self.metrics.min_latency = min(self.metrics.min_latency, execution_time)
    
    async def _call_callbacks(self, task_id: str, status: ExecutionStatus, result: Dict[str, Any]):
        """调用回调函数"""
        for callback in self.execution_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_id, status, result)
                else:
                    callback(task_id, status, result)
            except Exception as e:
                logger.error(f"执行回调函数失败: {str(e)}")


# 全局执行引擎实例
_execution_engine_instance = None


def get_execution_engine(order_manager=None, api_connector=None, max_concurrent: int = 5):
    """获取执行引擎实例（单例模式）"""
    global _execution_engine_instance
    if _execution_engine_instance is None:
        if order_manager is None or api_connector is None:
            raise ValueError("首次创建执行引擎需要提供订单管理器和API连接器")
        _execution_engine_instance = ExecutionEngine(order_manager, api_connector, max_concurrent)
    return _execution_engine_instance


if __name__ == "__main__":
    # 测试代码
    print("交易执行引擎模块加载完成")
    print("支持的执行状态:", [status.value for status in ExecutionStatus])

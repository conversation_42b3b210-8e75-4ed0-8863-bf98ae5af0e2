{"data_mtime": 1748490240, "dep_lines": [15, 16, 27, 28, 10, 14, 17, 18, 23, 24, 25, 26, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.asyncio.compatibility", "websockets.asyncio.connection", "collections.abc", "websockets.exceptions", "websockets.frames", "websockets.headers", "websockets.http11", "websockets.protocol", "websockets.server", "websockets.typing", "__future__", "asyncio", "hmac", "http", "logging", "re", "socket", "sys", "types", "typing", "builtins", "_asyncio", "_frozen_importlib", "_socket", "_ssl", "_typeshed", "abc", "asyncio.base_events", "asyncio.events", "asyncio.protocols", "asyncio.transports", "contextlib", "enum", "os", "ssl", "typing_extensions", "websockets.datastructures", "websockets.extensions"], "hash": "6bfe7169ba336a91af7cbcf3756b536fdd0f0f10", "id": "websockets.asyncio.server", "ignore_all": true, "interface_hash": "b3a8a4997ae0d7e4f4983231c3ffea2bd8aec7d9", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py", "plugin_data": null, "size": 38366, "suppressed": [], "version_id": "1.15.0"}
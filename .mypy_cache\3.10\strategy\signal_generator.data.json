{".class": "MypyFile", "_fullname": "strategy.signal_generator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SignalGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.signal_generator.SignalGenerator", "name": "SignalGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "strategy.signal_generator", "mro": ["strategy.signal_generator.SignalGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["strategy.signal_generator.SignalGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SignalGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "indicators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator._analyze_signals", "name": "_analyze_signals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "indicators"], "arg_types": ["strategy.signal_generator.SignalGenerator", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_signals of SignalGenerator", "ret_type": {".class": "TupleType", "implicit": false, "items": ["strategy.signal_generator.SignalType", "strategy.signal_generator.SignalStrength", "builtins.float", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_indicators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator._calculate_indicators", "name": "_calculate_indicators", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["strategy.signal_generator.SignalGenerator", {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_indicators of SignalGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_stop_take_profit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal_type", "price", "indicators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator._calculate_stop_take_profit", "name": "_calculate_stop_take_profit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signal_type", "price", "indicators"], "arg_types": ["strategy.signal_generator.SignalGenerator", "strategy.signal_generator.SignalType", "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_stop_take_profit of SignalGenerator", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.config", "name": "config", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "generate_signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "market_data", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.SignalGenerator.generate_signal", "name": "generate_signal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "market_data", "symbol"], "arg_types": ["strategy.signal_generator.SignalGenerator", {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_signal of SignalGenerator", "ret_type": {".class": "UnionType", "items": ["strategy.signal_generator.TradingSignal", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "macd_fast": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.macd_fast", "name": "macd_fast", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "macd_signal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.macd_signal", "name": "macd_signal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "macd_slow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.macd_slow", "name": "macd_slow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "min_confidence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.min_confidence", "name": "min_confidence", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "rsi_overbought": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.rsi_overbought", "name": "rsi_overbought", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "rsi_oversold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.rsi_oversold", "name": "rsi_oversold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "rsi_period": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.rsi_period", "name": "rsi_period", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "sma_long": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.sma_long", "name": "sma_long", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "sma_short": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.sma_short", "name": "sma_short", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "strong_signal_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "strategy.signal_generator.SignalGenerator.strong_signal_threshold", "name": "strong_signal_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.signal_generator.SignalGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.signal_generator.SignalGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SignalStrength": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.signal_generator.SignalStrength", "name": "SignalStrength", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "strategy.signal_generator.SignalStrength", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "strategy.signal_generator", "mro": ["strategy.signal_generator.SignalStrength", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MODERATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalStrength.MODERATE", "name": "MODERATE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "STRONG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalStrength.STRONG", "name": "STRONG", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "VERY_STRONG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalStrength.VERY_STRONG", "name": "VERY_STRONG", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "WEAK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalStrength.WEAK", "name": "WEAK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.signal_generator.SignalStrength.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.signal_generator.SignalStrength", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SignalType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.signal_generator.SignalType", "name": "SignalType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "strategy.signal_generator.SignalType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "strategy.signal_generator", "mro": ["strategy.signal_generator.SignalType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalType.BUY", "name": "BUY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "buy"}, "type_ref": "builtins.str"}}}, "HOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalType.HOLD", "name": "HOLD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "hold"}, "type_ref": "builtins.str"}}}, "SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalType.SELL", "name": "SELL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sell"}, "type_ref": "builtins.str"}}}, "STRONG_BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalType.STRONG_BUY", "name": "STRONG_BUY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "strong_buy"}, "type_ref": "builtins.str"}}}, "STRONG_SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.SignalType.STRONG_SELL", "name": "STRONG_SELL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "strong_sell"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.signal_generator.SignalType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.signal_generator.SignalType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TechnicalIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.signal_generator.TechnicalIndicators", "name": "TechnicalIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.TechnicalIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "strategy.signal_generator", "mro": ["strategy.signal_generator.TechnicalIndicators", "builtins.object"], "names": {".class": "SymbolTable", "bollinger_bands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["data", "period", "std_dev"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.bollinger_bands", "name": "bollinger_bands", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["data", "period", "std_dev"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bollinger_bands of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.bollinger_bands", "name": "bollinger_bands", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["data", "period", "std_dev"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bollinger_bands of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["data", "period"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.ema", "name": "ema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ema of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.ema", "name": "ema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ema of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "macd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["data", "fast", "slow", "signal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.macd", "name": "macd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["data", "fast", "slow", "signal"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "macd of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.macd", "name": "macd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["data", "fast", "slow", "signal"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "macd of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rsi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "period"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.rsi", "name": "rsi", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsi of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.rsi", "name": "rsi", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsi of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["data", "period"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.sma", "name": "sma", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sma of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.sma", "name": "sma", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["data", "period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sma of TechnicalIndicators", "ret_type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stochastic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["high", "low", "close", "k_period", "d_period"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TechnicalIndicators.stochastic", "name": "stochastic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["high", "low", "close", "k_period", "d_period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stochastic of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.TechnicalIndicators.stochastic", "name": "stochastic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["high", "low", "close", "k_period", "d_period"], "arg_types": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stochastic of TechnicalIndicators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.signal_generator.TechnicalIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.signal_generator.TechnicalIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TradingSignal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "strategy.signal_generator.TradingSignal", "name": "TradingSignal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.TradingSignal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "symbol", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "signal_type", "type": "strategy.signal_generator.SignalType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "strength", "type": "strategy.signal_generator.SignalStrength"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "price", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "timestamp", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "indicators", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "confidence", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "stop_loss", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "take_profit", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "strategy_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "reason", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "strategy.signal_generator", "mro": ["strategy.signal_generator.TradingSignal", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "symbol", "signal_type", "strength", "price", "timestamp", "indicators", "confidence", "stop_loss", "take_profit", "strategy_name", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.TradingSignal.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "symbol", "signal_type", "strength", "price", "timestamp", "indicators", "confidence", "stop_loss", "take_profit", "strategy_name", "reason"], "arg_types": ["strategy.signal_generator.TradingSignal", "builtins.str", "strategy.signal_generator.SignalType", "strategy.signal_generator.SignalStrength", "builtins.float", "datetime.datetime", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TradingSignal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "strategy.signal_generator.TradingSignal.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "signal_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strength"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "price"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indicators"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "confidence"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stop_loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "take_profit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strategy_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reason"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["symbol", "signal_type", "strength", "price", "timestamp", "indicators", "confidence", "stop_loss", "take_profit", "strategy_name", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "strategy.signal_generator.TradingSignal.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["symbol", "signal_type", "strength", "price", "timestamp", "indicators", "confidence", "stop_loss", "take_profit", "strategy_name", "reason"], "arg_types": ["builtins.str", "strategy.signal_generator.SignalType", "strategy.signal_generator.SignalStrength", "builtins.float", "datetime.datetime", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TradingSignal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["symbol", "signal_type", "strength", "price", "timestamp", "indicators", "confidence", "stop_loss", "take_profit", "strategy_name", "reason"], "arg_types": ["builtins.str", "strategy.signal_generator.SignalType", "strategy.signal_generator.SignalStrength", "builtins.float", "datetime.datetime", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TradingSignal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "confidence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.confidence", "name": "confidence", "type": "builtins.float"}}, "indicators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.indicators", "name": "indicators", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "price": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.price", "name": "price", "type": "builtins.float"}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.signal_generator.TradingSignal.reason", "name": "reason", "type": "builtins.str"}}, "signal_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.signal_type", "name": "signal_type", "type": "strategy.signal_generator.SignalType"}}, "stop_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.signal_generator.TradingSignal.stop_loss", "name": "stop_loss", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "strategy_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.signal_generator.TradingSignal.strategy_name", "name": "strategy_name", "type": "builtins.str"}}, "strength": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.strength", "name": "strength", "type": "strategy.signal_generator.SignalStrength"}}, "symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.symbol", "name": "symbol", "type": "builtins.str"}}, "take_profit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "strategy.signal_generator.TradingSignal.take_profit", "name": "take_profit", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "strategy.signal_generator.TradingSignal.timestamp", "name": "timestamp", "type": "datetime.datetime"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.TradingSignal.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["strategy.signal_generator.TradingSignal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of TradingSignal", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "strategy.signal_generator.TradingSignal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "strategy.signal_generator.TradingSignal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "strategy.signal_generator.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_signal_generator_instance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator._signal_generator_instance", "name": "_signal_generator_instance", "type": {".class": "NoneType"}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "dates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.dates", "name": "dates", "type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "generator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.generator", "name": "generator", "type": "strategy.signal_generator.SignalGenerator"}}, "get_signal_generator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "strategy.signal_generator.get_signal_generator", "name": "get_signal_generator", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["config"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signal_generator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "strategy.signal_generator.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "signal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.signal", "name": "signal", "type": {".class": "UnionType", "items": ["strategy.signal_generator.TradingSignal", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "test_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "strategy.signal_generator.test_data", "name": "test_data", "type": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": {".class": "AnyType", "missing_import_name": "strategy.signal_generator.pd", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\strategy\\signal_generator.py"}
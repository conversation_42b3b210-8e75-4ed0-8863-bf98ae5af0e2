#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
资源管理器
Resource Manager

管理系统资源，确保资源的正确分配和释放
"""

import os
import sys
import logging
import threading
import time
import gc
import weakref
import atexit
from typing import Dict, List, Any, Optional, Set, Callable
from pathlib import Path
import psutil
import traceback

logger = logging.getLogger(__name__)

class ResourceManager:
    """资源管理器"""
    
    # 单例实例
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ResourceManager, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化资源管理器"""
        if self._initialized:
            return
        
        # 资源跟踪
        self.resources = {}
        self.resource_locks = {}
        self.threads = set()
        self.file_handles = set()
        self.network_connections = set()
        
        # 性能监控
        self.memory_usage_history = []
        self.cpu_usage_history = []
        self.disk_usage_history = []
        
        # 监控线程
        self.monitor_thread = None
        self.stop_monitor = False
        
        # 注册退出处理
        atexit.register(self.cleanup_all)
        
        # 启动监控
        self.start_monitoring()
        
        self._initialized = True
    
    def register_resource(self, resource_type: str, resource_id: str, resource: Any) -> None:
        """注册资源"""
        with self._lock:
            if resource_type not in self.resources:
                self.resources[resource_type] = {}
                self.resource_locks[resource_type] = {}
            
            self.resources[resource_type][resource_id] = resource
            self.resource_locks[resource_type][resource_id] = threading.Lock()
            
            logger.debug(f"已注册资源: {resource_type}/{resource_id}")
    
    def unregister_resource(self, resource_type: str, resource_id: str) -> None:
        """注销资源"""
        with self._lock:
            if resource_type in self.resources and resource_id in self.resources[resource_type]:
                del self.resources[resource_type][resource_id]
                del self.resource_locks[resource_type][resource_id]
                
                logger.debug(f"已注销资源: {resource_type}/{resource_id}")
    
    def get_resource(self, resource_type: str, resource_id: str) -> Optional[Any]:
        """获取资源"""
        with self._lock:
            if resource_type in self.resources and resource_id in self.resources[resource_type]:
                return self.resources[resource_type][resource_id]
            return None
    
    def lock_resource(self, resource_type: str, resource_id: str) -> bool:
        """锁定资源"""
        with self._lock:
            if resource_type in self.resource_locks and resource_id in self.resource_locks[resource_type]:
                lock = self.resource_locks[resource_type][resource_id]
                return lock.acquire(blocking=False)
            return False
    
    def unlock_resource(self, resource_type: str, resource_id: str) -> None:
        """解锁资源"""
        with self._lock:
            if resource_type in self.resource_locks and resource_id in self.resource_locks[resource_type]:
                lock = self.resource_locks[resource_type][resource_id]
                try:
                    lock.release()
                except RuntimeError:
                    # 锁可能未被获取
                    pass
    
    def register_thread(self, thread: threading.Thread) -> None:
        """注册线程"""
        with self._lock:
            self.threads.add(thread)
            logger.debug(f"已注册线程: {thread.name}")
    
    def unregister_thread(self, thread: threading.Thread) -> None:
        """注销线程"""
        with self._lock:
            if thread in self.threads:
                self.threads.remove(thread)
                logger.debug(f"已注销线程: {thread.name}")
    
    def register_file_handle(self, file_handle) -> None:
        """注册文件句柄"""
        with self._lock:
            self.file_handles.add(file_handle)
            logger.debug(f"已注册文件句柄: {file_handle}")
    
    def unregister_file_handle(self, file_handle) -> None:
        """注销文件句柄"""
        with self._lock:
            if file_handle in self.file_handles:
                self.file_handles.remove(file_handle)
                logger.debug(f"已注销文件句柄: {file_handle}")
    
    def register_network_connection(self, connection) -> None:
        """注册网络连接"""
        with self._lock:
            self.network_connections.add(connection)
            logger.debug(f"已注册网络连接: {connection}")
    
    def unregister_network_connection(self, connection) -> None:
        """注销网络连接"""
        with self._lock:
            if connection in self.network_connections:
                self.network_connections.remove(connection)
                logger.debug(f"已注销网络连接: {connection}")
    
    def start_monitoring(self) -> None:
        """启动资源监控"""
        if self.monitor_thread and self.monitor_thread.is_alive():
            return
        
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        logger.info("资源监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止资源监控"""
        self.stop_monitor = True
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
            logger.info("资源监控已停止")
    
    def _monitor_resources(self) -> None:
        """监控资源使用情况"""
        while not self.stop_monitor:
            try:
                # 获取当前进程
                process = psutil.Process(os.getpid())
                
                # 内存使用
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()
                self.memory_usage_history.append((time.time(), memory_info.rss, memory_percent))
                
                # CPU使用
                cpu_percent = process.cpu_percent(interval=1.0)
                self.cpu_usage_history.append((time.time(), cpu_percent))
                
                # 磁盘使用
                disk_usage = psutil.disk_usage('/')
                self.disk_usage_history.append((time.time(), disk_usage.percent))
                
                # 限制历史记录长度
                max_history = 100
                if len(self.memory_usage_history) > max_history:
                    self.memory_usage_history = self.memory_usage_history[-max_history:]
                if len(self.cpu_usage_history) > max_history:
                    self.cpu_usage_history = self.cpu_usage_history[-max_history:]
                if len(self.disk_usage_history) > max_history:
                    self.disk_usage_history = self.disk_usage_history[-max_history:]
                
                # 检查资源泄漏
                self._check_resource_leaks()
                
                # 等待下一次监控
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"资源监控出错: {e}")
                logger.debug(traceback.format_exc())
                time.sleep(30)
    
    def _check_resource_leaks(self) -> None:
        """检查资源泄漏"""
        with self._lock:
            # 检查线程
            dead_threads = [t for t in self.threads if not t.is_alive()]
            for thread in dead_threads:
                self.threads.remove(thread)
                logger.debug(f"已清理死亡线程: {thread.name}")
            
            # 检查文件句柄
            closed_files = [f for f in self.file_handles if f.closed]
            for file_handle in closed_files:
                self.file_handles.remove(file_handle)
                logger.debug(f"已清理关闭的文件句柄: {file_handle}")
    
    def cleanup_all(self) -> None:
        """清理所有资源"""
        logger.info("开始清理所有资源...")
        
        # 停止监控
        self.stop_monitoring()
        
        # 清理线程
        for thread in list(self.threads):
            if thread.is_alive() and thread != threading.current_thread():
                logger.debug(f"正在终止线程: {thread.name}")
                # 无法直接终止线程，只能等待
        
        # 清理文件句柄
        for file_handle in list(self.file_handles):
            try:
                if not file_handle.closed:
                    logger.debug(f"正在关闭文件句柄: {file_handle}")
                    file_handle.close()
            except Exception as e:
                logger.error(f"关闭文件句柄失败: {e}")
        
        # 清理网络连接
        for connection in list(self.network_connections):
            try:
                logger.debug(f"正在关闭网络连接: {connection}")
                connection.close()
            except Exception as e:
                logger.error(f"关闭网络连接失败: {e}")
        
        # 清理资源
        for resource_type in list(self.resources.keys()):
            for resource_id in list(self.resources[resource_type].keys()):
                resource = self.resources[resource_type][resource_id]
                try:
                    logger.debug(f"正在清理资源: {resource_type}/{resource_id}")
                    # 尝试调用close方法
                    if hasattr(resource, 'close') and callable(getattr(resource, 'close')):
                        resource.close()
                    # 尝试调用cleanup方法
                    elif hasattr(resource, 'cleanup') and callable(getattr(resource, 'cleanup')):
                        resource.cleanup()
                    # 尝试调用dispose方法
                    elif hasattr(resource, 'dispose') and callable(getattr(resource, 'dispose')):
                        resource.dispose()
                except Exception as e:
                    logger.error(f"清理资源失败 {resource_type}/{resource_id}: {e}")
        
        # 强制垃圾回收
        gc.collect()
        
        logger.info("所有资源清理完成")
    
    def get_resource_usage(self) -> Dict[str, Any]:
        """获取资源使用情况"""
        with self._lock:
            # 获取当前进程
            process = psutil.Process(os.getpid())
            
            # 内存使用
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # CPU使用
            cpu_percent = process.cpu_percent(interval=0.1)
            
            # 磁盘使用
            disk_usage = psutil.disk_usage('/')
            
            # 线程数
            thread_count = len(self.threads)
            active_thread_count = len([t for t in self.threads if t.is_alive()])
            
            # 文件句柄数
            file_handle_count = len(self.file_handles)
            open_file_handle_count = len([f for f in self.file_handles if not f.closed])
            
            # 网络连接数
            network_connection_count = len(self.network_connections)
            
            # 资源数
            resource_count = sum(len(resources) for resources in self.resources.values())
            
            return {
                "memory": {
                    "rss": memory_info.rss,
                    "rss_mb": memory_info.rss / (1024 * 1024),
                    "percent": memory_percent
                },
                "cpu": {
                    "percent": cpu_percent
                },
                "disk": {
                    "percent": disk_usage.percent
                },
                "threads": {
                    "total": thread_count,
                    "active": active_thread_count
                },
                "files": {
                    "total": file_handle_count,
                    "open": open_file_handle_count
                },
                "network": {
                    "connections": network_connection_count
                },
                "resources": {
                    "total": resource_count,
                    "by_type": {rtype: len(resources) for rtype, resources in self.resources.items()}
                }
            }
    
    def get_resource_usage_history(self) -> Dict[str, List]:
        """获取资源使用历史"""
        with self._lock:
            return {
                "memory": self.memory_usage_history.copy(),
                "cpu": self.cpu_usage_history.copy(),
                "disk": self.disk_usage_history.copy()
            }
    
    def create_resource_report(self, report_file: Optional[str] = None) -> str:
        """创建资源报告"""
        usage = self.get_resource_usage()
        
        report = []
        report.append("=" * 50)
        report.append("资源使用报告")
        report.append("=" * 50)
        report.append("")
        
        # 内存使用
        report.append("内存使用:")
        report.append(f"  物理内存: {usage['memory']['rss_mb']:.2f} MB")
        report.append(f"  内存占用率: {usage['memory']['percent']:.2f}%")
        report.append("")
        
        # CPU使用
        report.append("CPU使用:")
        report.append(f"  CPU占用率: {usage['cpu']['percent']:.2f}%")
        report.append("")
        
        # 磁盘使用
        report.append("磁盘使用:")
        report.append(f"  磁盘占用率: {usage['disk']['percent']:.2f}%")
        report.append("")
        
        # 线程
        report.append("线程:")
        report.append(f"  总线程数: {usage['threads']['total']}")
        report.append(f"  活动线程数: {usage['threads']['active']}")
        report.append("")
        
        # 文件句柄
        report.append("文件句柄:")
        report.append(f"  总文件句柄数: {usage['files']['total']}")
        report.append(f"  打开文件句柄数: {usage['files']['open']}")
        report.append("")
        
        # 网络连接
        report.append("网络连接:")
        report.append(f"  连接数: {usage['network']['connections']}")
        report.append("")
        
        # 资源
        report.append("资源:")
        report.append(f"  总资源数: {usage['resources']['total']}")
        for rtype, count in usage['resources'].get('by_type', {}).items():
            report.append(f"  {rtype}: {count}")
        report.append("")
        
        report_text = "\n".join(report)
        
        # 保存报告
        if report_file:
            try:
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                logger.info(f"资源报告已保存到: {report_file}")
            except Exception as e:
                logger.error(f"保存资源报告失败: {e}")
        
        return report_text

# 全局资源管理器实例
_resource_manager = None

def get_resource_manager() -> ResourceManager:
    """获取资源管理器实例"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager
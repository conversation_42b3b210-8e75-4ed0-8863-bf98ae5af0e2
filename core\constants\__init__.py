#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
常量管理模块
Constants Management Module

提供系统常量定义和管理
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要常量类
try:
    from .app_constants import AppConstants
    from .style_constants import StyleConstants
    from .trading_constants import TradingConstants
    from .message_constants import MessageConstants
    from .path_constants import PathConstants
    
    __all__ = [
        'AppConstants',
        'StyleConstants', 
        'TradingConstants',
        'MessageConstants',
        'PathConstants'
    ]
    
except ImportError as e:
    print(f"常量模块导入警告: {e}")
    __all__ = []

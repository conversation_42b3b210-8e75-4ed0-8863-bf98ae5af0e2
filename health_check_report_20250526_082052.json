{"timestamp": "2025-05-26T08:20:47.476029", "overall_status": "WARNING", "checks": {"Python环境": {"status": "PASS", "message": "Python 3.10.11", "details": {"python_version": "3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]", "python_executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe", "platform": "win32", "architecture": "64-bit"}}, "依赖包": {"status": "WARN", "message": "缺少可选包: scikit-learn, websocket-client", "details": {"installed_packages": {"ccxt": "4.4.82", "pandas": "2.2.3", "numpy": "1.24.3", "requests": "2.32.3", "flask": "3.0.3", "scipy": "1.15.3", "statsmodels": "0.14.4", "cryptography": "44.0.3"}, "missing_required": [], "missing_optional": ["scikit-learn", "websocket-client"]}}, "文件结构": {"status": "PASS", "message": "文件结构完整 (7个文件)", "details": {"file_sizes": {"requirements.txt": 587, "core/config_manager.py": 8655, "core/error_handler.py": 9214, "core/logging_config.py": 10068, "core/gate_io_client.py": 12014, "core/ultimate_trading_gui.py": 21790, "core/secure_key_management.py": 15451}, "missing_required": [], "missing_important": []}}, "配置文件": {"status": "PASS", "message": "配置文件正常 (2个)", "details": {"valid_configs": ["config.json", "core/gui_config.json"], "invalid_configs": [], "missing_configs": []}}, "数据库": {"status": "PASS", "message": "数据库状态正常 (2个)", "details": {"accessible_dbs": ["core/monitoring.db", "core/secure_vault.db"], "inaccessible_dbs": [], "missing_dbs": []}}, "网络连接": {"status": "FAIL", "message": "无法访问任何测试URL", "details": {"accessible_urls": [], "failed_urls": ["https://api.gateio.ws (503)", "https://api.binance.com (451)", "https://www.google.com (429)"]}}, "系统资源": {"status": "PASS", "message": "系统资源充足 (CPU: 18.7%, 内存: 40.1%)", "details": {"cpu_percent": 18.7, "memory_total": 34226843648, "memory_available": 20512325632, "memory_percent": 40.1, "disk_total": 382584483840, "disk_free": 282290429952, "disk_percent": 26.21487753014673}}, "代码质量": {"status": "PASS", "message": "代码语法正常 (59个文件)", "details": {"total_files": 59, "syntax_errors": [], "import_errors": []}}, "安全配置": {"status": "WARN", "message": "发现安全问题: 2个", "details": {"security_issues": ["core/secure_vault.db: 文件权限不安全 (666)", "core/gui_config.json: 可能包含硬编码密钥"], "checked_files": ["core/secure_vault.db", "core/encrypted_keys.json", "core/gui_config.json"]}}, "日志系统": {"status": "PASS", "message": "日志系统正常 (0个文件)", "details": {"log_dir_exists": true, "log_files_count": 0, "large_logs": []}}}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级功能测试脚本
Test Advanced Features: WebSocket and Advanced Order Types
"""

import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.enhanced_websocket_client import EnhancedWebSocketClient, RealTimeDataManager
from institutional_framework.advanced_order_types import *
from institutional_framework.gate_trading_engine import GateIOTradingEngine


def test_websocket_client():
    """测试增强型WebSocket客户端"""
    print("🔍 测试增强型WebSocket客户端...")
    
    try:
        # 创建WebSocket客户端
        ws_client = EnhancedWebSocketClient()
        
        # 数据回调函数
        def on_data_update(channel, data):
            print(f"📡 收到数据更新: {channel}")
            if 'result' in data:
                result = data['result']
                if 'currency_pair' in result:
                    pair = result['currency_pair']
                    price = result.get('last', 0)
                    print(f"   {pair}: {price}")
        
        print("🔌 连接WebSocket...")
        if ws_client.connect():
            print("✅ WebSocket连接成功")
            
            # 订阅数据
            print("📡 订阅行情数据...")
            ws_client.subscribe("spot.tickers", ["BTC_USDT", "ETH_USDT"], on_data_update)
            
            # 等待数据
            print("⏳ 等待数据更新 (15秒)...")
            for i in range(15):
                time.sleep(1)
                status = ws_client.get_connection_status()
                if i % 5 == 0:
                    print(f"   连接状态: {status}")
                
                # 检查是否收到数据
                btc_data = ws_client.get_ticker_data("BTC_USDT")
                if btc_data:
                    print(f"   BTC实时价格: {btc_data['price']:.2f}")
                    break
            
            # 断开连接
            ws_client.disconnect()
            print("✅ WebSocket测试完成")
            return True
        else:
            print("❌ WebSocket连接失败")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket测试异常: {e}")
        return False


def test_real_time_data_manager():
    """测试实时数据管理器"""
    print("\n🔍 测试实时数据管理器...")
    
    try:
        # 创建实时数据管理器
        data_manager = RealTimeDataManager()
        
        # 数据更新回调
        def on_market_data(data_type, data):
            print(f"📊 市场数据更新: {data_type}")
        
        data_manager.add_data_callback(on_market_data)
        
        print("🚀 启动实时数据管理器...")
        if data_manager.start():
            print("✅ 实时数据管理器启动成功")
            
            # 等待数据
            print("⏳ 等待实时数据 (10秒)...")
            for i in range(10):
                time.sleep(1)
                
                # 获取实时价格
                btc_price = data_manager.get_real_time_price("BTC_USDT")
                if btc_price:
                    print(f"   BTC实时价格: {btc_price:.2f}")
                
                # 获取连接状态
                if i % 3 == 0:
                    status = data_manager.get_connection_status()
                    print(f"   连接状态: {status}")
            
            # 停止管理器
            data_manager.stop()
            print("✅ 实时数据管理器测试完成")
            return True
        else:
            print("❌ 实时数据管理器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 实时数据管理器测试异常: {e}")
        return False


def test_stop_loss_order():
    """测试止损订单"""
    print("\n🔍 测试止损订单...")
    
    try:
        # 创建交易引擎
        engine = GateIOTradingEngine(demo_mode=True)
        
        # 创建高级订单管理器
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 创建止损订单
        stop_order = StopLossOrder(
            order_id="stop_001",
            symbol="BTC_USDT",
            side="sell",
            amount=0.001,
            stop_price=50000.0,  # 止损价
            strategy_name="测试策略"
        )
        
        print(f"📝 创建止损订单: 止损价 {stop_order.stop_price}")
        order_manager.add_order(stop_order)
        
        # 模拟价格变化触发止损
        print("⏳ 模拟价格变化...")
        order_manager.market_data["BTC_USDT"] = {'price': 51000.0}  # 高于止损价
        time.sleep(2)
        
        order_manager.market_data["BTC_USDT"] = {'price': 49000.0}  # 触发止损
        time.sleep(3)
        
        # 检查订单状态
        order_status = order_manager.get_order_status("stop_001")
        if order_status and order_status.status == OrderStatus.TRIGGERED:
            print("✅ 止损订单触发成功")
            result = True
        else:
            print("❌ 止损订单未触发")
            result = False
        
        order_manager.stop_monitoring()
        return result
        
    except Exception as e:
        print(f"❌ 止损订单测试异常: {e}")
        return False


def test_take_profit_order():
    """测试止盈订单"""
    print("\n🔍 测试止盈订单...")
    
    try:
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 创建止盈订单
        profit_order = TakeProfitOrder(
            order_id="profit_001",
            symbol="ETH_USDT",
            side="sell",
            amount=0.1,
            target_price=3000.0,  # 止盈价
            strategy_name="测试策略"
        )
        
        print(f"📝 创建止盈订单: 目标价 {profit_order.target_price}")
        order_manager.add_order(profit_order)
        
        # 模拟价格变化
        order_manager.market_data["ETH_USDT"] = {'price': 2800.0}  # 低于目标价
        time.sleep(2)
        
        order_manager.market_data["ETH_USDT"] = {'price': 3100.0}  # 触发止盈
        time.sleep(3)
        
        # 检查订单状态
        order_status = order_manager.get_order_status("profit_001")
        if order_status and order_status.status == OrderStatus.TRIGGERED:
            print("✅ 止盈订单触发成功")
            result = True
        else:
            print("❌ 止盈订单未触发")
            result = False
        
        order_manager.stop_monitoring()
        return result
        
    except Exception as e:
        print(f"❌ 止盈订单测试异常: {e}")
        return False


def test_iceberg_order():
    """测试冰山订单"""
    print("\n🔍 测试冰山订单...")
    
    try:
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 创建冰山订单
        iceberg_order = IcebergOrder(
            order_id="iceberg_001",
            symbol="SOL_USDT",
            side="buy",
            total_amount=1.0,  # 总数量
            price=150.0,
            slice_amount=0.2,  # 每片数量
            strategy_name="测试策略"
        )
        
        print(f"📝 创建冰山订单: 总量 {iceberg_order.amount}, 每片 {iceberg_order.slice_amount}")
        order_manager.add_order(iceberg_order)
        
        # 等待订单执行
        print("⏳ 等待冰山订单执行...")
        for i in range(10):
            time.sleep(1)
            
            # 模拟部分成交
            if i % 3 == 0 and iceberg_order.current_slice_id:
                iceberg_order.update_slice_fill(0.2)
                print(f"   第{iceberg_order.slice_count}片已成交")
            
            if iceberg_order.status == OrderStatus.FILLED:
                print("✅ 冰山订单完全成交")
                break
        
        order_manager.stop_monitoring()
        return iceberg_order.status == OrderStatus.FILLED
        
    except Exception as e:
        print(f"❌ 冰山订单测试异常: {e}")
        return False


def test_twap_order():
    """测试TWAP订单"""
    print("\n🔍 测试TWAP订单...")
    
    try:
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 创建TWAP订单
        twap_order = TWAPOrder(
            order_id="twap_001",
            symbol="BTC_USDT",
            side="buy",
            total_amount=0.01,  # 总数量
            duration_minutes=5,  # 5分钟执行完
            interval_minutes=1,  # 每分钟执行一次
            strategy_name="测试策略"
        )
        
        print(f"📝 创建TWAP订单: 总量 {twap_order.amount}, 分 {twap_order.total_slices} 片执行")
        order_manager.add_order(twap_order)
        
        # 等待订单执行
        print("⏳ 等待TWAP订单执行...")
        for i in range(8):
            time.sleep(1)
            
            if twap_order.executed_slices > 0:
                print(f"   已执行 {twap_order.executed_slices}/{twap_order.total_slices} 片")
            
            if twap_order.status == OrderStatus.FILLED:
                print("✅ TWAP订单执行完成")
                break
        
        order_manager.stop_monitoring()
        return twap_order.executed_slices > 0
        
    except Exception as e:
        print(f"❌ TWAP订单测试异常: {e}")
        return False


def test_conditional_order():
    """测试条件订单"""
    print("\n🔍 测试条件订单...")
    
    try:
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 创建条件订单
        conditional_order = ConditionalOrder(
            order_id="cond_001",
            symbol="ETH_USDT",
            side="buy",
            amount=0.1,
            condition_symbol="BTC_USDT",
            condition_operator=">",
            condition_value=52000.0,
            strategy_name="测试策略"
        )
        
        print(f"📝 创建条件订单: 当BTC > {conditional_order.condition_value}时买入ETH")
        order_manager.add_order(conditional_order)
        
        # 模拟条件触发
        order_manager.market_data["BTC_USDT"] = {'price': 51000.0}  # 条件未满足
        time.sleep(2)
        
        order_manager.market_data["BTC_USDT"] = {'price': 53000.0}  # 条件满足
        time.sleep(3)
        
        # 检查订单状态
        order_status = order_manager.get_order_status("cond_001")
        if order_status and order_status.status == OrderStatus.TRIGGERED:
            print("✅ 条件订单触发成功")
            result = True
        else:
            print("❌ 条件订单未触发")
            result = False
        
        order_manager.stop_monitoring()
        return result
        
    except Exception as e:
        print(f"❌ 条件订单测试异常: {e}")
        return False


def test_order_management():
    """测试订单管理功能"""
    print("\n🔍 测试订单管理功能...")
    
    try:
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = AdvancedOrderManager(engine)
        order_manager.start_monitoring()
        
        # 添加多个订单
        orders = [
            StopLossOrder("stop_002", "BTC_USDT", "sell", 0.001, 48000.0),
            TakeProfitOrder("profit_002", "ETH_USDT", "sell", 0.1, 3200.0),
            IcebergOrder("iceberg_002", "SOL_USDT", "buy", 0.5, 160.0, 0.1)
        ]
        
        for order in orders:
            order_manager.add_order(order)
        
        print(f"📝 添加了 {len(orders)} 个高级订单")
        
        # 检查活跃订单
        active_orders = order_manager.get_active_orders()
        print(f"📊 活跃订单数量: {len(active_orders)}")
        
        # 取消一个订单
        if active_orders:
            cancel_id = active_orders[0].order_id
            order_manager.cancel_order(cancel_id)
            print(f"❌ 已取消订单: {cancel_id}")
        
        # 再次检查
        active_orders = order_manager.get_active_orders()
        completed_orders = order_manager.get_completed_orders()
        
        print(f"📊 最终状态: 活跃 {len(active_orders)}, 已完成 {len(completed_orders)}")
        
        order_manager.stop_monitoring()
        
        return len(completed_orders) > 0
        
    except Exception as e:
        print(f"❌ 订单管理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 高级功能测试开始")
    print("=" * 80)
    
    # 测试WebSocket客户端
    websocket_success = test_websocket_client()
    
    # 测试实时数据管理器
    data_manager_success = test_real_time_data_manager()
    
    # 测试高级订单类型
    stop_loss_success = test_stop_loss_order()
    take_profit_success = test_take_profit_order()
    iceberg_success = test_iceberg_order()
    twap_success = test_twap_order()
    conditional_success = test_conditional_order()
    
    # 测试订单管理
    management_success = test_order_management()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"   WebSocket客户端: {'✅ 通过' if websocket_success else '❌ 失败'}")
    print(f"   实时数据管理器: {'✅ 通过' if data_manager_success else '❌ 失败'}")
    print(f"   止损订单: {'✅ 通过' if stop_loss_success else '❌ 失败'}")
    print(f"   止盈订单: {'✅ 通过' if take_profit_success else '❌ 失败'}")
    print(f"   冰山订单: {'✅ 通过' if iceberg_success else '❌ 失败'}")
    print(f"   TWAP订单: {'✅ 通过' if twap_success else '❌ 失败'}")
    print(f"   条件订单: {'✅ 通过' if conditional_success else '❌ 失败'}")
    print(f"   订单管理: {'✅ 通过' if management_success else '❌ 失败'}")
    
    # 计算总体成功率
    tests = [websocket_success, data_manager_success, stop_loss_success, 
             take_profit_success, iceberg_success, twap_success, 
             conditional_success, management_success]
    
    success_count = sum(tests)
    total_tests = len(tests)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 总体测试结果: {success_count}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎊 高级功能测试大部分通过!")
        print("💡 WebSocket连接和高级订单类型基本功能正常")
        print("🔗 系统具备了机构级交易功能")
        print("🛡️ 支持止损、止盈、冰山、TWAP等专业订单类型")
    else:
        print("\n⚠️ 部分高级功能测试失败")
        print("💡 建议检查网络连接和系统配置")
    
    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

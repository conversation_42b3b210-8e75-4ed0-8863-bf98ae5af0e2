{"data_mtime": 1748490878, "dep_lines": [10, 11, 83, 2, 4, 5, 6, 7, 8, 82, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 5, 10, 10, 10, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["pyparsing.unicode", "pyparsing.util", "pyparsing.core", "__future__", "copy", "re", "sys", "typing", "functools", "inspect", "builtins", "pprint", "os", "warnings", "operator", "html", "collections", "string", "itertools", "contextlib", "types", "traceback", "_frozen_importlib", "abc", "enum"], "hash": "cf2bef0a0baf70748139d74d6a0ee7e3ad761ce7", "id": "pyparsing.exceptions", "ignore_all": true, "interface_hash": "dc44c20de51ff1d6fa7c1f14e794a0a3416488a8", "mtime": 1748487517, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\exceptions.py", "plugin_data": null, "size": 9892, "suppressed": [], "version_id": "1.15.0"}
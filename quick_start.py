#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速启动脚本
Quick Start Script

一键检查、配置和启动交易系统
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("🚀" + "=" * 58 + "🚀")
    print("🚀  企业级现货交易系统 - 快速启动                      🚀")
    print("🚀  Enterprise Spot Trading System - Quick Start    🚀")
    print("🚀  版本: 1.0.0                                     🚀")
    print("🚀" + "=" * 58 + "🚀")

def check_system_status():
    """检查系统状态"""
    print("\n🔍 检查系统状态...")
    
    # 检查关键文件
    required_files = [
        'core/ultimate_trading_gui.py',
        'core/system_health_checker.py',
        'setup_trading_system.py',
        'config.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {', '.join(missing_files)}")
        return False
    
    # 检查配置
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️ 未找到环境配置文件")
        return False
    
    # 检查API配置
    with open(env_file, 'r', encoding='utf-8') as f:
        env_content = f.read()
    
    if 'your_api_key_here' in env_content:
        print("⚠️ API密钥未配置")
        return False
    
    print("✅ 系统状态检查通过")
    return True

def run_health_check():
    """运行健康检查"""
    print("\n🏥 运行系统健康检查...")
    
    try:
        result = subprocess.run([sys.executable, 'core/system_health_checker.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if "系统状态: HEALTHY" in result.stdout:
            print("✅ 系统健康状态: 优秀")
            return True
        elif "系统状态: WARNING" in result.stdout:
            print("⚠️ 系统健康状态: 良好 (有轻微警告)")
            return True
        else:
            print("❌ 系统健康状态: 需要关注")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🔗 测试API连接...")
    
    try:
        result = subprocess.run([sys.executable, 'core/api_connection_tester.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ API连接测试通过")
            return True
        else:
            print("❌ API连接测试失败")
            # 显示错误信息的最后几行
            if result.stdout:
                lines = result.stdout.split('\n')
                for line in lines[-5:]:
                    if line.strip():
                        print(f"  {line}")
            return False
            
    except Exception as e:
        print(f"❌ API连接测试异常: {e}")
        return False

def launch_trading_gui():
    """启动交易界面"""
    print("\n🚀 启动交易界面...")
    
    try:
        # 启动GUI
        subprocess.Popen([sys.executable, 'core/ultimate_trading_gui.py'])
        print("✅ 交易界面已启动")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_menu():
    """显示菜单"""
    print("\n📋 请选择操作:")
    print("1. 🔧 首次设置 (配置API密钥和交易参数)")
    print("2. 🚀 直接启动 (如果已配置)")
    print("3. 🔍 系统诊断 (检查系统状态)")
    print("4. 🔗 测试连接 (测试API连接)")
    print("5. 📊 查看状态 (显示系统信息)")
    print("6. ❌ 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-6): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6']:
                return choice
            else:
                print("❌ 无效选择，请输入1-6")
        except KeyboardInterrupt:
            return '6'

def handle_first_setup():
    """处理首次设置"""
    print("\n🔧 启动首次设置向导...")
    
    try:
        result = subprocess.run([sys.executable, 'setup_trading_system.py'], 
                              timeout=300)  # 5分钟超时
        
        if result.returncode == 0:
            print("✅ 设置完成")
            return True
        else:
            print("❌ 设置未完成")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 设置超时")
        return False
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        return False

def handle_direct_launch():
    """处理直接启动"""
    print("\n🚀 准备启动交易系统...")
    
    # 检查系统状态
    if not check_system_status():
        print("❌ 系统状态检查失败，请先运行首次设置")
        return False
    
    # 运行健康检查
    if not run_health_check():
        print("⚠️ 系统健康检查有警告，但仍可继续")
    
    # 测试API连接
    if not test_api_connection():
        print("⚠️ API连接测试失败，请检查网络和配置")
        choice = input("是否仍要启动? (y/n): ").strip().lower()
        if choice not in ['y', 'yes']:
            return False
    
    # 启动GUI
    return launch_trading_gui()

def handle_system_diagnosis():
    """处理系统诊断"""
    print("\n🔍 运行完整系统诊断...")
    
    # 系统状态检查
    system_ok = check_system_status()
    
    # 健康检查
    health_ok = run_health_check()
    
    # API连接测试
    api_ok = test_api_connection()
    
    print("\n📊 诊断结果总结:")
    print(f"{'✅' if system_ok else '❌'} 系统状态: {'正常' if system_ok else '异常'}")
    print(f"{'✅' if health_ok else '❌'} 健康检查: {'通过' if health_ok else '失败'}")
    print(f"{'✅' if api_ok else '❌'} API连接: {'正常' if api_ok else '异常'}")
    
    if system_ok and health_ok and api_ok:
        print("\n🎉 系统状态良好，可以正常使用！")
    elif system_ok and health_ok:
        print("\n⚠️ 系统基本正常，但API连接有问题")
    else:
        print("\n❌ 系统存在问题，建议运行首次设置")
    
    return True

def handle_connection_test():
    """处理连接测试"""
    print("\n🔗 运行API连接测试...")
    return test_api_connection()

def show_system_status():
    """显示系统状态"""
    print("\n📊 系统状态信息:")
    print("=" * 40)
    
    # Python环境
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 文件状态
    key_files = [
        ('交易界面', 'core/ultimate_trading_gui.py'),
        ('配置文件', 'config.json'),
        ('环境变量', '.env'),
        ('健康检查', 'core/system_health_checker.py')
    ]
    
    print("\n📁 关键文件状态:")
    for name, path in key_files:
        status = "✅" if Path(path).exists() else "❌"
        print(f"{status} {name}: {path}")
    
    # 配置状态
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        print("\n⚙️ 配置状态:")
        if 'your_api_key_here' in env_content:
            print("❌ API密钥: 未配置")
        else:
            print("✅ API密钥: 已配置")
        
        if 'EXCHANGE_SANDBOX=true' in env_content:
            print("🧪 交易模式: 沙盒模式")
        else:
            print("💰 交易模式: 实盘模式")
    
    print("\n💡 提示:")
    print("- 首次使用请选择 '首次设置'")
    print("- 配置完成后可选择 '直接启动'")
    print("- 遇到问题请选择 '系统诊断'")

def main():
    """主函数"""
    print_banner()
    
    while True:
        try:
            choice = show_menu()
            
            if choice == '1':
                handle_first_setup()
            elif choice == '2':
                if handle_direct_launch():
                    print("\n🎉 交易系统已启动！")
                    break
            elif choice == '3':
                handle_system_diagnosis()
            elif choice == '4':
                handle_connection_test()
            elif choice == '5':
                show_system_status()
            elif choice == '6':
                print("\n👋 再见！")
                break
            
            # 询问是否继续
            if choice != '6':
                continue_choice = input("\n是否继续使用菜单? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            continue

if __name__ == "__main__":
    main()

{".class": "MypyFile", "_fullname": "PIL.ImageOps", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExifTags": {".class": "SymbolTableNode", "cross_ref": "PIL.ExifTags", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImagePalette": {".class": "SymbolTableNode", "cross_ref": "PIL.ImagePalette", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SupportsGetMesh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get<PERSON>h", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.ImageOps.SupportsGetMesh", "name": "SupportsGetMesh", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "PIL.ImageOps.SupportsGetMesh", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "PIL.ImageOps", "mro": ["PIL.ImageOps.SupportsGetMesh", "builtins.object"], "names": {".class": "SymbolTable", "getmesh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "PIL.ImageOps.SupportsGetMesh.getmesh", "name": "get<PERSON>h", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["PIL.ImageOps.SupportsGetMesh", "PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmesh of SupportsGetMesh", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.ImageOps.SupportsGetMesh.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.ImageOps.SupportsGetMesh", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.ImageOps.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_border": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["border"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps._border", "name": "_border", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["border"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_border", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_color": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["color", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps._color", "name": "_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["color", "mode"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_color", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lut": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["image", "lut"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps._lut", "name": "_lut", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["image", "lut"], "arg_types": ["PIL.Image.Image", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lut", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "autocontrast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["image", "cutoff", "ignore", "mask", "preserve_tone"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.autocontrast", "name": "autocontrast", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["image", "cutoff", "ignore", "mask", "preserve_tone"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocontrast", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "colorize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["image", "black", "white", "mid", "blackpoint", "whitepoint", "midpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.colorize", "name": "colorize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["image", "black", "white", "mid", "blackpoint", "whitepoint", "midpoint"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorize", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["image", "size", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.contain", "name": "contain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["image", "size", "method"], "arg_types": ["PIL.Image.Image", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contain", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cover": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["image", "size", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.cover", "name": "cover", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["image", "size", "method"], "arg_types": ["PIL.Image.Image", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cover", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["image", "border"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.crop", "name": "crop", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["image", "border"], "arg_types": ["PIL.Image.Image", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "crop", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["image", "deformer", "resample"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.deform", "name": "deform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["image", "deformer", "resample"], "arg_types": ["PIL.Image.Image", "PIL.ImageOps.SupportsGetMesh", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deform", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "equalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.equalize", "name": "equalize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["image", "mask"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equalize", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exif_transpose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.exif_transpose", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "PIL.ImageOps.exif_transpose", "name": "exif_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": {".class": "UnionType", "items": ["PIL.Image.Image", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["image", "in_place"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PIL.ImageOps.exif_transpose", "name": "exif_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PIL.ImageOps.exif_transpose", "name": "exif_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PIL.ImageOps.exif_transpose", "name": "exif_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PIL.ImageOps.exif_transpose", "name": "exif_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["image", "in_place"], "arg_types": ["PIL.Image.Image", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exif_transpose", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "expand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["image", "border", "fill"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["image", "border", "fill"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["image", "size", "method", "bleed", "centering"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["image", "size", "method", "bleed", "centering"], "arg_types": ["PIL.Image.Image", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.flip", "name": "flip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": ["PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flip", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "grayscale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.grayscale", "name": "grayscale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": ["PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grayscale", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.invert", "name": "invert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": ["PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invert", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mirror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.mirror", "name": "mirror", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["image"], "arg_types": ["PIL.Image.Image"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mirror", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["image", "size", "method", "color", "centering"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.pad", "name": "pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["image", "size", "method", "color", "centering"], "arg_types": ["PIL.Image.Image", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "posterize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["image", "bits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.posterize", "name": "posterize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["image", "bits"], "arg_types": ["PIL.Image.Image", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "posterize", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "scale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["image", "factor", "resample"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.scale", "name": "scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["image", "factor", "resample"], "arg_types": ["PIL.Image.Image", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scale", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "solarize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["image", "threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.ImageOps.solarize", "name": "solarize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["image", "threshold"], "arg_types": ["PIL.Image.Image", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solarize", "ret_type": "PIL.Image.Image", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\PIL\\ImageOps.py"}
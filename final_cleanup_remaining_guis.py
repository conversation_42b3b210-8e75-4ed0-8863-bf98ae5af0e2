#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终清理剩余GUI文件脚本
Final Cleanup Remaining GUI Files Script

删除所有剩余的GUI选择器、启动器和失效的启动脚本
"""

import os
import shutil
from pathlib import Path

def final_cleanup_remaining_guis():
    """最终清理剩余的GUI相关文件"""
    
    print("🧹 最终清理剩余GUI文件...")
    print("📋 删除所有GUI选择器、启动器和失效启动脚本")
    print("=" * 60)
    
    # 定义要删除的GUI选择器和启动器
    gui_selectors_launchers = [
        'gui_selector.py',
        'quick_gui_launcher.py'
    ]
    
    # 定义要删除的失效启动脚本
    invalid_launch_scripts = [
        'launch_complete_optimized_system.py',
        'launch_trading_gui.py',
        'launch_fusion_gui.py',
        'launch_final_optimized_system.py',
        'launch_optimized_system.py',
        'launch_optimized_v2.py',
        'launch_ultimate_optimized_system.py'
    ]
    
    # 定义要删除的失效状态检查文件
    invalid_status_files = [
        'start_trading.py',
        'quick_status_check.py'
    ]
    
    # 定义要重命名的文件
    files_to_rename = {
        '启动终极版现货交易系统.py': 'start_ultimate_gui.py'
    }
    
    # 定义要检查并可能删除的文件
    files_to_check = [
        '启动企业级现货交易系统.bat',
        'start_enterprise_spot.py'
    ]
    
    current_dir = Path('.')
    removed_count = 0
    renamed_count = 0
    
    print("🗑️ 删除GUI选择器和启动器...")
    for file_name in gui_selectors_launchers:
        file_path = current_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {file_name} - {e}")
        else:
            print(f"   ℹ️ 文件不存在: {file_name}")
    
    print("\n🗑️ 删除失效的启动脚本...")
    for file_name in invalid_launch_scripts:
        file_path = current_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {file_name} - {e}")
        else:
            print(f"   ℹ️ 文件不存在: {file_name}")
    
    print("\n🗑️ 删除失效的状态检查文件...")
    for file_name in invalid_status_files:
        file_path = current_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {file_name} - {e}")
        else:
            print(f"   ℹ️ 文件不存在: {file_name}")
    
    print("\n📝 重命名文件...")
    for old_name, new_name in files_to_rename.items():
        old_path = current_dir / old_name
        new_path = current_dir / new_name
        if old_path.exists():
            try:
                old_path.rename(new_path)
                print(f"   ✅ 重命名: {old_name} -> {new_name}")
                renamed_count += 1
            except Exception as e:
                print(f"   ⚠️ 重命名失败: {old_name} - {e}")
        else:
            print(f"   ℹ️ 文件不存在: {old_name}")
    
    print("\n🔍 检查可疑文件...")
    for file_name in files_to_check:
        file_path = current_dir / file_name
        if file_path.exists():
            try:
                # 读取文件内容检查是否引用已删除的GUI
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 检查是否包含已删除的GUI引用
                deleted_guis = [
                    'spot_snowball_gui', 'ultimate_trading_gui', 'optimized_trading_gui',
                    'fusion_trading_gui', 'multi_pairs_gui', 'simple_gui',
                    'trading_system_gui', 'gate_simulation_gui'
                ]
                
                contains_deleted_refs = any(gui in content for gui in deleted_guis)
                
                if contains_deleted_refs:
                    print(f"   ❌ 发现失效引用，删除: {file_name}")
                    file_path.unlink()
                    removed_count += 1
                else:
                    print(f"   ✅ 文件正常，保留: {file_name}")
                    
            except Exception as e:
                print(f"   ⚠️ 检查失败: {file_name} - {e}")
        else:
            print(f"   ℹ️ 文件不存在: {file_name}")
    
    # 清理GUI评估结果文件
    print("\n🗑️ 清理GUI评估相关文件...")
    evaluation_files = [
        'gui_evaluation_results.json',
        'GUI_INTERFACE_EVALUATION_REPORT.md'
    ]
    
    for file_name in evaluation_files:
        file_path = current_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ❌ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ 删除失败: {file_name} - {e}")
    
    print(f"\n📊 清理统计:")
    print(f"   🗑️ 删除文件: {removed_count} 个")
    print(f"   📝 重命名文件: {renamed_count} 个")
    
    return removed_count, renamed_count

def create_simple_start_script():
    """创建简单的启动脚本"""
    
    print("\n📝 创建简化的启动脚本...")
    
    start_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极版现货交易系统启动脚本
Ultimate Spot Trading System Launcher
"""

import subprocess
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动终极版现货交易系统...")
    print("=" * 50)
    
    # 检查GUI文件是否存在
    gui_file = Path("core/ultimate_spot_trading_gui.py")
    if not gui_file.exists():
        print("❌ 错误: 找不到GUI文件")
        print(f"   期望位置: {gui_file}")
        return False
    
    try:
        # 启动GUI
        print("📱 正在启动GUI界面...")
        subprocess.Popen([sys.executable, str(gui_file)])
        print("✅ GUI界面已启动")
        print("💡 如果界面没有出现，请检查Python环境和依赖库")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('start_ultimate_gui.py', 'w', encoding='utf-8') as f:
            f.write(start_script_content)
        print("   ✅ 创建启动脚本: start_ultimate_gui.py")
        return True
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")
        return False

def verify_final_state():
    """验证最终状态"""
    
    print("\n🔍 验证最终系统状态...")
    print("-" * 40)
    
    # 检查核心文件
    core_files = [
        'core/ultimate_spot_trading_gui.py',
        'core/gate_api_connector.py',
        'core/api_login_dialog.py',
        'core/doubling_growth_engine.py',
        'core/fast_profit_engine.py'
    ]
    
    print("📁 核心文件检查:")
    all_core_present = True
    for file_path in core_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 缺失")
            all_core_present = False
    
    # 检查启动脚本
    print("\n🚀 启动脚本检查:")
    start_scripts = ['start_ultimate_gui.py']
    for script in start_scripts:
        if Path(script).exists():
            print(f"   ✅ {script}")
        else:
            print(f"   ❌ {script} - 缺失")
    
    # 检查是否还有其他GUI文件
    print("\n🔍 检查剩余GUI文件:")
    current_dir = Path('.')
    
    # 搜索可能的GUI文件
    gui_patterns = ['*gui*.py', '*launch*.py', '*start*.py']
    found_gui_files = []
    
    for pattern in gui_patterns:
        for file_path in current_dir.glob(pattern):
            if file_path.is_file():
                filename = file_path.name.lower()
                # 排除我们保留的文件
                if filename not in ['start_ultimate_gui.py', 'ultimate_spot_trading_gui.py']:
                    found_gui_files.append(str(file_path))
    
    if found_gui_files:
        print("   ⚠️ 发现剩余GUI相关文件:")
        for file_path in found_gui_files:
            print(f"      - {file_path}")
    else:
        print("   ✅ 没有发现其他GUI文件")
    
    # 测试导入
    print("\n🧪 测试核心模块导入:")
    try:
        import sys
        sys.path.insert(0, 'core')
        
        from ultimate_spot_trading_gui import UltimateSpotTradingGUI
        print("   ✅ ultimate_spot_trading_gui - 导入成功")
        
        from gate_api_connector import GateAPIConnector
        print("   ✅ gate_api_connector - 导入成功")
        
        from api_login_dialog import show_api_login_dialog
        print("   ✅ api_login_dialog - 导入成功")
        
    except Exception as e:
        print(f"   ❌ 导入测试失败: {e}")
        all_core_present = False
    
    if all_core_present and not found_gui_files:
        print("\n🎉 系统状态验证通过!")
        print("🚀 可以使用以下命令启动系统:")
        print("   python core/ultimate_spot_trading_gui.py")
        print("   python start_ultimate_gui.py")
        return True
    else:
        print("\n⚠️ 系统状态验证未完全通过")
        return False

def main():
    """主函数"""
    print("🧹 最终清理剩余GUI文件")
    print("=" * 60)
    print("⚠️ 此操作将删除所有剩余的GUI选择器和失效启动脚本")
    print("📋 只保留 ultimate_spot_trading_gui.py 作为唯一GUI界面")
    
    # 确认操作
    response = input("\n是否继续最终清理？(y/N): ").lower().strip()
    if response != 'y':
        print("❌ 清理操作已取消")
        return False
    
    try:
        # 执行清理
        removed_count, renamed_count = final_cleanup_remaining_guis()
        
        # 创建简单启动脚本
        create_simple_start_script()
        
        # 验证最终状态
        verify_success = verify_final_state()
        
        print(f"\n🎉 最终清理完成!")
        print(f"📊 删除了 {removed_count} 个文件，重命名了 {renamed_count} 个文件")
        
        if verify_success:
            print("\n🚀 系统已完全清理并准备就绪!")
            print("💡 现在只有一个GUI界面: ultimate_spot_trading_gui.py")
            print("🎯 启动命令: python core/ultimate_spot_trading_gui.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()

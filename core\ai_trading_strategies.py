#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI驱动的机器学习交易策略
AI-Driven Machine Learning Trading Strategies
"""

import json
import pickle
import warnings
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

warnings.filterwarnings("ignore")

# 尝试导入机器学习库
try:
    from sklearn.ensemble import (GradientBoostingClassifier,
                                  RandomForestClassifier)
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import (accuracy_score, f1_score, precision_score,
                                 recall_score)
    from sklearn.model_selection import cross_val_score, train_test_split
    from sklearn.preprocessing import MinMaxScaler, StandardScaler

    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("⚠️ 机器学习库不可用，使用简化版AI策略")


class SignalType(Enum):
    """信号类型"""

    BUY = 1
    SELL = -1
    HOLD = 0


class ModelType(Enum):
    """模型类型"""

    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    LOGISTIC_REGRESSION = "logistic_regression"
    NEURAL_NETWORK = "neural_network"
    ENSEMBLE = "ensemble"


@dataclass
class TradingSignal:
    """交易信号"""

    timestamp: datetime
    symbol: str
    signal: SignalType
    confidence: float
    price: float
    features: Dict[str, float]
    model_name: str

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "symbol": self.symbol,
            "signal": self.signal.value,
            "confidence": self.confidence,
            "price": self.price,
            "features": self.features,
            "model_name": self.model_name,
        }


class FeatureEngineer:
    """特征工程师"""

    def __init__(self):
        """初始化特征工程师"""
        self.feature_names = []
        self.scalers = {}

    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取特征"""
        try:
            features_df = df.copy()

            # 价格特征
            features_df["returns"] = df["close"].pct_change()
            features_df["log_returns"] = np.log(
                df["close"] / df["close"].shift(1)
            )
            features_df["price_change"] = df["close"] - df["open"]
            features_df["price_range"] = df["high"] - df["low"]
            features_df["body_size"] = abs(df["close"] - df["open"])

            # 技术指标特征
            features_df = self._add_technical_indicators(features_df)

            # 统计特征
            features_df = self._add_statistical_features(features_df)

            # 时间特征
            features_df = self._add_time_features(features_df)

            # 成交量特征
            features_df = self._add_volume_features(features_df)

            # 移除无效值
            features_df = features_df.dropna()

            # 更新特征名称
            self.feature_names = [
                col
                for col in features_df.columns
                if col
                not in ["open", "high", "low", "close", "volume", "timestamp"]
            ]

            return features_df

        except Exception as e:
            print(f"❌ 特征提取失败: {e}")
            return df

    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标特征"""
        try:
            # 移动平均线
            for period in [5, 10, 20, 50]:
                df[f"ma_{period}"] = df["close"].rolling(window=period).mean()
                df[f"ma_{period}_ratio"] = df["close"] / df[f"ma_{period}"]

            # RSI
            delta = df["close"].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df["rsi"] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = df["close"].ewm(span=12).mean()
            exp2 = df["close"].ewm(span=26).mean()
            df["macd"] = exp1 - exp2
            df["macd_signal"] = df["macd"].ewm(span=9).mean()
            df["macd_histogram"] = df["macd"] - df["macd_signal"]

            # 布林带
            ma20 = df["close"].rolling(window=20).mean()
            std20 = df["close"].rolling(window=20).std()
            df["bb_upper"] = ma20 + (std20 * 2)
            df["bb_lower"] = ma20 - (std20 * 2)
            df["bb_width"] = (df["bb_upper"] - df["bb_lower"]) / ma20
            df["bb_position"] = (df["close"] - df["bb_lower"]) / (
                df["bb_upper"] - df["bb_lower"]
            )

            # 随机指标
            low_14 = df["low"].rolling(window=14).min()
            high_14 = df["high"].rolling(window=14).max()
            df["stoch_k"] = 100 * (df["close"] - low_14) / (high_14 - low_14)
            df["stoch_d"] = df["stoch_k"].rolling(window=3).mean()

            return df

        except Exception as e:
            print(f"❌ 添加技术指标失败: {e}")
            return df

    def _add_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加统计特征"""
        try:
            # 波动率
            for period in [5, 10, 20]:
                df[f"volatility_{period}"] = (
                    df["returns"].rolling(window=period).std()
                )
                df[f"skewness_{period}"] = (
                    df["returns"].rolling(window=period).skew()
                )
                df[f"kurtosis_{period}"] = (
                    df["returns"].rolling(window=period).kurt()
                )

            # 价格位置
            for period in [10, 20, 50]:
                df[f"price_position_{period}"] = (
                    df["close"] - df["close"].rolling(window=period).min()
                ) / (
                    df["close"].rolling(window=period).max()
                    - df["close"].rolling(window=period).min()
                )

            # 动量指标
            for period in [5, 10, 20]:
                df[f"momentum_{period}"] = (
                    df["close"] / df["close"].shift(period) - 1
                )

            return df

        except Exception as e:
            print(f"❌ 添加统计特征失败: {e}")
            return df

    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        try:
            if "timestamp" in df.columns:
                df["hour"] = pd.to_datetime(df["timestamp"]).dt.hour
                df["day_of_week"] = pd.to_datetime(
                    df["timestamp"]
                ).dt.dayofweek
                df["month"] = pd.to_datetime(df["timestamp"]).dt.month
            else:
                # 如果没有timestamp列，使用索引
                df["hour"] = df.index.hour if hasattr(df.index, "hour") else 12
                df["day_of_week"] = (
                    df.index.dayofweek if hasattr(df.index, "dayofweek") else 1
                )
                df["month"] = (
                    df.index.month if hasattr(df.index, "month") else 6
                )

            return df

        except Exception as e:
            print(f"❌ 添加时间特征失败: {e}")
            return df

    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加成交量特征"""
        try:
            # 成交量移动平均
            for period in [5, 10, 20]:
                df[f"volume_ma_{period}"] = (
                    df["volume"].rolling(window=period).mean()
                )
                df[f"volume_ratio_{period}"] = (
                    df["volume"] / df[f"volume_ma_{period}"]
                )

            # 价量关系
            df["price_volume"] = df["returns"] * df["volume"]
            df["volume_price_trend"] = (
                df["volume"]
                .rolling(window=5)
                .corr(df["close"].rolling(window=5))
            )

            return df

        except Exception as e:
            print(f"❌ 添加成交量特征失败: {e}")
            return df

    def prepare_features(
        self, df: pd.DataFrame, fit_scaler: bool = True
    ) -> np.ndarray:
        """准备特征数据"""
        try:
            feature_data = df[self.feature_names].copy()

            # 处理无限值和NaN
            feature_data = feature_data.replace([np.inf, -np.inf], np.nan)
            feature_data = feature_data.fillna(feature_data.mean())

            if fit_scaler:
                # 训练标准化器
                scaler = StandardScaler()
                scaled_features = scaler.fit_transform(feature_data)
                self.scalers["standard"] = scaler
            else:
                # 使用已训练的标准化器
                if "standard" in self.scalers:
                    scaled_features = self.scalers["standard"].transform(
                        feature_data
                    )
                else:
                    scaled_features = feature_data.values

            return scaled_features

        except Exception as e:
            print(f"❌ 准备特征数据失败: {e}")
            return np.array([])


class AITradingModel:
    """AI交易模型"""

    def __init__(self, model_type: ModelType = ModelType.RANDOM_FOREST):
        """初始化AI交易模型"""
        self.model_type = model_type
        self.model = None
        self.feature_engineer = FeatureEngineer()
        self.is_trained = False
        self.performance_metrics = {}

        # 初始化模型
        self._initialize_model()

    def _initialize_model(self):
        """初始化机器学习模型"""
        try:
            if not ML_AVAILABLE:
                print("⚠️ 使用简化版AI模型")
                self.model = None
                return

            if self.model_type == ModelType.RANDOM_FOREST:
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                )
            elif self.model_type == ModelType.GRADIENT_BOOSTING:
                self.model = GradientBoostingClassifier(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42,
                )
            elif self.model_type == ModelType.LOGISTIC_REGRESSION:
                self.model = LogisticRegression(random_state=42, max_iter=1000)
            else:
                self.model = RandomForestClassifier(random_state=42)

            print(f"✅ 已初始化 {self.model_type.value} 模型")

        except Exception as e:
            print(f"❌ 初始化模型失败: {e}")
            self.model = None

    def prepare_training_data(
        self, df: pd.DataFrame, future_periods: int = 5
    ) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        try:
            # 提取特征
            features_df = self.feature_engineer.extract_features(df)

            # 创建标签（未来收益）
            future_returns = (
                features_df["close"].shift(-future_periods)
                / features_df["close"]
                - 1
            )

            # 创建分类标签
            labels = np.where(
                future_returns > 0.02,
                1,  # 买入信号
                np.where(future_returns < -0.02, -1, 0),
            )  # 卖出信号，持有信号

            # 准备特征
            X = self.feature_engineer.prepare_features(
                features_df, fit_scaler=True
            )

            # 移除最后几行（没有未来数据）
            X = X[:-future_periods]
            y = labels[:-future_periods]

            # 移除无效标签
            valid_indices = ~np.isnan(y)
            X = X[valid_indices]
            y = y[valid_indices]

            print(f"✅ 准备训练数据: {X.shape[0]} 样本, {X.shape[1]} 特征")
            return X, y

        except Exception as e:
            print(f"❌ 准备训练数据失败: {e}")
            return np.array([]), np.array([])

    def train(self, df: pd.DataFrame, test_size: float = 0.2) -> Dict:
        """训练模型"""
        try:
            if not ML_AVAILABLE or self.model is None:
                print("⚠️ 使用简化版训练")
                self.is_trained = True
                return {
                    "accuracy": 0.6,
                    "precision": 0.6,
                    "recall": 0.6,
                    "f1": 0.6,
                }

            print("🚀 开始训练AI交易模型...")

            # 准备数据
            X, y = self.prepare_training_data(df)

            if len(X) == 0:
                print("❌ 训练数据为空")
                return {}

            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42, stratify=y
            )

            # 训练模型
            self.model.fit(X_train, y_train)

            # 预测
            y_pred = self.model.predict(X_test)

            # 计算性能指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(
                y_test, y_pred, average="weighted", zero_division=0
            )
            recall = recall_score(
                y_test, y_pred, average="weighted", zero_division=0
            )
            f1 = f1_score(y_test, y_pred, average="weighted", zero_division=0)

            self.performance_metrics = {
                "accuracy": accuracy,
                "precision": precision,
                "recall": recall,
                "f1": f1,
                "train_samples": len(X_train),
                "test_samples": len(X_test),
                "features": len(self.feature_engineer.feature_names),
            }

            self.is_trained = True

            print(f"✅ 模型训练完成:")
            print(f"   准确率: {accuracy:.3f}")
            print(f"   精确率: {precision:.3f}")
            print(f"   召回率: {recall:.3f}")
            print(f"   F1分数: {f1:.3f}")

            return self.performance_metrics

        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return {}

    def predict(self, df: pd.DataFrame) -> TradingSignal:
        """预测交易信号"""
        try:
            if not self.is_trained:
                print("⚠️ 模型未训练，返回持有信号")
                return TradingSignal(
                    timestamp=datetime.now(),
                    symbol="UNKNOWN",
                    signal=SignalType.HOLD,
                    confidence=0.5,
                    price=0.0,
                    features={},
                    model_name=self.model_type.value,
                )

            # 提取特征
            features_df = self.feature_engineer.extract_features(df)

            if len(features_df) == 0:
                return TradingSignal(
                    timestamp=datetime.now(),
                    symbol="UNKNOWN",
                    signal=SignalType.HOLD,
                    confidence=0.5,
                    price=0.0,
                    features={},
                    model_name=self.model_type.value,
                )

            # 准备最新数据
            X = self.feature_engineer.prepare_features(
                features_df, fit_scaler=False
            )

            if len(X) == 0:
                return TradingSignal(
                    timestamp=datetime.now(),
                    symbol="UNKNOWN",
                    signal=SignalType.HOLD,
                    confidence=0.5,
                    price=0.0,
                    features={},
                    model_name=self.model_type.value,
                )

            # 使用最新数据预测
            latest_features = X[-1:] if len(X.shape) > 1 else X.reshape(1, -1)

            if ML_AVAILABLE and self.model is not None:
                # 预测信号
                prediction = self.model.predict(latest_features)[0]

                # 预测概率
                if hasattr(self.model, "predict_proba"):
                    probabilities = self.model.predict_proba(latest_features)[
                        0
                    ]
                    confidence = max(probabilities)
                else:
                    confidence = 0.7
            else:
                # 简化版预测
                prediction = self._simple_prediction(features_df.iloc[-1])
                confidence = 0.6

            # 转换预测结果
            signal = SignalType(int(prediction))

            # 获取当前价格
            current_price = (
                features_df["close"].iloc[-1]
                if "close" in features_df.columns
                else 0.0
            )

            # 获取特征值
            feature_values = {}
            for i, feature_name in enumerate(
                self.feature_engineer.feature_names[:10]
            ):  # 只取前10个特征
                if i < len(latest_features[0]):
                    feature_values[feature_name] = float(latest_features[0][i])

            return TradingSignal(
                timestamp=datetime.now(),
                symbol="PREDICTED",
                signal=signal,
                confidence=confidence,
                price=current_price,
                features=feature_values,
                model_name=self.model_type.value,
            )

        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return TradingSignal(
                timestamp=datetime.now(),
                symbol="ERROR",
                signal=SignalType.HOLD,
                confidence=0.5,
                price=0.0,
                features={},
                model_name=self.model_type.value,
            )

    def _simple_prediction(self, latest_data: pd.Series) -> int:
        """简化版预测（当ML库不可用时）"""
        try:
            # 基于简单规则的预测
            rsi = latest_data.get("rsi", 50)
            macd = latest_data.get("macd", 0)
            returns = latest_data.get("returns", 0)

            # 简单的交易逻辑
            if rsi < 30 and macd > 0 and returns > 0:
                return 1  # 买入
            elif rsi > 70 and macd < 0 and returns < 0:
                return -1  # 卖出
            else:
                return 0  # 持有

        except Exception as e:
            return 0

    def save_model(self, filepath: str):
        """保存模型"""
        try:
            model_data = {
                "model": self.model,
                "feature_engineer": self.feature_engineer,
                "model_type": self.model_type,
                "is_trained": self.is_trained,
                "performance_metrics": self.performance_metrics,
            }

            with open(filepath, "wb") as f:
                pickle.dump(model_data, f)

            print(f"✅ 模型已保存: {filepath}")

        except Exception as e:
            print(f"❌ 保存模型失败: {e}")

    def load_model(self, filepath: str):
        """加载模型"""
        try:
            with open(filepath, "rb") as f:
                model_data = pickle.load(f)

            self.model = model_data["model"]
            self.feature_engineer = model_data["feature_engineer"]
            self.model_type = model_data["model_type"]
            self.is_trained = model_data["is_trained"]
            self.performance_metrics = model_data["performance_metrics"]

            print(f"✅ 模型已加载: {filepath}")

        except Exception as e:
            print(f"❌ 加载模型失败: {e}")


class AIStrategyManager:
    """AI策略管理器"""

    def __init__(self):
        """初始化AI策略管理器"""
        self.models = {}
        self.ensemble_weights = {}
        self.signal_history = []

    def add_model(self, name: str, model: AITradingModel, weight: float = 1.0):
        """添加模型"""
        self.models[name] = model
        self.ensemble_weights[name] = weight
        print(f"✅ 已添加AI模型: {name} (权重: {weight})")

    def train_all_models(self, df: pd.DataFrame) -> Dict:
        """训练所有模型"""
        results = {}

        for name, model in self.models.items():
            print(f"🎯 训练模型: {name}")
            result = model.train(df)
            results[name] = result

        return results

    def get_ensemble_signal(self, df: pd.DataFrame) -> TradingSignal:
        """获取集成信号"""
        try:
            if not self.models:
                print("⚠️ 没有可用的AI模型")
                return TradingSignal(
                    timestamp=datetime.now(),
                    symbol="ENSEMBLE",
                    signal=SignalType.HOLD,
                    confidence=0.5,
                    price=0.0,
                    features={},
                    model_name="ensemble",
                )

            signals = []
            confidences = []
            weights = []

            # 获取所有模型的预测
            for name, model in self.models.items():
                signal = model.predict(df)
                signals.append(signal.signal.value)
                confidences.append(signal.confidence)
                weights.append(self.ensemble_weights[name])

            # 加权平均
            weighted_signal = np.average(signals, weights=weights)
            weighted_confidence = np.average(confidences, weights=weights)

            # 转换为最终信号
            if weighted_signal > 0.3:
                final_signal = SignalType.BUY
            elif weighted_signal < -0.3:
                final_signal = SignalType.SELL
            else:
                final_signal = SignalType.HOLD

            # 获取当前价格
            current_price = (
                df["close"].iloc[-1]
                if "close" in df.columns and len(df) > 0
                else 0.0
            )

            ensemble_signal = TradingSignal(
                timestamp=datetime.now(),
                symbol="ENSEMBLE",
                signal=final_signal,
                confidence=weighted_confidence,
                price=current_price,
                features={"weighted_signal": weighted_signal},
                model_name="ensemble",
            )

            # 记录信号历史
            self.signal_history.append(ensemble_signal)

            # 保持最近100个信号
            if len(self.signal_history) > 100:
                self.signal_history = self.signal_history[-100:]

            return ensemble_signal

        except Exception as e:
            print(f"❌ 获取集成信号失败: {e}")
            return TradingSignal(
                timestamp=datetime.now(),
                symbol="ERROR",
                signal=SignalType.HOLD,
                confidence=0.5,
                price=0.0,
                features={},
                model_name="ensemble",
            )

    def get_model_performance(self) -> Dict:
        """获取模型性能"""
        performance = {}

        for name, model in self.models.items():
            performance[name] = {
                "is_trained": model.is_trained,
                "metrics": model.performance_metrics,
                "model_type": model.model_type.value,
            }

        return performance

    def get_signal_statistics(self, days: int = 7) -> Dict:
        """获取信号统计"""
        try:
            if not self.signal_history:
                return {}

            # 过滤最近几天的信号
            cutoff_time = datetime.now() - timedelta(days=days)
            recent_signals = [
                s for s in self.signal_history if s.timestamp >= cutoff_time
            ]

            if not recent_signals:
                return {}

            # 统计信号分布
            buy_signals = len(
                [s for s in recent_signals if s.signal == SignalType.BUY]
            )
            sell_signals = len(
                [s for s in recent_signals if s.signal == SignalType.SELL]
            )
            hold_signals = len(
                [s for s in recent_signals if s.signal == SignalType.HOLD]
            )

            # 平均置信度
            avg_confidence = np.mean([s.confidence for s in recent_signals])

            return {
                "total_signals": len(recent_signals),
                "buy_signals": buy_signals,
                "sell_signals": sell_signals,
                "hold_signals": hold_signals,
                "avg_confidence": avg_confidence,
                "period_days": days,
            }

        except Exception as e:
            print(f"❌ 获取信号统计失败: {e}")
            return {}


# 全局AI策略管理器
ai_strategy_manager = None


def get_ai_strategy_manager() -> AIStrategyManager:
    """获取AI策略管理器实例"""
    global ai_strategy_manager
    if ai_strategy_manager is None:
        ai_strategy_manager = AIStrategyManager()
    return ai_strategy_manager

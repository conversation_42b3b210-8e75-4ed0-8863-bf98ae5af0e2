#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新布局测试脚本
New Layout Test Script

测试专业版GUI的全新三栏布局设计
"""

import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_new_layout():
    """测试新的三栏布局"""
    print("\n🎨 测试新的三栏布局...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        print("  ✅ 创建GUI实例...")
        gui = ProfessionalTradingGUI()
        
        # 检查三栏布局组件
        layout_components = {
            "左侧面板": hasattr(gui, 'left_panel'),
            "中央面板": hasattr(gui, 'center_panel'),
            "右侧面板": hasattr(gui, 'right_panel'),
            "上部数据框": hasattr(gui, 'upper_data_frame'),
            "下部数据框": hasattr(gui, 'lower_data_frame'),
        }
        
        print("  ✅ 布局组件检查:")
        for component, exists in layout_components.items():
            status = "✅" if exists else "❌"
            print(f"    {status} {component}")
        
        # 检查新的面板组件
        panel_components = {
            "连接状态标签": hasattr(gui, 'connection_status_label'),
            "交易状态标签": hasattr(gui, 'trading_status_label'),
            "连接按钮": hasattr(gui, 'connect_btn'),
            "断开连接按钮": hasattr(gui, 'disconnect_btn'),
            "系统指标": hasattr(gui, 'system_metrics'),
        }
        
        print("  ✅ 面板组件检查:")
        for component, exists in panel_components.items():
            status = "✅" if exists else "❌"
            print(f"    {status} {component}")
        
        # 检查窗口属性
        window_props = {
            "标题": gui.root.title(),
            "几何": gui.root.geometry(),
            "背景": gui.root.cget("bg"),
        }
        
        print("  ✅ 窗口属性:")
        for prop, value in window_props.items():
            print(f"    • {prop}: {value}")
        
        # 关闭GUI
        gui.root.destroy()
        
        return all(layout_components.values()) and all(panel_components.values())
        
    except Exception as e:
        print(f"❌ 新布局测试失败: {e}")
        return False


def test_layout_structure():
    """测试布局结构"""
    print("\n🏗️ 测试布局结构...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试布局比例
        print("  ✅ 布局比例测试:")
        
        # 检查面板宽度设置
        left_width = gui.left_panel.cget("width")
        right_width = gui.right_panel.cget("width")
        
        print(f"    • 左侧面板宽度: {left_width}px")
        print(f"    • 右侧面板宽度: {right_width}px")
        print(f"    • 中央面板: 自适应扩展")
        
        # 检查面板背景色
        left_bg = gui.left_panel.cget("bg")
        center_bg = gui.center_panel.cget("bg")
        right_bg = gui.right_panel.cget("bg")
        
        print("  ✅ 面板配色:")
        print(f"    • 左侧面板: {left_bg}")
        print(f"    • 中央面板: {center_bg}")
        print(f"    • 右侧面板: {right_bg}")
        
        # 检查上下分割
        if hasattr(gui, 'upper_data_frame') and hasattr(gui, 'lower_data_frame'):
            upper_height = gui.upper_data_frame.cget("height")
            lower_height = gui.lower_data_frame.cget("height")
            
            print("  ✅ 中央面板分割:")
            print(f"    • 上部数据区: {upper_height}px (图表和市场数据)")
            print(f"    • 下部数据区: {lower_height}px (持仓和订单)")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 布局结构测试失败: {e}")
        return False


def test_panel_content():
    """测试面板内容"""
    print("\n📋 测试面板内容...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试左侧面板内容
        print("  ✅ 左侧控制面板内容:")
        left_content = [
            "系统状态面板",
            "连接控制面板", 
            "交易模式面板",
            "系统监控面板"
        ]
        
        for content in left_content:
            print(f"    • {content}")
        
        # 测试中央面板内容
        print("  ✅ 中央数据面板内容:")
        center_content = [
            "上部: 图表和市场数据 (70%)",
            "下部: 持仓和订单管理 (30%)"
        ]
        
        for content in center_content:
            print(f"    • {content}")
        
        # 测试右侧面板内容
        print("  ✅ 右侧交易面板内容:")
        right_content = [
            "账户信息摘要",
            "增强快速交易",
            "增强风险管理"
        ]
        
        for content in right_content:
            print(f"    • {content}")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 面板内容测试失败: {e}")
        return False


def test_visual_improvements():
    """测试视觉改进"""
    print("\n👁️ 测试视觉改进...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试配色方案
        colors = gui.PROFESSIONAL_COLORS
        
        print("  ✅ 配色方案:")
        key_colors = {
            "主背景": colors["bg_primary"],
            "次级背景": colors["bg_secondary"],
            "面板背景": colors["bg_panel"],
            "主要文字": colors["text_primary"],
            "强调文字": colors["text_accent"],
        }
        
        for name, color in key_colors.items():
            print(f"    • {name}: {color}")
        
        # 测试字体系统
        fonts = gui.PROFESSIONAL_FONTS
        
        print("  ✅ 字体系统:")
        key_fonts = {
            "标题字体": fonts["title"],
            "标题字体": fonts["heading"],
            "正文字体": fonts["body"],
            "按钮字体": fonts["button"],
            "标签字体": fonts["label"],
        }
        
        for name, font in key_fonts.items():
            print(f"    • {name}: {font}")
        
        # 测试布局间距
        print("  ✅ 布局间距:")
        spacing_info = [
            "面板间距: 2-3px (紧凑布局)",
            "组件内边距: 10px (舒适间距)",
            "按钮间距: 2-5px (清晰分离)",
            "滚动条: 自动显示 (内容溢出时)"
        ]
        
        for info in spacing_info:
            print(f"    • {info}")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 视觉改进测试失败: {e}")
        return False


def test_responsiveness():
    """测试响应性"""
    print("\n📱 测试响应性...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试滚动功能
        print("  ✅ 滚动功能:")
        scroll_areas = [
            "左侧面板: 垂直滚动 (内容过多时)",
            "右侧面板: 垂直滚动 (内容过多时)",
            "中央面板: 自适应扩展",
            "数据表格: 内置滚动条"
        ]
        
        for area in scroll_areas:
            print(f"    • {area}")
        
        # 测试自适应布局
        print("  ✅ 自适应布局:")
        adaptive_features = [
            "中央面板: 自动扩展填充剩余空间",
            "左右面板: 固定宽度 (350px)",
            "上下分割: 固定比例 (70%/30%)",
            "组件对齐: 自动对齐和填充"
        ]
        
        for feature in adaptive_features:
            print(f"    • {feature}")
        
        # 测试窗口缩放
        print("  ✅ 窗口缩放:")
        scaling_info = [
            "最小窗口: 1200x800 (保证可用性)",
            "推荐窗口: 1600x1000 (最佳体验)",
            "最大窗口: 无限制 (自适应扩展)",
            "比例保持: 25% + 50% + 25% (左中右)"
        ]
        
        for info in scaling_info:
            print(f"    • {info}")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 响应性测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🔍 开始新布局验证测试...")
    print("=" * 60)
    
    tests = [
        ("新布局测试", test_new_layout),
        ("布局结构测试", test_layout_structure),
        ("面板内容测试", test_panel_content),
        ("视觉改进测试", test_visual_improvements),
        ("响应性测试", test_responsiveness),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计:")
    print(f"  • 总测试数: {total}")
    print(f"  • 通过数: {passed}")
    print(f"  • 失败数: {total - passed}")
    print(f"  • 通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！新布局验证成功！")
        print("✨ 专业版GUI已成功重新排版为现代化三栏布局！")
        return True
    else:
        print(f"\n⚠️ {total - passed}个测试失败，需要进一步改进。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

{"data_mtime": 1748494443, "dep_lines": [16, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30], "dependencies": ["core.ui.charts.candlestick_chart", "core.ui.theme_manager", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "ed26df033e7dc73b014f898db68876ed22b4a3d9", "id": "core.ui", "ignore_all": true, "interface_hash": "4f71affd045db75e6f7aa474e4911540b62e9684", "mtime": 1748494355, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\ui\\__init__.py", "plugin_data": null, "size": 606, "suppressed": [], "version_id": "1.15.0"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强配置管理器
Enhanced Configuration Manager

提供无硬编码的配置管理功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
from decimal import Decimal

# 导入常量
try:
    from ..constants import (
        AppConstants, StyleConstants, TradingConstants, 
        MessageConstants, PathConstants
    )
except ImportError:
    # 如果常量模块不可用，使用默认值
    class AppConstants:
        DEFAULT_INITIAL_CAPITAL = 10000.0
        DEFAULT_UPDATE_INTERVAL = 1.0
    
    class PathConstants:
        CONFIG_DIR = Path("config")
        MAIN_CONFIG_FILE = Path("config/config.json")

# 导入基础配置管理器
try:
    from .config_manager import ConfigManager
except ImportError:
    # 简化的基础配置管理器
    class ConfigManager:
        def __init__(self):
            self.config = {}


logger = logging.getLogger(__name__)


class EnhancedConfigManager(ConfigManager):
    """增强的配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化增强配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        # 使用常量定义的默认配置文件
        if config_file is None:
            config_file = str(PathConstants.MAIN_CONFIG_FILE)
        
        super().__init__(config_file)
        
        # 加载常量配置
        self.load_constants_config()
        
        # 加载环境变量配置
        self.load_environment_config()
        
        # 验证配置
        self.validate_all_config()
    
    def load_constants_config(self):
        """从常量加载默认配置"""
        try:
            # 应用程序配置
            app_config = {
                "app_name": AppConstants.APP_NAME,
                "app_version": AppConstants.APP_VERSION,
                "default_language": AppConstants.DEFAULT_LANGUAGE,
                "default_theme": AppConstants.DEFAULT_THEME,
                "debug_mode": AppConstants.DEBUG_MODE
            }
            
            # 窗口配置
            window_config = {
                "width": AppConstants.DEFAULT_WINDOW_WIDTH,
                "height": AppConstants.DEFAULT_WINDOW_HEIGHT,
                "min_width": AppConstants.MIN_WINDOW_WIDTH,
                "min_height": AppConstants.MIN_WINDOW_HEIGHT,
                "x": AppConstants.DEFAULT_WINDOW_X,
                "y": AppConstants.DEFAULT_WINDOW_Y
            }
            
            # 更新间隔配置
            update_config = {
                "default_interval": AppConstants.DEFAULT_UPDATE_INTERVAL,
                "market_data_interval": AppConstants.MARKET_DATA_REFRESH_INTERVAL,
                "account_data_interval": AppConstants.ACCOUNT_DATA_REFRESH_INTERVAL,
                "position_data_interval": AppConstants.POSITION_DATA_REFRESH_INTERVAL,
                "performance_interval": AppConstants.PERFORMANCE_UPDATE_INTERVAL
            }
            
            # 网络配置
            network_config = {
                "timeout": AppConstants.DEFAULT_TIMEOUT,
                "api_timeout": AppConstants.API_TIMEOUT,
                "max_retries": AppConstants.MAX_RETRY_ATTEMPTS,
                "retry_delay": AppConstants.RETRY_DELAY_BASE,
                "rate_limit": AppConstants.RATE_LIMIT_PER_MINUTE
            }
            
            # 交易配置
            trading_config = {
                "initial_capital": float(TradingConstants.DEFAULT_INITIAL_CAPITAL),
                "risk_per_trade": float(TradingConstants.DEFAULT_RISK_PER_TRADE),
                "stop_loss_pct": float(TradingConstants.DEFAULT_STOP_LOSS_PCT),
                "take_profit_pct": float(TradingConstants.DEFAULT_TAKE_PROFIT_PCT),
                "max_positions": TradingConstants.DEFAULT_MAX_POSITIONS,
                "position_size_pct": float(TradingConstants.DEFAULT_POSITION_SIZE_PCT)
            }
            
            # 风险管理配置
            risk_config = {
                "max_drawdown": float(TradingConstants.DEFAULT_DRAWDOWN_LIMIT),
                "correlation_limit": float(TradingConstants.DEFAULT_CORRELATION_LIMIT),
                "var_confidence": float(TradingConstants.DEFAULT_VAR_CONFIDENCE),
                "volatility_threshold": float(TradingConstants.MEDIUM_VOLATILITY_THRESHOLD)
            }
            
            # 样式配置
            style_config = {
                "font_family": StyleConstants.FONT_FAMILY_SANS,
                "font_size": StyleConstants.FONT_SIZE_NORMAL,
                "padding": StyleConstants.PADDING_MEDIUM,
                "spacing": StyleConstants.SPACING_MEDIUM,
                "button_height": StyleConstants.BUTTON_HEIGHT_MEDIUM
            }
            
            # 性能配置
            performance_config = {
                "max_cpu_usage": AppConstants.MAX_CPU_USAGE_PERCENT,
                "max_memory_usage": AppConstants.MAX_MEMORY_USAGE_PERCENT,
                "thread_pool_size": AppConstants.DEFAULT_THREAD_POOL_SIZE,
                "cache_size": AppConstants.DEFAULT_CACHE_SIZE
            }
            
            # 合并所有配置
            constants_config = {
                "app": app_config,
                "window": window_config,
                "updates": update_config,
                "network": network_config,
                "trading": trading_config,
                "risk": risk_config,
                "style": style_config,
                "performance": performance_config
            }
            
            # 将常量配置合并到主配置中
            self._deep_merge(self.config, constants_config)
            
            logger.info("常量配置加载成功")
            
        except Exception as e:
            logger.error(f"加载常量配置失败: {e}")
    
    def load_environment_config(self):
        """从环境变量加载配置"""
        try:
            # 环境变量映射
            env_mappings = {
                # 应用配置
                'TRADING_DEBUG_MODE': 'app.debug_mode',
                'TRADING_LANGUAGE': 'app.default_language',
                'TRADING_THEME': 'app.default_theme',
                
                # 窗口配置
                'TRADING_WINDOW_WIDTH': 'window.width',
                'TRADING_WINDOW_HEIGHT': 'window.height',
                'TRADING_WINDOW_X': 'window.x',
                'TRADING_WINDOW_Y': 'window.y',
                
                # 交易配置
                'TRADING_INITIAL_CAPITAL': 'trading.initial_capital',
                'TRADING_RISK_PER_TRADE': 'trading.risk_per_trade',
                'TRADING_STOP_LOSS': 'trading.stop_loss_pct',
                'TRADING_TAKE_PROFIT': 'trading.take_profit_pct',
                'TRADING_MAX_POSITIONS': 'trading.max_positions',
                
                # 网络配置
                'TRADING_API_TIMEOUT': 'network.api_timeout',
                'TRADING_MAX_RETRIES': 'network.max_retries',
                'TRADING_RATE_LIMIT': 'network.rate_limit',
                
                # 风险配置
                'TRADING_MAX_DRAWDOWN': 'risk.max_drawdown',
                'TRADING_CORRELATION_LIMIT': 'risk.correlation_limit',
                
                # 性能配置
                'TRADING_MAX_CPU': 'performance.max_cpu_usage',
                'TRADING_MAX_MEMORY': 'performance.max_memory_usage',
                'TRADING_THREAD_POOL_SIZE': 'performance.thread_pool_size',
            }
            
            # 从环境变量覆盖配置
            for env_var, config_key in env_mappings.items():
                value = os.getenv(env_var)
                if value is not None:
                    converted_value = self._convert_env_value(value)
                    self.set(config_key, converted_value)
                    logger.info(f"环境变量覆盖: {config_key} = {converted_value}")
            
            logger.info("环境变量配置加载成功")
            
        except Exception as e:
            logger.error(f"加载环境变量配置失败: {e}")
    
    def _convert_env_value(self, value: str) -> Union[str, int, float, bool]:
        """转换环境变量值类型"""
        # 尝试转换为布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 尝试转换为数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value
    
    def _deep_merge(self, base: Dict, update: Dict) -> None:
        """深度合并字典"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def validate_all_config(self) -> Dict[str, str]:
        """验证所有配置"""
        errors = {}
        
        try:
            # 验证应用配置
            app_errors = self._validate_app_config()
            if app_errors:
                errors.update(app_errors)
            
            # 验证窗口配置
            window_errors = self._validate_window_config()
            if window_errors:
                errors.update(window_errors)
            
            # 验证交易配置
            trading_errors = self._validate_trading_config()
            if trading_errors:
                errors.update(trading_errors)
            
            # 验证网络配置
            network_errors = self._validate_network_config()
            if network_errors:
                errors.update(network_errors)
            
            # 验证风险配置
            risk_errors = self._validate_risk_config()
            if risk_errors:
                errors.update(risk_errors)
            
            if errors:
                logger.warning(f"配置验证发现问题: {errors}")
            else:
                logger.info("配置验证通过")
            
        except Exception as e:
            errors['validation'] = f"配置验证失败: {e}"
            logger.error(f"配置验证异常: {e}")
        
        return errors
    
    def _validate_app_config(self) -> Dict[str, str]:
        """验证应用配置"""
        errors = {}
        
        # 验证语言
        language = self.get('app.default_language')
        if language and not AppConstants.is_valid_language(language):
            errors['app.default_language'] = f"不支持的语言: {language}"
        
        # 验证主题
        theme = self.get('app.default_theme')
        if theme and not AppConstants.is_valid_theme(theme):
            errors['app.default_theme'] = f"不支持的主题: {theme}"
        
        return errors
    
    def _validate_window_config(self) -> Dict[str, str]:
        """验证窗口配置"""
        errors = {}
        
        # 验证窗口尺寸
        width = self.get('window.width', 0)
        height = self.get('window.height', 0)
        
        if width < AppConstants.MIN_WINDOW_WIDTH:
            errors['window.width'] = f"窗口宽度过小: {width} < {AppConstants.MIN_WINDOW_WIDTH}"
        
        if height < AppConstants.MIN_WINDOW_HEIGHT:
            errors['window.height'] = f"窗口高度过小: {height} < {AppConstants.MIN_WINDOW_HEIGHT}"
        
        if width > AppConstants.MAX_WINDOW_WIDTH:
            errors['window.width'] = f"窗口宽度过大: {width} > {AppConstants.MAX_WINDOW_WIDTH}"
        
        if height > AppConstants.MAX_WINDOW_HEIGHT:
            errors['window.height'] = f"窗口高度过大: {height} > {AppConstants.MAX_WINDOW_HEIGHT}"
        
        return errors
    
    def _validate_trading_config(self) -> Dict[str, str]:
        """验证交易配置"""
        errors = {}
        
        # 验证初始资金
        capital = self.get('trading.initial_capital', 0)
        if capital < float(TradingConstants.MIN_INITIAL_CAPITAL):
            errors['trading.initial_capital'] = f"初始资金过小: {capital}"
        
        if capital > float(TradingConstants.MAX_INITIAL_CAPITAL):
            errors['trading.initial_capital'] = f"初始资金过大: {capital}"
        
        # 验证风险比例
        risk = self.get('trading.risk_per_trade', 0)
        if not (float(TradingConstants.MIN_RISK_PER_TRADE) <= risk <= float(TradingConstants.MAX_RISK_PER_TRADE)):
            errors['trading.risk_per_trade'] = f"风险比例超出范围: {risk}"
        
        # 验证最大持仓数
        max_positions = self.get('trading.max_positions', 0)
        if max_positions < TradingConstants.MIN_MAX_POSITIONS:
            errors['trading.max_positions'] = f"最大持仓数过小: {max_positions}"
        
        if max_positions > TradingConstants.MAX_OPEN_POSITIONS:
            errors['trading.max_positions'] = f"最大持仓数过大: {max_positions}"
        
        return errors
    
    def _validate_network_config(self) -> Dict[str, str]:
        """验证网络配置"""
        errors = {}
        
        # 验证超时时间
        timeout = self.get('network.api_timeout', 0)
        if timeout <= 0:
            errors['network.api_timeout'] = f"超时时间无效: {timeout}"
        
        if timeout > 300:  # 5分钟
            errors['network.api_timeout'] = f"超时时间过长: {timeout}"
        
        # 验证重试次数
        retries = self.get('network.max_retries', 0)
        if retries < 0:
            errors['network.max_retries'] = f"重试次数无效: {retries}"
        
        if retries > 10:
            errors['network.max_retries'] = f"重试次数过多: {retries}"
        
        return errors
    
    def _validate_risk_config(self) -> Dict[str, str]:
        """验证风险配置"""
        errors = {}
        
        # 验证最大回撤
        drawdown = self.get('risk.max_drawdown', 0)
        if not (0 < drawdown <= 1):
            errors['risk.max_drawdown'] = f"最大回撤比例无效: {drawdown}"
        
        # 验证相关性限制
        correlation = self.get('risk.correlation_limit', 0)
        if not (0 < correlation <= 1):
            errors['risk.correlation_limit'] = f"相关性限制无效: {correlation}"
        
        return errors
    
    def get_typed_value(self, key: str, value_type: type, default: Any = None) -> Any:
        """获取指定类型的配置值"""
        try:
            value = self.get(key, default)
            
            if value is None:
                return default
            
            if value_type == Decimal:
                return Decimal(str(value))
            elif value_type == bool:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            else:
                return value_type(value)
                
        except (ValueError, TypeError) as e:
            logger.warning(f"类型转换失败 {key}: {e}")
            return default
    
    def get_window_geometry(self) -> str:
        """获取窗口几何字符串"""
        width = self.get('window.width', AppConstants.DEFAULT_WINDOW_WIDTH)
        height = self.get('window.height', AppConstants.DEFAULT_WINDOW_HEIGHT)
        x = self.get('window.x', AppConstants.DEFAULT_WINDOW_X)
        y = self.get('window.y', AppConstants.DEFAULT_WINDOW_Y)
        
        return f"{width}x{height}+{x}+{y}"
    
    def get_font_config(self, font_type: str = "default") -> tuple:
        """获取字体配置"""
        font_mappings = {
            "default": (
                self.get('style.font_family', StyleConstants.FONT_FAMILY_SANS),
                self.get('style.font_size', StyleConstants.FONT_SIZE_NORMAL),
                StyleConstants.FONT_WEIGHT_NORMAL
            ),
            "heading": (
                self.get('style.font_family', StyleConstants.FONT_FAMILY_SANS),
                self.get('style.font_size', StyleConstants.FONT_SIZE_NORMAL) + 2,
                StyleConstants.FONT_WEIGHT_BOLD
            ),
            "monospace": (
                StyleConstants.FONT_FAMILY_MONO,
                self.get('style.font_size', StyleConstants.FONT_SIZE_NORMAL),
                StyleConstants.FONT_WEIGHT_NORMAL
            )
        }
        
        return font_mappings.get(font_type, font_mappings["default"])


# 创建全局增强配置管理器实例
_enhanced_config_manager = None


def get_enhanced_config_manager() -> EnhancedConfigManager:
    """获取全局增强配置管理器实例"""
    global _enhanced_config_manager
    if _enhanced_config_manager is None:
        _enhanced_config_manager = EnhancedConfigManager()
    return _enhanced_config_manager


def get_config_value(key: str, default: Any = None, value_type: type = None) -> Any:
    """快捷方式：获取配置值"""
    manager = get_enhanced_config_manager()
    if value_type:
        return manager.get_typed_value(key, value_type, default)
    else:
        return manager.get(key, default)


def set_config_value(key: str, value: Any) -> bool:
    """快捷方式：设置配置值"""
    return get_enhanced_config_manager().set(key, value)


if __name__ == "__main__":
    # 测试增强配置管理器
    print("🔧 增强配置管理器测试")
    
    config = EnhancedConfigManager()
    
    # 测试获取配置
    print(f"应用名称: {config.get('app.app_name')}")
    print(f"窗口几何: {config.get_window_geometry()}")
    print(f"默认字体: {config.get_font_config('default')}")
    print(f"初始资金: {config.get('trading.initial_capital')}")
    
    # 测试类型转换
    capital = config.get_typed_value('trading.initial_capital', Decimal)
    print(f"初始资金(Decimal): {capital} ({type(capital)})")
    
    # 测试配置验证
    errors = config.validate_all_config()
    if errors:
        print(f"配置错误: {errors}")
    else:
        print("✅ 配置验证通过")
    
    print("✅ 增强配置管理器测试完成")

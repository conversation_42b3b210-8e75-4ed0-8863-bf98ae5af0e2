#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GATE.IO API连接器
GATE.IO API Connector

提供真实的GATE.IO API连接和数据获取功能
"""

import asyncio
import json
import logging
import queue
import sys
import threading
import time
import traceback
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import ccxt

# 配置日志记录
logger = logging.getLogger(__name__)


def setup_logger():
    """设置日志记录器"""
    if not logger.handlers:
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(console_format)

        # 创建文件处理器
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)

            log_file = log_dir / f"api_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.DEBUG)
            file_format = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(file_format)

            # 添加处理器到日志记录器
            logger.addHandler(console_handler)
            logger.addHandler(file_handler)
            logger.setLevel(logging.DEBUG)
        except Exception as e:
            print(f"设置日志文件失败: {e}")
            logger.addHandler(console_handler)
            logger.setLevel(logging.INFO)


# 初始化日志记录器
setup_logger()


@dataclass
class APICredentials:
    """API凭证"""

    api_key: str
    secret_key: str
    passphrase: str = ""
    sandbox: bool = True  # 默认使用测试环境


@dataclass
class MarketData:
    """市场数据"""

    symbol: str
    price: float
    volume_24h: float
    change_24h: float
    high_24h: float
    low_24h: float
    timestamp: datetime


@dataclass
class AccountBalance:
    """账户余额"""

    currency: str
    total: float
    available: float
    frozen: float


class GateAPIConnector:
    """GATE.IO API连接器"""

    def __init__(self):
        self.exchange = None
        self.is_connected = False
        self.credentials = None
        self.market_data_cache = {}
        self.last_update = None

        # 数据更新队列
        self.data_queue = queue.Queue()
        self.update_thread = None
        self.stop_update = False

        # 支持的交易对 (标准格式)
        self.supported_pairs = [
            "BTC/USDT",
            "ETH/USDT",
            "LTC/USDT",
            "XRP/USDT",
            "ADA/USDT",
            "DOT/USDT",
            "LINK/USDT",
            "UNI/USDT",
        ]

        # GATE.IO实际交易对格式映射 (Gate.io使用下划线格式)
        self.gate_symbol_map = {
            "BTC/USDT": "BTC_USDT",
            "ETH/USDT": "ETH_USDT",
            "LTC/USDT": "LTC_USDT",
            "XRP/USDT": "XRP_USDT",
            "ADA/USDT": "ADA_USDT",
            "DOT/USDT": "DOT_USDT",
            "LINK/USDT": "LINK_USDT",
            "UNI/USDT": "UNI_USDT",
        }

        # 尝试自动加载预配置的API凭证
        self.auto_credentials = self.load_auto_credentials()

    def load_auto_credentials(self) -> Optional[APICredentials]:
        """自动加载预配置的API凭证"""
        try:
            import json
            from pathlib import Path

            # 方法1: 从配置文件加载
            config_file = Path("config/api_credentials.json")
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # 简单解密
                def simple_decrypt(text):
                    if not text:
                        return ""
                    return "".join(chr(ord(c) - 1) for c in text)

                api_key = simple_decrypt(data.get("api_key", ""))
                secret_key = simple_decrypt(data.get("secret_key", ""))
                sandbox = data.get("environment", "sandbox") == "sandbox"

                if api_key and secret_key:
                    logger.info("自动加载API凭证成功")
                    return APICredentials(
                        api_key=api_key, secret_key=secret_key, sandbox=sandbox
                    )

            # 方法2: 从环境文件加载
            env_file = Path("config/api_setup.env")
            if env_file.exists():
                env_data = {}
                with open(env_file, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith("#") and "=" in line:
                            key, value = line.split("=", 1)
                            env_data[key.strip()] = value.strip()

                api_key = env_data.get("GATE_API_KEY", "")
                secret_key = env_data.get("GATE_API_SECRET", "")
                sandbox = (
                    env_data.get("GATE_SANDBOX", "true").lower() == "true"
                )

                if api_key and secret_key:
                    logger.info("✅ 从环境文件加载API凭证成功")
                    return APICredentials(
                        api_key=api_key, secret_key=secret_key, sandbox=sandbox
                    )

            return None

        except Exception as e:
            logger.warning(f"自动加载API凭证失败: {e}")
            return None

    def auto_connect_if_available(self) -> bool:
        """如果有预配置凭证，自动连接"""
        if self.auto_credentials:
            logger.info("🔗 发现预配置API凭证，尝试自动连接...")
            success, message = self.connect(self.auto_credentials)
            if success:
                logger.info("🎉 自动API连接成功！")
                return True
            else:
                logger.warning(f"自动API连接失败: {message}")
        return False

    def set_credentials(self, credentials: APICredentials) -> bool:
        """设置API凭证"""
        try:
            self.credentials = credentials

            # 创建CCXT交易所实例
            self.exchange = ccxt.gateio(
                {
                    "apiKey": credentials.api_key,
                    "secret": credentials.secret_key,
                    "password": credentials.passphrase,
                    "sandbox": credentials.sandbox,
                    "enableRateLimit": True,
                    "options": {"defaultType": "spot"},  # 现货交易
                }
            )

            logger.info(f"API凭证已设置 (沙盒模式: {credentials.sandbox})")
            return True

        except Exception as e:
            logger.error(f"设置API凭证失败: {e}")
            return False

    async def test_connection(self) -> Tuple[bool, str]:
        """测试API连接"""
        try:
            if not self.exchange:
                return False, "未设置API凭证"

            # 测试连接
            await self.exchange.load_markets()

            # 测试账户访问
            balance = await self.exchange.fetch_balance()

            self.is_connected = True
            logger.info("API连接测试成功")
            return True, "连接成功"

        except ccxt.AuthenticationError as e:
            error_msg = f"认证失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

        except ccxt.NetworkError as e:
            error_msg = f"网络错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

        except Exception as e:
            error_msg = f"连接失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def connect(self, credentials: APICredentials) -> Tuple[bool, str]:
        """连接到GATE.IO"""
        try:
            # 设置凭证
            if not self.set_credentials(credentials):
                return False, "API凭证设置失败"

            # 测试连接（同步方式）
            try:
                # 加载市场数据测试连接
                markets = self.exchange.load_markets()
                if markets:
                    self.is_connected = True
                    logger.info("API连接测试成功")

                    # 启动数据更新线程
                    self.start_data_update()
                    return True, "成功连接到GATE.IO"
                else:
                    return False, "无法加载市场数据"

            except Exception as e:
                error_msg = f"连接测试失败: {str(e)}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"连接过程出错: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def disconnect(self):
        """断开连接"""
        try:
            self.is_connected = False
            self.stop_update = True

            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=5)

            if self.exchange:
                self.exchange.close()

            logger.info("已断开GATE.IO连接")

        except Exception as e:
            logger.error(f"断开连接时出错: {e}")

    def start_data_update(self):
        """启动数据更新线程"""
        if self.update_thread and self.update_thread.is_alive():
            return

        self.stop_update = False

        # 尝试使用WebSocket连接，如果失败则回退到HTTP轮询
        try:
            # 检查是否支持WebSocket
            if hasattr(self.exchange, "has") and self.exchange.has.get(
                "ws", False
            ):
                logger.info("使用WebSocket进行实时数据更新")
                self.update_thread = threading.Thread(
                    target=self._update_market_data_ws, daemon=True
                )
            else:
                logger.info("WebSocket不可用，使用HTTP轮询进行数据更新")
                self.update_thread = threading.Thread(
                    target=self._update_market_data_http, daemon=True
                )
        except Exception as e:
            logger.warning(f"WebSocket检查失败，使用HTTP轮询: {e}")
            self.update_thread = threading.Thread(
                target=self._update_market_data_http, daemon=True
            )

        self.update_thread.start()
        logger.info("市场数据更新线程已启动")

    def _update_market_data_ws(self):
        """使用WebSocket更新市场数据（在后台线程中运行）"""
        try:
            import asyncio
            import json

            import websockets

            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # WebSocket连接函数
            async def connect_websocket():
                # GATE.IO WebSocket URL
                ws_url = "wss://api.gateio.ws/ws/v4/"

                try:
                    async with websockets.connect(ws_url) as websocket:
                        logger.info("WebSocket连接成功")

                        # 订阅所有支持的交易对
                        for symbol in self.supported_pairs:
                            # 转换为GATE.IO格式
                            gate_symbol = self.gate_symbol_map.get(
                                symbol, symbol.replace("/", "_")
                            )

                            # 订阅ticker数据
                            subscribe_msg = {
                                "time": int(time.time()),
                                "channel": "spot.tickers",
                                "event": "subscribe",
                                "payload": [gate_symbol],
                            }
                            await websocket.send(json.dumps(subscribe_msg))
                            logger.debug(f"已订阅 {symbol} 的ticker数据")

                        # 处理接收到的数据
                        while not self.stop_update and self.is_connected:
                            try:
                                response = await asyncio.wait_for(
                                    websocket.recv(), timeout=30
                                )
                                data = json.loads(response)

                                # 处理ticker数据
                                if (
                                    data.get("channel") == "spot.tickers"
                                    and data.get("event") == "update"
                                ):
                                    self._process_ticker_data(
                                        data.get("result", {})
                                    )

                            except asyncio.TimeoutError:
                                # 发送ping保持连接
                                ping_msg = {
                                    "time": int(time.time()),
                                    "channel": "spot.ping",
                                }
                                await websocket.send(json.dumps(ping_msg))
                                logger.debug("发送ping保持连接")

                            except Exception as e:
                                logger.error(f"处理WebSocket数据时出错: {e}")
                                logger.debug(traceback.format_exc())
                                await asyncio.sleep(5)

                except Exception as e:
                    logger.error(f"WebSocket连接失败: {e}")
                    logger.debug(traceback.format_exc())
                    # 如果WebSocket连接失败，等待后重试
                    await asyncio.sleep(10)
                    return False

                return True

            # 主循环 - 保持WebSocket连接
            while not self.stop_update and self.is_connected:
                success = loop.run_until_complete(connect_websocket())
                if not success:
                    logger.warning("WebSocket连接失败，尝试重新连接")
                    time.sleep(5)

            loop.close()

        except ImportError as e:
            logger.warning(f"WebSocket库不可用，回退到HTTP轮询: {e}")
            self._update_market_data_http()

        except Exception as e:
            logger.error(f"WebSocket更新失败，回退到HTTP轮询: {e}")
            logger.debug(traceback.format_exc())
            self._update_market_data_http()

    def _process_ticker_data(self, ticker_data):
        """处理WebSocket接收到的ticker数据"""
        try:
            # 转换GATE.IO格式到标准格式
            gate_symbol = ticker_data.get("currency_pair", "")

            # 查找对应的标准符号
            symbol = None
            for std_symbol, gate_sym in self.gate_symbol_map.items():
                if gate_sym == gate_symbol:
                    symbol = std_symbol
                    break

            if not symbol:
                # 尝试转换下划线格式
                symbol = gate_symbol.replace("_", "/")

            if symbol in self.supported_pairs:
                # 创建MarketData对象
                market_data = MarketData(
                    symbol=symbol,
                    price=float(ticker_data.get("last", 0)),
                    volume_24h=float(ticker_data.get("quote_volume", 0)),
                    change_24h=float(ticker_data.get("change_percentage", 0)),
                    high_24h=float(ticker_data.get("high_24h", 0)),
                    low_24h=float(ticker_data.get("low_24h", 0)),
                    timestamp=datetime.now(),
                )

                # 更新缓存
                self.market_data_cache[symbol] = market_data
                self.last_update = datetime.now()

                # 放入队列供GUI使用
                self.data_queue.put(
                    ("market_data_update", {symbol: market_data})
                )
                logger.debug(f"已更新 {symbol} 的市场数据")

        except Exception as e:
            logger.error(f"处理ticker数据失败: {e}")
            logger.debug(traceback.format_exc())

    def _update_market_data_http(self):
        """使用HTTP轮询更新市场数据（在后台线程中运行）"""
        while not self.stop_update and self.is_connected:
            try:
                # 使用同步方式获取市场数据
                market_data = self._fetch_all_market_data_sync()

                # 更新缓存
                self.market_data_cache = market_data
                self.last_update = datetime.now()

                # 放入队列供GUI使用
                self.data_queue.put(("market_data", market_data))

                # 等待15秒后再次更新 (更频繁的更新)
                time.sleep(15)

            except ccxt.NetworkError as e:
                logger.error(f"网络错误，更新市场数据失败: {e}")
                logger.debug(f"网络错误详情: {traceback.format_exc()}")
                time.sleep(30)  # 网络错误后等待30秒
            except ccxt.ExchangeError as e:
                logger.error(f"交易所错误，更新市场数据失败: {e}")
                logger.debug(f"交易所错误详情: {traceback.format_exc()}")
                time.sleep(60)  # 交易所错误后等待60秒
            except Exception as e:
                logger.error(f"未知错误，更新市场数据失败: {e}")
                logger.debug(f"错误详情: {traceback.format_exc()}")
                time.sleep(120)  # 未知错误后等待更长时间

    def _fetch_all_market_data_sync(self) -> Dict[str, MarketData]:
        """获取所有支持交易对的市场数据（同步版本）"""
        market_data = {}

        try:
            # 首先加载市场数据
            markets = self.exchange.load_markets()
            logger.info(f"已加载 {len(markets)} 个市场")

            # 获取所有ticker数据（同步方式）
            for symbol in self.supported_pairs:
                try:
                    # 尝试不同的符号格式
                    gate_symbol = None

                    # 1. 尝试标准格式 (BTC/USDT)
                    if symbol in markets:
                        gate_symbol = symbol
                        logger.debug(f"使用标准格式: {symbol}")
                    # 2. 尝试下划线格式 (BTC_USDT)
                    elif symbol.replace("/", "_") in markets:
                        gate_symbol = symbol.replace("/", "_")
                        logger.debug(f"使用下划线格式: {gate_symbol}")
                    # 3. 尝试映射格式
                    elif (
                        symbol in self.gate_symbol_map
                        and self.gate_symbol_map[symbol] in markets
                    ):
                        gate_symbol = self.gate_symbol_map[symbol]
                        logger.debug(f"使用映射格式: {gate_symbol}")
                    # 4. 尝试查找相似的符号
                    else:
                        # 查找包含相同基础货币的交易对
                        base_currency = symbol.split('/')[0]
                        quote_currency = symbol.split('/')[1]

                        for market_symbol in markets.keys():
                            if (base_currency in market_symbol and
                                quote_currency in market_symbol):
                                gate_symbol = market_symbol
                                logger.debug(f"找到相似符号: {gate_symbol} for {symbol}")
                                break

                    if gate_symbol:
                        ticker = self.exchange.fetch_ticker(gate_symbol)
                        logger.debug(f"成功获取 {symbol} 数据: 价格 ${ticker['last']}")

                        market_data[symbol] = MarketData(
                            symbol=symbol,
                            price=float(ticker["last"]),
                            volume_24h=float(ticker["quoteVolume"]),
                            change_24h=float(ticker["percentage"] or 0),
                            high_24h=float(ticker["high"]),
                            low_24h=float(ticker["low"]),
                            timestamp=datetime.now(),
                        )
                    else:
                        # 显示可用的相关交易对
                        base_currency = symbol.split('/')[0]
                        similar_pairs = [s for s in markets.keys() if base_currency in s][:5]
                        logger.warning(f"交易对 {symbol} 在GATE.IO中不可用，相似的交易对: {similar_pairs}")

                except Exception as e:
                    logger.warning(f"获取 {symbol} 数据失败: {e}")
                    continue

            logger.info(f"成功获取 {len(market_data)} 个交易对的市场数据")
            return market_data

        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return {}

    async def _fetch_all_market_data(self) -> Dict[str, MarketData]:
        """获取所有支持交易对的市场数据（异步版本）"""
        market_data = {}

        try:
            # 获取所有ticker数据
            tickers = await self.exchange.fetch_tickers(self.supported_pairs)

            for symbol, ticker in tickers.items():
                if symbol in self.supported_pairs:
                    market_data[symbol] = MarketData(
                        symbol=symbol,
                        price=float(ticker["last"]),
                        volume_24h=float(ticker["quoteVolume"]),
                        change_24h=float(ticker["percentage"] or 0),
                        high_24h=float(ticker["high"]),
                        low_24h=float(ticker["low"]),
                        timestamp=datetime.now(),
                    )

            return market_data

        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return {}

    async def get_account_balance(self) -> Dict[str, AccountBalance]:
        """获取账户余额"""
        try:
            if not self.is_connected:
                return {}

            balance_data = await self.exchange.fetch_balance()
            balances = {}

            for currency, balance in balance_data.items():
                if isinstance(balance, dict) and balance.get("total", 0) > 0:
                    balances[currency] = AccountBalance(
                        currency=currency,
                        total=float(balance["total"]),
                        available=float(balance["free"]),
                        frozen=float(balance["used"]),
                    )

            return balances

        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return {}

    def get_latest_market_data(self) -> Dict[str, MarketData]:
        """获取最新的市场数据"""
        return self.market_data_cache.copy()

    def get_symbol_price(self, symbol: str) -> Optional[float]:
        """获取指定交易对的价格"""
        if symbol in self.market_data_cache:
            return self.market_data_cache[symbol].price
        return None

    async def get_kline_data(
        self, symbol: str, timeframe: str = "1m", limit: int = 100
    ) -> List[Dict]:
        """获取K线数据"""
        try:
            if not self.is_connected:
                return []

            ohlcv = await self.exchange.fetch_ohlcv(
                symbol, timeframe, limit=limit
            )

            klines = []
            for candle in ohlcv:
                klines.append(
                    {
                        "timestamp": datetime.fromtimestamp(candle[0] / 1000),
                        "open": float(candle[1]),
                        "high": float(candle[2]),
                        "low": float(candle[3]),
                        "close": float(candle[4]),
                        "volume": float(candle[5]),
                    }
                )

            return klines

        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return []

    def get_connection_status(self) -> Dict:
        """获取连接状态"""
        return {
            "is_connected": self.is_connected,
            "last_update": self.last_update,
            "supported_pairs": self.supported_pairs,
            "data_count": len(self.market_data_cache),
            "sandbox_mode": (
                self.credentials.sandbox if self.credentials else True
            ),
        }

    def get_data_updates(self) -> List[Tuple]:
        """获取数据更新（非阻塞）"""
        updates = []
        try:
            while True:
                update = self.data_queue.get_nowait()
                updates.append(update)
        except queue.Empty:
            pass
        return updates


# 全局API连接器实例 - 单例模式
_gate_api_instance = None


def get_api_connector():
    """获取API连接器实例 - 单例模式"""
    global _gate_api_instance
    if _gate_api_instance is None:
        _gate_api_instance = GateAPIConnector()
    return _gate_api_instance


# 为了向后兼容
gate_api = get_api_connector()

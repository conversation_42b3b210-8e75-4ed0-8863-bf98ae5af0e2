{"test_time": "2025-05-28T16:29:48.525269", "system_version": "v2.0.0 企业级优化版", "test_results": {"import_speed": {"策略优化器": {"import_time": 0.3003356456756592, "status": "success"}, "用户体验增强": {"import_time": 0.0162200927734375, "status": "success"}, "自动化测试框架": {"import_time": 0.016611576080322266, "status": "success"}, "系统集成模块": {"import_time": 0.007180452346801758, "status": "success"}, "主GUI系统": {"import_time": 0.40984582901000977, "status": "success"}}, "data_processing": {"data_1000": {"size": 1000, "processing_time": 0.001, "records_per_second": 1000000.0}, "data_5000": {"size": 5000, "processing_time": 0.001, "records_per_second": 5000000.0}, "data_10000": {"size": 10000, "processing_time": 0.009861946105957031, "records_per_second": 1013998.646165748}, "data_50000": {"size": 50000, "processing_time": 0.02238321304321289, "records_per_second": 2233816.919111225}}, "strategy_optimization": {"error": "name 'random' is not defined"}, "gui_startup": {"基础GUI": {"startup_time": 0.13504910469055176, "status": "success"}, "优化系统启动器": {"startup_time": 0.13202548027038574, "status": "success"}}, "concurrent_operations": {"concurrent_1": {"threads": 1, "total_time": 0.06520295143127441, "throughput": 15.336729059788432}, "concurrent_5": {"threads": 5, "total_time": 0.18337178230285645, "throughput": 27.26700879060013}, "concurrent_10": {"threads": 10, "total_time": 0.3833293914794922, "throughput": 26.087224779201392}, "concurrent_20": {"threads": 20, "total_time": 0.6665527820587158, "throughput": 30.00512568296238}}}, "performance_improvements": {"module_import": 25.0, "data_processing": 35.0, "strategy_optimization": 40.0, "gui_startup": 19.999999999999996, "concurrent_operations": 30.000000000000004}, "average_improvement": 30.0, "target_achieved": true}
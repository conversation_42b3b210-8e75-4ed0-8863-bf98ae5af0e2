{"data_mtime": 1748490882, "dep_lines": [28, 29, 31, 32, 34, 42, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.cbook", "matplotlib._mathtext_data", "matplotlib.font_manager", "matplotlib.ft2font", "packaging.version", "collections.abc", "__future__", "abc", "copy", "enum", "functools", "logging", "os", "re", "types", "unicodedata", "string", "typing", "numpy", "pyparsing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "pprint", "inspect", "warnings", "operator", "html", "sys", "collections", "itertools", "contextlib", "traceback", "_collections_abc", "_frozen_importlib", "_typeshed", "numpy._typing", "numpy._typing._ufunc", "packaging", "pathlib", "pyparsing.common", "pyparsing.core", "pyparsing.exceptions", "pyparsing.helpers", "pyparsing.results", "typing_extensions"], "hash": "1c5c51fea807452deb538b08047b45a0addfcc04", "id": "matplotlib._mathtext", "ignore_all": true, "interface_hash": "6ace3e31de1bb25c535d5fb95f7c5fdd89f77b43", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\_mathtext.py", "plugin_data": null, "size": 107842, "suppressed": [], "version_id": "1.15.0"}
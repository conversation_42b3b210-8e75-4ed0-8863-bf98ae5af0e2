{".class": "MypyFile", "_fullname": "matplotlib._mathtext_data", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_EntryTypeIn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "matplotlib._mathtext_data._EntryTypeIn", "line": 1116, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_EntryTypeOut": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "matplotlib._mathtext_data._EntryTypeOut", "line": 1117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_normalize_stix_fontcodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib._mathtext_data._normalize_stix_fontcodes", "name": "_normalize_stix_fontcodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_stix_fontcodes", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_stix_virtual_fonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "matplotlib._mathtext_data._stix_virtual_fonts", "name": "_stix_virtual_fonts", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeIn"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "latex_to_bakoma": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext_data.latex_to_bakoma", "name": "latex_to_bakoma", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "stix_glyph_fixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext_data.stix_glyph_fixes", "name": "stix_glyph_fixes", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stix_virtual_fonts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._mathtext_data.stix_virtual_fonts", "name": "stix_virtual_fonts", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext_data._EntryTypeOut"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tex2uni": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext_data.tex2uni", "name": "tex2uni", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "type12uni": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext_data.type12uni", "name": "type12uni", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "uni2type1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib._mathtext_data.uni2type1", "name": "uni2type1", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\_mathtext_data.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代码质量验证脚本
Code Quality Verification Script

快速验证代码质量提升效果
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def verify_constants_system():
    """验证常量系统"""
    print("🔧 验证常量系统...")
    
    try:
        from core.constants import (
            AppConstants, StyleConstants, TradingConstants, 
            MessageConstants, PathConstants
        )
        
        # 验证常量数量
        app_attrs = len([attr for attr in dir(AppConstants) if not attr.startswith('_')])
        style_attrs = len([attr for attr in dir(StyleConstants) if not attr.startswith('_')])
        trading_attrs = len([attr for attr in dir(TradingConstants) if not attr.startswith('_')])
        message_keys = len(MessageConstants.get_all_message_keys())
        path_attrs = len([attr for attr in dir(PathConstants) if not attr.startswith('_')])
        
        print(f"  ✅ AppConstants: {app_attrs} 个常量")
        print(f"  ✅ StyleConstants: {style_attrs} 个常量")
        print(f"  ✅ TradingConstants: {trading_attrs} 个常量")
        print(f"  ✅ MessageConstants: {message_keys} 个消息键")
        print(f"  ✅ PathConstants: {path_attrs} 个路径常量")
        
        total_constants = app_attrs + style_attrs + trading_attrs + message_keys + path_attrs
        print(f"  📊 总常量数量: {total_constants}")
        
        # 验证关键常量
        assert AppConstants.APP_NAME == "Ultimate Spot Trading Terminal"
        assert AppConstants.DEFAULT_WINDOW_WIDTH == 1600
        assert len(TradingConstants.ALL_SUPPORTED_SYMBOLS) >= 8
        assert len(TradingConstants.ALL_ORDER_TYPES) >= 5
        assert message_keys >= 150
        
        print("  ✅ 关键常量验证通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 常量系统验证失败: {e}")
        return False


def verify_config_system():
    """验证配置系统"""
    print("\n🔧 验证配置系统...")
    
    try:
        from core.config.enhanced_config_manager import EnhancedConfigManager
        
        config = EnhancedConfigManager()
        
        # 验证基础配置
        app_name = config.get('app.app_name')
        window_width = config.get('window.width')
        trading_capital = config.get('trading.initial_capital')
        
        assert app_name is not None
        assert window_width == 1600
        assert trading_capital == 10000.0
        
        print(f"  ✅ 基础配置验证通过")
        
        # 验证环境变量支持
        import os
        os.environ['TRADING_TEST_VAR'] = '12345'
        
        # 重新创建配置管理器测试环境变量
        test_config = EnhancedConfigManager()
        
        print(f"  ✅ 环境变量支持正常")
        
        # 验证配置验证
        errors = config.validate_all_config()
        print(f"  ✅ 配置验证完成 ({len(errors)} 个警告)")
        
        # 清理环境变量
        if 'TRADING_TEST_VAR' in os.environ:
            del os.environ['TRADING_TEST_VAR']
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置系统验证失败: {e}")
        return False


def verify_gui_improvements():
    """验证GUI改进"""
    print("\n🔧 验证GUI改进...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 检查常量使用
        import inspect
        source = inspect.getsource(ProfessionalTradingGUI)
        
        # 统计常量使用
        constant_usage = {
            'AppConstants': source.count('AppConstants.'),
            'TradingConstants': source.count('TradingConstants.'),
            'PathConstants': source.count('PathConstants.')
        }
        
        total_usage = sum(constant_usage.values())
        print(f"  ✅ 常量使用次数: {total_usage}")
        
        # 检查关键硬编码是否减少
        hardcoded_checks = {
            '"1600x1000"': source.count('"1600x1000"'),
            '"BTC/USDT"': source.count('"BTC/USDT"'),
            '"Market"': source.count('"Market"')
        }
        
        hardcode_count = sum(hardcoded_checks.values())
        print(f"  📊 关键硬编码数量: {hardcode_count}")
        
        if total_usage >= 20:
            print(f"  ✅ 常量使用充分")
        else:
            print(f"  ⚠️ 常量使用不足")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI改进验证失败: {e}")
        return False


def verify_quality_tools():
    """验证质量工具"""
    print("\n🔧 验证质量工具...")
    
    try:
        from tools.code_quality_checker import CodeQualityChecker
        
        # 创建检查器
        checker = CodeQualityChecker(".")
        
        # 检查少量文件
        test_files = [
            Path("core/constants/app_constants.py"),
            Path("core/config/enhanced_config_manager.py")
        ]
        
        issues_found = 0
        for file_path in test_files:
            if file_path.exists():
                checker._check_file(file_path)
                issues_found += len(checker.issues)
        
        print(f"  ✅ 质量检查工具正常工作")
        print(f"  📊 测试文件问题数: {issues_found}")
        
        # 验证报告生成
        summary = checker.get_summary()
        print(f"  ✅ 报告生成正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 质量工具验证失败: {e}")
        return False


def verify_project_structure():
    """验证项目结构"""
    print("\n🔧 验证项目结构...")
    
    try:
        from core.constants import PathConstants
        
        # 确保目录存在
        PathConstants.ensure_directories()
        
        # 检查关键目录
        required_dirs = [
            PathConstants.CONFIG_DIR,
            PathConstants.DATA_DIR,
            PathConstants.LOGS_DIR,
            PathConstants.CACHE_DIR
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not dir_path.exists():
                missing_dirs.append(str(dir_path))
        
        if missing_dirs:
            print(f"  ⚠️ 缺少目录: {missing_dirs}")
        else:
            print(f"  ✅ 所有必需目录存在")
        
        # 检查关键文件
        key_files = [
            "core/constants/__init__.py",
            "core/constants/app_constants.py",
            "core/constants/trading_constants.py",
            "core/config/enhanced_config_manager.py",
            "tools/code_quality_checker.py"
        ]
        
        missing_files = []
        for file_path in key_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"  ⚠️ 缺少文件: {missing_files}")
        else:
            print(f"  ✅ 所有关键文件存在")
        
        return len(missing_dirs) == 0 and len(missing_files) == 0
        
    except Exception as e:
        print(f"  ❌ 项目结构验证失败: {e}")
        return False


def calculate_quality_score(results):
    """计算质量分数"""
    passed = sum(results)
    total = len(results)
    score = (passed / total) * 100
    
    if score >= 95:
        grade = "A+"
        status = "优秀"
    elif score >= 90:
        grade = "A"
        status = "良好"
    elif score >= 80:
        grade = "B+"
        status = "合格"
    elif score >= 70:
        grade = "B"
        status = "需改进"
    else:
        grade = "C"
        status = "不合格"
    
    return score, grade, status


def main():
    """主验证函数"""
    print("🚀 开始代码质量验证")
    print("=" * 50)
    
    # 执行验证
    verifications = [
        ("常量系统", verify_constants_system),
        ("配置系统", verify_config_system),
        ("GUI改进", verify_gui_improvements),
        ("质量工具", verify_quality_tools),
        ("项目结构", verify_project_structure)
    ]
    
    results = []
    
    for name, verify_func in verifications:
        try:
            result = verify_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {name} 验证异常: {e}")
            results.append(False)
    
    # 计算质量分数
    score, grade, status = calculate_quality_score(results)
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    
    for i, (name, _) in enumerate(verifications):
        status_icon = "✅" if results[i] else "❌"
        print(f"  {status_icon} {name}")
    
    print(f"\n🏆 质量评估:")
    print(f"  📊 通过率: {score:.1f}%")
    print(f"  🎯 质量等级: {grade}")
    print(f"  📈 状态: {status}")
    
    # 总结
    if score >= 95:
        print("\n🎉 代码质量验证完全通过！")
        print("💎 系统已达到专业级质量标准")
    elif score >= 80:
        print("\n👍 代码质量验证基本通过")
        print("🔧 建议继续优化部分功能")
    else:
        print("\n⚠️ 代码质量需要进一步改进")
        print("🛠️ 请检查失败的验证项目")
    
    return score >= 80


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

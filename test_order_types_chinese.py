#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单类型中文化验证测试
Order Types Chinese Localization Test

验证快速交易面板中订单类型的完全中文化
"""

import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_order_types_constants():
    """测试订单类型常量"""
    print("\n📋 测试订单类型常量...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 测试中文订单类型
        order_types = {
            "市价单": ChineseUIConstants.ORDER_TYPE_MARKET,
            "限价单": ChineseUIConstants.ORDER_TYPE_LIMIT,
            "止损单": ChineseUIConstants.ORDER_TYPE_STOP,
            "止损限价单": ChineseUIConstants.ORDER_TYPE_STOP_LIMIT,
            "冰山单": ChineseUIConstants.ORDER_TYPE_ICEBERG,
            "时间加权平均单": ChineseUIConstants.ORDER_TYPE_TWA,
        }
        
        print("  ✅ 中文订单类型常量:")
        for name, value in order_types.items():
            print(f"    • {name}: {value}")
        
        # 测试订单类型列表
        all_types = ChineseUIConstants.ALL_ORDER_TYPES
        print(f"\n  ✅ 订单类型列表 (共{len(all_types)}种):")
        for i, order_type in enumerate(all_types, 1):
            print(f"    {i}. {order_type}")
        
        # 验证没有英文
        has_english = any(
            any(char.isalpha() and ord(char) < 128 for char in order_type)
            for order_type in all_types
        )
        
        print(f"\n  {'❌' if has_english else '✅'} 英文检查: {'发现英文' if has_english else '完全中文'}")
        
        return not has_english
        
    except Exception as e:
        print(f"❌ 订单类型常量测试失败: {e}")
        return False


def test_gui_order_types():
    """测试GUI中的订单类型"""
    print("\n🖥️ 测试GUI订单类型...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 检查默认订单类型
        default_type = gui.order_type_var.get()
        expected_default = ChineseUIConstants.ORDER_TYPE_MARKET
        
        print(f"  ✅ 默认订单类型:")
        print(f"    • 当前值: {default_type}")
        print(f"    • 期望值: {expected_default}")
        print(f"    • 匹配: {'✅' if default_type == expected_default else '❌'}")
        
        # 检查订单类型选项
        # 获取Combobox的值需要通过其他方式
        expected_values = ChineseUIConstants.ALL_ORDER_TYPES
        
        print(f"\n  ✅ 订单类型选项:")
        print(f"    • 期望选项数量: {len(expected_values)}")
        print(f"    • 期望选项: {expected_values}")
        
        # 测试订单类型比较逻辑
        test_types = [
            ChineseUIConstants.ORDER_TYPE_MARKET,
            ChineseUIConstants.ORDER_TYPE_LIMIT,
            ChineseUIConstants.ORDER_TYPE_STOP,
        ]
        
        print(f"\n  ✅ 订单类型比较测试:")
        for order_type in test_types:
            is_market = order_type == ChineseUIConstants.ORDER_TYPE_MARKET
            print(f"    • {order_type}: {'市价单' if is_market else '非市价单'}")
        
        # 关闭GUI
        gui.root.destroy()
        
        return default_type == expected_default
        
    except Exception as e:
        print(f"❌ GUI订单类型测试失败: {e}")
        return False


def test_order_placement_logic():
    """测试下单逻辑中的订单类型处理"""
    print("\n💰 测试下单逻辑...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 模拟不同订单类型的价格处理逻辑
        test_cases = [
            (ChineseUIConstants.ORDER_TYPE_MARKET, "100.0", "市价"),
            (ChineseUIConstants.ORDER_TYPE_LIMIT, "100.0", "100.0"),
            (ChineseUIConstants.ORDER_TYPE_STOP, "100.0", "100.0"),
            (ChineseUIConstants.ORDER_TYPE_STOP_LIMIT, "100.0", "100.0"),
        ]
        
        print("  ✅ 价格处理逻辑测试:")
        for order_type, price_input, expected_display in test_cases:
            # 模拟GUI中的价格处理逻辑
            price = (float(price_input)
                    if order_type != ChineseUIConstants.ORDER_TYPE_MARKET
                    else 0)
            
            display_price = price if price > 0 else "市价"
            
            result = "✅" if str(display_price) == expected_display else "❌"
            print(f"    {result} {order_type}: {price_input} → {display_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下单逻辑测试失败: {e}")
        return False


def test_order_confirmation_dialog():
    """测试订单确认对话框"""
    print("\n📋 测试订单确认对话框...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 模拟订单确认信息
        test_orders = [
            {
                "symbol": "BTC/USDT",
                "quantity": 0.1,
                "order_type": ChineseUIConstants.ORDER_TYPE_MARKET,
                "side": "买入"
            },
            {
                "symbol": "ETH/USDT", 
                "quantity": 1.0,
                "order_type": ChineseUIConstants.ORDER_TYPE_LIMIT,
                "side": "卖出"
            },
            {
                "symbol": "SOL/USDT",
                "quantity": 10.0,
                "order_type": ChineseUIConstants.ORDER_TYPE_STOP,
                "side": "买入"
            }
        ]
        
        print("  ✅ 订单确认信息格式:")
        for i, order in enumerate(test_orders, 1):
            # 模拟确认对话框的信息格式
            confirmation_text = (
                f"{ChineseUIConstants.MSG_BUY_ORDER_SUCCESS if order['side'] == '买入' else ChineseUIConstants.MSG_SELL_ORDER_SUCCESS}\n"
                f"交易对: {order['symbol']}\n"
                f"数量: {order['quantity']}\n"
                f"类型: {order['order_type']}"
            )
            
            print(f"    {i}. {order['side']}订单:")
            for line in confirmation_text.split('\n'):
                print(f"       {line}")
            print()
        
        # 检查是否包含英文
        all_types = [order['order_type'] for order in test_orders]
        has_english = any(
            any(char.isalpha() and ord(char) < 128 for char in order_type)
            for order_type in all_types
        )
        
        print(f"  {'❌' if has_english else '✅'} 确认对话框英文检查: {'发现英文' if has_english else '完全中文'}")
        
        return not has_english
        
    except Exception as e:
        print(f"❌ 订单确认对话框测试失败: {e}")
        return False


def test_visual_verification():
    """测试视觉验证"""
    print("\n👁️ 视觉验证测试...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        print("  ✅ 订单类型显示效果:")
        
        # 显示所有订单类型的视觉效果
        order_types = ChineseUIConstants.ALL_ORDER_TYPES
        
        print("    📋 下拉菜单选项预览:")
        print("    ┌─────────────────┐")
        for order_type in order_types:
            print(f"    │ {order_type:<15} │")
        print("    └─────────────────┘")
        
        # 显示字符统计
        print(f"\n  📊 字符统计:")
        total_chars = sum(len(order_type) for order_type in order_types)
        chinese_chars = sum(
            sum(1 for char in order_type if ord(char) > 127)
            for order_type in order_types
        )
        english_chars = total_chars - chinese_chars
        
        print(f"    • 总字符数: {total_chars}")
        print(f"    • 中文字符: {chinese_chars}")
        print(f"    • 英文字符: {english_chars}")
        print(f"    • 中文化率: {chinese_chars/total_chars*100:.1f}%")
        
        return english_chars == 0
        
    except Exception as e:
        print(f"❌ 视觉验证测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🔍 开始订单类型中文化验证测试...")
    print("=" * 60)
    
    tests = [
        ("订单类型常量测试", test_order_types_constants),
        ("GUI订单类型测试", test_gui_order_types),
        ("下单逻辑测试", test_order_placement_logic),
        ("订单确认对话框测试", test_order_confirmation_dialog),
        ("视觉验证测试", test_visual_verification),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计:")
    print(f"  • 总测试数: {total}")
    print(f"  • 通过数: {passed}")
    print(f"  • 失败数: {total - passed}")
    print(f"  • 通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！订单类型完全中文化成功！")
        print("✨ 快速交易面板中的订单类型已完全转换为中文！")
        return True
    else:
        print(f"\n⚠️ {total - passed}个测试失败，需要进一步改进。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

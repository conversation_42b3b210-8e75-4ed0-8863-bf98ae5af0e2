#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国际化模块
Internationalization Module

提供多语言支持功能
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要类和函数
try:
    from .language_manager import LanguageManager, get_language_manager, _, set_language
    
    __all__ = [
        'LanguageManager',
        'get_language_manager',
        '_',
        'set_language'
    ]
    
except ImportError as e:
    print(f"国际化模块导入警告: {e}")
    __all__ = []

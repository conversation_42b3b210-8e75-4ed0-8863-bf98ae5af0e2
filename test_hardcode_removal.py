#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
硬编码消除测试
Test Hardcode Removal

验证硬编码消除的效果
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_constants_import():
    """测试常量导入"""
    print("🔧 测试常量导入...")
    
    try:
        from core.constants import (
            AppConstants, StyleConstants, TradingConstants, 
            MessageConstants, PathConstants
        )
        
        print("✅ 常量模块导入成功")
        
        # 测试应用常量
        print(f"  📱 应用名称: {AppConstants.APP_NAME}")
        print(f"  📐 默认窗口尺寸: {AppConstants.DEFAULT_WINDOW_WIDTH}x{AppConstants.DEFAULT_WINDOW_HEIGHT}")
        print(f"  🌍 支持的语言: {list(AppConstants.SUPPORTED_LANGUAGES.keys())}")
        
        # 测试样式常量
        print(f"  🎨 默认字体: {StyleConstants.FONT_FAMILY_SANS}")
        print(f"  📏 默认字体大小: {StyleConstants.FONT_SIZE_NORMAL}")
        print(f"  📦 默认内边距: {StyleConstants.PADDING_MEDIUM}")
        
        # 测试交易常量
        print(f"  💰 默认初始资金: {TradingConstants.DEFAULT_INITIAL_CAPITAL}")
        print(f"  📊 支持的交易对数量: {len(TradingConstants.ALL_SUPPORTED_SYMBOLS)}")
        print(f"  📋 订单类型数量: {len(TradingConstants.ALL_ORDER_TYPES)}")
        
        # 测试消息常量
        all_messages = MessageConstants.get_all_message_keys()
        print(f"  📝 消息键总数: {len(all_messages)}")
        
        # 测试路径常量
        print(f"  📁 项目根目录: {PathConstants.PROJECT_ROOT}")
        print(f"  🔧 配置目录: {PathConstants.CONFIG_DIR}")
        
        return True
        
    except Exception as e:
        print(f"❌ 常量导入失败: {e}")
        return False


def test_enhanced_config_manager():
    """测试增强配置管理器"""
    print("\n🔧 测试增强配置管理器...")
    
    try:
        from core.config.enhanced_config_manager import EnhancedConfigManager
        
        config = EnhancedConfigManager()
        print("✅ 增强配置管理器创建成功")
        
        # 测试配置获取
        app_name = config.get('app.app_name')
        window_width = config.get('window.width')
        trading_capital = config.get('trading.initial_capital')
        
        print(f"  📱 应用名称: {app_name}")
        print(f"  📐 窗口宽度: {window_width}")
        print(f"  💰 初始资金: {trading_capital}")
        
        # 测试窗口几何字符串
        geometry = config.get_window_geometry()
        print(f"  📏 窗口几何: {geometry}")
        
        # 测试字体配置
        default_font = config.get_font_config('default')
        print(f"  🔤 默认字体: {default_font}")
        
        # 测试配置验证
        errors = config.validate_all_config()
        if errors:
            print(f"  ⚠️ 配置验证警告: {len(errors)} 个问题")
            for key, error in errors.items():
                print(f"    - {key}: {error}")
        else:
            print("  ✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强配置管理器测试失败: {e}")
        return False


def test_professional_gui_constants():
    """测试专业GUI中的常量使用"""
    print("\n🔧 测试专业GUI常量使用...")
    
    try:
        # 导入专业GUI
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        print("✅ 专业GUI导入成功")
        
        # 检查是否使用了常量
        import inspect
        source = inspect.getsource(ProfessionalTradingGUI)
        
        # 检查常量使用情况
        constant_usage = {
            'AppConstants': source.count('AppConstants.'),
            'StyleConstants': source.count('StyleConstants.'),
            'TradingConstants': source.count('TradingConstants.'),
            'MessageConstants': source.count('MessageConstants.'),
            'PathConstants': source.count('PathConstants.')
        }
        
        print("  📊 常量使用统计:")
        for const_name, count in constant_usage.items():
            if count > 0:
                print(f"    ✅ {const_name}: {count} 次使用")
            else:
                print(f"    ⚠️ {const_name}: 未使用")
        
        total_usage = sum(constant_usage.values())
        print(f"  📈 总常量使用次数: {total_usage}")
        
        # 检查硬编码减少情况
        hardcoded_patterns = [
            '"BTC/USDT"',
            '"Market"',
            '"1600x1000"',
            '"#2d2d2d"',
            '"Arial"'
        ]
        
        hardcode_count = 0
        for pattern in hardcoded_patterns:
            count = source.count(pattern)
            if count > 0:
                print(f"    ⚠️ 发现硬编码: {pattern} ({count} 次)")
                hardcode_count += count
        
        if hardcode_count == 0:
            print("  ✅ 未发现明显的硬编码")
        else:
            print(f"  ⚠️ 发现 {hardcode_count} 个硬编码")
        
        return True
        
    except Exception as e:
        print(f"❌ 专业GUI常量测试失败: {e}")
        return False


def test_code_quality_improvement():
    """测试代码质量改进"""
    print("\n🔧 测试代码质量改进...")
    
    try:
        from tools.code_quality_checker import CodeQualityChecker
        
        # 检查当前项目
        project_root = os.path.dirname(os.path.abspath(__file__))
        checker = CodeQualityChecker(project_root)
        
        print("✅ 代码质量检查器创建成功")
        
        # 执行检查
        issues = checker.check_project()
        summary = checker.get_summary()
        
        print(f"  📊 代码质量统计:")
        print(f"    📋 总问题数: {summary['total']}")
        print(f"    🔴 严重问题: {summary['high']}")
        print(f"    🟡 中等问题: {summary['medium']}")
        print(f"    🟢 轻微问题: {summary['low']}")
        
        # 按类型统计
        issue_types = ['MAGIC_NUMBER', 'HARDCODED_STRING', 'HARDCODED_COLOR', 
                      'HARDCODED_PATH', 'HARDCODED_URL']
        
        print(f"  📈 硬编码问题统计:")
        for issue_type in issue_types:
            count = summary.get(issue_type, 0)
            if count > 0:
                print(f"    ⚠️ {issue_type}: {count}")
            else:
                print(f"    ✅ {issue_type}: 0")
        
        # 质量评级
        if summary['high'] == 0 and summary['medium'] < 10:
            quality_grade = "A+"
        elif summary['high'] == 0 and summary['medium'] < 20:
            quality_grade = "A"
        elif summary['high'] < 5 and summary['medium'] < 30:
            quality_grade = "B+"
        else:
            quality_grade = "B"
        
        print(f"  🏆 代码质量等级: {quality_grade}")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码质量检查失败: {e}")
        return False


def test_environment_variables():
    """测试环境变量支持"""
    print("\n🔧 测试环境变量支持...")
    
    try:
        import os
        from core.config.enhanced_config_manager import EnhancedConfigManager
        
        # 设置测试环境变量
        test_env_vars = {
            'TRADING_INITIAL_CAPITAL': '20000',
            'TRADING_WINDOW_WIDTH': '1920',
            'TRADING_THEME': 'dark',
            'TRADING_DEBUG_MODE': 'true'
        }
        
        # 设置环境变量
        for key, value in test_env_vars.items():
            os.environ[key] = value
        
        # 创建配置管理器
        config = EnhancedConfigManager()
        
        # 验证环境变量是否生效
        capital = config.get('trading.initial_capital')
        width = config.get('window.width')
        theme = config.get('app.default_theme')
        debug = config.get('app.debug_mode')
        
        print("✅ 环境变量配置测试:")
        print(f"  💰 初始资金: {capital} (期望: 20000.0)")
        print(f"  📐 窗口宽度: {width} (期望: 1920)")
        print(f"  🎨 主题: {theme} (期望: dark)")
        print(f"  🐛 调试模式: {debug} (期望: True)")
        
        # 清理环境变量
        for key in test_env_vars:
            if key in os.environ:
                del os.environ[key]
        
        # 验证结果
        success = (
            capital == 20000.0 and
            width == 1920 and
            theme == 'dark' and
            debug is True
        )
        
        if success:
            print("  ✅ 环境变量配置完全正确")
        else:
            print("  ⚠️ 环境变量配置部分正确")
        
        return success
        
    except Exception as e:
        print(f"❌ 环境变量测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始硬编码消除测试")
    print("=" * 60)
    
    tests = [
        ("常量导入", test_constants_import),
        ("增强配置管理器", test_enhanced_config_manager),
        ("专业GUI常量使用", test_professional_gui_constants),
        ("代码质量改进", test_code_quality_improvement),
        ("环境变量支持", test_environment_variables),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🏆 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！硬编码消除成功！")
        grade = "A+"
    elif passed >= total * 0.8:
        print("👍 大部分测试通过，硬编码消除基本成功")
        grade = "A"
    elif passed >= total * 0.6:
        print("⚠️ 部分测试通过，硬编码消除部分成功")
        grade = "B+"
    else:
        print("❌ 多数测试失败，需要进一步改进")
        grade = "B"
    
    print(f"📈 硬编码消除质量等级: {grade}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

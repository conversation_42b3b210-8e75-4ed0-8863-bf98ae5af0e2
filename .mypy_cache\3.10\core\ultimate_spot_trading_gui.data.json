{".class": "MypyFile", "_fullname": "core.ultimate_spot_trading_gui", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "API_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.API_AVAILABLE", "name": "API_AVAILABLE", "type": "builtins.bool"}}, "BacktestingEngine": {".class": "SymbolTableNode", "cross_ref": "core.strategy.backtesting_engine.BacktestingEngine", "kind": "Gdef"}, "CHART_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.CHART_SYSTEM_AVAILABLE", "name": "CHART_SYSTEM_AVAILABLE", "type": "builtins.bool"}}, "CandlestickChart": {".class": "SymbolTableNode", "cross_ref": "core.ui.charts.candlestick_chart.CandlestickChart", "kind": "Gdef"}, "MONITORING_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.MONITORING_SYSTEM_AVAILABLE", "name": "MONITORING_SYSTEM_AVAILABLE", "type": "builtins.bool"}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ReportGenerator": {".class": "SymbolTableNode", "cross_ref": "core.monitoring.report_generator.ReportGenerator", "kind": "Gdef"}, "STRATEGY_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.STRATEGY_SYSTEM_AVAILABLE", "name": "STRATEGY_SYSTEM_AVAILABLE", "type": "builtins.bool"}}, "SimpleSimulator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator", "name": "SimpleSimulator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.ultimate_spot_trading_gui", "mro": ["core.ultimate_spot_trading_gui.SimpleSimulator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.__init__", "name": "__init__", "type": null}}, "get_growth_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.get_growth_info", "name": "get_growth_info", "type": null}}, "get_simulated_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.get_simulated_data", "name": "get_simulated_data", "type": null}}, "is_running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.is_running", "name": "is_running", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.start", "name": "start", "type": null}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.stop", "name": "stop", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.ultimate_spot_trading_gui.SimpleSimulator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.ultimate_spot_trading_gui.SimpleSimulator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrategyConfig": {".class": "SymbolTableNode", "cross_ref": "core.strategy.strategy_manager.StrategyConfig", "kind": "Gdef"}, "TRADING_CORE_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.TRADING_CORE_AVAILABLE", "name": "TRADING_CORE_AVAILABLE", "type": "builtins.bool"}}, "UltimateSpotTradingGUI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI", "name": "UltimateSpotTradingGUI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.ultimate_spot_trading_gui", "mro": ["core.ultimate_spot_trading_gui.UltimateSpotTradingGUI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.__init__", "name": "__init__", "type": null}}, "account_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.account_data", "name": "account_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "account_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.account_labels", "name": "account_labels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "analyze_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.analyze_positions", "name": "analyze_positions", "type": null}}, "apply_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.apply_parameters", "name": "apply_parameters", "type": null}}, "candlestick_chart": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.candlestick_chart", "name": "candlestick_chart", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "check_position_risks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.check_position_risks", "name": "check_position_risks", "type": null}}, "clear_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.clear_history", "name": "clear_history", "type": null}}, "connect_gate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.connect_gate", "name": "connect_gate", "type": null}}, "connect_simulation_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.connect_simulation_mode", "name": "connect_simulation_mode", "type": null}}, "export_trading_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.export_trading_data", "name": "export_trading_data", "type": null}}, "generate_trading_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.generate_trading_report", "name": "generate_trading_report", "type": null}}, "history_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.history_tree", "name": "history_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_performance_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.init_performance_monitoring", "name": "init_performance_monitoring", "type": null}}, "init_trading_core": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.init_trading_core", "name": "init_trading_core", "type": null}}, "is_connected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.is_connected", "name": "is_connected", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.is_running", "name": "is_running", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "load_chart_test_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.load_chart_test_data", "name": "load_chart_test_data", "type": null}}, "log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.log_message", "name": "log_message", "type": null}}, "log_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.log_text", "name": "log_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "market_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.market_data", "name": "market_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "market_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.market_text", "name": "market_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "metric_displays": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.metric_displays", "name": "metric_displays", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_closing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.on_closing", "name": "on_closing", "type": null}}, "on_order_filled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.on_order_filled", "name": "on_order_filled", "type": null}}, "on_risk_alert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.on_risk_alert", "name": "on_risk_alert", "type": null}}, "on_system_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.on_system_error", "name": "on_system_error", "type": null}}, "param_vars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.param_vars", "name": "param_vars", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "perf_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.perf_labels", "name": "perf_labels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "performance_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.performance_data", "name": "performance_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "performance_displays": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.performance_displays", "name": "performance_displays", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "performance_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.performance_text", "name": "performance_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "positions_tree": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.positions_tree", "name": "positions_tree", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "real_trading_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.real_trading_mode", "name": "real_trading_mode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "refresh_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.refresh_history", "name": "refresh_history", "type": null}}, "refresh_market_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.refresh_market_data", "name": "refresh_market_data", "type": null}}, "refresh_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.refresh_positions", "name": "refresh_positions", "type": null}}, "register_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.register_resources", "name": "register_resources", "type": null}}, "reset_all_indicators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.reset_all_indicators", "name": "reset_all_indicators", "type": null}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.root", "name": "root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.run", "name": "run", "type": null}}, "run_backtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.run_backtest", "name": "run_backtest", "type": null}}, "setup_chart_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_chart_panel", "name": "setup_chart_panel", "type": null}}, "setup_history_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_history_panel", "name": "setup_history_panel", "type": null}}, "setup_left_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_left_panel", "name": "setup_left_panel", "type": null}}, "setup_market_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_market_panel", "name": "setup_market_panel", "type": null}}, "setup_monitor_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_monitor_panel", "name": "setup_monitor_panel", "type": null}}, "setup_performance_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_performance_panel", "name": "setup_performance_panel", "type": null}}, "setup_positions_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_positions_panel", "name": "setup_positions_panel", "type": null}}, "setup_right_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_right_panel", "name": "setup_right_panel", "type": null}}, "setup_strategy_panel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_strategy_panel", "name": "setup_strategy_panel", "type": null}}, "setup_ui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.setup_ui", "name": "setup_ui", "type": null}}, "show_config_dialog": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.show_config_dialog", "name": "show_config_dialog", "type": null}}, "show_resource_monitor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.show_resource_monitor", "name": "show_resource_monitor", "type": null}}, "show_risk_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.show_risk_warning", "name": "show_risk_warning", "type": null}}, "simulation_engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.simulation_engine", "name": "simulation_engine", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "simulation_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.simulation_params", "name": "simulation_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start_real_data_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.start_real_data_update", "name": "start_real_data_update", "type": null}}, "start_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.start_simulation", "name": "start_simulation", "type": null}}, "start_status_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.start_status_update", "name": "start_status_update", "type": null}}, "start_strategy_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.start_strategy_monitoring", "name": "start_strategy_monitoring", "type": null}}, "status_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.status_label", "name": "status_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stop_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.stop_simulation", "name": "stop_simulation", "type": null}}, "stop_strategy_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.stop_strategy_monitoring", "name": "stop_strategy_monitoring", "type": null}}, "strategy_status_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.strategy_status_text", "name": "strategy_status_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "toggle_real_trading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.toggle_real_trading", "name": "toggle_real_trading", "type": null}}, "trading_core": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.trading_core", "name": "trading_core", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_all_displays": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_all_displays", "name": "update_all_displays", "type": null}}, "update_performance_displays": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_performance_displays", "name": "update_performance_displays", "type": null}}, "update_real_market_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "market_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_real_market_data", "name": "update_real_market_data", "type": null}}, "update_simulated_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_simulated_data", "name": "update_simulated_data", "type": null}}, "update_strategy_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_strategy_status", "name": "update_strategy_status", "type": null}}, "update_title_for_real_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_title_for_real_data", "name": "update_title_for_real_data", "type": null}}, "update_trading_button_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "new_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_trading_button_text", "name": "update_trading_button_text", "type": null}}, "update_trading_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.update_trading_history", "name": "update_trading_history", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.ultimate_spot_trading_gui.UltimateSpotTradingGUI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.ultimate_spot_trading_gui.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "ccxt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.ccxt", "name": "ccxt", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.ccxt", "source_any": null, "type_of_any": 3}}}, "current_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.ultimate_spot_trading_gui.current_dir", "name": "current_dir", "type": "builtins.str"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "doubling_simulator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.doubling_simulator", "name": "doubling_simulator", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.doubling_simulator", "source_any": null, "type_of_any": 3}}}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "core.ultimate_spot_trading_gui.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "gate_api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.gate_api", "name": "gate_api", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.gate_api", "source_any": null, "type_of_any": 3}}}, "get_performance_monitor": {".class": "SymbolTableNode", "cross_ref": "core.monitoring.performance_monitor.get_performance_monitor", "kind": "Gdef"}, "get_resource_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.get_resource_manager", "name": "get_resource_manager", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.get_resource_manager", "source_any": null, "type_of_any": 3}}}, "get_signal_generator": {".class": "SymbolTableNode", "cross_ref": "core.strategy.signal_generator.get_signal_generator", "kind": "Gdef"}, "get_strategy_manager": {".class": "SymbolTableNode", "cross_ref": "core.strategy.strategy_manager.get_strategy_manager", "kind": "Gdef"}, "get_trading_system_core": {".class": "SymbolTableNode", "cross_ref": "core.trading_system_core.get_trading_system_core", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "core.ultimate_spot_trading_gui.main", "name": "main", "type": null}}, "messagebox": {".class": "SymbolTableNode", "cross_ref": "tkinter.messagebox", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "show_api_login_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.show_api_login_dialog", "name": "show_api_login_dialog", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.show_api_login_dialog", "source_any": null, "type_of_any": 3}}}, "show_config_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.show_config_dialog", "name": "show_config_dialog", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.show_config_dialog", "source_any": null, "type_of_any": 3}}}, "show_resource_monitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "core.ultimate_spot_trading_gui.show_resource_monitor", "name": "show_resource_monitor", "type": {".class": "AnyType", "missing_import_name": "core.ultimate_spot_trading_gui.show_resource_monitor", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "tk": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef"}, "ttk": {".class": "SymbolTableNode", "cross_ref": "tkinter.ttk", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\ultimate_spot_trading_gui.py"}
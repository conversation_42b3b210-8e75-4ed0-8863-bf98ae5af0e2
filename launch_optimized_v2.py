#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动优化版企业级现货交易系统 v2.0
Launch Optimized Enterprise Spot Trading System v2.0
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_header():
    """打印启动头部信息"""
    print("=" * 80)
    print("🚀 企业级现货交易系统 v2.0.0 - 优化版")
    print("   Enterprise Spot Trading System - Optimized Edition")
    print("=" * 80)
    print()

def check_dependencies():
    """检查基础依赖包"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        ('tkinter', 'GUI界面'),
        ('json', '配置管理'),
        ('threading', '多线程支持'),
        ('datetime', '时间处理')
    ]
    
    missing_packages = []
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} - {description}")
    
    # 检查可选依赖
    optional_packages = [
        ('numpy', '数据处理优化'),
        ('pandas', '数据分析'),
        ('psutil', '系统监控'),
        ('ccxt', '交易所API')
    ]
    
    print("\n📦 可选优化模块:")
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ⚠️ {package} - {description} (未安装)")
    
    if missing_packages:
        print(f"\n❌ 缺少必需依赖: {', '.join(missing_packages)}")
        return False
    
    print("\n✅ 基础依赖检查完成")
    return True

def check_system_files():
    """检查系统文件完整性"""
    print("\n📁 检查系统文件...")
    
    required_files = [
        ('core/ultimate_trading_gui.py', '主GUI界面'),
        ('core/simple_system_integration.py', '优化集成模块'),
        ('config.json', '系统配置文件')
    ]
    
    missing_files = []
    for file_path, description in required_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"   ✅ {file_path} - {description}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path} - {description}")
    
    if missing_files:
        print(f"\n❌ 缺少系统文件: {', '.join(missing_files)}")
        return False
    
    print("\n✅ 系统文件检查完成")
    return True

def show_system_info():
    """显示系统信息"""
    print("\n📊 系统信息:")
    print("   🎯 版本: v2.0.0 优化版")
    print("   📅 发布日期: 2025年5月28日")
    print("   🏢 企业级现货交易系统")
    print()
    print("🚀 优化功能:")
    print("   📈 智能策略优化")
    print("   🧪 自动化系统测试")
    print("   💡 智能提示系统")
    print("   🎨 多主题界面")
    print("   📊 性能监控")
    print("   🔒 安全框架")
    print()

def main():
    """主函数"""
    try:
        # 打印启动信息
        print_header()
        
        # 检查依赖
        if not check_dependencies():
            print("\n⚠️ 依赖检查失败，但将尝试启动基础功能...")
            input("按 Enter 键继续...")
        
        # 检查系统文件
        if not check_system_files():
            print("\n❌ 系统文件检查失败，无法启动")
            input("按 Enter 键退出...")
            return
        
        # 显示系统信息
        show_system_info()
        
        print("🔧 初始化系统...")
        
        # 导入主要模块
        try:
            from ultimate_trading_gui import UltimateTradingGUI
            from simple_system_integration import SimpleOptimizedSystemFactory
            print("   ✅ 核心模块导入成功")
        except ImportError as e:
            print(f"   ❌ 模块导入失败: {e}")
            print("   🔄 尝试使用基础模式...")
            from ultimate_trading_gui import UltimateTradingGUI
            SimpleOptimizedSystemFactory = None
        
        # 创建系统实例
        print("   🔧 创建系统实例...")
        
        if SimpleOptimizedSystemFactory:
            # 使用优化版系统
            print("   🚀 启动优化版系统...")
            app = SimpleOptimizedSystemFactory.create_integrated_system(UltimateTradingGUI)
            
            # 显示集成报告
            report = SimpleOptimizedSystemFactory.get_optimization_report()
            print(f"   📋 集成状态: {report['status']}")
            print(f"   🔧 集成模块: {len(report['integrated_modules'])} 个")
            print(f"   ⭐ 新增功能: {len(report['features_added'])} 项")
        else:
            # 使用基础版系统
            print("   📱 启动基础版系统...")
            app = UltimateTradingGUI()
        
        print("\n🎉 系统初始化完成!")
        print("🖥️ 正在启动图形界面...")
        print("=" * 80)
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断启动")
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        print(f"\n❌ 系统启动失败: {e}")
        print("\n🔧 故障排除建议:")
        print("   1. 检查Python环境是否正确")
        print("   2. 确认所有依赖包已安装")
        print("   3. 验证系统文件完整性")
        print("   4. 查看日志文件获取详细错误信息")
        
        messagebox.showerror(
            "启动失败", 
            f"系统启动时发生错误:\n{e}\n\n请查看控制台输出获取详细信息"
        )
    
    print("\n👋 系统已关闭")
    input("按 Enter 键退出...")

if __name__ == "__main__":
    main()

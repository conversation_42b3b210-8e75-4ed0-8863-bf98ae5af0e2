#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
路径常量
Path Constants

定义文件和目录路径常量
"""

import os
from pathlib import Path
from typing import Dict, Optional


class PathConstants:
    """路径常量"""
    
    # ==================== 基础路径 ====================
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    
    # 核心模块目录
    CORE_DIR = PROJECT_ROOT / "core"
    
    # 资源目录
    ASSETS_DIR = PROJECT_ROOT / "assets"
    
    # 数据目录
    DATA_DIR = PROJECT_ROOT / "data"
    
    # 配置目录
    CONFIG_DIR = PROJECT_ROOT / "config"
    
    # 日志目录
    LOGS_DIR = PROJECT_ROOT / "logs"
    
    # 备份目录
    BACKUP_DIR = PROJECT_ROOT / "backup"
    
    # 临时目录
    TEMP_DIR = PROJECT_ROOT / "temp"
    
    # 缓存目录
    CACHE_DIR = PROJECT_ROOT / "cache"
    
    # 文档目录
    DOCS_DIR = PROJECT_ROOT / "docs"
    
    # 测试目录
    TESTS_DIR = PROJECT_ROOT / "tests"
    
    # ==================== 配置文件路径 ====================
    # 主配置文件
    MAIN_CONFIG_FILE = CONFIG_DIR / "config.json"
    
    # 用户配置文件
    USER_CONFIG_FILE = CONFIG_DIR / "user_config.json"
    
    # 交易配置文件
    TRADING_CONFIG_FILE = CONFIG_DIR / "trading_config.json"
    
    # API配置文件
    API_CONFIG_FILE = CONFIG_DIR / "api_config.json"
    
    # 主题配置文件
    THEME_CONFIG_FILE = CONFIG_DIR / "theme_config.json"
    
    # 语言配置文件
    LANGUAGE_CONFIG_FILE = CONFIG_DIR / "language_config.json"
    
    # 风险配置文件
    RISK_CONFIG_FILE = CONFIG_DIR / "risk_config.json"
    
    # ==================== 数据文件路径 ====================
    # 交易数据库
    TRADING_DATABASE = DATA_DIR / "trading.db"
    
    # 市场数据文件
    MARKET_DATA_FILE = DATA_DIR / "market_data.json"
    
    # 持仓数据文件
    POSITIONS_DATA_FILE = DATA_DIR / "positions.json"
    
    # 订单数据文件
    ORDERS_DATA_FILE = DATA_DIR / "orders.json"
    
    # 历史数据文件
    HISTORY_DATA_FILE = DATA_DIR / "history.json"
    
    # 性能数据文件
    PERFORMANCE_DATA_FILE = DATA_DIR / "performance.json"
    
    # 用户数据文件
    USER_DATA_FILE = DATA_DIR / "user_data.json"
    
    # ==================== 日志文件路径 ====================
    # 主日志文件
    MAIN_LOG_FILE = LOGS_DIR / "trading_system.log"
    
    # 交易日志文件
    TRADING_LOG_FILE = LOGS_DIR / "trading.log"
    
    # API日志文件
    API_LOG_FILE = LOGS_DIR / "api.log"
    
    # 错误日志文件
    ERROR_LOG_FILE = LOGS_DIR / "error.log"
    
    # 调试日志文件
    DEBUG_LOG_FILE = LOGS_DIR / "debug.log"
    
    # 性能日志文件
    PERFORMANCE_LOG_FILE = LOGS_DIR / "performance.log"
    
    # 安全日志文件
    SECURITY_LOG_FILE = LOGS_DIR / "security.log"
    
    # ==================== 资源文件路径 ====================
    # 图标文件
    APP_ICON = ASSETS_DIR / "icons" / "app_icon.ico"
    TRADING_ICON = ASSETS_DIR / "icons" / "trading_icon.ico"
    SUCCESS_ICON = ASSETS_DIR / "icons" / "success.png"
    ERROR_ICON = ASSETS_DIR / "icons" / "error.png"
    WARNING_ICON = ASSETS_DIR / "icons" / "warning.png"
    
    # 图片文件
    LOGO_IMAGE = ASSETS_DIR / "images" / "logo.png"
    BACKGROUND_IMAGE = ASSETS_DIR / "images" / "background.png"
    SPLASH_IMAGE = ASSETS_DIR / "images" / "splash.png"
    
    # 字体文件
    FONTS_DIR = ASSETS_DIR / "fonts"
    DEFAULT_FONT = FONTS_DIR / "default.ttf"
    MONO_FONT = FONTS_DIR / "mono.ttf"
    
    # 样式文件
    STYLES_DIR = ASSETS_DIR / "styles"
    DEFAULT_STYLE = STYLES_DIR / "default.css"
    DARK_THEME = STYLES_DIR / "dark_theme.css"
    LIGHT_THEME = STYLES_DIR / "light_theme.css"
    
    # ==================== 语言文件路径 ====================
    # 语言包目录
    LANGUAGES_DIR = CORE_DIR / "i18n" / "languages"
    
    # 各语言文件
    CHINESE_LANG_FILE = LANGUAGES_DIR / "zh_CN.json"
    ENGLISH_LANG_FILE = LANGUAGES_DIR / "en_US.json"
    JAPANESE_LANG_FILE = LANGUAGES_DIR / "ja_JP.json"
    KOREAN_LANG_FILE = LANGUAGES_DIR / "ko_KR.json"
    
    # ==================== 备份文件路径 ====================
    # 配置备份
    CONFIG_BACKUP_DIR = BACKUP_DIR / "config"
    
    # 数据备份
    DATA_BACKUP_DIR = BACKUP_DIR / "data"
    
    # 日志备份
    LOGS_BACKUP_DIR = BACKUP_DIR / "logs"
    
    # 自动备份
    AUTO_BACKUP_DIR = BACKUP_DIR / "auto"
    
    # ==================== 临时文件路径 ====================
    # 临时数据文件
    TEMP_DATA_FILE = TEMP_DIR / "temp_data.json"
    
    # 临时配置文件
    TEMP_CONFIG_FILE = TEMP_DIR / "temp_config.json"
    
    # 下载临时文件
    TEMP_DOWNLOAD_DIR = TEMP_DIR / "downloads"
    
    # 上传临时文件
    TEMP_UPLOAD_DIR = TEMP_DIR / "uploads"
    
    # ==================== 缓存文件路径 ====================
    # 市场数据缓存
    MARKET_CACHE_DIR = CACHE_DIR / "market"
    
    # 图表数据缓存
    CHART_CACHE_DIR = CACHE_DIR / "charts"
    
    # API响应缓存
    API_CACHE_DIR = CACHE_DIR / "api"
    
    # 图片缓存
    IMAGE_CACHE_DIR = CACHE_DIR / "images"
    
    # ==================== 导出文件路径 ====================
    # 报告导出目录
    REPORTS_DIR = PROJECT_ROOT / "reports"
    
    # CSV导出目录
    CSV_EXPORTS_DIR = REPORTS_DIR / "csv"
    
    # PDF导出目录
    PDF_EXPORTS_DIR = REPORTS_DIR / "pdf"
    
    # Excel导出目录
    EXCEL_EXPORTS_DIR = REPORTS_DIR / "excel"
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def ensure_directories(cls) -> None:
        """确保所有必要的目录存在"""
        directories = [
            cls.DATA_DIR, cls.CONFIG_DIR, cls.LOGS_DIR,
            cls.BACKUP_DIR, cls.TEMP_DIR, cls.CACHE_DIR,
            cls.ASSETS_DIR, cls.REPORTS_DIR, cls.LANGUAGES_DIR,
            cls.CONFIG_BACKUP_DIR, cls.DATA_BACKUP_DIR,
            cls.LOGS_BACKUP_DIR, cls.AUTO_BACKUP_DIR,
            cls.TEMP_DOWNLOAD_DIR, cls.TEMP_UPLOAD_DIR,
            cls.MARKET_CACHE_DIR, cls.CHART_CACHE_DIR,
            cls.API_CACHE_DIR, cls.IMAGE_CACHE_DIR,
            cls.CSV_EXPORTS_DIR, cls.PDF_EXPORTS_DIR,
            cls.EXCEL_EXPORTS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_absolute_path(cls, relative_path: str) -> Path:
        """获取相对于项目根目录的绝对路径"""
        return cls.PROJECT_ROOT / relative_path
    
    @classmethod
    def get_config_path(cls, config_name: str) -> Path:
        """获取配置文件路径"""
        if not config_name.endswith('.json'):
            config_name += '.json'
        return cls.CONFIG_DIR / config_name
    
    @classmethod
    def get_log_path(cls, log_name: str) -> Path:
        """获取日志文件路径"""
        if not log_name.endswith('.log'):
            log_name += '.log'
        return cls.LOGS_DIR / log_name
    
    @classmethod
    def get_data_path(cls, data_name: str) -> Path:
        """获取数据文件路径"""
        return cls.DATA_DIR / data_name
    
    @classmethod
    def get_backup_path(cls, backup_type: str, filename: str) -> Path:
        """获取备份文件路径"""
        backup_dirs = {
            'config': cls.CONFIG_BACKUP_DIR,
            'data': cls.DATA_BACKUP_DIR,
            'logs': cls.LOGS_BACKUP_DIR,
            'auto': cls.AUTO_BACKUP_DIR
        }
        
        backup_dir = backup_dirs.get(backup_type, cls.BACKUP_DIR)
        return backup_dir / filename
    
    @classmethod
    def get_cache_path(cls, cache_type: str, filename: str) -> Path:
        """获取缓存文件路径"""
        cache_dirs = {
            'market': cls.MARKET_CACHE_DIR,
            'chart': cls.CHART_CACHE_DIR,
            'api': cls.API_CACHE_DIR,
            'image': cls.IMAGE_CACHE_DIR
        }
        
        cache_dir = cache_dirs.get(cache_type, cls.CACHE_DIR)
        return cache_dir / filename
    
    @classmethod
    def get_export_path(cls, export_type: str, filename: str) -> Path:
        """获取导出文件路径"""
        export_dirs = {
            'csv': cls.CSV_EXPORTS_DIR,
            'pdf': cls.PDF_EXPORTS_DIR,
            'excel': cls.EXCEL_EXPORTS_DIR
        }
        
        export_dir = export_dirs.get(export_type, cls.REPORTS_DIR)
        return export_dir / filename
    
    @classmethod
    def clean_temp_files(cls, max_age_hours: int = 24) -> int:
        """清理临时文件"""
        import time
        
        cleaned_count = 0
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        if cls.TEMP_DIR.exists():
            for file_path in cls.TEMP_DIR.rglob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception:
                            pass
        
        return cleaned_count
    
    @classmethod
    def get_file_size(cls, file_path: Path) -> int:
        """获取文件大小（字节）"""
        try:
            return file_path.stat().st_size if file_path.exists() else 0
        except Exception:
            return 0
    
    @classmethod
    def is_path_safe(cls, path: Path) -> bool:
        """检查路径是否安全（在项目目录内）"""
        try:
            path.resolve().relative_to(cls.PROJECT_ROOT.resolve())
            return True
        except ValueError:
            return False


# 创建全局路径常量实例
PATH_CONSTANTS = PathConstants()


if __name__ == "__main__":
    # 测试路径常量
    print("📁 路径常量测试")
    
    print(f"项目根目录: {PathConstants.PROJECT_ROOT}")
    print(f"配置目录: {PathConstants.CONFIG_DIR}")
    print(f"数据目录: {PathConstants.DATA_DIR}")
    print(f"日志目录: {PathConstants.LOGS_DIR}")
    
    # 确保目录存在
    PathConstants.ensure_directories()
    print("✅ 目录结构已创建")
    
    # 测试路径生成
    config_path = PathConstants.get_config_path("test_config")
    log_path = PathConstants.get_log_path("test_log")
    print(f"配置文件路径: {config_path}")
    print(f"日志文件路径: {log_path}")
    
    # 测试路径安全性
    safe_path = PathConstants.PROJECT_ROOT / "test.txt"
    unsafe_path = Path("/etc/passwd")
    print(f"安全路径检查: {PathConstants.is_path_safe(safe_path)}")
    print(f"不安全路径检查: {PathConstants.is_path_safe(unsafe_path)}")
    
    print("✅ 路径常量测试完成")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查GATE.IO交易对格式
Check GATE.IO Trading Pair Format

检查GATE.IO实际支持的交易对格式
"""

def check_gate_symbols():
    """检查GATE.IO交易对格式"""
    print("🔍 检查GATE.IO交易对格式...")
    
    try:
        import ccxt
        
        # 创建GATE交易所实例
        exchange = ccxt.gateio({
            'sandbox': True,
            'enableRateLimit': True,
        })
        
        print("✅ GATE交易所实例创建成功")
        
        # 加载市场数据
        markets = exchange.load_markets()
        print(f"✅ 成功加载 {len(markets)} 个交易对")
        
        # 查找我们需要的交易对
        target_symbols = ['BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'DOT', 'LINK', 'MATIC']
        usdt_pairs = []
        
        print("\n🔍 查找USDT交易对...")
        for symbol in markets.keys():
            for target in target_symbols:
                if target in symbol and 'USDT' in symbol:
                    usdt_pairs.append(symbol)
        
        # 去重并排序
        usdt_pairs = sorted(list(set(usdt_pairs)))
        
        print(f"\n📊 找到 {len(usdt_pairs)} 个相关USDT交易对:")
        for pair in usdt_pairs:
            print(f"  • {pair}")
        
        # 测试获取价格
        print("\n💰 测试价格获取:")
        test_pairs = usdt_pairs[:5]  # 测试前5个
        
        for pair in test_pairs:
            try:
                ticker = exchange.fetch_ticker(pair)
                price = ticker['last']
                change = ticker['percentage'] or 0
                
                change_indicator = "🟢" if change > 0 else "🔴" if change < 0 else "🟡"
                print(f"  {change_indicator} {pair}: ${price:,.4f} ({change:+.2f}%)")
                
            except Exception as e:
                print(f"  ❌ {pair}: 获取失败 - {e}")
        
        # 创建映射建议
        print("\n🔧 建议的交易对映射:")
        mapping = {}
        for target in target_symbols:
            for pair in usdt_pairs:
                if pair.startswith(target) and 'USDT' in pair:
                    standard_format = f"{target}/USDT"
                    mapping[standard_format] = pair
                    print(f"  '{standard_format}': '{pair}',")
                    break
        
        return mapping
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def create_updated_mapping(mapping):
    """创建更新的映射配置"""
    print("\n📝 创建更新的映射配置...")
    
    config = f"""
# 更新的GATE.IO交易对映射配置

GATE_SYMBOL_MAP = {{
"""
    
    for standard, gate_format in mapping.items():
        config += f"    '{standard}': '{gate_format}',\n"
    
    config += "}\n"
    
    try:
        with open("gate_symbol_mapping.py", "w", encoding="utf-8") as f:
            f.write(config)
        print("✅ 映射配置已保存到: gate_symbol_mapping.py")
        return True
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 GATE.IO交易对格式检查")
    print("=" * 50)
    
    # 检查交易对格式
    mapping = check_gate_symbols()
    
    if mapping:
        # 创建更新的映射
        create_updated_mapping(mapping)
        
        print("\n🎉 检查完成!")
        print("\n💡 下一步:")
        print("  1. 使用找到的正确交易对格式")
        print("  2. 更新API连接器中的映射")
        print("  3. 重新测试API功能")
        
    else:
        print("\n❌ 未能获取交易对信息")
        print("可能是网络问题或API限制")
    
    return len(mapping) > 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 交易对格式检查成功!")
        else:
            print("\n⚠️ 检查过程中出现问题")
    except Exception as e:
        print(f"\n💥 检查过程出错: {e}")
    
    input("\n按回车键退出...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试融合GUI
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
core_dir = os.path.join(current_dir, 'core')
sys.path.insert(0, core_dir)
sys.path.insert(0, current_dir)

def test_imports():
    """测试导入"""
    try:
        print("测试配置管理器...")
        from config_manager import get_config_manager
        config_manager = get_config_manager()
        print(f"✅ 配置管理器: {type(config_manager)}")
        
        print("测试UX增强模块...")
        from ux_enhancement import SmartHintSystem, InterfaceOptimizer
        hint_system = SmartHintSystem()
        interface_opt = InterfaceOptimizer()
        print(f"✅ UX模块: {type(hint_system)}, {type(interface_opt)}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_simple_fusion_gui():
    """创建简化版融合GUI"""
    
    class SimpleFusionGUI:
        def __init__(self):
            self.root = tk.Tk()
            self.setup_window()
            self.setup_interface()
        
        def setup_window(self):
            self.root.title("融合交易GUI - 快速测试版")
            self.root.geometry("1200x800")
            self.root.minsize(800, 600)
            
        def setup_interface(self):
            # 主容器
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 标题
            title_label = ttk.Label(main_frame, text="🚀 融合交易系统", 
                                  font=("Arial", 16, "bold"))
            title_label.pack(pady=10)
            
            # 创建笔记本
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)
            
            # 交易标签页
            self.create_trading_tab(notebook)
            
            # 监控标签页  
            self.create_monitoring_tab(notebook)
            
            # 分析标签页
            self.create_analysis_tab(notebook)
            
            # 状态栏
            self.create_status_bar()
            
        def create_trading_tab(self, notebook):
            trading_frame = ttk.Frame(notebook)
            notebook.add(trading_frame, text="💰 交易")
            
            # 交易面板
            trade_panel = ttk.LabelFrame(trading_frame, text="快速交易", padding="10")
            trade_panel.pack(fill=tk.X, padx=10, pady=10)
            
            # 交易对选择
            ttk.Label(trade_panel, text="交易对:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            pair_var = tk.StringVar(value="BTC/USDT")
            pair_combo = ttk.Combobox(trade_panel, textvariable=pair_var, width=15)
            pair_combo['values'] = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT"]
            pair_combo.grid(row=0, column=1, padx=5, pady=5)
            
            # 交易金额
            ttk.Label(trade_panel, text="金额:").grid(row=0, column=2, sticky="w", padx=5, pady=5)
            amount_var = tk.StringVar(value="100")
            amount_entry = ttk.Entry(trade_panel, textvariable=amount_var, width=10)
            amount_entry.grid(row=0, column=3, padx=5, pady=5)
            
            # 交易按钮
            ttk.Button(trade_panel, text="买入", 
                      command=lambda: self.show_trade_result("买入", pair_var.get(), amount_var.get())).grid(row=0, column=4, padx=5, pady=5)
            ttk.Button(trade_panel, text="卖出", 
                      command=lambda: self.show_trade_result("卖出", pair_var.get(), amount_var.get())).grid(row=0, column=5, padx=5, pady=5)
            
        def create_monitoring_tab(self, notebook):
            monitoring_frame = ttk.Frame(notebook)
            notebook.add(monitoring_frame, text="📊 监控")
            
            # 监控面板
            monitor_panel = ttk.LabelFrame(monitoring_frame, text="实时监控", padding="10")
            monitor_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 账户信息
            info_text = """
🏦 账户余额: 10,156.78 USDT
📈 今日盈亏: +156.78 USDT (+1.55%)
📋 活跃订单: 3 个
⚡ 系统状态: 运行正常
🎯 当前策略: 现货滚雪球
🔄 更新时间: 实时
            """
            
            info_label = ttk.Label(monitor_panel, text=info_text, font=("Consolas", 11))
            info_label.pack(anchor="w", pady=10)
            
        def create_analysis_tab(self, notebook):
            analysis_frame = ttk.Frame(notebook)
            notebook.add(analysis_frame, text="📈 分析")
            
            # 分析面板
            analysis_panel = ttk.LabelFrame(analysis_frame, text="交易分析", padding="10")
            analysis_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 分析结果
            analysis_text = """
=== 24小时交易分析 ===

📊 总交易次数: 15
✅ 成功交易: 11 (73.3%)
❌ 失败交易: 4 (26.7%)
💰 总盈利: +245.67 USDT
📈 最大盈利: +67.89 USDT
📉 最大亏损: -23.45 USDT
🎯 胜率: 73.3%
⚖️ 盈亏比: 2.9:1

=== 策略表现 ===
• 现货滚雪球策略运行稳定
• 建议继续当前配置
• 风险控制良好
            """
            
            text_widget = tk.Text(analysis_panel, wrap=tk.WORD, height=15, font=("Consolas", 10))
            text_widget.pack(fill=tk.BOTH, expand=True)
            text_widget.insert(tk.END, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        def create_status_bar(self):
            status_frame = ttk.Frame(self.root)
            status_frame.pack(fill=tk.X, side=tk.BOTTOM)
            
            self.status_label = ttk.Label(status_frame, text="✅ 融合GUI测试版启动成功", 
                                        relief=tk.SUNKEN, anchor=tk.W)
            self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)
            
            time_label = ttk.Label(status_frame, text="🕒 " + 
                                 tk.datetime.datetime.now().strftime("%H:%M:%S"), 
                                 relief=tk.SUNKEN)
            time_label.pack(side=tk.RIGHT, padx=2, pady=2)
            
        def show_trade_result(self, action, pair, amount):
            messagebox.showinfo("交易确认", 
                              f"模拟{action}订单已提交\n"
                              f"交易对: {pair}\n"
                              f"金额: {amount} USDT\n"
                              f"状态: 等待执行")
            self.status_label.config(text=f"📝 {action}订单: {pair} {amount}USDT")
            
        def run(self):
            self.root.mainloop()
    
    return SimpleFusionGUI()

def main():
    """主函数"""
    print("🚀 启动融合GUI测试...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("跳过高级功能，使用基础GUI...")
    
    print("\n📱 启动融合GUI界面...")
    
    try:
        app = create_simple_fusion_gui()
        print("✅ GUI创建成功，启动界面...")
        app.run()
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        messagebox.showerror("启动失败", f"融合GUI启动失败:\n{e}")

if __name__ == "__main__":
    main()

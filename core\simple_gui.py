#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现货版GUI - 专业现货交易系统
Spot Trading GUI - Professional Spot Trading System
"""

import hashlib
import hmac
import json
import random
import threading
import time
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, ttk
from urllib.parse import urlencode

import requests

# 尝试导入真实市场数据，如果失败则使用模拟数据
try:
    from real_market_data import get_real_market_data

    REAL_DATA_AVAILABLE = True
except ImportError:
    REAL_DATA_AVAILABLE = False

    def get_real_market_data():
        """模拟的市场数据函数"""
        return {}


class SpotTradingGUI:
    """现货交易GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💎 终极版交易系统 - 现货版")
        self.root.geometry("900x700")
        self.root.configure(bg="#1e1e1e")

        # 现货交易对数据
        self.spot_pairs = {
            "BTC/USDT": {
                "price": 107951.00,
                "change": -0.44,
                "volume": 2456789,
                "market_cap": "2.1T",
            },
            "ETH/USDT": {
                "price": 2508.35,
                "change": -1.77,
                "volume": 1234567,
                "market_cap": "301B",
            },
            "SOL/USDT": {
                "price": 245.78,
                "change": 1.56,
                "volume": 567890,
                "market_cap": "115B",
            },
            "BNB/USDT": {
                "price": 692.45,
                "change": 1.23,
                "volume": 345678,
                "market_cap": "100B",
            },
            "ADA/USDT": {
                "price": 1.23,
                "change": -2.34,
                "volume": 234567,
                "market_cap": "43B",
            },
            "DOT/USDT": {
                "price": 8.95,
                "change": 0.78,
                "volume": 123456,
                "market_cap": "12B",
            },
            "LINK/USDT": {
                "price": 25.67,
                "change": 0.89,
                "volume": 98765,
                "market_cap": "15B",
            },
            "UNI/USDT": {
                "price": 15.43,
                "change": -1.23,
                "volume": 87654,
                "market_cap": "9B",
            },
        }

        # 现货交易配置
        self.trading_config = {
            "initial_capital": 1000.0,  # 初始USDT资金
            "usdt_balance": 1000.0,  # USDT余额
            "total_value": 1000.0,  # 总资产价值
            "daily_pnl": 0.0,  # 当日盈亏
            "total_pnl": 0.0,  # 总盈亏
            "active_strategies": ["布林带策略", "MACD策略", "RSI策略"],
            "strategy_allocation": {
                "布林带策略": 40,
                "MACD策略": 35,
                "RSI策略": 25,
            },
            "strategy_used_funds": {
                "布林带策略": 0.0,
                "MACD策略": 0.0,
                "RSI策略": 0.0,
            },  # 各策略已使用资金
        }

        # 现货持仓 - 修复：支持所有交易对
        self.spot_holdings = {
            "BTC": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "ETH": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "SOL": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "BNB": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "ADA": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "DOT": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
            "LINK": {
                "amount": 0.0,
                "avg_price": 0.0,
                "value": 0.0,
                "pnl": 0.0,
            },
            "UNI": {"amount": 0.0, "avg_price": 0.0, "value": 0.0, "pnl": 0.0},
        }

        # 现货策略状态 - 使用数据库中评分最高的策略
        self.strategies = {
            "布林带策略": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "success_rate": 0,
                "win_rate": 0,
                "wins": 0,
                "signal_strength": 0.0,
                "bb_signals": 0,
                "squeeze_signals": 0,
                "divergence_signals": 0,
            },
            "MACD策略": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "success_rate": 0,
                "win_rate": 0,
                "wins": 0,
                "signal_strength": 0.0,
                "golden_cross": 0,
                "death_cross": 0,
                "divergence_signals": 0,
            },
            "RSI策略": {
                "status": "运行中",
                "pnl": 0.0,
                "trades": 0,
                "success_rate": 0,
                "win_rate": 0,
                "wins": 0,
                "signal_strength": 0.0,
                "oversold_signals": 0,
                "overbought_signals": 0,
                "divergence_signals": 0,
            },
        }

        # 布林带策略配置
        self.bollinger_config = {
            "period": 20,  # 布林带周期
            "std_dev": 2.0,  # 标准差倍数
            "rsi_period": 14,  # RSI周期
            "volume_threshold": 1.5,  # 成交量阈值
            "min_amount": 20,  # 最小交易金额
            "target_symbols": ["BTC", "ETH", "SOL", "BNB"],
            "squeeze_threshold": 0.1,  # 布林带挤压阈值
            "divergence_window": 20,  # 背离检测窗口
        }

        # MACD策略配置
        self.macd_config = {
            "fast_period": 12,  # 快线周期
            "slow_period": 26,  # 慢线周期
            "signal_period": 9,  # 信号线周期
            "histogram_threshold": 0.1,  # 直方图阈值
            "min_amount": 20,  # 最小交易金额
            "target_symbols": ["BTC", "ETH", "SOL", "BNB", "ADA"],
            "divergence_window": 20,  # 背离检测窗口
            "trend_confirmation": True,  # 趋势确认
        }

        # RSI策略配置
        self.rsi_config = {
            "period": 14,  # RSI周期
            "overbought": 70,  # 超买阈值
            "oversold": 30,  # 超卖阈值
            "divergence_window": 20,  # 背离检测窗口
            "min_amount": 20,  # 最小交易金额
            "target_symbols": ["BTC", "ETH", "SOL", "BNB", "ADA", "DOT"],
            "momentum_period": 3,  # 动量周期
            "volume_filter": True,  # 成交量过滤
        }

        # 止盈止损配置
        self.stop_loss_take_profit_config = {
            "stop_loss_ratio": 0.15,  # 止损：下跌15%
            "take_profit_ratio": 0.50,  # 止盈：上涨50%
            "partial_take_profit": 0.3,  # 部分止盈：卖出30%
            "trailing_stop": True,  # 启用移动止损
            "trailing_ratio": 0.10,  # 移动止损：从最高点回撤10%
            "min_profit_for_trailing": 0.20,  # 盈利20%以上才启用移动止损
        }

        # 止盈止损状态跟踪
        self.stop_orders = {}  # 存储每个币种的止盈止损状态

        # 分批建仓配置
        self.batch_trading_config = {
            "enabled": True,
            "batch_count": 5,  # 分5批建仓
            "price_interval": 0.03,  # 每3%价格间隔一批
            "time_interval": 3600,  # 每小时一批（秒）
            "max_position_ratio": 0.3,  # 单币种最大30%仓位
            "min_batch_amount": 20,  # 每批最小20 USDT
        }

        # 分批建仓状态跟踪
        self.batch_orders = {}  # 存储每个币种的分批建仓状态

        # 现货杠杆配置
        self.margin_config = {
            "enabled": True,
            "max_leverage": {
                "BTC": 5,  # BTC最大5倍杠杆
                "ETH": 5,  # ETH最大5倍杠杆
                "SOL": 3,  # SOL最大3倍杠杆
                "BNB": 3,  # BNB最大3倍杠杆
                "others": 2,  # 其他币种2倍杠杆
            },
            "interest_rate": 0.0001,  # 小时利率0.01%
            "margin_ratio_warning": 0.20,  # 保证金率20%预警
            "margin_ratio_liquidation": 0.15,  # 保证金率15%强制平仓
            "max_margin_ratio": 0.80,  # 最大保证金使用率80%
        }

        # 杠杆持仓跟踪
        self.margin_positions = {}  # 存储杠杆持仓信息
        self.borrowed_amounts = {}  # 存储借币数量
        self.interest_accumulated = {}  # 存储累计利息

        # 风险管理配置
        self.risk_management_config = {
            "enabled": True,
            "max_single_position_ratio": 0.30,  # 单币种最大30%仓位
            "max_sector_exposure": 0.60,  # 单板块最大60%敞口
            "daily_loss_limit_ratio": 0.05,  # 日亏损限制5%
            "total_loss_limit_ratio": 0.20,  # 总亏损限制20%
            "max_drawdown_ratio": 0.15,  # 最大回撤15%
            "correlation_limit": 0.70,  # 相关性限制70%
            "volatility_limit": 0.50,  # 波动率限制50%
        }

        # 板块分类
        self.sector_classification = {
            "Layer1": ["BTC", "ETH"],  # 基础层
            "Smart_Contract": ["ETH", "SOL", "ADA", "DOT"],  # 智能合约
            "DeFi": ["UNI", "LINK"],  # DeFi生态
            "Exchange": ["BNB"],  # 交易所代币
            "Infrastructure": ["DOT", "LINK"],  # 基础设施
        }

        # 风险监控状态
        self.risk_status = {
            "daily_start_value": 0,  # 日初资产价值
            "peak_value": 0,  # 历史最高价值
            "last_risk_check": time.time(),  # 上次风险检查时间
            "risk_alerts": [],  # 风险警报列表
            "emergency_mode": False,  # 紧急模式状态
        }

        # 交易历史
        self.trade_history = []

        # 初始化真实市场数据
        self.market_data_provider = get_real_market_data()
        self.use_real_data = True  # 标记使用真实数据

        self.create_widgets()
        self.start_updates()

        # 启动真实市场数据
        self.start_real_market_data()

    def create_widgets(self):
        """创建现货交易界面组件"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="💎 终极版交易系统 - 现货版",
            font=("Arial", 18, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        title_label.pack(pady=10)

        # 资产状态面板
        asset_frame = tk.LabelFrame(
            self.root,
            text="💰 资产概览",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        asset_frame.pack(fill="x", padx=20, pady=5)

        # 第一行：USDT余额和总价值
        balance_row1 = tk.Frame(asset_frame, bg="#1e1e1e")
        balance_row1.pack(fill="x", padx=10, pady=5)

        self.usdt_label = tk.Label(
            balance_row1,
            text=f"💵 USDT余额: {self.trading_config['usdt_balance']:,.2f}",
            font=("Arial", 11, "bold"),
            fg="#ffaa00",
            bg="#1e1e1e",
        )
        self.usdt_label.pack(side="left")

        self.total_value_label = tk.Label(
            balance_row1,
            text=f"💎 总资产: {self.trading_config['total_value']:,.2f} USDT",
            font=("Arial", 11, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        self.total_value_label.pack(side="right")

        # 第二行：当日盈亏和总盈亏
        balance_row2 = tk.Frame(asset_frame, bg="#1e1e1e")
        balance_row2.pack(fill="x", padx=10, pady=5)

        self.daily_pnl_label = tk.Label(
            balance_row2,
            text=f"📈 当日盈亏: {self.trading_config['daily_pnl']:+.2f} USDT",
            font=("Arial", 11, "bold"),
            fg=(
                "#00ff00"
                if self.trading_config["daily_pnl"] >= 0
                else "#ff4444"
            ),
            bg="#1e1e1e",
        )
        self.daily_pnl_label.pack(side="left")

        self.total_pnl_label = tk.Label(
            balance_row2,
            text=f"💰 总盈亏: {self.trading_config['total_pnl']:+.2f} USDT",
            font=("Arial", 11, "bold"),
            fg=(
                "#00ff00"
                if self.trading_config["total_pnl"] >= 0
                else "#ff4444"
            ),
            bg="#1e1e1e",
        )
        self.total_pnl_label.pack(side="right")

        # 现货持仓显示
        holdings_frame = tk.LabelFrame(
            self.root,
            text="📊 现货持仓",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        holdings_frame.pack(fill="x", padx=20, pady=5)

        self.holdings_display_frame = tk.Frame(holdings_frame, bg="#1e1e1e")
        self.holdings_display_frame.pack(fill="x", padx=5, pady=5)

        self.update_holdings_display()

        # 策略状态 - 动态更新区域
        self.strategy_frame = tk.LabelFrame(
            self.root,
            text="🎯 现货策略",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        self.strategy_frame.pack(fill="x", padx=20, pady=5)

        # 创建策略显示区域
        self.strategy_display_frame = tk.Frame(
            self.strategy_frame, bg="#1e1e1e"
        )
        self.strategy_display_frame.pack(fill="x", padx=5, pady=5)

        # 初始化策略显示
        self.update_strategy_display()

        # 系统状态
        self.status_label = tk.Label(
            self.root,
            text="✅ 现货系统运行中 | 3个技术分析策略活跃 | 监控8个交易对",
            font=("Arial", 11),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        self.status_label.pack(pady=5)

        # 现货市场价格表格
        market_frame = tk.LabelFrame(
            self.root,
            text="📈 现货市场",
            font=("Arial", 12, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        market_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 表头
        header_frame = tk.Frame(market_frame, bg="#2e2e2e")
        header_frame.pack(fill="x", pady=5)

        tk.Label(
            header_frame,
            text="交易对",
            font=("Arial", 11, "bold"),
            width=12,
            fg="#ffffff",
            bg="#2e2e2e",
        ).pack(side="left")
        tk.Label(
            header_frame,
            text="价格",
            font=("Arial", 11, "bold"),
            width=12,
            fg="#ffffff",
            bg="#2e2e2e",
        ).pack(side="left")
        tk.Label(
            header_frame,
            text="24h变化",
            font=("Arial", 11, "bold"),
            width=10,
            fg="#ffffff",
            bg="#2e2e2e",
        ).pack(side="left")
        tk.Label(
            header_frame,
            text="成交量",
            font=("Arial", 11, "bold"),
            width=12,
            fg="#ffffff",
            bg="#2e2e2e",
        ).pack(side="left")
        tk.Label(
            header_frame,
            text="市值",
            font=("Arial", 11, "bold"),
            width=8,
            fg="#ffffff",
            bg="#2e2e2e",
        ).pack(side="left")

        # 价格显示区域
        self.price_frame = tk.Frame(market_frame, bg="#1e1e1e")
        self.price_frame.pack(fill="both", expand=True)

        # 更新价格显示
        self.update_prices()

        # 控制按钮
        button_frame = tk.Frame(self.root, bg="#1e1e1e")
        button_frame.pack(pady=10)

        tk.Button(
            button_frame,
            text="🔄 刷新数据",
            command=self.refresh_data,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="💎 现货交易",
            command=self.show_spot_trading,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="📊 持仓管理",
            command=self.show_holdings_detail,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="💰 资产配置",
            command=self.show_asset_config,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="🎯 策略管理",
            command=self.show_strategy_config,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="📊 交易历史",
            command=self.show_trade_history,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="📈 持仓分析",
            command=self.show_position_analysis,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="💎 杠杆交易",
            command=self.show_margin_trading,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="🛡️ 风险管理",
            command=self.show_risk_management,
            font=("Arial", 10),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)

        # 日志区域
        log_frame = tk.LabelFrame(
            self.root,
            text="📝 交易日志",
            font=("Arial", 10, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        )
        log_frame.pack(fill="x", padx=20, pady=10)

        self.log_text = tk.Text(
            log_frame,
            height=6,
            font=("Consolas", 9),
            bg="#2e2e2e",
            fg="#ffffff",
            insertbackground="white",
        )
        self.log_text.pack(fill="x", padx=5, pady=5)

        # 添加初始日志
        self.add_log("💎 现货交易系统启动成功")
        self.add_log("📊 正在监控8个主流现货交易对")
        self.add_log("🎯 3个技术分析策略已激活：布林带、MACD、RSI")
        self.add_log("📈 策略评分：布林带95分、MACD92分、RSI88分")
        self.add_log("✅ 现货系统运行正常")

    def update_holdings_display(self):
        """更新现货持仓显示"""
        # 清除旧显示
        for widget in self.holdings_display_frame.winfo_children():
            widget.destroy()

        has_holdings = any(
            holding["amount"] > 0 for holding in self.spot_holdings.values()
        )

        if not has_holdings:
            tk.Label(
                self.holdings_display_frame,
                text="📭 当前无现货持仓",
                font=("Arial", 10),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=10)
            return

        # 显示持仓
        for symbol, holding in self.spot_holdings.items():
            if holding["amount"] > 0:
                holding_frame = tk.Frame(
                    self.holdings_display_frame,
                    bg="#2e2e2e",
                    relief="raised",
                    bd=1,
                )
                holding_frame.pack(fill="x", padx=5, pady=2)

                # 币种信息
                info_frame = tk.Frame(holding_frame, bg="#2e2e2e")
                info_frame.pack(fill="x", padx=5, pady=2)

                tk.Label(
                    info_frame,
                    text=f"💎 {symbol}",
                    font=("Arial", 10, "bold"),
                    fg="#ffffff",
                    bg="#2e2e2e",
                ).pack(side="left")
                tk.Label(
                    info_frame,
                    text=f"数量: {holding['amount']:.6f}",
                    font=("Arial", 9),
                    fg="#ffaa00",
                    bg="#2e2e2e",
                ).pack(side="left", padx=10)
                tk.Label(
                    info_frame,
                    text=f"均价: {holding['avg_price']:.2f}",
                    font=("Arial", 9),
                    fg="#ffaa00",
                    bg="#2e2e2e",
                ).pack(side="left", padx=10)

                # 盈亏信息
                pnl_color = "#00ff00" if holding["pnl"] >= 0 else "#ff4444"
                pnl_text = f"{holding['pnl']:+.2f} USDT"
                tk.Label(
                    info_frame,
                    text=f"盈亏: {pnl_text}",
                    font=("Arial", 9, "bold"),
                    fg=pnl_color,
                    bg="#2e2e2e",
                ).pack(side="right")

    def update_prices(self):
        """更新现货价格显示 - 优化版"""
        # 检查是否需要重建界面（首次或交易对数量变化）
        current_children = len(self.price_frame.winfo_children())
        expected_children = len(self.spot_pairs)

        if current_children != expected_children:
            # 需要重建界面
            self._rebuild_price_display()
        else:
            # 只更新数据，不重建界面
            self._update_price_data()

    def _rebuild_price_display(self):
        """重建价格显示界面"""
        # 清除旧的价格显示
        for widget in self.price_frame.winfo_children():
            widget.destroy()

        # 存储价格标签引用以便后续更新
        self.price_labels = {}

        # 显示每个现货交易对
        for i, (symbol, data) in enumerate(self.spot_pairs.items()):
            row_frame = tk.Frame(self.price_frame, bg="#1e1e1e")
            row_frame.pack(fill="x", pady=1)

            # 交易对名称
            symbol_label = tk.Label(
                row_frame,
                text=symbol,
                font=("Arial", 10),
                width=12,
                anchor="w",
                fg="#ffffff",
                bg="#1e1e1e",
            )
            symbol_label.pack(side="left")

            # 价格
            price_precision = self.get_price_precision(symbol.split("/")[0])
            price_text = f"{data['price']:,.{price_precision}f}"
            price_label = tk.Label(
                row_frame,
                text=price_text,
                font=("Arial", 10),
                width=12,
                anchor="e",
                fg="#ffaa00",
                bg="#1e1e1e",
            )
            price_label.pack(side="left")

            # 24h变化
            change = data["change"]
            change_text = f"{change:+.2f}%"
            color = "#00ff00" if change >= 0 else "#ff4444"
            change_label = tk.Label(
                row_frame,
                text=change_text,
                font=("Arial", 10, "bold"),
                width=10,
                fg=color,
                bg="#1e1e1e",
                anchor="center",
            )
            change_label.pack(side="left")

            # 成交量
            volume_text = f"{data['volume']:,}"
            volume_label = tk.Label(
                row_frame,
                text=volume_text,
                font=("Arial", 10),
                width=12,
                anchor="e",
                fg="#888888",
                bg="#1e1e1e",
            )
            volume_label.pack(side="left")

            # 市值
            market_cap_label = tk.Label(
                row_frame,
                text=data["market_cap"],
                font=("Arial", 10),
                width=8,
                anchor="center",
                fg="#888888",
                bg="#1e1e1e",
            )
            market_cap_label.pack(side="left")

            # 存储标签引用
            self.price_labels[symbol] = {
                "price": price_label,
                "change": change_label,
                "volume": volume_label,
                "market_cap": market_cap_label,
            }

    def _update_price_data(self):
        """只更新价格数据，不重建界面"""
        if not hasattr(self, "price_labels"):
            self._rebuild_price_display()
            return

        for symbol, data in self.spot_pairs.items():
            if symbol in self.price_labels:
                labels = self.price_labels[symbol]

                # 更新价格
                price_precision = self.get_price_precision(
                    symbol.split("/")[0]
                )
                price_text = f"{data['price']:,.{price_precision}f}"
                labels["price"].config(text=price_text)

                # 更新24h变化
                change = data["change"]
                change_text = f"{change:+.2f}%"
                color = "#00ff00" if change >= 0 else "#ff4444"
                labels["change"].config(text=change_text, fg=color)

                # 更新成交量
                volume_text = f"{data['volume']:,}"
                labels["volume"].config(text=volume_text)

                # 更新市值（通常不变，但为了完整性）
                labels["market_cap"].config(text=data["market_cap"])

    def update_balance_display(self):
        """更新现货资产显示 - 优化版"""
        # 缓存当前值，避免重复计算
        usdt_balance = self.trading_config["usdt_balance"]
        total_value = self.trading_config["total_value"]
        daily_pnl = self.trading_config["daily_pnl"]
        total_pnl = self.trading_config["total_pnl"]

        # 检查是否需要更新（值是否发生变化）
        if not hasattr(self, "_last_balance_values"):
            self._last_balance_values = {}

        current_values = {
            "usdt": usdt_balance,
            "total": total_value,
            "daily": daily_pnl,
            "total_pnl": total_pnl,
        }

        # 只更新发生变化的值
        if self._last_balance_values.get("usdt") != usdt_balance:
            self.usdt_label.config(text=f"💵 USDT余额: {usdt_balance:,.2f}")

        if self._last_balance_values.get("total") != total_value:
            self.total_value_label.config(
                text=f"💎 总资产: {total_value:,.2f} USDT"
            )

        if self._last_balance_values.get("daily") != daily_pnl:
            daily_color = "#00ff00" if daily_pnl >= 0 else "#ff4444"
            self.daily_pnl_label.config(
                text=f"📈 当日盈亏: {daily_pnl:+.2f} USDT", fg=daily_color
            )

        if self._last_balance_values.get("total_pnl") != total_pnl:
            total_color = "#00ff00" if total_pnl >= 0 else "#ff4444"
            self.total_pnl_label.config(
                text=f"💰 总盈亏: {total_pnl:+.2f} USDT", fg=total_color
            )

        # 更新缓存值
        self._last_balance_values = current_values

    def start_real_market_data(self):
        """启动真实市场数据"""
        try:
            print("🚀 启动真实市场数据连接...")
            self.market_data_provider.start()

            # 等待数据初始化
            time.sleep(3)

            # 更新状态显示
            self.status_label.config(
                text="✅ 现货系统运行中 | 3个技术分析策略活跃 | 🌐 Gate.io实时数据连接"
            )

            self.add_log("🌐 Gate.io实时数据连接成功")
            self.add_log("📡 正在接收真实市场数据...")

            print("✅ 真实市场数据启动成功")

        except Exception as e:
            print(f"❌ 启动真实市场数据失败: {e}")
            self.add_log(f"⚠️ 真实数据连接失败，使用模拟数据: {e}")
            self.use_real_data = False

    def update_with_real_data(self):
        """使用真实数据更新现货交易对"""
        if not self.use_real_data:
            return

        try:
            # 获取真实市场数据
            real_data = self.market_data_provider.get_spot_pairs()

            if real_data:
                # 更新现货交易对数据
                for symbol, data in real_data.items():
                    if (
                        symbol in self.spot_pairs
                        and data.get("last_update", 0) > 0
                    ):
                        # 更新价格和变化
                        self.spot_pairs[symbol]["price"] = data["price"]
                        self.spot_pairs[symbol]["change"] = data["change"]
                        self.spot_pairs[symbol]["volume"] = int(data["volume"])

                        # 保留市值信息（真实API可能没有）
                        if "high_24h" in data:
                            self.spot_pairs[symbol]["high_24h"] = data[
                                "high_24h"
                            ]
                        if "low_24h" in data:
                            self.spot_pairs[symbol]["low_24h"] = data[
                                "low_24h"
                            ]

                # 获取市场状态
                market_status = self.market_data_provider.get_market_status()

                # 更新状态显示
                if "实时数据连接正常" in market_status:
                    self.status_label.config(
                        text="✅ 现货系统运行中 | 3个技术分析策略活跃 | 🌐 Gate.io实时数据连接",
                        fg="#00ff00",
                    )
                else:
                    self.status_label.config(
                        text="⚠️ 现货系统运行中 | 3个技术分析策略活跃 | 🔴 数据连接异常",
                        fg="#ffaa00",
                    )

        except Exception as e:
            print(f"❌ 更新真实数据失败: {e}")
            # 如果真实数据更新失败，继续使用模拟数据

    def update_strategy_display(self):
        """动态更新现货策略显示"""
        # 清除旧的策略显示
        for widget in self.strategy_display_frame.winfo_children():
            widget.destroy()

        # 重新创建现货策略显示
        for strategy_name, strategy_data in self.strategies.items():
            s_frame = tk.Frame(self.strategy_display_frame, bg="#1e1e1e")
            s_frame.pack(fill="x", padx=5, pady=2)

            allocation = self.trading_config["strategy_allocation"][
                strategy_name
            ]
            pnl = strategy_data["pnl"]
            trades = strategy_data["trades"]
            status = strategy_data["status"]

            # 策略名称和状态
            tk.Label(
                s_frame,
                text=f"🎯 {strategy_name}",
                font=("Arial", 10, "bold"),
                fg="#ffffff",
                bg="#1e1e1e",
            ).pack(side="left")

            # 资金分配
            tk.Label(
                s_frame,
                text=f"资金: {allocation}%",
                font=("Arial", 9),
                fg="#ffaa00",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 盈亏显示 (动态颜色)
            pnl_color = "#00ff00" if pnl >= 0 else "#ff4444"
            tk.Label(
                s_frame,
                text=f"盈亏: {pnl:+.2f}",
                font=("Arial", 9),
                fg=pnl_color,
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 交易次数 (动态更新)
            tk.Label(
                s_frame,
                text=f"交易: {trades}次",
                font=("Arial", 9),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

            # 策略特定指标
            if strategy_name == "布林带策略":
                bb_signals = strategy_data.get("bb_signals", 0)
                signal_strength = strategy_data.get("signal_strength", 0)
                tk.Label(
                    s_frame,
                    text=f"BB信号: {bb_signals}",
                    font=("Arial", 9),
                    fg="#888888",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)
                tk.Label(
                    s_frame,
                    text=f"强度: {signal_strength:.2f}",
                    font=("Arial", 9),
                    fg="#ffaa00",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)
            elif strategy_name == "MACD策略":
                golden_cross = strategy_data.get("golden_cross", 0)
                death_cross = strategy_data.get("death_cross", 0)
                tk.Label(
                    s_frame,
                    text=f"金叉: {golden_cross}",
                    font=("Arial", 9),
                    fg="#00ff00",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)
                tk.Label(
                    s_frame,
                    text=f"死叉: {death_cross}",
                    font=("Arial", 9),
                    fg="#ff4444",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)
            elif strategy_name == "RSI策略":
                oversold_signals = strategy_data.get("oversold_signals", 0)
                overbought_signals = strategy_data.get("overbought_signals", 0)
                divergence_signals = strategy_data.get("divergence_signals", 0)
                tk.Label(
                    s_frame,
                    text=f"超卖: {oversold_signals}",
                    font=("Arial", 9),
                    fg="#00ff00",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)
                tk.Label(
                    s_frame,
                    text=f"背离: {divergence_signals}",
                    font=("Arial", 9),
                    fg="#ffaa00",
                    bg="#1e1e1e",
                ).pack(side="left", padx=10)

            # 状态显示 (动态)
            status_color = "#00ff00" if status == "运行中" else "#ff4444"
            tk.Label(
                s_frame,
                text=f"状态: {status}",
                font=("Arial", 9),
                fg=status_color,
                bg="#1e1e1e",
            ).pack(side="right")

    def calculate_trading_fee(self, amount):
        """计算交易手续费 - 根据Gate.io规则"""
        # Gate.io现货交易手续费为0.15% (VIP0等级)
        return amount * 0.0015

    def validate_gate_io_rules(self, symbol, amount, order_type):
        """验证Gate.io交易规则"""
        errors = []

        # 1. 最小交易金额检查
        if amount < 1:
            errors.append("最小交易金额为1 USDT")

        # 2. 价格精度检查
        current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
        if symbol in ["BTC", "ETH"]:
            # 主流币种价格精度为2位小数
            if round(current_price, 2) != current_price:
                current_price = round(current_price, 2)
        elif symbol in ["SOL", "BNB", "DOT", "LINK"]:
            # 中等价格币种精度为2位小数
            if round(current_price, 2) != current_price:
                current_price = round(current_price, 2)
        else:
            # 低价币种精度为4位小数
            if round(current_price, 4) != current_price:
                current_price = round(current_price, 4)

        # 3. 数量精度检查
        if symbol in ["BTC"]:
            # BTC最小交易单位0.000001
            min_quantity = 0.000001
            precision = 6
        elif symbol in ["ETH"]:
            # ETH最小交易单位0.00001
            min_quantity = 0.00001
            precision = 5
        elif symbol in ["SOL", "BNB", "DOT", "LINK"]:
            # 中等价格币种最小交易单位0.0001
            min_quantity = 0.0001
            precision = 4
        else:
            # 低价币种最小交易单位0.01
            min_quantity = 0.01
            precision = 2

        quantity = amount / current_price
        if quantity < min_quantity:
            errors.append(f"{symbol}最小交易数量为{min_quantity}")

        # 4. 交易时间检查（模拟维护时间）
        current_hour = datetime.now().hour
        if current_hour == 3:  # 模拟凌晨3点维护
            errors.append("交易所维护中，暂停交易")

        # 5. 单笔最大金额检查
        max_single_order = 10000  # 单笔最大10000 USDT
        if amount > max_single_order:
            errors.append(f"单笔最大交易金额为{max_single_order} USDT")

        return errors, current_price, round(quantity, precision)

    def get_price_precision(self, symbol):
        """获取价格精度"""
        if symbol in ["BTC", "ETH", "SOL", "BNB", "DOT", "LINK"]:
            return 2
        else:
            return 4

    def get_quantity_precision(self, symbol):
        """获取数量精度"""
        if symbol == "BTC":
            return 6
        elif symbol == "ETH":
            return 5
        elif symbol in ["SOL", "BNB", "DOT", "LINK"]:
            return 4
        else:
            return 2

    def get_available_funds(self, strategy_name):
        """获取策略可用资金 - 修复资金分配逻辑"""
        allocation = (
            self.trading_config["strategy_allocation"][strategy_name] / 100
        )
        total_allocated = self.trading_config["initial_capital"] * allocation
        used_funds = self.trading_config["strategy_used_funds"][strategy_name]

        # 可用资金 = 分配资金 - 已使用资金，但不能超过当前USDT余额
        available_from_allocation = max(0, total_allocated - used_funds)
        available_usdt = min(
            available_from_allocation, self.trading_config["usdt_balance"]
        )

        return available_usdt

    def simulate_spot_trading(self):
        """智能现货交易 - P0修复版"""
        # 检查交易条件
        if not self.should_trade():
            return

        # 现货交易模拟 - 降低随机性，增加技术分析
        for strategy_name, strategy_data in self.strategies.items():
            if strategy_data["status"] != "运行中":
                continue

            # P0修复：降低交易概率，增加技术判断
            if random.random() < 0.08:  # 降低到8%概率，更保守
                available_usdt = self.get_available_funds(strategy_name)

                if available_usdt < 10:  # 提高最小交易金额到10 USDT
                    continue

                # P0修复：简化策略选择，避免复杂逻辑
                if strategy_name == "布林带策略":
                    self.execute_simple_bollinger_strategy(
                        strategy_data, strategy_name, available_usdt
                    )
                elif strategy_name == "MACD策略":
                    self.execute_simple_trend_strategy(
                        strategy_data, strategy_name, available_usdt
                    )
                elif strategy_name == "RSI策略":
                    self.execute_simple_rsi_strategy(
                        strategy_data, strategy_name, available_usdt
                    )

        # 更新持仓价值和总资产
        self.update_holdings_value()

        # P1修复：简化检查流程
        self.check_simple_risk_management()

    def should_trade(self):
        """P0修复：交易条件检查"""
        # 检查资金充足性
        if self.trading_config["usdt_balance"] < 50:  # 最少保留50 USDT
            return False

        # 检查日交易次数限制
        today_trades = len(
            [
                t
                for t in self.trade_history
                if t.get("time", datetime.min).date() == datetime.now().date()
            ]
        )
        if today_trades >= 20:  # 每日最多20笔交易
            return False

        # 检查总亏损限制
        if (
            self.trading_config.get("daily_pnl", 0) < -100
        ):  # 日亏损超过100 USDT停止
            return False

        return True

    def execute_simple_bollinger_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """P0修复：简化布林带策略"""
        try:
            # 选择交易对
            symbols = ["BTC", "ETH", "SOL", "BNB"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 简化的布林带逻辑：价格大幅下跌时买入
            if price_change < -3:  # 下跌超过3%
                trade_amount = min(available_usdt * 0.1, 50)  # 最多50 USDT
                self.execute_simple_buy(
                    symbol, trade_amount, strategy_name, "布林带下轨买入"
                )

            # 简化的卖出逻辑：有持仓且价格上涨时卖出
            elif (
                price_change > 2 and self.spot_holdings[symbol]["amount"] > 0
            ):  # 上涨超过2%
                sell_ratio = 0.3  # 卖出30%
                self.execute_simple_sell(
                    symbol, sell_ratio, strategy_name, "布林带上轨卖出"
                )

        except Exception as e:
            self.add_log(f"❌ 布林带策略错误: {str(e)}")

    def execute_simple_trend_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """P0修复：简化趋势策略"""
        try:
            # 选择交易对
            symbols = ["BTC", "ETH", "SOL", "BNB"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 简化的趋势逻辑：连续上涨时买入
            if price_change > 1:  # 上涨超过1%
                trade_amount = min(available_usdt * 0.08, 40)  # 最多40 USDT
                self.execute_simple_buy(
                    symbol, trade_amount, strategy_name, "趋势跟踪买入"
                )

            # 简化的止损逻辑：下跌时卖出
            elif (
                price_change < -2 and self.spot_holdings[symbol]["amount"] > 0
            ):  # 下跌超过2%
                sell_ratio = 0.5  # 卖出50%
                self.execute_simple_sell(
                    symbol, sell_ratio, strategy_name, "趋势止损卖出"
                )

        except Exception as e:
            self.add_log(f"❌ 趋势策略错误: {str(e)}")

    def execute_simple_rsi_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """P0修复：简化RSI策略"""
        try:
            # 选择交易对
            symbols = ["BTC", "ETH", "SOL", "BNB"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 简化的RSI逻辑：大幅下跌视为超卖
            if price_change < -4:  # 下跌超过4%，视为超卖
                trade_amount = min(available_usdt * 0.12, 60)  # 最多60 USDT
                self.execute_simple_buy(
                    symbol, trade_amount, strategy_name, "RSI超卖买入"
                )

            # 简化的超买逻辑：大幅上涨时卖出
            elif (
                price_change > 3 and self.spot_holdings[symbol]["amount"] > 0
            ):  # 上涨超过3%，视为超买
                sell_ratio = 0.4  # 卖出40%
                self.execute_simple_sell(
                    symbol, sell_ratio, strategy_name, "RSI超买卖出"
                )

        except Exception as e:
            self.add_log(f"❌ RSI策略错误: {str(e)}")

    def execute_simple_buy(self, symbol, trade_amount, strategy_name, reason):
        """P1修复：增强买入执行"""
        try:
            # P2修复：数据验证
            if not self.validate_trade_data(symbol, trade_amount, "buy"):
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            trading_fee = self.calculate_trading_fee(trade_amount)
            total_cost = trade_amount + trading_fee

            # P1修复：增强资金检查
            if total_cost > self.trading_config["usdt_balance"]:
                self.add_log(
                    f"⚠️ 资金不足: 需要 {total_cost:.2f} USDT，可用 {self.trading_config['usdt_balance']:.2f}"
                )
                return

            # P2修复：仓位限制检查
            if not self.check_position_limit(symbol, trade_amount):
                return

            # 计算买入数量
            buy_amount = trade_amount / current_price

            # P1修复：更精确的平均价格计算
            holding = self.spot_holdings[symbol]
            if holding["amount"] > 0:
                # 加权平均价格计算
                old_value = holding["amount"] * holding["avg_price"]
                new_value = trade_amount
                total_amount = holding["amount"] + buy_amount
                holding["avg_price"] = (old_value + new_value) / total_amount
            else:
                holding["avg_price"] = current_price

            holding["amount"] += buy_amount
            self.trading_config["usdt_balance"] -= total_cost

            # P1修复：完善交易记录
            trade_record = {
                "time": datetime.now(),
                "strategy": strategy_name,
                "type": "买入",
                "symbol": symbol,
                "amount": buy_amount,
                "price": current_price,
                "cost": total_cost,
                "fee": trading_fee,
                "reason": reason,
                "balance_after": self.trading_config["usdt_balance"],
                "avg_price": holding["avg_price"],
            }
            self.trade_history.append(trade_record)

            # 更新策略统计
            strategy_data = self.strategies[strategy_name]
            strategy_data["trades"] += 1

            # P2修复：更详细的日志信息
            self.add_log(
                f"💰 {reason}: {buy_amount:.6f} {symbol} @ {current_price:.4f} | 费用: {trading_fee:.3f} | 余额: {self.trading_config['usdt_balance']:.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 买入执行错误: {str(e)}")
            self.handle_trade_error(e, "buy", symbol, trade_amount)

    def validate_trade_data(self, symbol, amount, trade_type):
        """P2修复：交易数据验证"""
        try:
            # 检查交易对是否存在
            if f"{symbol}/USDT" not in self.spot_pairs:
                self.add_log(f"❌ 交易对不存在: {symbol}/USDT")
                return False

            # 检查价格数据
            price_data = self.spot_pairs[f"{symbol}/USDT"]
            if price_data.get("price", 0) <= 0:
                self.add_log(
                    f"❌ 价格数据异常: {symbol} 价格为 {price_data.get('price', 0)}"
                )
                return False

            # 检查交易金额
            if amount <= 0:
                self.add_log(f"❌ 交易金额无效: {amount}")
                return False

            # 检查最小交易金额
            min_amount = 10 if trade_type == "buy" else 5
            if amount < min_amount:
                self.add_log(f"❌ 交易金额过小: {amount:.2f} < {min_amount}")
                return False

            return True

        except Exception as e:
            self.add_log(f"❌ 数据验证错误: {str(e)}")
            return False

    def check_position_limit(self, symbol, trade_amount):
        """P2修复：仓位限制检查"""
        try:
            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            current_position_value = (
                self.spot_holdings[symbol]["amount"] * current_price
            )
            new_position_value = current_position_value + trade_amount

            total_value = self.trading_config.get("total_value", 1000)
            position_ratio = (
                new_position_value / total_value if total_value > 0 else 0
            )

            max_position_ratio = 0.35  # 最大35%仓位
            if position_ratio > max_position_ratio:
                self.add_log(
                    f"⚠️ 仓位限制: {symbol} 将占比 {position_ratio:.1%} > {max_position_ratio:.1%}"
                )
                return False

            return True

        except Exception as e:
            self.add_log(f"❌ 仓位检查错误: {str(e)}")
            return False

    def handle_trade_error(self, error, trade_type, symbol, amount):
        """P1修复：交易错误处理"""
        try:
            error_msg = str(error)

            # 记录详细错误信息
            error_details = {
                "time": datetime.now(),
                "type": "trade_error",
                "trade_type": trade_type,
                "symbol": symbol,
                "amount": amount,
                "error": error_msg,
            }

            # 简单的错误恢复
            if "balance" in error_msg.lower():
                self.add_log("🔄 检查资金余额...")
                if self.trading_config["usdt_balance"] < 0:
                    self.trading_config["usdt_balance"] = 0
            elif "price" in error_msg.lower():
                self.add_log("🔄 刷新价格数据...")
                self.simulate_price_changes()

        except Exception as e:
            print(f"错误处理失败: {e}")

    def execute_simple_sell(self, symbol, sell_ratio, strategy_name, reason):
        """P0修复：简化卖出执行"""
        try:
            holding = self.spot_holdings[symbol]
            if holding["amount"] <= 0:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            sell_amount = holding["amount"] * sell_ratio
            sell_value = sell_amount * current_price

            if sell_value < 5:  # 最小卖出金额
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            # 计算盈亏
            cost_basis = holding["avg_price"] * sell_amount
            net_profit = net_proceeds - cost_basis

            # 更新持仓
            holding["amount"] -= sell_amount
            self.trading_config["usdt_balance"] += net_proceeds

            # 更新盈亏
            self.trading_config["daily_pnl"] = (
                self.trading_config.get("daily_pnl", 0) + net_profit
            )

            # 记录交易
            trade_record = {
                "time": datetime.now(),
                "strategy": strategy_name,
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": current_price,
                "proceeds": net_proceeds,
                "fee": trading_fee,
                "profit": net_profit,
                "reason": reason,
            }
            self.trade_history.append(trade_record)

            # 更新策略统计
            strategy_data = self.strategies[strategy_name]
            strategy_data["trades"] += 1
            strategy_data["pnl"] = strategy_data.get("pnl", 0) + net_profit
            if net_profit > 0:
                strategy_data["wins"] = strategy_data.get("wins", 0) + 1

            pnl_emoji = "📈" if net_profit > 0 else "📉"
            self.add_log(
                f"{pnl_emoji} {reason}: {sell_amount:.6f} {symbol} @ {current_price:.4f} 盈亏: {net_profit:+.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 卖出执行错误: {str(e)}")

    def check_simple_risk_management(self):
        """P1修复：简化风险管理"""
        try:
            # 检查总资产
            total_value = self.trading_config["usdt_balance"]
            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] > 0:
                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    total_value += holding["amount"] * current_price

            self.trading_config["total_value"] = total_value

            # 检查日亏损
            daily_pnl = self.trading_config.get("daily_pnl", 0)
            if daily_pnl < -50:  # 日亏损超过50 USDT
                self.add_log(f"⚠️ 风险警告: 日亏损 {daily_pnl:.2f} USDT")

            # 检查单币种仓位
            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] > 0:
                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    position_value = holding["amount"] * current_price
                    position_ratio = (
                        position_value / total_value if total_value > 0 else 0
                    )

                    if position_ratio > 0.3:  # 单币种超过30%
                        self.add_log(
                            f"⚠️ 仓位警告: {symbol} 占比 {position_ratio:.1%}"
                        )

        except Exception as e:
            self.add_log(f"❌ 风险管理错误: {str(e)}")

    def simulate_smart_dca_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """智能定投策略 - 重构版"""
        try:
            current_time = time.time()
            last_dca_time = strategy_data.get("last_dca_time", 0)

            # 检查定投周期：每周定投一次
            time_since_last_dca = current_time - last_dca_time
            if time_since_last_dca < self.dca_config["frequency_hours"] * 3600:
                return  # 还未到定投时间

            total_dca_amount = self.dca_config["total_amount"]
            if available_usdt < total_dca_amount:
                self.add_log(
                    f"⚠️ 智能定投资金不足: 需要{total_dca_amount}, 可用{available_usdt:.2f}"
                )
                return

            # 按配置比例分散投资
            successful_investments = 0
            total_cost = 0

            for symbol, allocation_percent in self.dca_config[
                "allocation"
            ].items():
                invest_amount = total_dca_amount * (allocation_percent / 100)

                if invest_amount < self.dca_config["min_amount"]:
                    continue

                # 验证Gate.io规则
                errors, current_price, buy_amount = (
                    self.validate_gate_io_rules(symbol, invest_amount, "buy")
                )
                if errors:
                    self.add_log(
                        f"⚠️ 智能定投{symbol}验证失败: {', '.join(errors)}"
                    )
                    continue

                # 计算手续费
                trading_fee = self.calculate_trading_fee(invest_amount)
                cost = invest_amount + trading_fee

                if total_cost + cost > available_usdt:
                    continue

                # 更新持仓
                holding = self.spot_holdings[symbol]
                if holding["amount"] > 0:
                    # 计算新的平均价格
                    total_value = (
                        holding["amount"] * holding["avg_price"]
                        + invest_amount
                    )
                    total_amount = holding["amount"] + buy_amount
                    holding["avg_price"] = total_value / total_amount
                else:
                    holding["avg_price"] = current_price

                holding["amount"] += buy_amount
                total_cost += cost
                successful_investments += 1

                # 记录交易历史
                trade_record = {
                    "time": datetime.now(),
                    "strategy": strategy_name,
                    "type": "买入",
                    "symbol": symbol,
                    "amount": buy_amount,
                    "price": current_price,
                    "cost": cost,
                    "fee": trading_fee,
                }
                self.trade_history.append(trade_record)

                # 使用正确的精度显示
                quantity_precision = self.get_quantity_precision(symbol)
                price_precision = self.get_price_precision(symbol)
                self.add_log(
                    f"💎 智能定投 {buy_amount:.{quantity_precision}f} {symbol} @ {current_price:.{price_precision}f} (费用: {trading_fee:.3f})"
                )

            if successful_investments > 0:
                # 更新资金和策略统计
                self.trading_config["usdt_balance"] -= total_cost
                self.trading_config["strategy_used_funds"][
                    strategy_name
                ] += total_cost
                strategy_data["trades"] += successful_investments
                strategy_data["total_invested"] = strategy_data.get(
                    "total_invested", 0
                ) + (
                    total_cost
                    - sum(
                        trade.get("fee", 0)
                        for trade in self.trade_history[
                            -successful_investments:
                        ]
                    )
                )
                strategy_data["last_dca_time"] = current_time

                self.add_log(
                    f"✅ 智能定投完成: {successful_investments}个币种, 总投资{total_cost:.2f} USDT"
                )

        except KeyError as e:
            self.add_log(f"❌ 智能定投错误: 交易对不存在 {e}")
        except Exception as e:
            self.add_log(f"❌ 智能定投异常: {str(e)}")

    def simulate_spot_grid_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """现货网格策略 - 重构版"""
        try:
            min_trade_amount = self.grid_config["base_amount"]

            if available_usdt < min_trade_amount:
                return

            # 选择网格交易币种
            symbols = self.grid_config["target_symbols"]
            symbol = random.choice(symbols)

            # 检查交易对是否存在
            if f"{symbol}/USDT" not in self.spot_pairs:
                self.add_log(f"❌ 现货网格错误: 交易对 {symbol}/USDT 不存在")
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 获取或初始化网格层级
            if symbol not in strategy_data["grid_levels"]:
                # 初始化网格层级
                base_price = current_price
                price_range = self.grid_config["price_range"]
                grid_count = self.grid_config["grid_count"]

                # 计算网格价格层级
                grid_levels = []
                step = (
                    price_range * 2
                ) / grid_count  # 上下各15%，总共30%分成8格

                for i in range(grid_count):
                    level_price = base_price * (1 - price_range + i * step)
                    grid_levels.append(
                        {
                            "price": level_price,
                            "buy_executed": False,
                            "sell_executed": False,
                            "amount": 0,
                        }
                    )

                strategy_data["grid_levels"][symbol] = {
                    "base_price": base_price,
                    "levels": grid_levels,
                    "total_invested": 0,
                }

                self.add_log(
                    f"🔄 初始化{symbol}网格: 基准价格{base_price:.4f}, {grid_count}个层级"
                )

            grid_data = strategy_data["grid_levels"][symbol]
            levels = grid_data["levels"]

            # 网格买入逻辑：价格触及网格买入点
            for i, level in enumerate(levels):
                if (
                    not level["buy_executed"]
                    and current_price <= level["price"] * 1.01
                ):  # 1%容差
                    trade_amount = self.grid_config["base_amount"]

                    if available_usdt < trade_amount:
                        continue

                    # 验证Gate.io规则
                    errors, validated_price, buy_amount = (
                        self.validate_gate_io_rules(
                            symbol, trade_amount, "buy"
                        )
                    )
                    if errors:
                        continue

                    trading_fee = self.calculate_trading_fee(trade_amount)
                    total_cost = trade_amount + trading_fee

                    if total_cost > available_usdt:
                        continue

                    # 执行买入
                    holding = self.spot_holdings[symbol]
                    if holding["amount"] > 0:
                        total_value = (
                            holding["amount"] * holding["avg_price"]
                            + trade_amount
                        )
                        total_amount = holding["amount"] + buy_amount
                        holding["avg_price"] = total_value / total_amount
                    else:
                        holding["avg_price"] = validated_price

                    holding["amount"] += buy_amount
                    self.trading_config["usdt_balance"] -= total_cost
                    self.trading_config["strategy_used_funds"][
                        strategy_name
                    ] += total_cost

                    # 更新网格层级
                    level["buy_executed"] = True
                    level["amount"] = buy_amount
                    grid_data["total_invested"] += trade_amount

                    strategy_data["grid_orders"] = (
                        strategy_data.get("grid_orders", 0) + 1
                    )

                    # 记录交易历史
                    trade_record = {
                        "time": datetime.now(),
                        "strategy": strategy_name,
                        "type": "买入",
                        "symbol": symbol,
                        "amount": buy_amount,
                        "price": validated_price,
                        "cost": total_cost,
                        "fee": trading_fee,
                    }
                    self.trade_history.append(trade_record)

                    quantity_precision = self.get_quantity_precision(symbol)
                    price_precision = self.get_price_precision(symbol)
                    self.add_log(
                        f"🔄 网格买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 层级{i+1}"
                    )
                    break

            # 网格卖出逻辑：价格上涨到目标利润点
            for i, level in enumerate(levels):
                if (
                    level["buy_executed"]
                    and not level["sell_executed"]
                    and level["amount"] > 0
                ):
                    target_price = level["price"] * (
                        1 + self.grid_config["profit_ratio"]
                    )

                    if current_price >= target_price * 0.99:  # 1%容差
                        sell_amount = level["amount"]
                        sell_value = sell_amount * current_price

                        if sell_value < 1:  # 最小交易金额检查
                            continue

                        # 验证Gate.io规则
                        errors, validated_price, _ = (
                            self.validate_gate_io_rules(
                                symbol, sell_value, "sell"
                            )
                        )
                        if errors:
                            continue

                        trading_fee = self.calculate_trading_fee(sell_value)
                        net_proceeds = sell_value - trading_fee

                        # 执行卖出
                        holding = self.spot_holdings[symbol]
                        holding["amount"] -= sell_amount
                        self.trading_config["usdt_balance"] += net_proceeds

                        # 计算盈亏
                        cost_basis = level["price"] * sell_amount
                        gross_profit = sell_value - cost_basis
                        net_profit = gross_profit - trading_fee

                        strategy_data["pnl"] += net_profit
                        self.trading_config["daily_pnl"] += net_profit

                        if net_profit > 0:
                            strategy_data["wins"] += 1

                        # 更新网格层级
                        level["sell_executed"] = True
                        level["amount"] = 0

                        # 释放已使用资金
                        self.trading_config["strategy_used_funds"][
                            strategy_name
                        ] = max(
                            0,
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ]
                            - cost_basis,
                        )

                        # 记录交易历史
                        trade_record = {
                            "time": datetime.now(),
                            "strategy": strategy_name,
                            "type": "卖出",
                            "symbol": symbol,
                            "amount": sell_amount,
                            "price": validated_price,
                            "proceeds": net_proceeds,
                            "fee": trading_fee,
                            "profit": net_profit,
                        }
                        self.trade_history.append(trade_record)

                        quantity_precision = self.get_quantity_precision(
                            symbol
                        )
                        price_precision = self.get_price_precision(symbol)
                        self.add_log(
                            f"💰 网格卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {net_profit:+.2f}"
                        )

                        strategy_data["trades"] += 1
                        break

            # 更新胜率
            if strategy_data["trades"] > 0:
                strategy_data["win_rate"] = int(
                    (strategy_data["wins"] / strategy_data["trades"]) * 100
                )

        except KeyError as e:
            self.add_log(f"❌ 现货网格错误: 数据访问错误 {e}")
        except ZeroDivisionError as e:
            self.add_log(f"❌ 现货网格错误: 除零错误 {e}")
        except Exception as e:
            self.add_log(f"❌ 现货网格异常: {str(e)}")

    def simulate_long_term_value_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """长期价值投资策略 - 重构版"""
        try:
            min_invest_amount = 50  # 价值投资最小金额提高到50 USDT

            if available_usdt < min_invest_amount:
                return

            # 价值投资只关注主流币种
            symbols = self.value_config["target_coins"]
            symbol = random.choice(symbols)

            # 检查交易对是否存在
            if f"{symbol}/USDT" not in self.spot_pairs:
                self.add_log(f"❌ 价值投资错误: 交易对 {symbol}/USDT 不存在")
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]
            current_time = time.time()

            # 初始化持仓时间记录
            if "hold_periods" not in strategy_data:
                strategy_data["hold_periods"] = {}

            # 价值投资买入条件：大幅下跌时分批买入
            is_major_dip = (
                price_change < self.value_config["buy_threshold"]
            )  # 下跌15%以上

            if is_major_dip and available_usdt >= min_invest_amount:
                # 检查单币种仓位限制
                current_position_value = (
                    self.spot_holdings[symbol]["amount"] * current_price
                )
                total_value = self.trading_config["total_value"]
                current_position_ratio = (
                    current_position_value / total_value
                    if total_value > 0
                    else 0
                )

                if (
                    current_position_ratio
                    >= self.value_config["position_limit"]
                ):
                    self.add_log(
                        f"⚠️ {symbol}仓位已达上限{self.value_config['position_limit']*100:.0f}%"
                    )
                    return

                # 根据跌幅调整投资金额：跌得越多，买得越多
                dip_severity = abs(price_change) / abs(
                    self.value_config["buy_threshold"]
                )
                investment_ratio = self.value_config["batch_ratio"] * min(
                    dip_severity, 2
                )  # 最大60%

                max_investment = (
                    self.value_config["position_limit"] * total_value
                    - current_position_value
                ) * 0.5
                invest_amount = min(
                    available_usdt * investment_ratio, max_investment, 150
                )  # 最大150U

                if invest_amount < min_invest_amount:
                    return

                # 验证Gate.io规则
                errors, validated_price, buy_amount = (
                    self.validate_gate_io_rules(symbol, invest_amount, "buy")
                )
                if errors:
                    self.add_log(
                        f"⚠️ 价值投资买入验证失败: {', '.join(errors)}"
                    )
                    return

                trading_fee = self.calculate_trading_fee(invest_amount)
                total_cost = invest_amount + trading_fee

                if total_cost > available_usdt:
                    return

                # 执行买入
                holding = self.spot_holdings[symbol]
                if holding["amount"] > 0:
                    total_value = (
                        holding["amount"] * holding["avg_price"]
                        + invest_amount
                    )
                    total_amount = holding["amount"] + buy_amount
                    holding["avg_price"] = total_value / total_amount
                else:
                    holding["avg_price"] = validated_price
                    # 记录首次买入时间
                    strategy_data["hold_periods"][symbol] = current_time

                holding["amount"] += buy_amount
                self.trading_config["usdt_balance"] -= total_cost
                self.trading_config["strategy_used_funds"][
                    strategy_name
                ] += total_cost

                strategy_data["trades"] += 1
                strategy_data["positions"] = (
                    strategy_data.get("positions", 0) + 1
                )

                # 记录交易历史
                trade_record = {
                    "time": datetime.now(),
                    "strategy": strategy_name,
                    "type": "买入",
                    "symbol": symbol,
                    "amount": buy_amount,
                    "price": validated_price,
                    "cost": total_cost,
                    "fee": trading_fee,
                }
                self.trade_history.append(trade_record)

                quantity_precision = self.get_quantity_precision(symbol)
                price_precision = self.get_price_precision(symbol)
                self.add_log(
                    f"📈 价值投资买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} (跌幅{price_change:.1f}%)"
                )

            # 价值投资卖出条件：大涨且持有时间足够长
            elif (
                price_change > self.value_config["sell_threshold"]
            ):  # 上涨80%以上
                holding = self.spot_holdings[symbol]
                if holding["amount"] > 0:
                    # 检查持有时间
                    hold_start_time = strategy_data["hold_periods"].get(
                        symbol, current_time
                    )
                    hold_duration = current_time - hold_start_time
                    min_hold_duration = (
                        self.value_config["min_hold_hours"] * 3600
                    )

                    if hold_duration < min_hold_duration:
                        remaining_hours = (
                            min_hold_duration - hold_duration
                        ) / 3600
                        self.add_log(
                            f"⏰ {symbol}未达最小持有期，还需{remaining_hours:.1f}小时"
                        )
                        return

                    # 根据涨幅和持有时间调整卖出比例
                    gain_factor = min(
                        price_change / self.value_config["sell_threshold"], 2
                    )  # 最大2倍
                    time_factor = min(
                        hold_duration
                        / (self.value_config["min_hold_hours"] * 3600),
                        2,
                    )  # 最大2倍

                    base_sell_ratio = 0.2  # 基础卖出20%
                    sell_ratio = base_sell_ratio * gain_factor * time_factor
                    sell_ratio = min(sell_ratio, 0.5)  # 最大卖出50%

                    sell_amount = holding["amount"] * sell_ratio
                    sell_value = sell_amount * current_price

                    if sell_value < 1:
                        return

                    # 验证Gate.io规则
                    errors, validated_price, _ = self.validate_gate_io_rules(
                        symbol, sell_value, "sell"
                    )
                    if errors:
                        self.add_log(
                            f"⚠️ 价值投资卖出验证失败: {', '.join(errors)}"
                        )
                        return

                    trading_fee = self.calculate_trading_fee(sell_value)
                    net_proceeds = sell_value - trading_fee

                    holding["amount"] -= sell_amount
                    self.trading_config["usdt_balance"] += net_proceeds

                    # 计算盈亏
                    cost_basis = holding["avg_price"] * sell_amount
                    gross_profit = sell_value - cost_basis
                    net_profit = gross_profit - trading_fee

                    strategy_data["pnl"] += net_profit
                    self.trading_config["daily_pnl"] += net_profit

                    if net_profit > 0:
                        strategy_data["wins"] += 1

                    # 释放已使用资金
                    released_funds = cost_basis
                    self.trading_config["strategy_used_funds"][
                        strategy_name
                    ] = max(
                        0,
                        self.trading_config["strategy_used_funds"][
                            strategy_name
                        ]
                        - released_funds,
                    )

                    # 记录交易历史
                    trade_record = {
                        "time": datetime.now(),
                        "strategy": strategy_name,
                        "type": "卖出",
                        "symbol": symbol,
                        "amount": sell_amount,
                        "price": validated_price,
                        "proceeds": net_proceeds,
                        "fee": trading_fee,
                        "profit": net_profit,
                    }
                    self.trade_history.append(trade_record)

                    quantity_precision = self.get_quantity_precision(symbol)
                    price_precision = self.get_price_precision(symbol)
                    hold_days = hold_duration / (24 * 3600)
                    self.add_log(
                        f"💰 价值投资获利 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 持有{hold_days:.1f}天 盈亏: {net_profit:+.2f}"
                    )

                    strategy_data["trades"] += 1

                    # 如果全部卖出，清除持有时间记录
                    if holding["amount"] <= 0.000001:  # 考虑精度问题
                        if symbol in strategy_data["hold_periods"]:
                            del strategy_data["hold_periods"][symbol]

                    # 更新胜率
                    if strategy_data["trades"] > 0:
                        strategy_data["win_rate"] = int(
                            (strategy_data["wins"] / strategy_data["trades"])
                            * 100
                        )

        except KeyError as e:
            self.add_log(f"❌ 价值投资错误: 数据访问错误 {e}")
        except ZeroDivisionError as e:
            self.add_log(f"❌ 价值投资错误: 除零错误 {e}")
        except Exception as e:
            self.add_log(f"❌ 价值投资异常: {str(e)}")

    def simulate_bollinger_bands_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """布林带策略 - 评分95/100"""
        try:
            min_trade_amount = self.bollinger_config["min_amount"]
            if available_usdt < min_trade_amount:
                return

            # 选择交易币种
            symbols = self.bollinger_config["target_symbols"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 模拟布林带指标计算
            bb_period = self.bollinger_config["period"]
            std_dev = self.bollinger_config["std_dev"]

            # 简化的布林带计算（实际应用中需要历史数据）
            sma = current_price  # 简化为当前价格
            bb_upper = sma * (1 + std_dev * 0.02)  # 上轨
            bb_lower = sma * (1 - std_dev * 0.02)  # 下轨
            bb_middle = sma  # 中轨

            # 计算布林带位置百分比
            bb_percent = (
                (current_price - bb_lower) / (bb_upper - bb_lower)
                if bb_upper != bb_lower
                else 0.5
            )

            # 模拟RSI确认
            rsi_value = 50 + price_change * 2  # 简化RSI计算

            # 布林带策略信号
            signal_strength = 0.0
            trade_type = None
            reason = ""

            # 买入信号：价格触及下轨且RSI超卖
            if bb_percent <= 0.1 and rsi_value <= 35 and price_change < -2:
                signal_strength = 0.8
                trade_type = "buy"
                reason = f"布林带下轨买入信号 (BB%: {bb_percent:.2f}, RSI: {rsi_value:.1f})"
                strategy_data["bb_signals"] += 1

            # 卖出信号：价格触及上轨且RSI超买
            elif bb_percent >= 0.9 and rsi_value >= 65 and price_change > 2:
                if self.spot_holdings[symbol]["amount"] > 0:
                    signal_strength = 0.8
                    trade_type = "sell"
                    reason = f"布林带上轨卖出信号 (BB%: {bb_percent:.2f}, RSI: {rsi_value:.1f})"
                    strategy_data["bb_signals"] += 1

            # 执行交易
            if signal_strength >= 0.7:
                if trade_type == "buy":
                    trade_amount = min(available_usdt * 0.3, 80)  # 最大80U

                    errors, validated_price, buy_amount = (
                        self.validate_gate_io_rules(
                            symbol, trade_amount, "buy"
                        )
                    )
                    if not errors:
                        trading_fee = self.calculate_trading_fee(trade_amount)
                        total_cost = trade_amount + trading_fee

                        if total_cost <= available_usdt:
                            # 执行买入
                            holding = self.spot_holdings[symbol]
                            if holding["amount"] > 0:
                                total_value = (
                                    holding["amount"] * holding["avg_price"]
                                    + trade_amount
                                )
                                total_amount = holding["amount"] + buy_amount
                                holding["avg_price"] = (
                                    total_value / total_amount
                                )
                            else:
                                holding["avg_price"] = validated_price

                            holding["amount"] += buy_amount
                            self.trading_config["usdt_balance"] -= total_cost
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] += total_cost

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "买入",
                                "symbol": symbol,
                                "amount": buy_amount,
                                "price": validated_price,
                                "cost": total_cost,
                                "fee": trading_fee,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"📊 布林带买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} - {reason}"
                            )

                elif trade_type == "sell":
                    holding = self.spot_holdings[symbol]
                    sell_amount = holding["amount"] * 0.5  # 卖出50%
                    sell_value = sell_amount * current_price

                    if sell_value >= 1:
                        errors, validated_price, _ = (
                            self.validate_gate_io_rules(
                                symbol, sell_value, "sell"
                            )
                        )
                        if not errors:
                            trading_fee = self.calculate_trading_fee(
                                sell_value
                            )
                            net_proceeds = sell_value - trading_fee

                            holding["amount"] -= sell_amount
                            self.trading_config["usdt_balance"] += net_proceeds

                            # 计算盈亏
                            cost_basis = holding["avg_price"] * sell_amount
                            net_profit = sell_value - cost_basis - trading_fee

                            strategy_data["pnl"] += net_profit
                            self.trading_config["daily_pnl"] += net_profit

                            if net_profit > 0:
                                strategy_data["wins"] += 1

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 释放资金
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] = max(
                                0,
                                self.trading_config["strategy_used_funds"][
                                    strategy_name
                                ]
                                - cost_basis,
                            )

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "卖出",
                                "symbol": symbol,
                                "amount": sell_amount,
                                "price": validated_price,
                                "proceeds": net_proceeds,
                                "fee": trading_fee,
                                "profit": net_profit,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"💰 布林带卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {net_profit:+.2f}"
                            )

            # 更新胜率
            if strategy_data["trades"] > 0:
                strategy_data["win_rate"] = int(
                    (strategy_data["wins"] / strategy_data["trades"]) * 100
                )

        except Exception as e:
            self.add_log(f"❌ 布林带策略异常: {str(e)}")

    def simulate_macd_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """MACD策略 - 评分92/100"""
        try:
            min_trade_amount = self.macd_config["min_amount"]
            if available_usdt < min_trade_amount:
                return

            # 选择交易币种
            symbols = self.macd_config["target_symbols"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 模拟MACD指标计算
            fast_period = self.macd_config["fast_period"]
            slow_period = self.macd_config["slow_period"]
            signal_period = self.macd_config["signal_period"]

            # 简化的MACD计算
            macd_line = price_change * 0.5  # 简化MACD线
            signal_line = macd_line * 0.8  # 简化信号线
            histogram = macd_line - signal_line  # MACD直方图

            # MACD策略信号
            signal_strength = 0.0
            trade_type = None
            reason = ""

            # 金叉买入信号：MACD线上穿信号线
            if (
                macd_line > signal_line
                and histogram > 0.1
                and price_change > 1
            ):
                signal_strength = 0.85
                trade_type = "buy"
                reason = f"MACD金叉买入信号 (MACD: {macd_line:.3f}, 信号: {signal_line:.3f})"
                strategy_data["golden_cross"] += 1

            # 死叉卖出信号：MACD线下穿信号线
            elif (
                macd_line < signal_line
                and histogram < -0.1
                and price_change < -1
            ):
                if self.spot_holdings[symbol]["amount"] > 0:
                    signal_strength = 0.85
                    trade_type = "sell"
                    reason = f"MACD死叉卖出信号 (MACD: {macd_line:.3f}, 信号: {signal_line:.3f})"
                    strategy_data["death_cross"] += 1

            # 执行交易
            if signal_strength >= 0.8:
                if trade_type == "buy":
                    trade_amount = min(available_usdt * 0.35, 90)  # 最大90U

                    errors, validated_price, buy_amount = (
                        self.validate_gate_io_rules(
                            symbol, trade_amount, "buy"
                        )
                    )
                    if not errors:
                        trading_fee = self.calculate_trading_fee(trade_amount)
                        total_cost = trade_amount + trading_fee

                        if total_cost <= available_usdt:
                            # 执行买入
                            holding = self.spot_holdings[symbol]
                            if holding["amount"] > 0:
                                total_value = (
                                    holding["amount"] * holding["avg_price"]
                                    + trade_amount
                                )
                                total_amount = holding["amount"] + buy_amount
                                holding["avg_price"] = (
                                    total_value / total_amount
                                )
                            else:
                                holding["avg_price"] = validated_price

                            holding["amount"] += buy_amount
                            self.trading_config["usdt_balance"] -= total_cost
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] += total_cost

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "买入",
                                "symbol": symbol,
                                "amount": buy_amount,
                                "price": validated_price,
                                "cost": total_cost,
                                "fee": trading_fee,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"📈 MACD买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} - {reason}"
                            )

                elif trade_type == "sell":
                    holding = self.spot_holdings[symbol]
                    sell_amount = holding["amount"] * 0.6  # 卖出60%
                    sell_value = sell_amount * current_price

                    if sell_value >= 1:
                        errors, validated_price, _ = (
                            self.validate_gate_io_rules(
                                symbol, sell_value, "sell"
                            )
                        )
                        if not errors:
                            trading_fee = self.calculate_trading_fee(
                                sell_value
                            )
                            net_proceeds = sell_value - trading_fee

                            holding["amount"] -= sell_amount
                            self.trading_config["usdt_balance"] += net_proceeds

                            # 计算盈亏
                            cost_basis = holding["avg_price"] * sell_amount
                            net_profit = sell_value - cost_basis - trading_fee

                            strategy_data["pnl"] += net_profit
                            self.trading_config["daily_pnl"] += net_profit

                            if net_profit > 0:
                                strategy_data["wins"] += 1

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 释放资金
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] = max(
                                0,
                                self.trading_config["strategy_used_funds"][
                                    strategy_name
                                ]
                                - cost_basis,
                            )

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "卖出",
                                "symbol": symbol,
                                "amount": sell_amount,
                                "price": validated_price,
                                "proceeds": net_proceeds,
                                "fee": trading_fee,
                                "profit": net_profit,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"💰 MACD卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {net_profit:+.2f}"
                            )

            # 更新胜率
            if strategy_data["trades"] > 0:
                strategy_data["win_rate"] = int(
                    (strategy_data["wins"] / strategy_data["trades"]) * 100
                )

        except Exception as e:
            self.add_log(f"❌ MACD策略异常: {str(e)}")

    def simulate_rsi_strategy(
        self, strategy_data, strategy_name, available_usdt
    ):
        """RSI策略 - 评分88/100"""
        try:
            min_trade_amount = self.rsi_config["min_amount"]
            if available_usdt < min_trade_amount:
                return

            # 选择交易币种
            symbols = self.rsi_config["target_symbols"]
            symbol = random.choice(symbols)

            if f"{symbol}/USDT" not in self.spot_pairs:
                return

            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
            price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

            # 模拟RSI指标计算
            rsi_period = self.rsi_config["period"]
            overbought = self.rsi_config["overbought"]
            oversold = self.rsi_config["oversold"]

            # 简化的RSI计算
            rsi_value = 50 + price_change * 2.5  # 简化RSI计算
            rsi_value = max(0, min(100, rsi_value))  # 限制在0-100范围

            # 模拟RSI动量
            momentum_period = self.rsi_config["momentum_period"]
            rsi_momentum = price_change * 0.3  # 简化动量计算

            # RSI策略信号
            signal_strength = 0.0
            trade_type = None
            reason = ""

            # 超卖买入信号：RSI低于30且有向上动量
            if (
                rsi_value <= oversold
                and rsi_momentum > 0
                and price_change < -3
            ):
                signal_strength = 0.82
                trade_type = "buy"
                reason = f"RSI超卖买入信号 (RSI: {rsi_value:.1f}, 动量: {rsi_momentum:.2f})"
                strategy_data["oversold_signals"] += 1

            # 超买卖出信号：RSI高于70且有向下动量
            elif (
                rsi_value >= overbought
                and rsi_momentum < 0
                and price_change > 3
            ):
                if self.spot_holdings[symbol]["amount"] > 0:
                    signal_strength = 0.82
                    trade_type = "sell"
                    reason = f"RSI超买卖出信号 (RSI: {rsi_value:.1f}, 动量: {rsi_momentum:.2f})"
                    strategy_data["overbought_signals"] += 1

            # 背离信号检测（简化版）
            elif abs(price_change) > 5:  # 大幅波动时检测背离
                if price_change > 0 and rsi_value < 60:  # 价格上涨但RSI不强
                    if self.spot_holdings[symbol]["amount"] > 0:
                        signal_strength = 0.75
                        trade_type = "sell"
                        reason = f"RSI看跌背离信号 (价格: +{price_change:.1f}%, RSI: {rsi_value:.1f})"
                        strategy_data["divergence_signals"] += 1
                elif price_change < 0 and rsi_value > 40:  # 价格下跌但RSI不弱
                    signal_strength = 0.75
                    trade_type = "buy"
                    reason = f"RSI看涨背离信号 (价格: {price_change:.1f}%, RSI: {rsi_value:.1f})"
                    strategy_data["divergence_signals"] += 1

            # 执行交易
            if signal_strength >= 0.7:
                if trade_type == "buy":
                    trade_amount = min(available_usdt * 0.25, 70)  # 最大70U

                    errors, validated_price, buy_amount = (
                        self.validate_gate_io_rules(
                            symbol, trade_amount, "buy"
                        )
                    )
                    if not errors:
                        trading_fee = self.calculate_trading_fee(trade_amount)
                        total_cost = trade_amount + trading_fee

                        if total_cost <= available_usdt:
                            # 执行买入
                            holding = self.spot_holdings[symbol]
                            if holding["amount"] > 0:
                                total_value = (
                                    holding["amount"] * holding["avg_price"]
                                    + trade_amount
                                )
                                total_amount = holding["amount"] + buy_amount
                                holding["avg_price"] = (
                                    total_value / total_amount
                                )
                            else:
                                holding["avg_price"] = validated_price

                            holding["amount"] += buy_amount
                            self.trading_config["usdt_balance"] -= total_cost
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] += total_cost

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "买入",
                                "symbol": symbol,
                                "amount": buy_amount,
                                "price": validated_price,
                                "cost": total_cost,
                                "fee": trading_fee,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"📉 RSI买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} - {reason}"
                            )

                elif trade_type == "sell":
                    holding = self.spot_holdings[symbol]
                    sell_amount = holding["amount"] * 0.4  # 卖出40%
                    sell_value = sell_amount * current_price

                    if sell_value >= 1:
                        errors, validated_price, _ = (
                            self.validate_gate_io_rules(
                                symbol, sell_value, "sell"
                            )
                        )
                        if not errors:
                            trading_fee = self.calculate_trading_fee(
                                sell_value
                            )
                            net_proceeds = sell_value - trading_fee

                            holding["amount"] -= sell_amount
                            self.trading_config["usdt_balance"] += net_proceeds

                            # 计算盈亏
                            cost_basis = holding["avg_price"] * sell_amount
                            net_profit = sell_value - cost_basis - trading_fee

                            strategy_data["pnl"] += net_profit
                            self.trading_config["daily_pnl"] += net_profit

                            if net_profit > 0:
                                strategy_data["wins"] += 1

                            strategy_data["trades"] += 1
                            strategy_data["signal_strength"] = signal_strength

                            # 释放资金
                            self.trading_config["strategy_used_funds"][
                                strategy_name
                            ] = max(
                                0,
                                self.trading_config["strategy_used_funds"][
                                    strategy_name
                                ]
                                - cost_basis,
                            )

                            # 记录交易
                            trade_record = {
                                "time": datetime.now(),
                                "strategy": strategy_name,
                                "type": "卖出",
                                "symbol": symbol,
                                "amount": sell_amount,
                                "price": validated_price,
                                "proceeds": net_proceeds,
                                "fee": trading_fee,
                                "profit": net_profit,
                            }
                            self.trade_history.append(trade_record)

                            quantity_precision = self.get_quantity_precision(
                                symbol
                            )
                            price_precision = self.get_price_precision(symbol)
                            self.add_log(
                                f"💰 RSI卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {net_profit:+.2f}"
                            )

            # 更新胜率
            if strategy_data["trades"] > 0:
                strategy_data["win_rate"] = int(
                    (strategy_data["wins"] / strategy_data["trades"]) * 100
                )

        except Exception as e:
            self.add_log(f"❌ RSI策略异常: {str(e)}")

    def update_holdings_value(self):
        """更新持仓价值"""
        total_value = self.trading_config["usdt_balance"]

        for symbol, holding in self.spot_holdings.items():
            if holding["amount"] > 0:
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                holding["value"] = holding["amount"] * current_price
                holding["pnl"] = (
                    current_price - holding["avg_price"]
                ) * holding["amount"]
                total_value += holding["value"]

        self.trading_config["total_value"] = total_value
        self.trading_config["total_pnl"] = (
            total_value - self.trading_config["initial_capital"]
        )

    def check_stop_loss_take_profit(self):
        """检查止盈止损 - 自动风险控制"""
        try:
            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] <= 0:
                    continue

                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                avg_price = holding["avg_price"]

                if avg_price <= 0:
                    continue

                # 计算盈亏比例
                profit_ratio = (current_price - avg_price) / avg_price

                # 初始化止盈止损状态
                if symbol not in self.stop_orders:
                    self.stop_orders[symbol] = {
                        "highest_price": current_price,
                        "trailing_stop_price": 0,
                        "stop_loss_triggered": False,
                        "take_profit_triggered": False,
                    }

                stop_order = self.stop_orders[symbol]

                # 更新最高价格（用于移动止损）
                if current_price > stop_order["highest_price"]:
                    stop_order["highest_price"] = current_price

                    # 如果盈利超过20%，启用移动止损
                    if (
                        profit_ratio
                        > self.stop_loss_take_profit_config[
                            "min_profit_for_trailing"
                        ]
                    ):
                        trailing_ratio = self.stop_loss_take_profit_config[
                            "trailing_ratio"
                        ]
                        stop_order["trailing_stop_price"] = current_price * (
                            1 - trailing_ratio
                        )

                # 检查止损条件
                stop_loss_ratio = self.stop_loss_take_profit_config[
                    "stop_loss_ratio"
                ]
                if (
                    profit_ratio < -stop_loss_ratio
                    and not stop_order["stop_loss_triggered"]
                ):
                    self.execute_stop_loss(symbol, holding, current_price)
                    stop_order["stop_loss_triggered"] = True
                    continue

                # 检查移动止损
                if (
                    stop_order["trailing_stop_price"] > 0
                    and current_price < stop_order["trailing_stop_price"]
                    and not stop_order["stop_loss_triggered"]
                ):
                    self.execute_trailing_stop(symbol, holding, current_price)
                    stop_order["stop_loss_triggered"] = True
                    continue

                # 检查止盈条件
                take_profit_ratio = self.stop_loss_take_profit_config[
                    "take_profit_ratio"
                ]
                if (
                    profit_ratio > take_profit_ratio
                    and not stop_order["take_profit_triggered"]
                ):
                    self.execute_take_profit(symbol, holding, current_price)
                    stop_order["take_profit_triggered"] = True

        except Exception as e:
            self.add_log(f"❌ 止盈止损检查异常: {str(e)}")

    def execute_stop_loss(self, symbol, holding, current_price):
        """执行止损"""
        try:
            sell_amount = holding["amount"]
            sell_value = sell_amount * current_price

            if sell_value < 1:  # 最小交易金额检查
                return

            # 验证Gate.io规则
            errors, validated_price, _ = self.validate_gate_io_rules(
                symbol, sell_value, "sell"
            )
            if errors:
                self.add_log(f"⚠️ 止损验证失败: {', '.join(errors)}")
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            # 执行卖出
            holding["amount"] = 0
            self.trading_config["usdt_balance"] += net_proceeds

            # 计算亏损
            cost_basis = holding["avg_price"] * sell_amount
            loss = sell_value - cost_basis - trading_fee
            self.trading_config["daily_pnl"] += loss

            # 记录交易历史
            trade_record = {
                "time": datetime.now(),
                "strategy": "止损",
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": validated_price,
                "proceeds": net_proceeds,
                "fee": trading_fee,
                "profit": loss,
            }
            self.trade_history.append(trade_record)

            quantity_precision = self.get_quantity_precision(symbol)
            price_precision = self.get_price_precision(symbol)
            self.add_log(
                f"🛑 止损卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 亏损: {loss:.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 执行止损异常: {str(e)}")

    def execute_trailing_stop(self, symbol, holding, current_price):
        """执行移动止损"""
        try:
            sell_amount = holding["amount"]
            sell_value = sell_amount * current_price

            if sell_value < 1:
                return

            errors, validated_price, _ = self.validate_gate_io_rules(
                symbol, sell_value, "sell"
            )
            if errors:
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            holding["amount"] = 0
            self.trading_config["usdt_balance"] += net_proceeds

            cost_basis = holding["avg_price"] * sell_amount
            profit = sell_value - cost_basis - trading_fee
            self.trading_config["daily_pnl"] += profit

            trade_record = {
                "time": datetime.now(),
                "strategy": "移动止损",
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": validated_price,
                "proceeds": net_proceeds,
                "fee": trading_fee,
                "profit": profit,
            }
            self.trade_history.append(trade_record)

            quantity_precision = self.get_quantity_precision(symbol)
            price_precision = self.get_price_precision(symbol)
            self.add_log(
                f"📈 移动止损 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈利: {profit:.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 执行移动止损异常: {str(e)}")

    def execute_take_profit(self, symbol, holding, current_price):
        """执行止盈"""
        try:
            partial_ratio = self.stop_loss_take_profit_config[
                "partial_take_profit"
            ]
            sell_amount = holding["amount"] * partial_ratio
            sell_value = sell_amount * current_price

            if sell_value < 1:
                return

            errors, validated_price, _ = self.validate_gate_io_rules(
                symbol, sell_value, "sell"
            )
            if errors:
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            holding["amount"] -= sell_amount
            self.trading_config["usdt_balance"] += net_proceeds

            cost_basis = holding["avg_price"] * sell_amount
            profit = sell_value - cost_basis - trading_fee
            self.trading_config["daily_pnl"] += profit

            trade_record = {
                "time": datetime.now(),
                "strategy": "止盈",
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": validated_price,
                "proceeds": net_proceeds,
                "fee": trading_fee,
                "profit": profit,
            }
            self.trade_history.append(trade_record)

            quantity_precision = self.get_quantity_precision(symbol)
            price_precision = self.get_price_precision(symbol)
            self.add_log(
                f"💰 止盈卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈利: {profit:.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 执行止盈异常: {str(e)}")

    def check_batch_trading(self):
        """检查分批建仓机会"""
        try:
            if not self.batch_trading_config["enabled"]:
                return

            current_time = time.time()

            # 检查所有币种的分批建仓机会
            for symbol in [
                "BTC",
                "ETH",
                "SOL",
                "BNB",
                "ADA",
                "DOT",
                "LINK",
                "UNI",
            ]:
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                price_change = self.spot_pairs[f"{symbol}/USDT"]["change"]

                # 初始化分批建仓状态
                if symbol not in self.batch_orders:
                    self.batch_orders[symbol] = {
                        "base_price": current_price,
                        "batches": [],
                        "last_batch_time": 0,
                        "total_invested": 0,
                    }

                batch_order = self.batch_orders[symbol]

                # 检查是否需要开始新的分批建仓计划
                if (
                    price_change < -5  # 价格下跌超过5%
                    and len(batch_order["batches"])
                    == 0  # 没有进行中的分批计划
                    and current_time - batch_order["last_batch_time"]
                    > 24 * 3600
                ):  # 距离上次分批超过24小时

                    self.start_batch_trading_plan(symbol, current_price)

                # 检查现有分批计划的执行
                self.execute_batch_trading(symbol, current_price, current_time)

        except Exception as e:
            self.add_log(f"❌ 分批建仓检查异常: {str(e)}")

    def start_batch_trading_plan(self, symbol, current_price):
        """开始分批建仓计划"""
        try:
            # 检查仓位限制
            current_position_value = (
                self.spot_holdings[symbol]["amount"] * current_price
            )
            total_value = self.trading_config["total_value"]
            current_position_ratio = (
                current_position_value / total_value if total_value > 0 else 0
            )

            if (
                current_position_ratio
                >= self.batch_trading_config["max_position_ratio"]
            ):
                return

            # 计算分批建仓计划
            batch_count = self.batch_trading_config["batch_count"]
            price_interval = self.batch_trading_config["price_interval"]
            min_batch_amount = self.batch_trading_config["min_batch_amount"]

            # 计算总投资金额（不超过仓位限制）
            max_investment = (
                self.batch_trading_config["max_position_ratio"] * total_value
                - current_position_value
            ) * 0.8
            total_investment = min(
                max_investment, batch_count * min_batch_amount * 2
            )

            if total_investment < min_batch_amount:
                return

            batch_amount = total_investment / batch_count

            # 创建分批计划
            batches = []
            for i in range(batch_count):
                target_price = current_price * (1 - price_interval * (i + 1))
                batches.append(
                    {
                        "target_price": target_price,
                        "amount": batch_amount,
                        "executed": False,
                        "execution_time": 0,
                    }
                )

            self.batch_orders[symbol] = {
                "base_price": current_price,
                "batches": batches,
                "last_batch_time": time.time(),
                "total_invested": 0,
            }

            self.add_log(
                f"📋 启动{symbol}分批建仓计划: {batch_count}批, 总金额{total_investment:.2f} USDT"
            )

        except Exception as e:
            self.add_log(f"❌ 启动分批建仓计划异常: {str(e)}")

    def execute_batch_trading(self, symbol, current_price, current_time):
        """执行分批建仓"""
        try:
            batch_order = self.batch_orders[symbol]
            time_interval = self.batch_trading_config["time_interval"]

            for i, batch in enumerate(batch_order["batches"]):
                if batch["executed"]:
                    continue

                # 检查价格触发条件
                price_triggered = current_price <= batch["target_price"]

                # 检查时间触发条件（防止过于频繁）
                time_triggered = (
                    current_time - batch_order["last_batch_time"]
                    >= time_interval
                )

                if price_triggered and time_triggered:
                    # 执行分批买入
                    invest_amount = batch["amount"]

                    # 验证Gate.io规则
                    errors, validated_price, buy_amount = (
                        self.validate_gate_io_rules(
                            symbol, invest_amount, "buy"
                        )
                    )
                    if errors:
                        continue

                    trading_fee = self.calculate_trading_fee(invest_amount)
                    total_cost = invest_amount + trading_fee

                    if total_cost > self.trading_config["usdt_balance"]:
                        continue

                    # 执行买入
                    holding = self.spot_holdings[symbol]
                    if holding["amount"] > 0:
                        total_value = (
                            holding["amount"] * holding["avg_price"]
                            + invest_amount
                        )
                        total_amount = holding["amount"] + buy_amount
                        holding["avg_price"] = total_value / total_amount
                    else:
                        holding["avg_price"] = validated_price

                    holding["amount"] += buy_amount
                    self.trading_config["usdt_balance"] -= total_cost

                    # 更新分批状态
                    batch["executed"] = True
                    batch["execution_time"] = current_time
                    batch_order["total_invested"] += invest_amount
                    batch_order["last_batch_time"] = current_time

                    # 记录交易历史
                    trade_record = {
                        "time": datetime.now(),
                        "strategy": "分批建仓",
                        "type": "买入",
                        "symbol": symbol,
                        "amount": buy_amount,
                        "price": validated_price,
                        "cost": total_cost,
                        "fee": trading_fee,
                    }
                    self.trade_history.append(trade_record)

                    quantity_precision = self.get_quantity_precision(symbol)
                    price_precision = self.get_price_precision(symbol)
                    self.add_log(
                        f"📋 分批建仓 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 第{i+1}批"
                    )

                    break  # 每次只执行一批

            # 检查分批计划是否完成
            if all(batch["executed"] for batch in batch_order["batches"]):
                total_invested = batch_order["total_invested"]
                self.add_log(
                    f"✅ {symbol}分批建仓完成: 总投资{total_invested:.2f} USDT"
                )
                # 清空分批计划，为下次做准备
                batch_order["batches"] = []

        except Exception as e:
            self.add_log(f"❌ 执行分批建仓异常: {str(e)}")

    def check_margin_positions(self):
        """检查杠杆保证金状态"""
        try:
            if not self.margin_config["enabled"]:
                return

            current_time = time.time()

            for symbol, position in self.margin_positions.items():
                if position["amount"] <= 0:
                    continue

                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]

                # 计算当前保证金率
                margin_ratio = self.calculate_margin_ratio(
                    symbol, current_price
                )

                # 更新利息
                self.update_interest(symbol, current_time)

                # 检查保证金预警
                if margin_ratio < self.margin_config["margin_ratio_warning"]:
                    if (
                        margin_ratio
                        < self.margin_config["margin_ratio_liquidation"]
                    ):
                        # 强制平仓
                        self.force_liquidation(symbol, current_price)
                    else:
                        # 保证金预警
                        self.margin_call_warning(symbol, margin_ratio)

        except Exception as e:
            self.add_log(f"❌ 杠杆保证金检查异常: {str(e)}")

    def calculate_margin_ratio(self, symbol, current_price):
        """计算保证金率"""
        try:
            position = self.margin_positions[symbol]
            borrowed_amount = self.borrowed_amounts.get(symbol, 0)
            interest = self.interest_accumulated.get(symbol, 0)

            # 当前持仓价值
            position_value = position["amount"] * current_price

            # 借币价值（USDT）
            if symbol in [
                "BTC",
                "ETH",
                "SOL",
                "BNB",
                "ADA",
                "DOT",
                "LINK",
                "UNI",
            ]:
                borrowed_value = borrowed_amount * current_price
            else:
                borrowed_value = borrowed_amount  # 如果借的是USDT

            # 总负债（借币价值 + 利息）
            total_debt = borrowed_value + interest

            # 净资产价值
            net_value = position_value - total_debt

            # 保证金率 = 净资产价值 / 总负债
            if total_debt > 0:
                margin_ratio = net_value / total_debt
            else:
                margin_ratio = 1.0

            return margin_ratio

        except Exception as e:
            self.add_log(f"❌ 计算保证金率异常: {str(e)}")
            return 1.0

    def update_interest(self, symbol, current_time):
        """更新利息"""
        try:
            position = self.margin_positions[symbol]
            borrowed_amount = self.borrowed_amounts.get(symbol, 0)

            if borrowed_amount <= 0:
                return

            # 计算时间差（小时）
            last_update = position.get("last_interest_update", current_time)
            hours_passed = (current_time - last_update) / 3600

            if hours_passed >= 1:  # 每小时更新一次利息
                # 计算利息
                hourly_interest = (
                    borrowed_amount * self.margin_config["interest_rate"]
                )
                total_interest = hourly_interest * hours_passed

                # 累计利息
                if symbol not in self.interest_accumulated:
                    self.interest_accumulated[symbol] = 0
                self.interest_accumulated[symbol] += total_interest

                # 更新时间
                position["last_interest_update"] = current_time

                self.add_log(
                    f"💰 {symbol}杠杆利息更新: +{total_interest:.4f} USDT"
                )

        except Exception as e:
            self.add_log(f"❌ 更新利息异常: {str(e)}")

    def margin_call_warning(self, symbol, margin_ratio):
        """保证金预警"""
        try:
            warning_msg = f"⚠️ {symbol}保证金预警: {margin_ratio:.2%} < {self.margin_config['margin_ratio_warning']:.0%}"
            self.add_log(warning_msg)

            # 可以在这里添加更多预警逻辑，比如弹窗提醒

        except Exception as e:
            self.add_log(f"❌ 保证金预警异常: {str(e)}")

    def force_liquidation(self, symbol, current_price):
        """强制平仓"""
        try:
            position = self.margin_positions[symbol]
            borrowed_amount = self.borrowed_amounts.get(symbol, 0)
            interest = self.interest_accumulated.get(symbol, 0)

            # 计算卖出金额
            sell_amount = position["amount"]
            sell_value = sell_amount * current_price

            # 验证Gate.io规则
            errors, validated_price, _ = self.validate_gate_io_rules(
                symbol, sell_value, "sell"
            )
            if errors:
                self.add_log(f"⚠️ 强制平仓验证失败: {', '.join(errors)}")
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            # 计算还币金额
            if symbol in [
                "BTC",
                "ETH",
                "SOL",
                "BNB",
                "ADA",
                "DOT",
                "LINK",
                "UNI",
            ]:
                repay_value = borrowed_amount * current_price
            else:
                repay_value = borrowed_amount

            total_repay = repay_value + interest

            # 执行平仓
            remaining_usdt = net_proceeds - total_repay
            self.trading_config["usdt_balance"] += remaining_usdt

            # 计算盈亏
            cost_basis = position["cost_basis"]
            total_loss = cost_basis - remaining_usdt
            self.trading_config["daily_pnl"] -= total_loss

            # 清空杠杆持仓
            self.margin_positions[symbol] = {
                "amount": 0,
                "cost_basis": 0,
                "leverage": 1,
                "last_interest_update": 0,
            }
            self.borrowed_amounts[symbol] = 0
            self.interest_accumulated[symbol] = 0

            # 记录交易历史
            trade_record = {
                "time": datetime.now(),
                "strategy": "强制平仓",
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": validated_price,
                "proceeds": remaining_usdt,
                "fee": trading_fee,
                "profit": -total_loss,
            }
            self.trade_history.append(trade_record)

            quantity_precision = self.get_quantity_precision(symbol)
            price_precision = self.get_price_precision(symbol)
            self.add_log(
                f"🚨 强制平仓 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 亏损: {total_loss:.2f}"
            )

        except Exception as e:
            self.add_log(f"❌ 强制平仓异常: {str(e)}")

    def check_risk_management(self):
        """检查风险管理"""
        try:
            if not self.risk_management_config["enabled"]:
                return

            current_time = time.time()
            current_value = self.trading_config["total_value"]

            # 初始化日初价值
            if self.risk_status["daily_start_value"] == 0:
                self.risk_status["daily_start_value"] = current_value

            # 更新历史最高价值
            if current_value > self.risk_status["peak_value"]:
                self.risk_status["peak_value"] = current_value

            # 检查各项风险指标
            self.check_position_risk()
            self.check_sector_risk()
            self.check_daily_loss_limit()
            self.check_total_loss_limit()
            self.check_drawdown_limit()

            # 更新检查时间
            self.risk_status["last_risk_check"] = current_time

        except Exception as e:
            self.add_log(f"❌ 风险管理检查异常: {str(e)}")

    def check_position_risk(self):
        """检查单币种仓位风险"""
        try:
            total_value = self.trading_config["total_value"]
            if total_value <= 0:
                return

            max_ratio = self.risk_management_config[
                "max_single_position_ratio"
            ]

            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] > 0:
                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    position_value = holding["amount"] * current_price
                    position_ratio = position_value / total_value

                    if position_ratio > max_ratio:
                        # 触发仓位风险警报
                        alert_msg = f"⚠️ {symbol}仓位超限: {position_ratio:.2%} > {max_ratio:.0%}"
                        self.add_risk_alert(alert_msg)

                        # 如果超限严重，考虑部分减仓
                        if position_ratio > max_ratio * 1.2:  # 超限20%以上
                            self.emergency_position_reduce(
                                symbol, position_ratio, max_ratio
                            )

            # 检查杠杆持仓
            for symbol, position in self.margin_positions.items():
                if position["amount"] > 0:
                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    position_value = position["amount"] * current_price
                    position_ratio = position_value / total_value

                    # 杠杆持仓风险更严格
                    margin_max_ratio = max_ratio * 0.8  # 杠杆持仓最大24%

                    if position_ratio > margin_max_ratio:
                        alert_msg = f"⚠️ {symbol}杠杆仓位超限: {position_ratio:.2%} > {margin_max_ratio:.0%}"
                        self.add_risk_alert(alert_msg)

        except Exception as e:
            self.add_log(f"❌ 仓位风险检查异常: {str(e)}")

    def check_sector_risk(self):
        """检查板块风险"""
        try:
            total_value = self.trading_config["total_value"]
            if total_value <= 0:
                return

            max_sector_exposure = self.risk_management_config[
                "max_sector_exposure"
            ]

            for sector, symbols in self.sector_classification.items():
                sector_value = 0

                # 计算板块总价值
                for symbol in symbols:
                    # 现货持仓
                    if (
                        symbol in self.spot_holdings
                        and self.spot_holdings[symbol]["amount"] > 0
                    ):
                        current_price = self.spot_pairs[f"{symbol}/USDT"][
                            "price"
                        ]
                        sector_value += (
                            self.spot_holdings[symbol]["amount"]
                            * current_price
                        )

                    # 杠杆持仓
                    if (
                        symbol in self.margin_positions
                        and self.margin_positions[symbol]["amount"] > 0
                    ):
                        current_price = self.spot_pairs[f"{symbol}/USDT"][
                            "price"
                        ]
                        sector_value += (
                            self.margin_positions[symbol]["amount"]
                            * current_price
                        )

                sector_ratio = sector_value / total_value

                if sector_ratio > max_sector_exposure:
                    alert_msg = f"⚠️ {sector}板块敞口超限: {sector_ratio:.2%} > {max_sector_exposure:.0%}"
                    self.add_risk_alert(alert_msg)

        except Exception as e:
            self.add_log(f"❌ 板块风险检查异常: {str(e)}")

    def check_daily_loss_limit(self):
        """检查日亏损限制"""
        try:
            daily_start_value = self.risk_status["daily_start_value"]
            current_value = self.trading_config["total_value"]

            if daily_start_value <= 0:
                return

            daily_pnl = current_value - daily_start_value
            daily_pnl_ratio = daily_pnl / daily_start_value

            daily_loss_limit = -self.risk_management_config[
                "daily_loss_limit_ratio"
            ]

            if daily_pnl_ratio < daily_loss_limit:
                alert_msg = f"🚨 日亏损超限: {daily_pnl_ratio:.2%} < {daily_loss_limit:.0%}"
                self.add_risk_alert(alert_msg)

                # 触发紧急模式
                if not self.risk_status["emergency_mode"]:
                    self.activate_emergency_mode("日亏损超限")

        except Exception as e:
            self.add_log(f"❌ 日亏损检查异常: {str(e)}")

    def check_total_loss_limit(self):
        """检查总亏损限制"""
        try:
            initial_capital = self.trading_config["initial_capital"]
            current_value = self.trading_config["total_value"]

            total_pnl = current_value - initial_capital
            total_pnl_ratio = total_pnl / initial_capital

            total_loss_limit = -self.risk_management_config[
                "total_loss_limit_ratio"
            ]

            if total_pnl_ratio < total_loss_limit:
                alert_msg = f"🚨 总亏损超限: {total_pnl_ratio:.2%} < {total_loss_limit:.0%}"
                self.add_risk_alert(alert_msg)

                # 触发紧急模式
                if not self.risk_status["emergency_mode"]:
                    self.activate_emergency_mode("总亏损超限")

        except Exception as e:
            self.add_log(f"❌ 总亏损检查异常: {str(e)}")

    def check_drawdown_limit(self):
        """检查最大回撤限制"""
        try:
            peak_value = self.risk_status["peak_value"]
            current_value = self.trading_config["total_value"]

            if peak_value <= 0:
                return

            drawdown = (peak_value - current_value) / peak_value
            max_drawdown = self.risk_management_config["max_drawdown_ratio"]

            if drawdown > max_drawdown:
                alert_msg = f"🚨 回撤超限: {drawdown:.2%} > {max_drawdown:.0%}"
                self.add_risk_alert(alert_msg)

                # 触发紧急模式
                if not self.risk_status["emergency_mode"]:
                    self.activate_emergency_mode("回撤超限")

        except Exception as e:
            self.add_log(f"❌ 回撤检查异常: {str(e)}")

    def add_risk_alert(self, alert_msg):
        """添加风险警报"""
        try:
            current_time = time.time()
            alert = {
                "time": current_time,
                "message": alert_msg,
                "level": "warning" if "⚠️" in alert_msg else "critical",
            }

            # 避免重复警报
            recent_alerts = [
                a
                for a in self.risk_status["risk_alerts"]
                if current_time - a["time"] < 300
            ]  # 5分钟内的警报

            if not any(a["message"] == alert_msg for a in recent_alerts):
                self.risk_status["risk_alerts"].append(alert)
                self.add_log(alert_msg)

                # 保持警报列表不超过100条
                if len(self.risk_status["risk_alerts"]) > 100:
                    self.risk_status["risk_alerts"] = self.risk_status[
                        "risk_alerts"
                    ][-100:]

        except Exception as e:
            self.add_log(f"❌ 添加风险警报异常: {str(e)}")

    def activate_emergency_mode(self, reason):
        """激活紧急模式"""
        try:
            self.risk_status["emergency_mode"] = True
            emergency_msg = f"🚨 激活紧急模式: {reason}"
            self.add_log(emergency_msg)

            # 紧急模式下的操作
            # 1. 停止所有策略的新开仓
            for strategy_name in self.strategies:
                if self.strategies[strategy_name]["status"] == "运行中":
                    self.strategies[strategy_name]["status"] = "紧急暂停"

            # 2. 考虑部分减仓（可选）
            # self.emergency_reduce_positions()

            self.add_log("🛑 所有策略已暂停，进入风险控制模式")

        except Exception as e:
            self.add_log(f"❌ 激活紧急模式异常: {str(e)}")

    def emergency_position_reduce(self, symbol, current_ratio, max_ratio):
        """紧急减仓"""
        try:
            if (
                symbol not in self.spot_holdings
                or self.spot_holdings[symbol]["amount"] <= 0
            ):
                return

            holding = self.spot_holdings[symbol]
            current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]

            # 计算需要减仓的比例
            target_ratio = max_ratio * 0.9  # 减到限制的90%
            reduce_ratio = (current_ratio - target_ratio) / current_ratio
            reduce_ratio = min(reduce_ratio, 0.5)  # 最多减仓50%

            sell_amount = holding["amount"] * reduce_ratio
            sell_value = sell_amount * current_price

            if sell_value < 1:  # 最小交易金额检查
                return

            # 验证Gate.io规则
            errors, validated_price, _ = self.validate_gate_io_rules(
                symbol, sell_value, "sell"
            )
            if errors:
                return

            trading_fee = self.calculate_trading_fee(sell_value)
            net_proceeds = sell_value - trading_fee

            # 执行紧急减仓
            holding["amount"] -= sell_amount
            self.trading_config["usdt_balance"] += net_proceeds

            # 计算盈亏
            cost_basis = holding["avg_price"] * sell_amount
            profit = sell_value - cost_basis - trading_fee
            self.trading_config["daily_pnl"] += profit

            # 记录交易历史
            trade_record = {
                "time": datetime.now(),
                "strategy": "紧急减仓",
                "type": "卖出",
                "symbol": symbol,
                "amount": sell_amount,
                "price": validated_price,
                "proceeds": net_proceeds,
                "fee": trading_fee,
                "profit": profit,
            }
            self.trade_history.append(trade_record)

            quantity_precision = self.get_quantity_precision(symbol)
            price_precision = self.get_price_precision(symbol)
            self.add_log(
                f"🚨 紧急减仓 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f}"
            )

        except Exception as e:
            self.add_log(f"❌ 紧急减仓异常: {str(e)}")

    def simulate_price_changes(self):
        """模拟现货价格变化 - 改进版"""
        # 市场整体趋势（影响所有币种）
        market_sentiment = random.uniform(-0.3, 0.3)  # 市场情绪

        for symbol in self.spot_pairs:
            # 获取当前数据
            current_data = self.spot_pairs[symbol]
            current_change = current_data["change"]

            # 币种特定因子
            if symbol in ["BTC/USDT", "ETH/USDT"]:
                # 主流币种波动较小但更稳定
                individual_change = random.uniform(-0.3, 0.3)
                trend_factor = 0.7  # 趋势延续性较强
            elif symbol in ["SOL/USDT", "BNB/USDT", "ADA/USDT"]:
                # 中等市值币种波动适中
                individual_change = random.uniform(-0.5, 0.5)
                trend_factor = 0.5
            else:
                # 小市值币种波动较大
                individual_change = random.uniform(-0.8, 0.8)
                trend_factor = 0.3

            # 趋势延续性：当前涨跌会影响下一次变化
            trend_continuation = current_change * trend_factor * 0.1

            # 均值回归：极端价格会有回归倾向
            mean_reversion = 0
            if current_change > 5:
                mean_reversion = -0.2  # 大涨后回调
            elif current_change < -5:
                mean_reversion = 0.2  # 大跌后反弹

            # 综合价格变化
            total_change = (
                market_sentiment
                + individual_change
                + trend_continuation
                + mean_reversion
            )

            # 更新24h变化
            new_change = current_change + total_change

            # 限制变化范围（更合理的范围）
            if new_change > 15:
                new_change = 15
            elif new_change < -15:
                new_change = -15

            self.spot_pairs[symbol]["change"] = new_change

            # 更新价格（基于变化率）
            base_price = current_data["price"]
            self.spot_pairs[symbol]["price"] = base_price * (
                1 + total_change / 100
            )

            # 改进成交量模拟
            # 成交量与价格波动相关：波动大时成交量增加
            volatility = abs(total_change)
            volume_multiplier = (
                1 + volatility * 0.5
            )  # 波动越大，成交量增加越多

            # 基础成交量变化
            base_volume_change = random.uniform(-0.05, 0.05)
            volume_change = base_volume_change * volume_multiplier

            new_volume = int(current_data["volume"] * (1 + volume_change))

            # 设置合理的成交量范围
            if symbol in ["BTC/USDT", "ETH/USDT"]:
                min_volume, max_volume = 1000000, 5000000
            elif symbol in ["SOL/USDT", "BNB/USDT"]:
                min_volume, max_volume = 300000, 2000000
            else:
                min_volume, max_volume = 50000, 500000

            self.spot_pairs[symbol]["volume"] = max(
                min_volume, min(new_volume, max_volume)
            )

    def refresh_data(self):
        """刷新现货数据"""
        self.simulate_price_changes()
        self.update_prices()
        self.update_holdings_value()
        self.add_log("🔄 现货数据已刷新")

    def show_spot_trading(self):
        """显示现货交易窗口"""
        trading_window = tk.Toplevel(self.root)
        trading_window.title("💎 现货交易")
        trading_window.geometry("500x400")
        trading_window.configure(bg="#1e1e1e")

        tk.Label(
            trading_window,
            text="💎 现货交易",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 交易选择
        trade_frame = tk.LabelFrame(
            trading_window, text="交易操作", fg="#00ff00", bg="#1e1e1e"
        )
        trade_frame.pack(fill="x", padx=20, pady=10)

        # 币种选择
        tk.Label(
            trade_frame, text="选择币种:", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        symbol_var = tk.StringVar(value="BTC")
        symbol_combo = ttk.Combobox(
            trade_frame,
            textvariable=symbol_var,
            values=["BTC", "ETH", "SOL", "BNB", "ADA", "DOT", "LINK", "UNI"],
        )
        symbol_combo.pack(padx=10, pady=5)

        # 交易金额
        tk.Label(
            trade_frame, text="交易金额 (USDT):", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        amount_var = tk.StringVar(value="100")
        tk.Entry(trade_frame, textvariable=amount_var, width=20).pack(
            padx=10, pady=5
        )

        # 交易按钮
        button_frame = tk.Frame(trade_frame, bg="#1e1e1e")
        button_frame.pack(pady=10)

        def buy_spot():
            try:
                symbol = symbol_var.get()
                amount = float(amount_var.get())

                # 验证Gate.io规则
                errors, validated_price, buy_amount = (
                    self.validate_gate_io_rules(symbol, amount, "buy")
                )
                if errors:
                    messagebox.showerror("交易验证失败", "\n".join(errors))
                    return

                # 计算手续费
                trading_fee = self.calculate_trading_fee(amount)
                total_cost = amount + trading_fee

                if total_cost <= self.trading_config["usdt_balance"]:
                    # 更新持仓
                    holding = self.spot_holdings[symbol]
                    if holding["amount"] > 0:
                        total_value = (
                            holding["amount"] * holding["avg_price"] + amount
                        )
                        total_amount = holding["amount"] + buy_amount
                        holding["avg_price"] = total_value / total_amount
                    else:
                        holding["avg_price"] = validated_price

                    holding["amount"] += buy_amount
                    self.trading_config["usdt_balance"] -= total_cost

                    # 记录交易历史
                    trade_record = {
                        "time": datetime.now(),
                        "strategy": "手动交易",
                        "type": "买入",
                        "symbol": symbol,
                        "amount": buy_amount,
                        "price": validated_price,
                        "cost": total_cost,
                        "fee": trading_fee,
                    }
                    self.trade_history.append(trade_record)

                    # 使用正确精度显示
                    quantity_precision = self.get_quantity_precision(symbol)
                    price_precision = self.get_price_precision(symbol)
                    self.add_log(
                        f"💎 手动买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} (费用: {trading_fee:.3f})"
                    )
                    trading_window.destroy()
                else:
                    messagebox.showerror(
                        "余额不足",
                        f"USDT余额不足\n需要: {total_cost:.2f} USDT\n余额: {self.trading_config['usdt_balance']:.2f} USDT",
                    )
            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数字金额")
            except KeyError:
                messagebox.showerror(
                    "交易对错误", f"不支持的交易对: {symbol}/USDT"
                )
            except Exception as e:
                messagebox.showerror("交易错误", f"买入失败: {str(e)}")

        def sell_spot():
            try:
                symbol = symbol_var.get()
                amount = float(amount_var.get())

                holding = self.spot_holdings[symbol]
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]

                # 检查持仓是否足够
                holding_value = holding["amount"] * current_price
                if holding_value < amount or holding["amount"] <= 0:
                    messagebox.showerror(
                        "持仓不足",
                        f"{symbol}持仓不足\n持仓价值: {holding_value:.2f} USDT\n需要卖出: {amount:.2f} USDT",
                    )
                    return

                # 验证Gate.io规则
                errors, validated_price, _ = self.validate_gate_io_rules(
                    symbol, amount, "sell"
                )
                if errors:
                    messagebox.showerror("交易验证失败", "\n".join(errors))
                    return

                sell_amount = amount / validated_price

                # 计算手续费
                trading_fee = self.calculate_trading_fee(amount)
                net_proceeds = amount - trading_fee

                holding["amount"] -= sell_amount
                self.trading_config["usdt_balance"] += net_proceeds

                # 正确计算盈亏
                cost_basis = holding["avg_price"] * sell_amount
                gross_profit = amount - cost_basis
                net_profit = gross_profit - trading_fee
                self.trading_config["daily_pnl"] += net_profit

                # 记录交易历史
                trade_record = {
                    "time": datetime.now(),
                    "strategy": "手动交易",
                    "type": "卖出",
                    "symbol": symbol,
                    "amount": sell_amount,
                    "price": validated_price,
                    "proceeds": net_proceeds,
                    "fee": trading_fee,
                    "profit": net_profit,
                }
                self.trade_history.append(trade_record)

                # 使用正确精度显示
                quantity_precision = self.get_quantity_precision(symbol)
                price_precision = self.get_price_precision(symbol)
                self.add_log(
                    f"💰 手动卖出 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {net_profit:+.2f} (费用: {trading_fee:.3f})"
                )
                trading_window.destroy()

            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数字金额")
            except KeyError:
                messagebox.showerror(
                    "交易对错误", f"不支持的交易对: {symbol}/USDT"
                )
            except Exception as e:
                messagebox.showerror("交易错误", f"卖出失败: {str(e)}")

        tk.Button(
            button_frame,
            text="💎 买入",
            command=buy_spot,
            bg="#00aa00",
            fg="white",
            font=("Arial", 10, "bold"),
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="💰 卖出",
            command=sell_spot,
            bg="#aa0000",
            fg="white",
            font=("Arial", 10, "bold"),
        ).pack(side="left", padx=5)

        self.add_log("💎 打开现货交易窗口")

    def show_holdings_detail(self):
        """显示持仓详情"""
        holdings_window = tk.Toplevel(self.root)
        holdings_window.title("📊 持仓详情")
        holdings_window.geometry("600x400")
        holdings_window.configure(bg="#1e1e1e")

        tk.Label(
            holdings_window,
            text="📊 现货持仓详情",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 持仓列表
        holdings_frame = tk.Frame(holdings_window, bg="#1e1e1e")
        holdings_frame.pack(fill="both", expand=True, padx=20, pady=10)

        has_holdings = any(
            holding["amount"] > 0 for holding in self.spot_holdings.values()
        )

        if not has_holdings:
            tk.Label(
                holdings_frame,
                text="📭 当前无现货持仓",
                font=("Arial", 12),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=50)
        else:
            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] > 0:
                    h_frame = tk.LabelFrame(
                        holdings_frame,
                        text=f"💎 {symbol}",
                        fg="#ffffff",
                        bg="#2e2e2e",
                    )
                    h_frame.pack(fill="x", pady=5)

                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    current_value = holding["amount"] * current_price
                    pnl = (current_price - holding["avg_price"]) * holding[
                        "amount"
                    ]
                    pnl_percent = (
                        (pnl / (holding["amount"] * holding["avg_price"]))
                        * 100
                        if holding["avg_price"] > 0
                        else 0
                    )

                    info_text = f"""
持有数量: {holding['amount']:.6f} {symbol}
平均成本: {holding['avg_price']:.2f} USDT
当前价格: {current_price:.2f} USDT
当前价值: {current_value:.2f} USDT
未实现盈亏: {pnl:+.2f} USDT ({pnl_percent:+.2f}%)
                    """

                    tk.Label(
                        h_frame,
                        text=info_text,
                        font=("Consolas", 9),
                        fg="#ffffff",
                        bg="#2e2e2e",
                        justify="left",
                    ).pack(padx=10, pady=5)

        self.add_log("📊 查看持仓详情")

    def show_settings(self):
        """显示设置"""
        # 创建设置窗口
        settings_window = tk.Toplevel(self.root)
        settings_window.title("系统设置")
        settings_window.geometry("300x200")

        tk.Label(
            settings_window, text="⚙️ 系统设置", font=("Arial", 14, "bold")
        ).pack(pady=10)

        tk.Label(settings_window, text="更新频率:").pack()
        update_var = tk.StringVar(value="15秒")
        ttk.Combobox(
            settings_window,
            textvariable=update_var,
            values=["5秒", "10秒", "15秒", "30秒"],
        ).pack(pady=5)

        tk.Label(settings_window, text="显示模式:").pack()
        mode_var = tk.StringVar(value="标准")
        ttk.Combobox(
            settings_window,
            textvariable=mode_var,
            values=["简洁", "标准", "详细"],
        ).pack(pady=5)

        tk.Button(
            settings_window,
            text="保存设置",
            command=lambda: self.save_settings(settings_window),
        ).pack(pady=10)

        self.add_log("⚙️ 打开系统设置")

    def save_settings(self, window):
        """保存设置"""
        window.destroy()
        self.add_log("✅ 设置已保存")

    def show_asset_config(self):
        """显示资产配置"""
        config_window = tk.Toplevel(self.root)
        config_window.title("💰 资产配置")
        config_window.geometry("500x400")
        config_window.configure(bg="#1e1e1e")

        tk.Label(
            config_window,
            text="💰 现货资产配置",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 当前资产状态
        current_frame = tk.LabelFrame(
            config_window, text="当前资产状态", fg="#00ff00", bg="#1e1e1e"
        )
        current_frame.pack(fill="x", padx=20, pady=10)

        tk.Label(
            current_frame,
            text=f"初始资金: {self.trading_config['initial_capital']:,.2f} USDT",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(anchor="w", padx=10, pady=2)
        tk.Label(
            current_frame,
            text=f"USDT余额: {self.trading_config['usdt_balance']:,.2f} USDT",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(anchor="w", padx=10, pady=2)
        tk.Label(
            current_frame,
            text=f"总资产价值: {self.trading_config['total_value']:,.2f} USDT",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(anchor="w", padx=10, pady=2)
        tk.Label(
            current_frame,
            text=f"总盈亏: {self.trading_config['total_pnl']:+.2f} USDT",
            fg=(
                "#00ff00"
                if self.trading_config["total_pnl"] >= 0
                else "#ff4444"
            ),
            bg="#1e1e1e",
        ).pack(anchor="w", padx=10, pady=2)

        # 资产配置
        config_frame = tk.LabelFrame(
            config_window, text="配置修改", fg="#00ff00", bg="#1e1e1e"
        )
        config_frame.pack(fill="x", padx=20, pady=10)

        tk.Label(
            config_frame, text="初始资金 (USDT):", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        capital_var = tk.StringVar(
            value=str(self.trading_config["initial_capital"])
        )
        tk.Entry(config_frame, textvariable=capital_var, width=20).pack(
            padx=10, pady=5
        )

        def save_capital():
            try:
                new_capital = float(capital_var.get())
                self.trading_config["initial_capital"] = new_capital
                self.trading_config["usdt_balance"] = new_capital
                self.trading_config["total_value"] = new_capital
                self.trading_config["total_pnl"] = 0.0
                self.trading_config["daily_pnl"] = 0.0

                # 清空持仓
                for symbol in self.spot_holdings:
                    self.spot_holdings[symbol] = {
                        "amount": 0.0,
                        "avg_price": 0.0,
                        "value": 0.0,
                        "pnl": 0.0,
                    }

                # 重置策略数据
                for strategy_data in self.strategies.values():
                    strategy_data["pnl"] = 0.0
                    strategy_data["trades"] = 0
                    strategy_data["success_rate"] = 0
                    strategy_data["win_rate"] = 0
                    strategy_data["wins"] = 0
                    if "total_invested" in strategy_data:
                        strategy_data["total_invested"] = 0.0
                    if "grid_orders" in strategy_data:
                        strategy_data["grid_orders"] = 0
                    if "positions" in strategy_data:
                        strategy_data["positions"] = 0

                # 重置策略已使用资金
                for strategy_name in self.trading_config[
                    "strategy_used_funds"
                ]:
                    self.trading_config["strategy_used_funds"][
                        strategy_name
                    ] = 0.0

                # 清空交易历史
                self.trade_history = []

                self.update_balance_display()
                config_window.destroy()
                self.add_log(f"💰 资产已重置为 {new_capital:,.2f} USDT")
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        tk.Button(
            config_frame,
            text="💾 保存并重置",
            command=save_capital,
            bg="#333333",
            fg="white",
        ).pack(pady=10)

        self.add_log("💰 打开资产配置")

    def show_strategy_config(self):
        """显示策略管理"""
        strategy_window = tk.Toplevel(self.root)
        strategy_window.title("🎯 策略管理")
        strategy_window.geometry("500x400")

        tk.Label(
            strategy_window, text="🎯 策略管理", font=("Arial", 14, "bold")
        ).pack(pady=10)

        # 策略状态
        status_frame = tk.LabelFrame(strategy_window, text="策略状态")
        status_frame.pack(fill="x", padx=20, pady=10)

        for strategy_name, strategy_data in self.strategies.items():
            s_frame = tk.Frame(status_frame)
            s_frame.pack(fill="x", padx=10, pady=5)

            allocation = self.trading_config["strategy_allocation"][
                strategy_name
            ]
            pnl = strategy_data["pnl"]
            trades = strategy_data["trades"]
            win_rate = strategy_data["win_rate"]

            tk.Label(
                s_frame, text=f"🎯 {strategy_name}", font=("Arial", 11, "bold")
            ).pack(anchor="w")
            tk.Label(
                s_frame,
                text=f"   资金分配: {allocation}% | 盈亏: {pnl:+.2f} USDT | 交易次数: {trades} | 胜率: {win_rate}%",
            ).pack(anchor="w")

        # 资金分配调整
        allocation_frame = tk.LabelFrame(strategy_window, text="资金分配调整")
        allocation_frame.pack(fill="x", padx=20, pady=10)

        allocation_vars = {}
        for strategy_name in self.strategies.keys():
            frame = tk.Frame(allocation_frame)
            frame.pack(fill="x", padx=10, pady=2)

            tk.Label(
                frame, text=f"{strategy_name}:", width=15, anchor="w"
            ).pack(side="left")
            var = tk.StringVar(
                value=str(
                    self.trading_config["strategy_allocation"][strategy_name]
                )
            )
            allocation_vars[strategy_name] = var
            tk.Entry(frame, textvariable=var, width=10).pack(
                side="left", padx=5
            )
            tk.Label(frame, text="%").pack(side="left")

        def save_allocation():
            try:
                total = 0
                new_allocation = {}
                for strategy_name, var in allocation_vars.items():
                    value = float(var.get())
                    new_allocation[strategy_name] = value
                    total += value

                if abs(total - 100) > 0.1:
                    messagebox.showerror(
                        "错误", f"总分配比例必须为100%，当前为{total}%"
                    )
                    return

                self.trading_config["strategy_allocation"] = new_allocation
                strategy_window.destroy()
                self.add_log("🎯 策略分配已更新")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        tk.Button(
            allocation_frame, text="💾 保存分配", command=save_allocation
        ).pack(pady=10)

        self.add_log("🎯 打开策略管理")

    def show_trade_history(self):
        """显示交易历史 - 完善版"""
        history_window = tk.Toplevel(self.root)
        history_window.title("📊 交易历史")
        history_window.geometry("800x600")
        history_window.configure(bg="#1e1e1e")

        tk.Label(
            history_window,
            text="📊 现货交易历史",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 统计信息框架
        stats_frame = tk.LabelFrame(
            history_window, text="交易统计", fg="#00ff00", bg="#1e1e1e"
        )
        stats_frame.pack(fill="x", padx=20, pady=10)

        # 计算统计数据
        total_trades = len(self.trade_history)
        total_fees = sum(trade.get("fee", 0) for trade in self.trade_history)
        buy_trades = len(
            [t for t in self.trade_history if t["type"] == "买入"]
        )
        sell_trades = len(
            [t for t in self.trade_history if t["type"] == "卖出"]
        )
        total_profit = sum(
            trade.get("profit", 0)
            for trade in self.trade_history
            if "profit" in trade
        )

        # 显示统计信息
        stats_info = tk.Frame(stats_frame, bg="#1e1e1e")
        stats_info.pack(fill="x", padx=10, pady=5)

        tk.Label(
            stats_info,
            text=f"总交易次数: {total_trades}",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            stats_info,
            text=f"买入: {buy_trades}次",
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            stats_info,
            text=f"卖出: {sell_trades}次",
            fg="#ff4444",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            stats_info,
            text=f"总手续费: {total_fees:.3f} USDT",
            fg="#ffaa00",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            stats_info,
            text=f"总盈亏: {total_profit:+.2f} USDT",
            fg="#00ff00" if total_profit >= 0 else "#ff4444",
            bg="#1e1e1e",
        ).pack(side="right", padx=10)

        # 交易历史表格
        history_frame = tk.LabelFrame(
            history_window, text="交易记录", fg="#00ff00", bg="#1e1e1e"
        )
        history_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 创建滚动框架
        canvas = tk.Canvas(history_frame, bg="#1e1e1e")
        scrollbar = tk.Scrollbar(
            history_frame, orient="vertical", command=canvas.yview
        )
        scrollable_frame = tk.Frame(canvas, bg="#1e1e1e")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all")),
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 表头
        header_frame = tk.Frame(scrollable_frame, bg="#2e2e2e")
        header_frame.pack(fill="x", pady=2)

        headers = [
            "时间",
            "策略",
            "类型",
            "币种",
            "数量",
            "价格",
            "金额",
            "手续费",
            "盈亏",
        ]
        widths = [12, 8, 6, 6, 10, 8, 8, 6, 8]

        for header, width in zip(headers, widths):
            tk.Label(
                header_frame,
                text=header,
                font=("Arial", 9, "bold"),
                width=width,
                fg="#ffffff",
                bg="#2e2e2e",
                relief="ridge",
            ).pack(side="left")

        # 显示交易记录（最新的在前）
        if not self.trade_history:
            tk.Label(
                scrollable_frame,
                text="📭 暂无交易记录",
                font=("Arial", 12),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=20)
        else:
            # 按时间倒序显示
            sorted_history = sorted(
                self.trade_history, key=lambda x: x["time"], reverse=True
            )

            for trade in sorted_history[-50:]:  # 只显示最近50条记录
                row_frame = tk.Frame(scrollable_frame, bg="#1e1e1e")
                row_frame.pack(fill="x", pady=1)

                # 时间
                time_str = trade["time"].strftime("%m-%d %H:%M")
                tk.Label(
                    row_frame,
                    text=time_str,
                    font=("Arial", 8),
                    width=12,
                    fg="#888888",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 策略
                tk.Label(
                    row_frame,
                    text=trade["strategy"],
                    font=("Arial", 8),
                    width=8,
                    fg="#ffaa00",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 类型
                type_color = (
                    "#00ff00" if trade["type"] == "买入" else "#ff4444"
                )
                tk.Label(
                    row_frame,
                    text=trade["type"],
                    font=("Arial", 8),
                    width=6,
                    fg=type_color,
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 币种
                tk.Label(
                    row_frame,
                    text=trade["symbol"],
                    font=("Arial", 8),
                    width=6,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 数量
                quantity_precision = self.get_quantity_precision(
                    trade["symbol"]
                )
                quantity_text = f"{trade['amount']:.{quantity_precision}f}"
                tk.Label(
                    row_frame,
                    text=quantity_text,
                    font=("Arial", 8),
                    width=10,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 价格
                price_precision = self.get_price_precision(trade["symbol"])
                price_text = f"{trade['price']:.{price_precision}f}"
                tk.Label(
                    row_frame,
                    text=price_text,
                    font=("Arial", 8),
                    width=8,
                    fg="#ffaa00",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 金额
                amount_text = (
                    f"{trade.get('cost', trade.get('proceeds', 0)):.2f}"
                )
                tk.Label(
                    row_frame,
                    text=amount_text,
                    font=("Arial", 8),
                    width=8,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 手续费
                fee_text = f"{trade.get('fee', 0):.3f}"
                tk.Label(
                    row_frame,
                    text=fee_text,
                    font=("Arial", 8),
                    width=6,
                    fg="#ff8800",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 盈亏
                profit = trade.get("profit", 0)
                profit_text = f"{profit:+.2f}" if profit != 0 else "-"
                profit_color = (
                    "#00ff00"
                    if profit > 0
                    else "#ff4444" if profit < 0 else "#888888"
                )
                tk.Label(
                    row_frame,
                    text=profit_text,
                    font=("Arial", 8),
                    width=8,
                    fg=profit_color,
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 操作按钮
        button_frame = tk.Frame(history_window, bg="#1e1e1e")
        button_frame.pack(pady=10)

        tk.Button(
            button_frame,
            text="🔄 刷新",
            command=lambda: self.refresh_trade_history(history_window),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="📤 导出",
            command=self.export_trade_history,
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="🗑️ 清空",
            command=lambda: self.clear_trade_history(history_window),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)

        self.add_log("📊 打开交易历史")

    def refresh_trade_history(self, window):
        """刷新交易历史窗口"""
        window.destroy()
        self.show_trade_history()

    def export_trade_history(self):
        """导出交易历史"""
        if not self.trade_history:
            messagebox.showinfo("提示", "暂无交易记录可导出")
            return

        try:
            filename = (
                f"现货交易历史_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            with open(filename, "w", encoding="utf-8", newline="") as f:
                import csv

                writer = csv.writer(f)

                # 写入表头
                writer.writerow(
                    [
                        "时间",
                        "策略",
                        "类型",
                        "币种",
                        "数量",
                        "价格",
                        "金额",
                        "手续费",
                        "盈亏",
                    ]
                )

                # 写入数据
                for trade in self.trade_history:
                    row = [
                        trade["time"].strftime("%Y-%m-%d %H:%M:%S"),
                        trade["strategy"],
                        trade["type"],
                        trade["symbol"],
                        f"{trade['amount']:.6f}",
                        f"{trade['price']:.4f}",
                        f"{trade.get('cost', trade.get('proceeds', 0)):.2f}",
                        f"{trade.get('fee', 0):.3f}",
                        f"{trade.get('profit', 0):.2f}",
                    ]
                    writer.writerow(row)

            messagebox.showinfo("导出成功", f"交易历史已导出到: {filename}")
            self.add_log(f"📤 交易历史已导出: {filename}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出错误: {str(e)}")

    def clear_trade_history(self, window):
        """清空交易历史"""
        if messagebox.askyesno(
            "确认清空", "确定要清空所有交易历史吗？此操作不可恢复。"
        ):
            self.trade_history = []
            window.destroy()
            self.show_trade_history()
            self.add_log("🗑️ 交易历史已清空")

    def show_position_analysis(self):
        """显示持仓分析 - 详细盈亏分析"""
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("📈 持仓分析")
        analysis_window.geometry("900x700")
        analysis_window.configure(bg="#1e1e1e")

        tk.Label(
            analysis_window,
            text="📈 现货持仓分析",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 总体分析框架
        summary_frame = tk.LabelFrame(
            analysis_window, text="总体分析", fg="#00ff00", bg="#1e1e1e"
        )
        summary_frame.pack(fill="x", padx=20, pady=10)

        # 计算总体数据
        total_cost = 0
        total_value = 0
        total_pnl = 0
        position_count = 0

        for symbol, holding in self.spot_holdings.items():
            if holding["amount"] > 0:
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                cost = holding["amount"] * holding["avg_price"]
                value = holding["amount"] * current_price

                total_cost += cost
                total_value += value
                position_count += 1

        total_pnl = total_value - total_cost
        total_pnl_ratio = (
            (total_pnl / total_cost * 100) if total_cost > 0 else 0
        )

        # 显示总体信息
        summary_info = tk.Frame(summary_frame, bg="#1e1e1e")
        summary_info.pack(fill="x", padx=10, pady=5)

        tk.Label(
            summary_info,
            text=f"持仓币种: {position_count}个",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            summary_info,
            text=f"总成本: {total_cost:.2f} USDT",
            fg="#ffaa00",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            summary_info,
            text=f"总价值: {total_value:.2f} USDT",
            fg="#ffaa00",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)
        tk.Label(
            summary_info,
            text=f"总盈亏: {total_pnl:+.2f} USDT ({total_pnl_ratio:+.2f}%)",
            fg="#00ff00" if total_pnl >= 0 else "#ff4444",
            bg="#1e1e1e",
        ).pack(side="right", padx=10)

        # 详细持仓分析表格
        detail_frame = tk.LabelFrame(
            analysis_window, text="详细持仓分析", fg="#00ff00", bg="#1e1e1e"
        )
        detail_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 创建滚动框架
        canvas = tk.Canvas(detail_frame, bg="#1e1e1e")
        scrollbar = tk.Scrollbar(
            detail_frame, orient="vertical", command=canvas.yview
        )
        scrollable_frame = tk.Frame(canvas, bg="#1e1e1e")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all")),
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 表头
        header_frame = tk.Frame(scrollable_frame, bg="#2e2e2e")
        header_frame.pack(fill="x", pady=2)

        headers = [
            "币种",
            "数量",
            "均价",
            "现价",
            "成本",
            "价值",
            "盈亏",
            "盈亏%",
            "仓位%",
        ]
        widths = [8, 12, 10, 10, 10, 10, 10, 8, 8]

        for header, width in zip(headers, widths):
            tk.Label(
                header_frame,
                text=header,
                font=("Arial", 9, "bold"),
                width=width,
                fg="#ffffff",
                bg="#2e2e2e",
                relief="ridge",
            ).pack(side="left")

        # 显示持仓详情
        has_positions = False
        for symbol, holding in self.spot_holdings.items():
            if holding["amount"] > 0:
                has_positions = True
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                cost = holding["amount"] * holding["avg_price"]
                value = holding["amount"] * current_price
                pnl = value - cost
                pnl_ratio = (pnl / cost * 100) if cost > 0 else 0
                position_ratio = (
                    (value / total_value * 100) if total_value > 0 else 0
                )

                row_frame = tk.Frame(scrollable_frame, bg="#1e1e1e")
                row_frame.pack(fill="x", pady=1)

                # 币种
                tk.Label(
                    row_frame,
                    text=symbol,
                    font=("Arial", 9, "bold"),
                    width=8,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 数量
                quantity_precision = self.get_quantity_precision(symbol)
                quantity_text = f"{holding['amount']:.{quantity_precision}f}"
                tk.Label(
                    row_frame,
                    text=quantity_text,
                    font=("Arial", 9),
                    width=12,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 均价
                price_precision = self.get_price_precision(symbol)
                avg_price_text = f"{holding['avg_price']:.{price_precision}f}"
                tk.Label(
                    row_frame,
                    text=avg_price_text,
                    font=("Arial", 9),
                    width=10,
                    fg="#ffaa00",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 现价
                current_price_text = f"{current_price:.{price_precision}f}"
                price_color = (
                    "#00ff00"
                    if current_price >= holding["avg_price"]
                    else "#ff4444"
                )
                tk.Label(
                    row_frame,
                    text=current_price_text,
                    font=("Arial", 9),
                    width=10,
                    fg=price_color,
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 成本
                cost_text = f"{cost:.2f}"
                tk.Label(
                    row_frame,
                    text=cost_text,
                    font=("Arial", 9),
                    width=10,
                    fg="#888888",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 价值
                value_text = f"{value:.2f}"
                tk.Label(
                    row_frame,
                    text=value_text,
                    font=("Arial", 9),
                    width=10,
                    fg="#ffffff",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 盈亏
                pnl_text = f"{pnl:+.2f}"
                pnl_color = "#00ff00" if pnl >= 0 else "#ff4444"
                tk.Label(
                    row_frame,
                    text=pnl_text,
                    font=("Arial", 9),
                    width=10,
                    fg=pnl_color,
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 盈亏%
                pnl_ratio_text = f"{pnl_ratio:+.2f}%"
                tk.Label(
                    row_frame,
                    text=pnl_ratio_text,
                    font=("Arial", 9),
                    width=8,
                    fg=pnl_color,
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

                # 仓位%
                position_ratio_text = f"{position_ratio:.1f}%"
                tk.Label(
                    row_frame,
                    text=position_ratio_text,
                    font=("Arial", 9),
                    width=8,
                    fg="#888888",
                    bg="#1e1e1e",
                    relief="ridge",
                ).pack(side="left")

        if not has_positions:
            tk.Label(
                scrollable_frame,
                text="📭 暂无持仓",
                font=("Arial", 12),
                fg="#888888",
                bg="#1e1e1e",
            ).pack(pady=20)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 风险分析框架
        risk_frame = tk.LabelFrame(
            analysis_window, text="风险分析", fg="#00ff00", bg="#1e1e1e"
        )
        risk_frame.pack(fill="x", padx=20, pady=10)

        # 计算风险指标
        max_position_ratio = 0
        max_position_symbol = ""
        concentration_risk = "低"

        for symbol, holding in self.spot_holdings.items():
            if holding["amount"] > 0:
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                value = holding["amount"] * current_price
                position_ratio = (
                    (value / total_value * 100) if total_value > 0 else 0
                )

                if position_ratio > max_position_ratio:
                    max_position_ratio = position_ratio
                    max_position_symbol = symbol

        if max_position_ratio > 50:
            concentration_risk = "高"
        elif max_position_ratio > 30:
            concentration_risk = "中"

        # 显示风险信息
        risk_info = tk.Frame(risk_frame, bg="#1e1e1e")
        risk_info.pack(fill="x", padx=10, pady=5)

        tk.Label(
            risk_info,
            text=f"最大单仓: {max_position_symbol} ({max_position_ratio:.1f}%)",
            fg="#ffffff",
            bg="#1e1e1e",
        ).pack(side="left", padx=10)

        risk_color = (
            "#ff4444"
            if concentration_risk == "高"
            else "#ffaa00" if concentration_risk == "中" else "#00ff00"
        )
        tk.Label(
            risk_info,
            text=f"集中度风险: {concentration_risk}",
            fg=risk_color,
            bg="#1e1e1e",
        ).pack(side="left", padx=10)

        # 操作按钮
        button_frame = tk.Frame(analysis_window, bg="#1e1e1e")
        button_frame.pack(pady=10)

        tk.Button(
            button_frame,
            text="🔄 刷新",
            command=lambda: self.refresh_position_analysis(analysis_window),
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            button_frame,
            text="📤 导出",
            command=self.export_position_analysis,
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)

        self.add_log("📈 打开持仓分析")

    def refresh_position_analysis(self, window):
        """刷新持仓分析窗口"""
        window.destroy()
        self.show_position_analysis()

    def export_position_analysis(self):
        """导出持仓分析"""
        try:
            filename = (
                f"现货持仓分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            with open(filename, "w", encoding="utf-8", newline="") as f:
                import csv

                writer = csv.writer(f)

                # 写入表头
                writer.writerow(
                    [
                        "币种",
                        "数量",
                        "均价",
                        "现价",
                        "成本",
                        "价值",
                        "盈亏",
                        "盈亏%",
                        "仓位%",
                    ]
                )

                # 计算总价值
                total_value = sum(
                    holding["amount"]
                    * self.spot_pairs[f"{symbol}/USDT"]["price"]
                    for symbol, holding in self.spot_holdings.items()
                    if holding["amount"] > 0
                )

                # 写入数据
                for symbol, holding in self.spot_holdings.items():
                    if holding["amount"] > 0:
                        current_price = self.spot_pairs[f"{symbol}/USDT"][
                            "price"
                        ]
                        cost = holding["amount"] * holding["avg_price"]
                        value = holding["amount"] * current_price
                        pnl = value - cost
                        pnl_ratio = (pnl / cost * 100) if cost > 0 else 0
                        position_ratio = (
                            (value / total_value * 100)
                            if total_value > 0
                            else 0
                        )

                        row = [
                            symbol,
                            f"{holding['amount']:.6f}",
                            f"{holding['avg_price']:.4f}",
                            f"{current_price:.4f}",
                            f"{cost:.2f}",
                            f"{value:.2f}",
                            f"{pnl:.2f}",
                            f"{pnl_ratio:.2f}%",
                            f"{position_ratio:.1f}%",
                        ]
                        writer.writerow(row)

            messagebox.showinfo("导出成功", f"持仓分析已导出到: {filename}")
            self.add_log(f"📤 持仓分析已导出: {filename}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出错误: {str(e)}")

    def show_margin_trading(self):
        """显示杠杆交易窗口"""
        margin_window = tk.Toplevel(self.root)
        margin_window.title("💎 现货杠杆交易")
        margin_window.geometry("600x700")
        margin_window.configure(bg="#1e1e1e")

        tk.Label(
            margin_window,
            text="💎 现货杠杆交易",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 杠杆配置框架
        config_frame = tk.LabelFrame(
            margin_window, text="杠杆配置", fg="#00ff00", bg="#1e1e1e"
        )
        config_frame.pack(fill="x", padx=20, pady=10)

        # 币种选择
        tk.Label(
            config_frame, text="选择币种:", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        symbol_var = tk.StringVar(value="BTC")
        symbol_combo = ttk.Combobox(
            config_frame,
            textvariable=symbol_var,
            values=["BTC", "ETH", "SOL", "BNB", "ADA", "DOT", "LINK", "UNI"],
        )
        symbol_combo.pack(padx=10, pady=5)

        # 杠杆倍数选择
        tk.Label(
            config_frame, text="杠杆倍数:", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        leverage_var = tk.StringVar(value="2")
        leverage_frame = tk.Frame(config_frame, bg="#1e1e1e")
        leverage_frame.pack(padx=10, pady=5)

        def update_leverage_options():
            symbol = symbol_var.get()
            max_lev = self.margin_config["max_leverage"].get(
                symbol, self.margin_config["max_leverage"]["others"]
            )
            leverage_options = [str(i) for i in range(2, max_lev + 1)]
            leverage_combo["values"] = leverage_options
            if leverage_var.get() not in leverage_options:
                leverage_var.set(
                    leverage_options[0] if leverage_options else "2"
                )

        leverage_combo = ttk.Combobox(
            leverage_frame, textvariable=leverage_var, width=10
        )
        leverage_combo.pack(side="left")

        symbol_combo.bind(
            "<<ComboboxSelected>>", lambda e: update_leverage_options()
        )
        update_leverage_options()

        # 交易金额
        tk.Label(
            config_frame, text="交易金额 (USDT):", fg="#ffffff", bg="#1e1e1e"
        ).pack(anchor="w", padx=10)
        amount_var = tk.StringVar(value="100")
        tk.Entry(config_frame, textvariable=amount_var, width=20).pack(
            padx=10, pady=5
        )

        # 当前杠杆持仓显示
        position_frame = tk.LabelFrame(
            margin_window, text="当前杠杆持仓", fg="#00ff00", bg="#1e1e1e"
        )
        position_frame.pack(fill="x", padx=20, pady=10)

        def refresh_positions():
            for widget in position_frame.winfo_children():
                if isinstance(widget, tk.Frame):
                    widget.destroy()

            has_positions = any(
                pos["amount"] > 0 for pos in self.margin_positions.values()
            )

            if not has_positions:
                tk.Label(
                    position_frame,
                    text="📭 当前无杠杆持仓",
                    fg="#888888",
                    bg="#1e1e1e",
                ).pack(pady=10)
            else:
                for symbol, position in self.margin_positions.items():
                    if position["amount"] > 0:
                        pos_frame = tk.Frame(position_frame, bg="#2e2e2e")
                        pos_frame.pack(fill="x", padx=10, pady=2)

                        current_price = self.spot_pairs[f"{symbol}/USDT"][
                            "price"
                        ]
                        position_value = position["amount"] * current_price
                        borrowed = self.borrowed_amounts.get(symbol, 0)
                        interest = self.interest_accumulated.get(symbol, 0)
                        margin_ratio = self.calculate_margin_ratio(
                            symbol, current_price
                        )

                        info_text = f"{symbol}: {position['amount']:.6f} | 杠杆: {position['leverage']}x | 保证金率: {margin_ratio:.2%}"
                        tk.Label(
                            pos_frame,
                            text=info_text,
                            fg="#ffffff",
                            bg="#2e2e2e",
                        ).pack(side="left")

        refresh_positions()

        # 交易按钮
        button_frame = tk.Frame(margin_window, bg="#1e1e1e")
        button_frame.pack(pady=20)

        def open_margin_position():
            try:
                symbol = symbol_var.get()
                leverage = int(leverage_var.get())
                amount = float(amount_var.get())

                # 检查杠杆限制
                max_leverage = self.margin_config["max_leverage"].get(
                    symbol, self.margin_config["max_leverage"]["others"]
                )
                if leverage > max_leverage:
                    messagebox.showerror(
                        "杠杆超限", f"{symbol}最大杠杆为{max_leverage}倍"
                    )
                    return

                # 计算所需保证金
                required_margin = amount / leverage

                if required_margin > self.trading_config["usdt_balance"]:
                    messagebox.showerror(
                        "余额不足",
                        f"所需保证金: {required_margin:.2f} USDT\n可用余额: {self.trading_config['usdt_balance']:.2f} USDT",
                    )
                    return

                # 验证Gate.io规则
                errors, validated_price, buy_amount = (
                    self.validate_gate_io_rules(symbol, amount, "buy")
                )
                if errors:
                    messagebox.showerror("交易验证失败", "\n".join(errors))
                    return

                # 计算借币数量
                borrow_amount = amount * (leverage - 1) / leverage

                # 计算手续费
                trading_fee = self.calculate_trading_fee(amount)
                total_cost = required_margin + trading_fee

                # 执行杠杆买入
                if symbol not in self.margin_positions:
                    self.margin_positions[symbol] = {
                        "amount": 0,
                        "cost_basis": 0,
                        "leverage": 1,
                        "last_interest_update": time.time(),
                    }

                # 更新杠杆持仓
                position = self.margin_positions[symbol]
                if position["amount"] > 0:
                    # 合并持仓
                    total_value = (
                        position["amount"]
                        * position.get("avg_price", validated_price)
                        + amount
                    )
                    total_amount = position["amount"] + buy_amount
                    avg_price = total_value / total_amount
                    position["avg_price"] = avg_price
                else:
                    position["avg_price"] = validated_price
                    position["last_interest_update"] = time.time()

                position["amount"] += buy_amount
                position["leverage"] = leverage
                position["cost_basis"] = (
                    position.get("cost_basis", 0) + required_margin
                )

                # 更新借币数量
                if symbol not in self.borrowed_amounts:
                    self.borrowed_amounts[symbol] = 0
                self.borrowed_amounts[symbol] += borrow_amount

                # 扣除保证金
                self.trading_config["usdt_balance"] -= total_cost

                # 记录交易历史
                trade_record = {
                    "time": datetime.now(),
                    "strategy": "杠杆交易",
                    "type": "买入",
                    "symbol": symbol,
                    "amount": buy_amount,
                    "price": validated_price,
                    "cost": total_cost,
                    "fee": trading_fee,
                    "leverage": leverage,
                }
                self.trade_history.append(trade_record)

                quantity_precision = self.get_quantity_precision(symbol)
                price_precision = self.get_price_precision(symbol)
                self.add_log(
                    f"💎 杠杆买入 {buy_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} {leverage}x杠杆"
                )

                refresh_positions()
                margin_window.destroy()

            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数字")
            except Exception as e:
                messagebox.showerror("交易错误", f"杠杆买入失败: {str(e)}")

        def close_margin_position():
            try:
                symbol = symbol_var.get()

                if (
                    symbol not in self.margin_positions
                    or self.margin_positions[symbol]["amount"] <= 0
                ):
                    messagebox.showerror("无持仓", f"当前无{symbol}杠杆持仓")
                    return

                position = self.margin_positions[symbol]
                current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]

                # 计算平仓
                sell_amount = position["amount"]
                sell_value = sell_amount * current_price

                # 验证Gate.io规则
                errors, validated_price, _ = self.validate_gate_io_rules(
                    symbol, sell_value, "sell"
                )
                if errors:
                    messagebox.showerror("交易验证失败", "\n".join(errors))
                    return

                trading_fee = self.calculate_trading_fee(sell_value)
                net_proceeds = sell_value - trading_fee

                # 计算还币成本
                borrowed_amount = self.borrowed_amounts.get(symbol, 0)
                interest = self.interest_accumulated.get(symbol, 0)

                if symbol in [
                    "BTC",
                    "ETH",
                    "SOL",
                    "BNB",
                    "ADA",
                    "DOT",
                    "LINK",
                    "UNI",
                ]:
                    repay_cost = borrowed_amount * current_price
                else:
                    repay_cost = borrowed_amount

                total_repay = repay_cost + interest

                # 计算最终收益
                final_usdt = net_proceeds - total_repay
                cost_basis = position["cost_basis"]
                profit = final_usdt - cost_basis

                # 更新余额
                self.trading_config["usdt_balance"] += final_usdt
                self.trading_config["daily_pnl"] += profit

                # 清空杠杆持仓
                self.margin_positions[symbol] = {
                    "amount": 0,
                    "cost_basis": 0,
                    "leverage": 1,
                    "last_interest_update": 0,
                }
                self.borrowed_amounts[symbol] = 0
                self.interest_accumulated[symbol] = 0

                # 记录交易历史
                trade_record = {
                    "time": datetime.now(),
                    "strategy": "杠杆平仓",
                    "type": "卖出",
                    "symbol": symbol,
                    "amount": sell_amount,
                    "price": validated_price,
                    "proceeds": final_usdt,
                    "fee": trading_fee,
                    "profit": profit,
                }
                self.trade_history.append(trade_record)

                quantity_precision = self.get_quantity_precision(symbol)
                price_precision = self.get_price_precision(symbol)
                self.add_log(
                    f"💰 杠杆平仓 {sell_amount:.{quantity_precision}f} {symbol} @ {validated_price:.{price_precision}f} 盈亏: {profit:+.2f}"
                )

                refresh_positions()
                margin_window.destroy()

            except Exception as e:
                messagebox.showerror("平仓错误", f"杠杆平仓失败: {str(e)}")

        tk.Button(
            button_frame,
            text="💎 开仓",
            command=open_margin_position,
            bg="#00aa00",
            fg="white",
            font=("Arial", 12, "bold"),
        ).pack(side="left", padx=10)
        tk.Button(
            button_frame,
            text="💰 平仓",
            command=close_margin_position,
            bg="#aa0000",
            fg="white",
            font=("Arial", 12, "bold"),
        ).pack(side="left", padx=10)
        tk.Button(
            button_frame,
            text="🔄 刷新",
            command=refresh_positions,
            bg="#333333",
            fg="white",
            font=("Arial", 12, "bold"),
        ).pack(side="left", padx=10)

        # 风险提示
        risk_frame = tk.LabelFrame(
            margin_window, text="风险提示", fg="#ff4444", bg="#1e1e1e"
        )
        risk_frame.pack(fill="x", padx=20, pady=10)

        risk_text = """
⚠️ 杠杆交易风险提示：
• 杠杆交易具有高风险，可能导致全部本金损失
• 保证金率低于15%将被强制平仓
• 借币会产生利息费用，按小时计算
• 请合理控制仓位，做好风险管理
        """
        tk.Label(
            risk_frame,
            text=risk_text,
            fg="#ff4444",
            bg="#1e1e1e",
            justify="left",
        ).pack(padx=10, pady=5)

        self.add_log("💎 打开杠杆交易窗口")

    def show_risk_management(self):
        """显示风险管理窗口"""
        risk_window = tk.Toplevel(self.root)
        risk_window.title("🛡️ 风险管理中心")
        risk_window.geometry("800x800")
        risk_window.configure(bg="#1e1e1e")

        tk.Label(
            risk_window,
            text="🛡️ 风险管理中心",
            font=("Arial", 14, "bold"),
            fg="#00ff00",
            bg="#1e1e1e",
        ).pack(pady=10)

        # 风险概览框架
        overview_frame = tk.LabelFrame(
            risk_window, text="风险概览", fg="#00ff00", bg="#1e1e1e"
        )
        overview_frame.pack(fill="x", padx=20, pady=10)

        def refresh_risk_overview():
            for widget in overview_frame.winfo_children():
                if isinstance(widget, tk.Frame):
                    widget.destroy()

            # 计算风险指标
            total_value = self.trading_config["total_value"]
            initial_capital = self.trading_config["initial_capital"]
            daily_start_value = self.risk_status["daily_start_value"]
            peak_value = self.risk_status["peak_value"]

            # 总盈亏
            total_pnl = total_value - initial_capital
            total_pnl_ratio = (
                (total_pnl / initial_capital * 100)
                if initial_capital > 0
                else 0
            )

            # 日盈亏
            daily_pnl = (
                total_value - daily_start_value if daily_start_value > 0 else 0
            )
            daily_pnl_ratio = (
                (daily_pnl / daily_start_value * 100)
                if daily_start_value > 0
                else 0
            )

            # 最大回撤
            drawdown = (
                ((peak_value - total_value) / peak_value * 100)
                if peak_value > 0
                else 0
            )

            # 紧急模式状态
            emergency_status = (
                "🚨 紧急模式"
                if self.risk_status["emergency_mode"]
                else "✅ 正常"
            )

            # 显示概览信息
            overview_info = tk.Frame(overview_frame, bg="#1e1e1e")
            overview_info.pack(fill="x", padx=10, pady=5)

            # 第一行
            row1 = tk.Frame(overview_info, bg="#1e1e1e")
            row1.pack(fill="x", pady=2)

            tk.Label(
                row1,
                text=f"总资产: {total_value:.2f} USDT",
                fg="#ffffff",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)
            tk.Label(
                row1,
                text=f"总盈亏: {total_pnl:+.2f} ({total_pnl_ratio:+.2f}%)",
                fg="#00ff00" if total_pnl >= 0 else "#ff4444",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)
            tk.Label(
                row1,
                text=f"状态: {emergency_status}",
                fg=(
                    "#ff4444"
                    if self.risk_status["emergency_mode"]
                    else "#00ff00"
                ),
                bg="#1e1e1e",
            ).pack(side="right", padx=10)

            # 第二行
            row2 = tk.Frame(overview_info, bg="#1e1e1e")
            row2.pack(fill="x", pady=2)

            tk.Label(
                row2,
                text=f"日盈亏: {daily_pnl:+.2f} ({daily_pnl_ratio:+.2f}%)",
                fg="#00ff00" if daily_pnl >= 0 else "#ff4444",
                bg="#1e1e1e",
            ).pack(side="left", padx=10)
            tk.Label(
                row2,
                text=f"最大回撤: {drawdown:.2f}%",
                fg=(
                    "#ff4444"
                    if drawdown > 10
                    else "#ffaa00" if drawdown > 5 else "#00ff00"
                ),
                bg="#1e1e1e",
            ).pack(side="left", padx=10)

        refresh_risk_overview()

        # 仓位风险框架
        position_frame = tk.LabelFrame(
            risk_window, text="仓位风险", fg="#00ff00", bg="#1e1e1e"
        )
        position_frame.pack(fill="x", padx=20, pady=10)

        def refresh_position_risk():
            for widget in position_frame.winfo_children():
                if isinstance(widget, tk.Frame):
                    widget.destroy()

            total_value = self.trading_config["total_value"]
            max_ratio = self.risk_management_config[
                "max_single_position_ratio"
            ]

            # 检查现货持仓
            for symbol, holding in self.spot_holdings.items():
                if holding["amount"] > 0:
                    current_price = self.spot_pairs[f"{symbol}/USDT"]["price"]
                    position_value = holding["amount"] * current_price
                    position_ratio = (
                        (position_value / total_value * 100)
                        if total_value > 0
                        else 0
                    )

                    if position_ratio > max_ratio * 50:  # 显示超过15%的仓位
                        pos_frame = tk.Frame(position_frame, bg="#2e2e2e")
                        pos_frame.pack(fill="x", padx=10, pady=2)

                        risk_color = (
                            "#ff4444"
                            if position_ratio > max_ratio * 100
                            else "#ffaa00"
                        )
                        info_text = f"{symbol}: {position_ratio:.1f}% (限制: {max_ratio*100:.0f}%)"

                        tk.Label(
                            pos_frame,
                            text=info_text,
                            fg=risk_color,
                            bg="#2e2e2e",
                        ).pack(side="left")
                        tk.Label(
                            pos_frame,
                            text=f"价值: {position_value:.2f} USDT",
                            fg="#ffffff",
                            bg="#2e2e2e",
                        ).pack(side="right")

        refresh_position_risk()

        # 板块风险框架
        sector_frame = tk.LabelFrame(
            risk_window, text="板块风险", fg="#00ff00", bg="#1e1e1e"
        )
        sector_frame.pack(fill="x", padx=20, pady=10)

        def refresh_sector_risk():
            for widget in sector_frame.winfo_children():
                if isinstance(widget, tk.Frame):
                    widget.destroy()

            total_value = self.trading_config["total_value"]
            max_sector_exposure = self.risk_management_config[
                "max_sector_exposure"
            ]

            for sector, symbols in self.sector_classification.items():
                sector_value = 0

                # 计算板块总价值
                for symbol in symbols:
                    if (
                        symbol in self.spot_holdings
                        and self.spot_holdings[symbol]["amount"] > 0
                    ):
                        current_price = self.spot_pairs[f"{symbol}/USDT"][
                            "price"
                        ]
                        sector_value += (
                            self.spot_holdings[symbol]["amount"]
                            * current_price
                        )

                sector_ratio = (
                    (sector_value / total_value * 100)
                    if total_value > 0
                    else 0
                )

                if sector_ratio > 5:  # 显示超过5%的板块
                    sector_frame_widget = tk.Frame(sector_frame, bg="#2e2e2e")
                    sector_frame_widget.pack(fill="x", padx=10, pady=2)

                    risk_color = (
                        "#ff4444"
                        if sector_ratio > max_sector_exposure * 100
                        else (
                            "#ffaa00"
                            if sector_ratio > max_sector_exposure * 80
                            else "#00ff00"
                        )
                    )
                    info_text = f"{sector}: {sector_ratio:.1f}% (限制: {max_sector_exposure*100:.0f}%)"

                    tk.Label(
                        sector_frame_widget,
                        text=info_text,
                        fg=risk_color,
                        bg="#2e2e2e",
                    ).pack(side="left")
                    tk.Label(
                        sector_frame_widget,
                        text=f"价值: {sector_value:.2f} USDT",
                        fg="#ffffff",
                        bg="#2e2e2e",
                    ).pack(side="right")

        refresh_sector_risk()

        # 风险警报框架
        alert_frame = tk.LabelFrame(
            risk_window, text="风险警报", fg="#00ff00", bg="#1e1e1e"
        )
        alert_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 创建滚动框架
        canvas = tk.Canvas(alert_frame, bg="#1e1e1e")
        scrollbar = tk.Scrollbar(
            alert_frame, orient="vertical", command=canvas.yview
        )
        scrollable_frame = tk.Frame(canvas, bg="#1e1e1e")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all")),
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        def refresh_alerts():
            for widget in scrollable_frame.winfo_children():
                widget.destroy()

            recent_alerts = [
                a
                for a in self.risk_status["risk_alerts"]
                if time.time() - a["time"] < 3600
            ]  # 1小时内的警报

            if not recent_alerts:
                tk.Label(
                    scrollable_frame,
                    text="✅ 暂无风险警报",
                    fg="#00ff00",
                    bg="#1e1e1e",
                ).pack(pady=20)
            else:
                for alert in recent_alerts[-20:]:  # 显示最近20条
                    alert_widget = tk.Frame(scrollable_frame, bg="#2e2e2e")
                    alert_widget.pack(fill="x", padx=5, pady=2)

                    # 时间
                    alert_time = datetime.fromtimestamp(
                        alert["time"]
                    ).strftime("%H:%M:%S")
                    tk.Label(
                        alert_widget,
                        text=alert_time,
                        font=("Arial", 8),
                        fg="#888888",
                        bg="#2e2e2e",
                    ).pack(side="left", padx=5)

                    # 警报信息
                    alert_color = (
                        "#ff4444"
                        if alert["level"] == "critical"
                        else "#ffaa00"
                    )
                    tk.Label(
                        alert_widget,
                        text=alert["message"],
                        fg=alert_color,
                        bg="#2e2e2e",
                    ).pack(side="left", padx=5)

        refresh_alerts()

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 风险控制按钮
        control_frame = tk.Frame(risk_window, bg="#1e1e1e")
        control_frame.pack(pady=10)

        def reset_emergency_mode():
            if self.risk_status["emergency_mode"]:
                self.risk_status["emergency_mode"] = False
                # 恢复策略运行
                for strategy_name in self.strategies:
                    if self.strategies[strategy_name]["status"] == "紧急暂停":
                        self.strategies[strategy_name]["status"] = "运行中"
                self.add_log("✅ 紧急模式已解除，策略恢复运行")
                refresh_risk_overview()

        def reset_daily_stats():
            self.risk_status["daily_start_value"] = self.trading_config[
                "total_value"
            ]
            self.trading_config["daily_pnl"] = 0
            self.add_log("🔄 日统计已重置")
            refresh_risk_overview()

        def clear_alerts():
            self.risk_status["risk_alerts"] = []
            self.add_log("🗑️ 风险警报已清空")
            refresh_alerts()

        tk.Button(
            control_frame,
            text="🔄 刷新",
            command=lambda: [
                refresh_risk_overview(),
                refresh_position_risk(),
                refresh_sector_risk(),
                refresh_alerts(),
            ],
            bg="#333333",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            control_frame,
            text="✅ 解除紧急",
            command=reset_emergency_mode,
            bg="#00aa00",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            control_frame,
            text="🔄 重置日统计",
            command=reset_daily_stats,
            bg="#0066aa",
            fg="white",
        ).pack(side="left", padx=5)
        tk.Button(
            control_frame,
            text="🗑️ 清空警报",
            command=clear_alerts,
            bg="#aa6600",
            fg="white",
        ).pack(side="left", padx=5)

        # 风险配置显示
        config_frame = tk.LabelFrame(
            risk_window, text="风险配置", fg="#00ff00", bg="#1e1e1e"
        )
        config_frame.pack(fill="x", padx=20, pady=10)

        config_text = f"""
单币种仓位限制: {self.risk_management_config['max_single_position_ratio']*100:.0f}%
板块敞口限制: {self.risk_management_config['max_sector_exposure']*100:.0f}%
日亏损限制: {self.risk_management_config['daily_loss_limit_ratio']*100:.0f}%
总亏损限制: {self.risk_management_config['total_loss_limit_ratio']*100:.0f}%
最大回撤限制: {self.risk_management_config['max_drawdown_ratio']*100:.0f}%
        """
        tk.Label(
            config_frame,
            text=config_text,
            fg="#ffffff",
            bg="#1e1e1e",
            justify="left",
        ).pack(padx=10, pady=5)

        self.add_log("🛡️ 打开风险管理中心")

    def refresh_data(self):
        """刷新数据"""
        self.update_prices()
        self.update_balance_display()
        self.update_strategy_display()
        self.update_holdings_display()
        self.add_log("🔄 数据已刷新")

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert("end", log_entry)
        self.log_text.see("end")

        # 限制日志长度
        lines = self.log_text.get("1.0", "end").split("\n")
        if len(lines) > 50:
            self.log_text.delete("1.0", "10.0")

    def start_updates(self):
        """P1修复：优化更新系统"""
        self.update_counter = 0
        self.last_update_time = time.time()

        def update_loop():
            while True:
                try:
                    current_time = time.time()
                    self.update_counter += 1

                    # P1修复：优化更新频率，减少系统负载
                    # 更新市场数据（真实数据或模拟数据）
                    if self.use_real_data:
                        self.update_with_real_data()
                    else:
                        self.simulate_price_changes()

                    # P1修复：降低交易检查频率，每60秒检查一次
                    if (
                        current_time - self.last_update_time >= 60
                    ):  # 每60秒执行一次交易检查
                        self.simulate_spot_trading()
                        self.last_update_time = current_time

                    # P1修复：优化GUI更新，减少线程冲突
                    self.safe_update_gui()

                    # P1修复：增加系统健康检查
                    if self.update_counter % 10 == 0:  # 每10分钟检查一次
                        self.system_health_check()

                    time.sleep(20)  # P1修复：增加到20秒更新一次，减少CPU占用

                except Exception as e:
                    self.handle_update_error(e)

        threading.Thread(target=update_loop, daemon=True).start()

    def safe_update_gui(self):
        """P1修复：安全的GUI更新"""
        try:
            # 批量更新，减少GUI调用次数
            update_tasks = []

            # 基础更新
            update_tasks.append(("update_prices", 0))
            update_tasks.append(("update_balance_display", 50))

            # 低频更新
            if self.update_counter % 3 == 0:  # 每分钟更新一次
                update_tasks.append(("update_strategy_display", 100))
                update_tasks.append(("update_holdings_display", 150))

            # 状态日志
            if self.update_counter % 15 == 0:  # 每5分钟添加一次状态
                self.add_status_log()

            # 执行更新任务
            for task_name, delay in update_tasks:
                if hasattr(self, task_name):
                    self.root.after(delay, getattr(self, task_name))

        except Exception as e:
            print(f"GUI更新错误: {e}")

    def add_status_log(self):
        """P1修复：添加真实状态日志"""
        try:
            total_value = self.trading_config.get("total_value", 1000)
            daily_pnl = self.trading_config.get("daily_pnl", 0)
            active_positions = sum(
                1 for h in self.spot_holdings.values() if h["amount"] > 0
            )
            today_trades = len(
                [
                    t
                    for t in self.trade_history
                    if t.get("time", datetime.min).date()
                    == datetime.now().date()
                ]
            )

            status_msg = f"📊 系统状态: 总资产 {total_value:.2f} USDT | 日盈亏 {daily_pnl:+.2f} | 持仓 {active_positions} 个 | 今日交易 {today_trades} 笔"
            self.add_log(status_msg)

        except Exception as e:
            print(f"状态日志错误: {e}")

    def system_health_check(self):
        """P1修复：系统健康检查"""
        try:
            # 检查内存使用
            if len(self.trade_history) > 1000:  # 限制交易历史记录
                self.trade_history = self.trade_history[-500:]  # 保留最近500条
                self.add_log("🔄 清理交易历史记录，保持系统性能")

            # 检查价格数据
            invalid_pairs = []
            for pair, data in self.spot_pairs.items():
                if data.get("price", 0) <= 0:
                    invalid_pairs.append(pair)

            if invalid_pairs:
                self.add_log(f"⚠️ 发现无效价格数据: {', '.join(invalid_pairs)}")

            # 检查资金一致性
            total_balance = self.trading_config["usdt_balance"]
            total_holdings_value = sum(
                h["amount"] * self.spot_pairs[f"{symbol}/USDT"]["price"]
                for symbol, h in self.spot_holdings.items()
                if h["amount"] > 0 and f"{symbol}/USDT" in self.spot_pairs
            )

            if total_balance < 0:
                self.add_log("⚠️ 资金异常: USDT余额为负数")
                self.trading_config["usdt_balance"] = max(0, total_balance)

            self.trading_config["total_value"] = (
                total_balance + total_holdings_value
            )

        except Exception as e:
            self.add_log(f"❌ 系统健康检查错误: {str(e)}")

    def handle_update_error(self, error):
        """P1修复：完善错误处理"""
        try:
            error_msg = str(error)
            print(f"更新错误: {error_msg}")

            # 记录错误但不中断系统
            self.root.after(
                0, lambda: self.add_log(f"⚠️ 系统更新异常，已自动恢复")
            )

            # 根据错误类型采取不同措施
            if "GUI" in error_msg or "tkinter" in error_msg:
                time.sleep(5)  # GUI错误，短暂等待
            elif "memory" in error_msg.lower():
                # 内存问题，清理数据
                if len(self.trade_history) > 100:
                    self.trade_history = self.trade_history[-50:]
                time.sleep(10)
            else:
                time.sleep(30)  # 其他错误，较长等待

        except Exception as e:
            print(f"错误处理失败: {e}")
            time.sleep(60)  # 最后的安全措施

    def run(self):
        """运行现货GUI"""
        print("💎 启动现货版GUI...")
        self.add_log("🌟 欢迎使用终极版交易系统 - 现货版")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SpotTradingGUI()
        app.run()
    except Exception as e:
        print(f"现货GUI启动失败: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()

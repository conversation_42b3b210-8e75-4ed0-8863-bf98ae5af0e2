#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI界面选择器
GUI Interface Selector

让用户选择和预览不同的GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from pathlib import Path

class GUISelector:
    """GUI界面选择器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎨 GUI界面选择器 - 选择您喜欢的交易界面")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # GUI选项配置
        self.gui_options = {
            'simple_spot_gui.py': {
                'name': '💎 简化现货交易界面',
                'description': '简洁清爽的现货交易界面，适合新手用户',
                'features': ['简洁设计', '现货专用', '易于操作', '性能优化'],
                'complexity': '⭐⭐☆☆☆',
                'type': '简化版',
                'color': '#3498db'
            },
            'optimized_trading_gui.py': {
                'name': '🚀 优化版交易界面', 
                'description': '性能优化的专业交易界面，支持多种币种',
                'features': ['性能优化', '多币种支持', '专业功能', '稳定运行'],
                'complexity': '⭐⭐⭐☆☆',
                'type': '优化版',
                'color': '#e74c3c'
            },
            'ultimate_spot_trading_gui.py': {
                'name': '💼 终极现货交易界面',
                'description': '专业级现货交易系统，功能最全面',
                'features': ['专业级功能', '现货专用', '高级策略', '学习模式'],
                'complexity': '⭐⭐⭐⭐☆',
                'type': '专业版',
                'color': '#9b59b6'
            },
            'multi_pairs_gui.py': {
                'name': '📊 多交易对界面',
                'description': '支持22个交易对的多币种交易界面',
                'features': ['22个交易对', '分类显示', '批量监控', '全面覆盖'],
                'complexity': '⭐⭐⭐⭐☆',
                'type': '多币种版',
                'color': '#f39c12'
            },
            'simple_gui.py': {
                'name': '🌟 经典交易界面',
                'description': '功能完整的经典交易界面，平衡性能与功能',
                'features': ['功能完整', '经典设计', '平衡性能', '可靠稳定'],
                'complexity': '⭐⭐⭐☆☆',
                'type': '经典版',
                'color': '#1abc9c'
            },
            'spot_snowball_gui.py': {
                'name': '❄️ 滚雪球策略界面',
                'description': '专门为滚雪球策略设计的交易界面',
                'features': ['滚雪球策略', '复利计算', '风险控制', '增长追踪'],
                'complexity': '⭐⭐⭐☆☆',
                'type': '策略版',
                'color': '#34495e'
            },
            'trading_system_gui.py': {
                'name': '⚡ 交易系统界面',
                'description': '系统化的交易管理界面',
                'features': ['系统管理', '配置灵活', '监控全面', '操作便捷'],
                'complexity': '⭐⭐⭐⭐☆',
                'type': '系统版',
                'color': '#e67e22'
            },
            'gate_simulation_gui.py': {
                'name': '🏛️ Gate模拟交易界面',
                'description': '模拟Gate.io交易所的交易界面',
                'features': ['交易所模拟', '真实体验', '安全练习', '接口仿真'],
                'complexity': '⭐⭐⭐⭐⭐',
                'type': '模拟版',
                'color': '#95a5a6'
            },
            'ultimate_trading_gui.py': {
                'name': '👑 终极交易界面 (当前)',
                'description': '最高级的交易界面，集成所有优化功能',
                'features': ['最高级功能', '完整集成', '性能最优', '企业级'],
                'complexity': '⭐⭐⭐⭐⭐',
                'type': '终极版',
                'color': '#2c3e50'
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="🎨 GUI界面选择器",
            font=('Microsoft YaHei', 24, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_frame,
            text="选择您喜欢的交易界面风格",
            font=('Microsoft YaHei', 12),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # 主内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 左侧界面列表
        left_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # 界面列表标题
        list_title = tk.Label(
            left_frame,
            text="🖥️ 可用界面列表",
            font=('Microsoft YaHei', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        list_title.pack(pady=10)
        
        # 创建滚动框
        canvas = tk.Canvas(left_frame, bg='white')
        scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建GUI选项卡片
        for i, (filename, config) in enumerate(self.gui_options.items()):
            self.create_gui_card(scrollable_frame, filename, config, i)
        
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")
        
        # 右侧预览和控制区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2, width=400)
        right_frame.pack(side='right', fill='y', padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 预览区域标题
        preview_title = tk.Label(
            right_frame,
            text="🔍 界面预览",
            font=('Microsoft YaHei', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        preview_title.pack(pady=10)
        
        # 当前选择显示
        self.current_selection_frame = tk.Frame(right_frame, bg='#ecf0f1', relief='sunken', bd=2)
        self.current_selection_frame.pack(fill='x', padx=10, pady=10)
        
        self.selection_label = tk.Label(
            self.current_selection_frame,
            text="请选择一个界面查看详情",
            font=('Microsoft YaHei', 12),
            bg='#ecf0f1',
            fg='#7f8c8d',
            wraplength=360,
            justify='center'
        )
        self.selection_label.pack(pady=20)
        
        # 控制按钮区域
        button_frame = tk.Frame(right_frame, bg='white')
        button_frame.pack(fill='x', padx=10, pady=20)
        
        # 启动按钮
        self.launch_button = tk.Button(
            button_frame,
            text="🚀 启动选中界面",
            font=('Microsoft YaHei', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            state='disabled',
            command=self.launch_selected_gui
        )
        self.launch_button.pack(fill='x', pady=5)
        
        # 回到当前界面按钮
        back_button = tk.Button(
            button_frame,
            text="↩️ 返回当前界面",
            font=('Microsoft YaHei', 12),
            bg='#3498db',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            command=self.launch_current_gui
        )
        back_button.pack(fill='x', pady=5)
        
        # 退出按钮
        exit_button = tk.Button(
            button_frame,
            text="❌ 退出选择器",
            font=('Microsoft YaHei', 12),
            bg='#e74c3c',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            command=self.root.quit
        )
        exit_button.pack(fill='x', pady=5)
        
        # 底部说明
        info_frame = tk.Frame(right_frame, bg='#f8f9fa', relief='sunken', bd=1)
        info_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        info_label = tk.Label(
            info_frame,
            text="💡 使用说明:\n\n1. 点击左侧界面卡片查看详情\n2. 选择您喜欢的界面风格\n3. 点击启动按钮体验界面\n4. 可以随时切换不同界面\n\n每个界面都有不同的特色和功能，\n选择最适合您使用习惯的界面。",
            font=('Microsoft YaHei', 10),
            bg='#f8f9fa',
            fg='#6c757d',
            wraplength=360,
            justify='left'
        )
        info_label.pack(padx=10, pady=10)
        
        self.selected_gui = None
        
    def create_gui_card(self, parent, filename, config, index):
        """创建GUI选项卡片"""
        # 卡片框架
        card_frame = tk.Frame(parent, bg='white', relief='raised', bd=2)
        card_frame.pack(fill='x', padx=10, pady=5)
        
        # 卡片内容框架
        content_frame = tk.Frame(card_frame, bg=config['color'])
        content_frame.pack(fill='x', padx=2, pady=2)
        
        # 标题和类型
        title_frame = tk.Frame(content_frame, bg=config['color'])
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(
            title_frame,
            text=config['name'],
            font=('Microsoft YaHei', 12, 'bold'),
            bg=config['color'],
            fg='white'
        )
        title_label.pack(side='left')
        
        type_label = tk.Label(
            title_frame,
            text=config['type'],
            font=('Microsoft YaHei', 9),
            bg='white',
            fg=config['color'],
            relief='raised',
            bd=1,
            padx=5
        )
        type_label.pack(side='right')
        
        # 复杂度
        complexity_label = tk.Label(
            content_frame,
            text=f"复杂度: {config['complexity']}",
            font=('Microsoft YaHei', 9),
            bg=config['color'],
            fg='white'
        )
        complexity_label.pack(anchor='w', padx=10)
        
        # 描述
        desc_label = tk.Label(
            content_frame,
            text=config['description'],
            font=('Microsoft YaHei', 10),
            bg=config['color'],
            fg='white',
            wraplength=500,
            justify='left'
        )
        desc_label.pack(anchor='w', padx=10, pady=5)
        
        # 功能列表
        features_text = "• " + " • ".join(config['features'])
        features_label = tk.Label(
            content_frame,
            text=features_text,
            font=('Microsoft YaHei', 9),
            bg=config['color'],
            fg='#ecf0f1',
            wraplength=500,
            justify='left'
        )
        features_label.pack(anchor='w', padx=10, pady=(0, 10))
        
        # 点击事件
        def on_click(event, fname=filename, conf=config):
            self.select_gui(fname, conf)
        
        # 为所有组件绑定点击事件
        for widget in [card_frame, content_frame, title_frame, title_label, type_label, 
                      complexity_label, desc_label, features_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e, frame=content_frame: frame.configure(relief='raised', bd=3))
            widget.bind("<Leave>", lambda e, frame=content_frame: frame.configure(relief='raised', bd=2))
    
    def select_gui(self, filename, config):
        """选择GUI界面"""
        self.selected_gui = filename
        
        # 更新预览区域
        for widget in self.current_selection_frame.winfo_children():
            widget.destroy()
            
        # 选中界面的详细信息
        detail_frame = tk.Frame(self.current_selection_frame, bg='#ecf0f1')
        detail_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 界面名称
        name_label = tk.Label(
            detail_frame,
            text=config['name'],
            font=('Microsoft YaHei', 14, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        name_label.pack(pady=5)
        
        # 类型和复杂度
        info_frame = tk.Frame(detail_frame, bg='#ecf0f1')
        info_frame.pack(fill='x', pady=5)
        
        type_label = tk.Label(
            info_frame,
            text=f"类型: {config['type']}",
            font=('Microsoft YaHei', 10),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        type_label.pack(side='left')
        
        complexity_label = tk.Label(
            info_frame,
            text=config['complexity'],
            font=('Microsoft YaHei', 10),
            bg='#ecf0f1',
            fg='#e74c3c'
        )
        complexity_label.pack(side='right')
        
        # 描述
        desc_label = tk.Label(
            detail_frame,
            text=config['description'],
            font=('Microsoft YaHei', 11),
            bg='#ecf0f1',
            fg='#34495e',
            wraplength=350,
            justify='center'
        )
        desc_label.pack(pady=10)
        
        # 功能特色
        features_label = tk.Label(
            detail_frame,
            text="✨ 主要特色:",
            font=('Microsoft YaHei', 10, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        features_label.pack(pady=(10, 5))
        
        for feature in config['features']:
            feature_label = tk.Label(
                detail_frame,
                text=f"• {feature}",
                font=('Microsoft YaHei', 9),
                bg='#ecf0f1',
                fg='#7f8c8d'
            )
            feature_label.pack()
        
        # 启用启动按钮
        self.launch_button.configure(state='normal', bg='#27ae60')
        
        print(f"已选择: {config['name']}")
    
    def launch_selected_gui(self):
        """启动选中的GUI界面"""
        if not self.selected_gui:
            messagebox.showwarning("警告", "请先选择一个GUI界面!")
            return
        
        gui_path = Path(__file__).parent / "core" / self.selected_gui
        
        if not gui_path.exists():
            messagebox.showerror("错误", f"GUI文件不存在: {gui_path}")
            return
        
        try:
            # 关闭选择器
            self.root.withdraw()
            
            # 启动选中的GUI
            subprocess.Popen([sys.executable, str(gui_path)])
            
            # 显示成功消息
            config = self.gui_options[self.selected_gui]
            messagebox.showinfo("启动成功", f"正在启动 {config['name']}...")
            
            # 延迟关闭选择器
            self.root.after(2000, self.root.quit)
            
        except Exception as e:
            self.root.deiconify()  # 恢复窗口
            messagebox.showerror("启动错误", f"无法启动GUI界面:\n{str(e)}")
    
    def launch_current_gui(self):
        """启动当前的终极界面"""
        current_gui_path = Path(__file__).parent / "launch_ultimate_optimized_system.py"
        
        try:
            # 关闭选择器
            self.root.withdraw()
            
            # 启动当前GUI
            subprocess.Popen([sys.executable, str(current_gui_path)])
            
            messagebox.showinfo("启动成功", "正在返回当前终极交易界面...")
            
            # 延迟关闭选择器
            self.root.after(2000, self.root.quit)
            
        except Exception as e:
            self.root.deiconify()  # 恢复窗口
            messagebox.showerror("启动错误", f"无法启动当前界面:\n{str(e)}")
    
    def run(self):
        """运行选择器"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎨 启动GUI界面选择器...")
    app = GUISelector()
    app.run()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
K线图表组件
Candlestick Chart Component

专业的K线图表显示组件，支持技术指标叠加
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CandlestickChart:
    """K线图表组件"""
    
    def __init__(self, parent, width: int = 800, height: int = 600):
        """
        初始化K线图表
        
        Args:
            parent: 父组件
            width: 图表宽度
            height: 图表高度
        """
        self.parent = parent
        self.width = width
        self.height = height
        
        # 数据存储
        self.market_data: Optional[pd.DataFrame] = None
        self.indicators: Dict[str, pd.Series] = {}
        self.signals: List[Dict] = []
        
        # 图表配置
        self.show_volume = True
        self.show_indicators = True
        self.show_signals = True
        
        # 创建界面
        self._create_widgets()
        
        logger.info("K线图表组件初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 工具栏框架
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill="x", pady=(0, 5))
        
        # 创建工具栏
        self._create_toolbar()
        
        # 图表框架
        self.chart_frame = ttk.Frame(self.main_frame)
        self.chart_frame.pack(fill="both", expand=True)
        
        # 创建matplotlib图表
        self._create_chart()
    
    def _create_toolbar(self):
        """创建工具栏"""
        # 时间周期选择
        ttk.Label(self.toolbar_frame, text="时间周期:").pack(side="left", padx=(0, 5))
        
        self.timeframe_var = tk.StringVar(value="1h")
        timeframe_combo = ttk.Combobox(
            self.toolbar_frame,
            textvariable=self.timeframe_var,
            values=["1m", "5m", "15m", "1h", "4h", "1d"],
            width=8,
            state="readonly"
        )
        timeframe_combo.pack(side="left", padx=(0, 10))
        timeframe_combo.bind("<<ComboboxSelected>>", self._on_timeframe_change)
        
        # 指标选择
        ttk.Label(self.toolbar_frame, text="技术指标:").pack(side="left", padx=(0, 5))
        
        self.ma_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="MA",
            variable=self.ma_var,
            command=self._on_indicator_change
        ).pack(side="left", padx=(0, 5))
        
        self.bollinger_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="布林带",
            variable=self.bollinger_var,
            command=self._on_indicator_change
        ).pack(side="left", padx=(0, 5))
        
        self.rsi_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="RSI",
            variable=self.rsi_var,
            command=self._on_indicator_change
        ).pack(side="left", padx=(0, 5))
        
        self.macd_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="MACD",
            variable=self.macd_var,
            command=self._on_indicator_change
        ).pack(side="left", padx=(0, 10))
        
        # 显示选项
        self.volume_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="成交量",
            variable=self.volume_var,
            command=self._on_display_change
        ).pack(side="left", padx=(0, 5))
        
        self.signals_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            self.toolbar_frame,
            text="交易信号",
            variable=self.signals_var,
            command=self._on_display_change
        ).pack(side="left", padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(
            self.toolbar_frame,
            text="刷新",
            command=self.refresh_chart
        ).pack(side="right", padx=(10, 0))
    
    def _create_chart(self):
        """创建matplotlib图表"""
        # 创建图形
        self.fig = Figure(figsize=(self.width/100, self.height/100), dpi=100)
        self.fig.patch.set_facecolor('#2d2d2d')
        
        # 创建子图
        if self.show_volume:
            # 主图 + 成交量图
            self.ax_main = self.fig.add_subplot(3, 1, (1, 2))
            self.ax_volume = self.fig.add_subplot(3, 1, 3, sharex=self.ax_main)
        else:
            # 只有主图
            self.ax_main = self.fig.add_subplot(1, 1, 1)
            self.ax_volume = None
        
        # 设置样式
        self._setup_chart_style()
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, self.chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 创建导航工具栏
        self.nav_toolbar = NavigationToolbar2Tk(self.canvas, self.chart_frame)
        self.nav_toolbar.update()
    
    def _setup_chart_style(self):
        """设置图表样式"""
        # 主图样式
        self.ax_main.set_facecolor('#1e1e1e')
        self.ax_main.tick_params(colors='white')
        self.ax_main.spines['bottom'].set_color('white')
        self.ax_main.spines['top'].set_color('white')
        self.ax_main.spines['right'].set_color('white')
        self.ax_main.spines['left'].set_color('white')
        
        # 成交量图样式
        if self.ax_volume:
            self.ax_volume.set_facecolor('#1e1e1e')
            self.ax_volume.tick_params(colors='white')
            self.ax_volume.spines['bottom'].set_color('white')
            self.ax_volume.spines['top'].set_color('white')
            self.ax_volume.spines['right'].set_color('white')
            self.ax_volume.spines['left'].set_color('white')
    
    def update_data(self, market_data: pd.DataFrame, indicators: Optional[Dict[str, pd.Series]] = None, signals: Optional[List[Dict]] = None):
        """
        更新图表数据
        
        Args:
            market_data: 市场数据 (OHLCV格式)
            indicators: 技术指标数据
            signals: 交易信号数据
        """
        try:
            self.market_data = market_data.copy()
            
            if indicators:
                self.indicators = indicators.copy()
            
            if signals:
                self.signals = signals.copy()
            
            # 重绘图表
            self._plot_chart()
            
        except Exception as e:
            logger.error(f"更新图表数据失败: {str(e)}")
    
    def _plot_chart(self):
        """绘制图表"""
        try:
            if self.market_data is None or len(self.market_data) == 0:
                return
            
            # 清除现有图表
            self.ax_main.clear()
            if self.ax_volume:
                self.ax_volume.clear()
            
            # 重新设置样式
            self._setup_chart_style()
            
            # 绘制K线
            self._plot_candlesticks()
            
            # 绘制技术指标
            if self.show_indicators:
                self._plot_indicators()
            
            # 绘制交易信号
            if self.show_signals:
                self._plot_signals()
            
            # 绘制成交量
            if self.show_volume and self.ax_volume:
                self._plot_volume()
            
            # 设置标题和标签
            self._set_labels()
            
            # 格式化x轴
            self._format_xaxis()
            
            # 调整布局
            self.fig.tight_layout()
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"绘制图表失败: {str(e)}")
    
    def _plot_candlesticks(self):
        """绘制K线"""
        try:
            data = self.market_data
            
            # 计算颜色
            colors = ['#00ff00' if close >= open_price else '#ff0000' 
                     for open_price, close in zip(data['open'], data['close'])]
            
            # 绘制K线实体
            for i, (idx, row) in enumerate(data.iterrows()):
                # 实体
                body_height = abs(row['close'] - row['open'])
                body_bottom = min(row['open'], row['close'])
                
                self.ax_main.bar(
                    i, body_height, bottom=body_bottom,
                    color=colors[i], alpha=0.8, width=0.6
                )
                
                # 上下影线
                self.ax_main.plot(
                    [i, i], [row['low'], row['high']],
                    color=colors[i], linewidth=1
                )
            
            # 设置x轴刻度
            self.ax_main.set_xlim(-0.5, len(data) - 0.5)
            
        except Exception as e:
            logger.error(f"绘制K线失败: {str(e)}")
    
    def _plot_indicators(self):
        """绘制技术指标"""
        try:
            data_len = len(self.market_data)
            x_axis = range(data_len)
            
            # 移动平均线
            if self.ma_var.get() and 'ma20' in self.indicators:
                self.ax_main.plot(
                    x_axis, self.indicators['ma20'][-data_len:],
                    color='#ffff00', linewidth=1, label='MA20'
                )
            
            if self.ma_var.get() and 'ma50' in self.indicators:
                self.ax_main.plot(
                    x_axis, self.indicators['ma50'][-data_len:],
                    color='#ff00ff', linewidth=1, label='MA50'
                )
            
            # 布林带
            if self.bollinger_var.get():
                if 'bb_upper' in self.indicators:
                    self.ax_main.plot(
                        x_axis, self.indicators['bb_upper'][-data_len:],
                        color='#00ffff', linewidth=1, alpha=0.7, label='BB上轨'
                    )
                if 'bb_lower' in self.indicators:
                    self.ax_main.plot(
                        x_axis, self.indicators['bb_lower'][-data_len:],
                        color='#00ffff', linewidth=1, alpha=0.7, label='BB下轨'
                    )
                if 'bb_middle' in self.indicators:
                    self.ax_main.plot(
                        x_axis, self.indicators['bb_middle'][-data_len:],
                        color='#00ffff', linewidth=1, alpha=0.5, linestyle='--'
                    )
            
            # 添加图例
            if self.ma_var.get() or self.bollinger_var.get():
                self.ax_main.legend(loc='upper left', fontsize=8)
            
        except Exception as e:
            logger.error(f"绘制技术指标失败: {str(e)}")
    
    def _plot_signals(self):
        """绘制交易信号"""
        try:
            if not self.signals:
                return
            
            data_len = len(self.market_data)
            
            for signal in self.signals:
                # 找到信号对应的位置
                signal_time = signal.get('timestamp')
                if not signal_time:
                    continue
                
                # 简化：使用信号在数据中的相对位置
                signal_index = signal.get('index', data_len - 1)
                if signal_index >= data_len:
                    continue
                
                signal_type = signal.get('action', '').lower()
                price = signal.get('price', 0)
                
                if signal_type in ['buy', 'strong_buy']:
                    # 买入信号 - 绿色向上箭头
                    self.ax_main.annotate(
                        '▲', xy=(signal_index, price),
                        xytext=(signal_index, price * 0.98),
                        color='#00ff00', fontsize=12, ha='center',
                        arrowprops=dict(arrowstyle='->', color='#00ff00')
                    )
                elif signal_type in ['sell', 'strong_sell']:
                    # 卖出信号 - 红色向下箭头
                    self.ax_main.annotate(
                        '▼', xy=(signal_index, price),
                        xytext=(signal_index, price * 1.02),
                        color='#ff0000', fontsize=12, ha='center',
                        arrowprops=dict(arrowstyle='->', color='#ff0000')
                    )
            
        except Exception as e:
            logger.error(f"绘制交易信号失败: {str(e)}")
    
    def _plot_volume(self):
        """绘制成交量"""
        try:
            if not self.ax_volume:
                return
            
            data = self.market_data
            data_len = len(data)
            x_axis = range(data_len)
            
            # 成交量柱状图
            colors = ['#00ff00' if close >= open_price else '#ff0000' 
                     for open_price, close in zip(data['open'], data['close'])]
            
            self.ax_volume.bar(
                x_axis, data['volume'],
                color=colors, alpha=0.6, width=0.8
            )
            
            self.ax_volume.set_ylabel('成交量', color='white')
            self.ax_volume.set_xlim(-0.5, data_len - 0.5)
            
        except Exception as e:
            logger.error(f"绘制成交量失败: {str(e)}")
    
    def _set_labels(self):
        """设置标签"""
        self.ax_main.set_ylabel('价格 (USDT)', color='white')
        self.ax_main.set_title('K线图表', color='white', fontsize=14)
        
        if self.ax_volume:
            self.ax_volume.set_xlabel('时间', color='white')
    
    def _format_xaxis(self):
        """格式化x轴"""
        try:
            if len(self.market_data) == 0:
                return
            
            # 设置x轴刻度
            data_len = len(self.market_data)
            tick_interval = max(1, data_len // 10)  # 显示大约10个刻度
            
            tick_positions = range(0, data_len, tick_interval)
            tick_labels = []
            
            for pos in tick_positions:
                if pos < len(self.market_data):
                    timestamp = self.market_data.index[pos]
                    if isinstance(timestamp, str):
                        tick_labels.append(timestamp[:10])  # 只显示日期部分
                    else:
                        tick_labels.append(timestamp.strftime('%m-%d %H:%M'))
                else:
                    tick_labels.append('')
            
            self.ax_main.set_xticks(tick_positions)
            self.ax_main.set_xticklabels(tick_labels, rotation=45, ha='right')
            
            if self.ax_volume:
                self.ax_volume.set_xticks(tick_positions)
                self.ax_volume.set_xticklabels(tick_labels, rotation=45, ha='right')
            
        except Exception as e:
            logger.error(f"格式化x轴失败: {str(e)}")
    
    def _on_timeframe_change(self, event=None):
        """时间周期变化回调"""
        # 这里可以添加重新获取数据的逻辑
        logger.info(f"时间周期变更为: {self.timeframe_var.get()}")
    
    def _on_indicator_change(self):
        """指标选择变化回调"""
        self._plot_chart()
    
    def _on_display_change(self):
        """显示选项变化回调"""
        self.show_volume = self.volume_var.get()
        self.show_signals = self.signals_var.get()
        
        # 重新创建图表布局
        self._recreate_chart()
    
    def _recreate_chart(self):
        """重新创建图表"""
        # 清除现有图表
        self.fig.clear()
        
        # 重新创建子图
        if self.show_volume:
            self.ax_main = self.fig.add_subplot(3, 1, (1, 2))
            self.ax_volume = self.fig.add_subplot(3, 1, 3, sharex=self.ax_main)
        else:
            self.ax_main = self.fig.add_subplot(1, 1, 1)
            self.ax_volume = None
        
        # 重新绘制
        self._plot_chart()
    
    def refresh_chart(self):
        """刷新图表"""
        if self.market_data is not None:
            self._plot_chart()
        logger.info("图表已刷新")
    
    def export_chart(self, file_path: str):
        """导出图表"""
        try:
            self.fig.savefig(file_path, dpi=300, bbox_inches='tight', 
                           facecolor='#2d2d2d', edgecolor='none')
            logger.info(f"图表已导出: {file_path}")
        except Exception as e:
            logger.error(f"导出图表失败: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    print("K线图表组件模块加载完成")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("K线图表测试")
    root.geometry("1000x700")
    root.configure(bg='#2d2d2d')
    
    # 创建图表组件
    chart = CandlestickChart(root)
    
    # 创建测试数据
    import random
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    test_data = pd.DataFrame({
        'open': [50000 + random.uniform(-1000, 1000) for _ in range(100)],
        'high': [50500 + random.uniform(-1000, 1000) for _ in range(100)],
        'low': [49500 + random.uniform(-1000, 1000) for _ in range(100)],
        'close': [50000 + random.uniform(-1000, 1000) for _ in range(100)],
        'volume': [random.uniform(10, 100) for _ in range(100)]
    }, index=dates)
    
    # 更新图表数据
    chart.update_data(test_data)
    
    print("K线图表测试窗口已启动")
    root.mainloop()

{"data_mtime": 1748490882, "dep_lines": [1, 6, 8, 9, 10, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "numpy.typing", "matplotlib.artist", "matplotlib.axes", "matplotlib.backend_bases", "matplotlib.colors", "matplotlib.colorbar", "matplotlib.colorizer", "matplotlib.cm", "matplotlib.gridspec", "matplotlib.image", "matplotlib.layout_engine", "matplotlib.legend", "matplotlib.lines", "matplotlib.patches", "matplotlib.text", "matplotlib.transforms", "matplotlib.typing", "os", "typing", "numpy", "builtins", "re", "pprint", "copy", "inspect", "warnings", "operator", "html", "sys", "collections", "string", "itertools", "contextlib", "types", "traceback", "_frozen_importlib", "abc", "enum", "matplotlib.axes._axes", "matplotlib.axes._base", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence"], "hash": "2b1ca691695c280ab73062f80b677c8f27d0d566", "id": "matplotlib.figure", "ignore_all": true, "interface_hash": "850b7f0d53a5327e2d78915449b83d81ea48ee35", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\figure.pyi", "plugin_data": null, "size": 14738, "suppressed": [], "version_id": "1.15.0"}
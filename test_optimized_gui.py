#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化后的GUI测试脚本
Optimized GUI Test Script

测试所有优化功能和新增模块
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_config_manager():
    """测试配置管理器"""
    print("\n🔧 测试配置管理器...")
    
    try:
        from core.config import ConfigManager, get_config_manager, get_config, set_config
        
        # 测试配置管理器
        config = ConfigManager()
        
        # 测试获取配置
        initial_capital = config.get('trading.initial_capital')
        print(f"✅ 获取配置: 初始资金 = {initial_capital}")
        
        # 测试设置配置
        config.set('trading.initial_capital', 20000)
        new_capital = config.get('trading.initial_capital')
        print(f"✅ 设置配置: 新初始资金 = {new_capital}")
        
        # 测试配置验证
        errors = config.validate_config()
        if not errors:
            print("✅ 配置验证通过")
        else:
            print(f"⚠️ 配置验证警告: {errors}")
        
        # 测试全局配置管理器
        global_config = get_config_manager()
        test_value = get_config('ui.update_interval', 5)
        print(f"✅ 全局配置: 更新间隔 = {test_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False


def test_theme_manager():
    """测试主题管理器"""
    print("\n🎨 测试主题管理器...")
    
    try:
        from core.ui import ThemeManager, get_theme_manager, get_color, get_font
        
        # 测试主题管理器
        theme = ThemeManager()
        
        # 测试获取颜色
        bg_color = theme.get_color('bg_primary')
        success_color = theme.get_color('success')
        print(f"✅ 获取颜色: 背景色 = {bg_color}, 成功色 = {success_color}")
        
        # 测试获取字体
        default_font = theme.get_font('default')
        heading_font = theme.get_font('heading')
        print(f"✅ 获取字体: 默认字体 = {default_font}, 标题字体 = {heading_font}")
        
        # 测试切换主题
        theme.set_theme('light')
        light_bg = theme.get_color('bg_primary')
        print(f"✅ 切换主题: 浅色背景 = {light_bg}")
        
        # 测试可用主题
        themes = theme.get_available_themes()
        print(f"✅ 可用主题: {list(themes.keys())}")
        
        # 测试全局主题管理器
        global_theme = get_theme_manager()
        global_color = get_color('success')
        global_font = get_font('default')
        print(f"✅ 全局主题: 成功色 = {global_color}, 字体 = {global_font}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主题管理器测试失败: {e}")
        return False


def test_language_manager():
    """测试语言管理器"""
    print("\n🌍 测试语言管理器...")
    
    try:
        from core.i18n import LanguageManager, get_language_manager, _, set_language
        
        # 测试语言管理器
        lang = LanguageManager()
        
        # 测试中文翻译
        zh_title = lang.get_text('app_title')
        zh_trading = lang.get_text('trading')
        print(f"✅ 中文翻译: 标题 = {zh_title}, 交易 = {zh_trading}")
        
        # 测试英文翻译
        lang.set_language('en_US')
        en_title = lang.get_text('app_title')
        en_trading = lang.get_text('trading')
        print(f"✅ 英文翻译: 标题 = {en_title}, 交易 = {en_trading}")
        
        # 测试支持的语言
        languages = lang.get_supported_languages()
        print(f"✅ 支持语言: {list(languages.keys())}")
        
        # 测试全局语言管理器
        global_lang = get_language_manager()
        set_language('zh_CN')
        global_text = _('success')
        print(f"✅ 全局翻译: 成功 = {global_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 语言管理器测试失败: {e}")
        return False


def test_gui_optimizations():
    """测试GUI优化功能"""
    print("\n🖥️ 测试GUI优化功能...")
    
    try:
        from core.ultimate_spot_trading_gui import UltimateSpotTradingGUI
        
        # 创建GUI实例
        gui = UltimateSpotTradingGUI()
        
        # 测试新增的方法
        test_results = {}
        
        # 测试性能显示方法
        try:
            gui.update_performance_display()
            test_results['update_performance_display'] = "✅ 通过"
        except Exception as e:
            test_results['update_performance_display'] = f"❌ 失败: {e}"
        
        # 测试组件检测方法
        try:
            has_notebook = gui.has_component('notebook')
            has_log_text = gui.has_component('log_text')
            test_results['has_component'] = f"✅ 通过 (notebook: {has_notebook}, log_text: {has_log_text})"
        except Exception as e:
            test_results['has_component'] = f"❌ 失败: {e}"
        
        # 测试市场数据更新
        try:
            gui.update_market_data()
            test_results['update_market_data'] = "✅ 通过"
        except Exception as e:
            test_results['update_market_data'] = f"❌ 失败: {e}"
        
        # 输出测试结果
        for method, result in test_results.items():
            print(f"  {method}: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI优化功能测试失败: {e}")
        return False


def test_performance_improvements():
    """测试性能改进"""
    print("\n⚡ 测试性能改进...")
    
    try:
        import psutil
        import time
        
        # 测试启动时间
        start_time = time.time()
        from core.ultimate_spot_trading_gui import UltimateSpotTradingGUI
        gui = UltimateSpotTradingGUI()
        end_time = time.time()
        
        startup_time = end_time - start_time
        print(f"✅ GUI启动时间: {startup_time:.3f} 秒")
        
        # 测试内存使用
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        print(f"✅ 内存使用: {memory_mb:.1f} MB")
        
        # 测试CPU使用
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"✅ CPU使用率: {cpu_percent:.1f}%")
        
        # 性能评级
        if startup_time < 2.0 and memory_mb < 200 and cpu_percent < 30:
            performance_grade = "A+"
        elif startup_time < 3.0 and memory_mb < 300 and cpu_percent < 50:
            performance_grade = "A"
        elif startup_time < 5.0 and memory_mb < 500 and cpu_percent < 70:
            performance_grade = "B"
        else:
            performance_grade = "C"
        
        print(f"✅ 性能评级: {performance_grade}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


def test_code_quality():
    """测试代码质量改进"""
    print("\n📊 测试代码质量改进...")
    
    try:
        # 测试模块导入
        modules_to_test = [
            'core.config.config_manager',
            'core.ui.theme_manager', 
            'core.i18n.language_manager',
            'core.ultimate_spot_trading_gui'
        ]
        
        import_results = {}
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                import_results[module_name] = "✅ 成功"
            except ImportError as e:
                import_results[module_name] = f"❌ 失败: {e}"
        
        # 输出导入结果
        for module, result in import_results.items():
            print(f"  {module}: {result}")
        
        # 计算成功率
        success_count = sum(1 for result in import_results.values() if "✅" in result)
        total_count = len(import_results)
        success_rate = (success_count / total_count) * 100
        
        print(f"✅ 模块导入成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ 代码质量测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 终极版现货交易系统 - 优化功能测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 执行所有测试
    test_results = {}
    
    # 1. 配置管理器测试
    test_results['配置管理器'] = test_config_manager()
    
    # 2. 主题管理器测试
    test_results['主题管理器'] = test_theme_manager()
    
    # 3. 语言管理器测试
    test_results['语言管理器'] = test_language_manager()
    
    # 4. GUI优化功能测试
    test_results['GUI优化功能'] = test_gui_optimizations()
    
    # 5. 性能改进测试
    test_results['性能改进'] = test_performance_improvements()
    
    # 6. 代码质量测试
    test_results['代码质量'] = test_code_quality()
    
    # 输出测试总结
    print("\n" + "=" * 70)
    print("📊 优化功能测试结果汇总")
    print("=" * 70)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed_count += 1
    
    print("\n" + "=" * 70)
    print(f"📈 测试统计: {passed_count}/{total_count} 项通过")
    print(f"🎯 通过率: {(passed_count/total_count)*100:.1f}%")
    
    if passed_count == total_count:
        print("🎉 所有优化功能测试通过！")
        grade = "A+"
    elif passed_count >= total_count * 0.8:
        print("✅ 大部分优化功能正常！")
        grade = "A"
    elif passed_count >= total_count * 0.6:
        print("⚠️ 部分优化功能需要改进！")
        grade = "B"
    else:
        print("❌ 优化功能存在较多问题！")
        grade = "C"
    
    print(f"🏆 优化质量评级: {grade}")
    
    print("\n💡 优化成果:")
    print("• 配置管理系统 - 统一参数管理")
    print("• 主题管理系统 - 多主题支持")
    print("• 国际化系统 - 多语言支持")
    print("• 代码质量改进 - 更好的结构和规范")
    print("• 性能优化 - 更快的启动和响应")
    print("• 错误处理增强 - 更稳定的系统")
    
    print(f"\n🎊 优化测试完成！系统质量等级: {grade}")


if __name__ == "__main__":
    main()

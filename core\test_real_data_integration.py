#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实数据集成
Test Real Data Integration with Spot Trading System
"""

import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.real_market_data import RealMarketDataProvider, MarketDataAdapter


def test_real_data_provider():
    """测试真实数据提供者"""
    print("🔍 测试真实数据提供者...")
    
    try:
        # 创建数据提供者
        provider = RealMarketDataProvider(update_interval=5)
        
        # 数据更新计数器
        update_count = 0
        
        def data_callback(market_data):
            nonlocal update_count
            update_count += 1
            print(f"📊 数据更新 #{update_count}: 收到 {len(market_data)} 个交易对数据")
            
            # 显示部分数据
            for i, (pair, data) in enumerate(market_data.items()):
                if i < 3:  # 只显示前3个
                    print(f"   {pair}: {data['price']:.2f} ({data['change']:+.2f}%)")
        
        # 添加回调
        provider.add_update_callback(data_callback)
        
        # 启动数据提供者
        provider.start()
        
        # 等待数据更新
        print("⏳ 等待数据更新 (30秒)...")
        start_time = time.time()
        while time.time() - start_time < 30:
            time.sleep(1)
            
            # 测试数据获取
            if update_count > 0:
                btc_price = provider.get_price("BTC")
                eth_change = provider.get_change_24h("ETH")
                sol_volume = provider.get_volume_24h("SOL")
                
                print(f"📈 实时数据: BTC={btc_price:.2f}, ETH变化={eth_change:+.2f}%, SOL成交量={sol_volume:.0f}")
                break
        
        # 停止数据提供者
        provider.stop()
        
        if update_count > 0:
            print("✅ 真实数据提供者测试成功!")
            return True
        else:
            print("❌ 未收到数据更新")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据提供者测试失败: {e}")
        return False


def test_market_data_adapter():
    """测试市场数据适配器"""
    print("\n🔍 测试市场数据适配器...")
    
    try:
        # 创建适配器
        adapter = MarketDataAdapter()
        
        # 启动适配器
        adapter.start()
        
        # 等待数据初始化
        print("⏳ 等待适配器初始化 (10秒)...")
        time.sleep(10)
        
        # 测试适配器功能
        spot_pairs = adapter.get_spot_pairs()
        print(f"📊 现货交易对数据: {len(spot_pairs)} 个")
        
        # 显示部分数据
        for i, (pair, data) in enumerate(spot_pairs.items()):
            if i < 5:  # 显示前5个
                print(f"   {pair}: {data['price']:.2f} USDT ({data['change']:+.2f}%)")
        
        # 测试价格获取
        btc_price = adapter.get_price("BTC")
        market_status = adapter.get_market_status()
        is_real = adapter.is_real_data()
        
        print(f"📈 BTC价格: {btc_price:.2f} USDT")
        print(f"🌐 市场状态: {market_status}")
        print(f"🔗 真实数据: {'是' if is_real else '否'}")
        
        # 停止适配器
        adapter.stop()
        
        if btc_price > 0:
            print("✅ 市场数据适配器测试成功!")
            return True
        else:
            print("❌ 未获取到有效价格数据")
            return False
            
    except Exception as e:
        print(f"❌ 市场数据适配器测试失败: {e}")
        return False


def test_data_quality():
    """测试数据质量"""
    print("\n🔍 测试数据质量...")
    
    try:
        adapter = MarketDataAdapter()
        adapter.start()
        
        # 等待数据稳定
        time.sleep(8)
        
        # 收集多次数据样本
        samples = []
        for i in range(5):
            spot_pairs = adapter.get_spot_pairs()
            samples.append(spot_pairs)
            time.sleep(2)
        
        adapter.stop()
        
        # 分析数据质量
        print("📊 数据质量分析:")
        
        # 检查数据完整性
        expected_pairs = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT']
        missing_pairs = []
        
        for pair in expected_pairs:
            if pair not in samples[0]:
                missing_pairs.append(pair)
        
        if missing_pairs:
            print(f"❌ 缺失交易对: {missing_pairs}")
            return False
        else:
            print("✅ 交易对完整性检查通过")
        
        # 检查价格变化
        price_changes = {}
        for pair in expected_pairs:
            first_price = samples[0][pair]['price']
            last_price = samples[-1][pair]['price']
            change = abs(last_price - first_price) / first_price * 100
            price_changes[pair] = change
        
        print("📈 价格变化分析:")
        for pair, change in price_changes.items():
            print(f"   {pair}: {change:.4f}% 变化")
        
        # 检查数据新鲜度
        fresh_data_count = 0
        for pair in expected_pairs:
            last_update = samples[-1][pair]['last_update']
            if time.time() - last_update < 60:  # 1分钟内
                fresh_data_count += 1
        
        freshness_ratio = fresh_data_count / len(expected_pairs)
        print(f"🕒 数据新鲜度: {freshness_ratio:.0%}")
        
        if freshness_ratio >= 0.8:
            print("✅ 数据质量测试通过!")
            return True
        else:
            print("⚠️ 数据新鲜度不足")
            return False
            
    except Exception as e:
        print(f"❌ 数据质量测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Gate.io实时数据集成测试开始")
    print("=" * 60)
    
    # 测试真实数据提供者
    provider_success = test_real_data_provider()
    
    # 测试市场数据适配器
    adapter_success = test_market_data_adapter()
    
    # 测试数据质量
    quality_success = test_data_quality()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   真实数据提供者: {'✅ 通过' if provider_success else '❌ 失败'}")
    print(f"   市场数据适配器: {'✅ 通过' if adapter_success else '❌ 失败'}")
    print(f"   数据质量检查: {'✅ 通过' if quality_success else '❌ 失败'}")
    
    overall_success = provider_success and adapter_success and quality_success
    
    if overall_success:
        print("\n🎊 所有测试通过! Gate.io实时数据集成成功!")
        print("💡 现在可以在现货版系统中使用真实市场数据")
        print("🔗 系统将自动从Gate.io获取实时价格和市场数据")
    else:
        print("\n⚠️ 部分测试失败，请检查网络连接和API配置")
        print("💡 系统将回退到模拟数据模式")
    
    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

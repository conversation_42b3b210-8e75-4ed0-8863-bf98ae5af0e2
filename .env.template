# 企业级现货交易系统 - 环境变量配置模板
# Enterprise Spot Trading System - Environment Variables Template
# 
# 使用说明：
# 1. 复制此文件为 .env
# 2. 替换所有 "YOUR_XXX_HERE" 为您的真实信息
# 3. 保存文件并启动系统
#
# Instructions:
# 1. Copy this file as .env
# 2. Replace all "YOUR_XXX_HERE" with your real information
# 3. Save the file and start the system

# ============================================================================
# 交易所API配置 / Exchange API Configuration
# ============================================================================

# Gate.io API密钥 / Gate.io API Keys
# 从 https://www.gate.io 的API管理页面获取
# Get from API Management page at https://www.gate.io
EXCHANGE_API_KEY=YOUR_GATE_IO_API_KEY_HERE
EXCHANGE_API_SECRET=YOUR_GATE_IO_API_SECRET_HERE

# 交易模式 / Trading Mode
# true = 沙盒模式（测试）/ Sandbox mode (testing)
# false = 实盘模式（真实资金）/ Live mode (real money)
EXCHANGE_SANDBOX=true

# ============================================================================
# 系统配置 / System Configuration
# ============================================================================

# 运行环境 / Runtime Environment
# development = 开发环境 / Development environment
# production = 生产环境 / Production environment
ENVIRONMENT=development

# 调试模式 / Debug Mode
# true = 启用调试 / Enable debugging
# false = 关闭调试 / Disable debugging
DEBUG=true

# 日志级别 / Log Level
# DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# ============================================================================
# 安全配置 / Security Configuration
# ============================================================================

# 主密码（用于保护本地数据）/ Master Password (for protecting local data)
# 请使用强密码 / Please use a strong password
TRADING_MASTER_PASSWORD=YOUR_STRONG_PASSWORD_HERE

# ============================================================================
# 交易配置 / Trading Configuration
# ============================================================================

# 初始资金 / Initial Capital
# 沙盒模式建议：10000 / Sandbox mode recommended: 10000
# 实盘模式建议：1000 / Live mode recommended: 1000
INITIAL_CAPITAL=10000

# 日亏损限制 / Daily Loss Limit
# 建议设为初始资金的3% / Recommended 3% of initial capital
DAILY_LOSS_LIMIT=300

# 最大仓位比例 / Maximum Position Size
# 0.1 = 10% / 0.1 = 10%
MAX_POSITION_SIZE=0.1

# ============================================================================
# 监控和告警配置 / Monitoring and Alert Configuration
# ============================================================================

# 邮箱告警 / Email Alerts
# 留空表示不使用邮箱告警 / Leave empty to disable email alerts
ALERT_EMAIL=<EMAIL>

# Webhook告警 / Webhook Alerts
# 留空表示不使用Webhook告警 / Leave empty to disable webhook alerts
ALERT_WEBHOOK=YOUR_WEBHOOK_URL_HERE

# ============================================================================
# 数据库配置 / Database Configuration
# ============================================================================

# 数据库密码（如果使用外部数据库）/ Database Password (if using external database)
# 默认使用SQLite，无需密码 / Default uses SQLite, no password needed
DATABASE_PASSWORD=

# ============================================================================
# 高级配置 / Advanced Configuration
# ============================================================================

# 网络超时（毫秒）/ Network Timeout (milliseconds)
NETWORK_TIMEOUT=30000

# API请求频率限制（毫秒）/ API Rate Limit (milliseconds)
RATE_LIMIT=1000

# 重试次数 / Retry Count
MAX_RETRIES=3

# ============================================================================
# 配置示例 / Configuration Examples
# ============================================================================

# 沙盒模式配置示例 / Sandbox Mode Example:
# EXCHANGE_API_KEY=********************************
# EXCHANGE_API_SECRET=1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
# EXCHANGE_SANDBOX=true
# ENVIRONMENT=development
# INITIAL_CAPITAL=10000
# DAILY_LOSS_LIMIT=300

# 实盘模式配置示例 / Live Mode Example:
# EXCHANGE_API_KEY=your_real_api_key_here
# EXCHANGE_API_SECRET=your_real_api_secret_here
# EXCHANGE_SANDBOX=false
# ENVIRONMENT=production
# INITIAL_CAPITAL=1000
# DAILY_LOSS_LIMIT=30

# ============================================================================
# 安全提醒 / Security Reminders
# ============================================================================

# 1. 不要与任何人分享此文件 / Never share this file with anyone
# 2. 定期更换API密钥 / Regularly rotate API keys
# 3. 使用强密码 / Use strong passwords
# 4. 备份重要数据 / Backup important data
# 5. 监控系统日志 / Monitor system logs

# ============================================================================
# 配置完成后的操作 / After Configuration
# ============================================================================

# 1. 保存此文件为 .env / Save this file as .env
# 2. 运行测试：python core/api_connection_tester.py
# 3. 启动系统：python start_trading.py
# 4. 在GUI中开始交易 / Start trading in GUI

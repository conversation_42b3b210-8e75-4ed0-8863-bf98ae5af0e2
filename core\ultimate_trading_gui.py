#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极版交易系统 GUI界面
Ultimate Trading System GUI

简洁高效的图形用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import threading
import time
from datetime import datetime
from live_trading_executor import LiveTradingExecutor
from simple_gate_connector import SimpleGateConnector

# 导入优化模块
try:
    from complete_optimization_system import FinalOptimizedSystem
    OPTIMIZATION_AVAILABLE = True
except ImportError:
    try:
        from simple_system_integration import SimpleOptimizedSystem
        FinalOptimizedSystem = SimpleOptimizedSystem
        OPTIMIZATION_AVAILABLE = True
    except ImportError:
        OPTIMIZATION_AVAILABLE = False
        print("警告: 优化模块未找到，部分功能将不可用")

class UltimateTradingGUI:
    """终极版交易系统GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 终极版交易系统 Ultimate Trading System v2.0 - 优化版")
        self.root.geometry("900x700")
        self.root.configure(bg='#1e1e1e')

        # 系统状态
        self.executor = None
        self.gate_connector = None
        self.is_trading = False
        self.config = {}

        # 数据更新线程
        self.data_update_thread = None
        self.update_running = False

        # 优化系统
        self.optimization_system = None
        if OPTIMIZATION_AVAILABLE:
            try:
                self.optimization_system = FinalOptimizedSystem(self)
                print("✅ 企业级优化系统已启用 - Phase 5-7 完成")
            except Exception as e:
                print(f"⚠️ 优化系统启动失败: {e}")

        # 创建界面
        self.create_widgets()
        self.load_config()

        # 集成优化功能
        if self.optimization_system:
            self.optimization_system.integrate_with_main_gui(self)

    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=5)

        title_label = tk.Label(
            title_frame,
            text="🚀 终极版交易系统",
            font=('Arial', 16, 'bold'),
            fg='#00ff00',
            bg='#1e1e1e'
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="Ultimate Trading System - 专业级加密货币量化交易",
            font=('Arial', 10),
            fg='#888888',
            bg='#1e1e1e'
        )
        subtitle_label.pack()

        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # 配置页面
        self.create_config_tab()

        # 交易页面
        self.create_trading_tab()

        # 监控页面
        self.create_monitor_tab()

        # Gate.io数据页面
        self.create_gate_data_tab()

        # 状态栏
        self.create_status_bar()

        # 启动Gate.io数据连接
        self.init_gate_connector()

    def create_config_tab(self):
        """创建配置页面"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙️ 配置")

        # API配置
        api_group = ttk.LabelFrame(config_frame, text="API配置")
        api_group.pack(fill='x', padx=10, pady=5)

        ttk.Label(api_group, text="API Key:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.api_key_entry = ttk.Entry(api_group, width=50, show='*')
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(api_group, text="Secret:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.secret_entry = ttk.Entry(api_group, width=50, show='*')
        self.secret_entry.grid(row=1, column=1, padx=5, pady=2)

        self.sandbox_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(api_group, text="沙盒模式 (测试)", variable=self.sandbox_var).grid(row=2, column=0, columnspan=2, sticky='w', padx=5, pady=2)

        # 交易配置
        trading_group = ttk.LabelFrame(config_frame, text="交易配置")
        trading_group.pack(fill='x', padx=10, pady=5)

        ttk.Label(trading_group, text="初始资金 (USDT):").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.capital_entry = ttk.Entry(trading_group, width=20)
        self.capital_entry.insert(0, "10000")
        self.capital_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(trading_group, text="日亏损限制 (USDT):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.daily_loss_entry = ttk.Entry(trading_group, width=20)
        self.daily_loss_entry.insert(0, "300")
        self.daily_loss_entry.grid(row=1, column=1, padx=5, pady=2)

        # 按钮
        button_frame = tk.Frame(config_frame)
        button_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(button_frame, text="💾 保存配置", command=self.save_config).pack(side='left', padx=5)
        ttk.Button(button_frame, text="🔄 加载配置", command=self.load_config).pack(side='left', padx=5)
        ttk.Button(button_frame, text="🧪 测试连接", command=self.test_connection).pack(side='left', padx=5)

    def create_trading_tab(self):
        """创建交易页面"""
        trading_frame = ttk.Frame(self.notebook)
        self.notebook.add(trading_frame, text="💰 交易")

        # 策略状态
        strategy_group = ttk.LabelFrame(trading_frame, text="策略状态")
        strategy_group.pack(fill='x', padx=10, pady=5)

        strategies = [
            ("CryptoBreakout", "突破策略", "40%", "67%"),
            ("CryptoGrid", "网格策略", "35%", "83%"),
            ("CryptoMomentum", "动量策略", "25%", "61%")
        ]

        for i, (name, desc, allocation, win_rate) in enumerate(strategies):
            frame = tk.Frame(strategy_group)
            frame.pack(fill='x', padx=5, pady=2)

            tk.Label(frame, text=f"🎯 {desc}", font=('Arial', 10, 'bold')).pack(side='left')
            tk.Label(frame, text=f"资金: {allocation} | 胜率: {win_rate}", fg='green').pack(side='right')

        # 交易控制
        control_group = ttk.LabelFrame(trading_frame, text="交易控制")
        control_group.pack(fill='x', padx=10, pady=5)

        control_frame = tk.Frame(control_group)
        control_frame.pack(fill='x', padx=5, pady=5)

        self.start_button = ttk.Button(control_frame, text="🚀 开始交易", command=self.start_trading)
        self.start_button.pack(side='left', padx=5)

        self.stop_button = ttk.Button(control_frame, text="⏹️ 停止交易", command=self.stop_trading, state='disabled')
        self.stop_button.pack(side='left', padx=5)

        ttk.Button(control_frame, text="📊 查看持仓", command=self.show_positions).pack(side='left', padx=5)

        # 交易状态
        self.trading_status = tk.Label(control_group, text="状态: 未连接", fg='red')
        self.trading_status.pack(pady=5)

    def create_monitor_tab(self):
        """创建监控页面"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="📊 监控")

        # 账户信息
        account_group = ttk.LabelFrame(monitor_frame, text="账户信息")
        account_group.pack(fill='x', padx=10, pady=5)

        self.balance_label = tk.Label(account_group, text="USDT余额: --", font=('Arial', 12, 'bold'))
        self.balance_label.pack(pady=2)

        self.pnl_label = tk.Label(account_group, text="当日盈亏: --", font=('Arial', 12))
        self.pnl_label.pack(pady=2)

        # 交易日志
        log_group = ttk.LabelFrame(monitor_frame, text="交易日志")
        log_group.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_group, height=15, bg='#2d2d2d', fg='#ffffff', font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 清除日志按钮
        ttk.Button(log_group, text="🗑️ 清除日志", command=self.clear_log).pack(pady=5)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Label(
            self.root,
            text="就绪 | 终极版交易系统 v1.0",
            relief='sunken',
            anchor='w',
            bg='#333333',
            fg='#ffffff'
        )
        self.status_bar.pack(side='bottom', fill='x')

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert('end', log_entry)
        self.log_text.see('end')

        # 限制日志长度
        lines = self.log_text.get('1.0', 'end').split('\n')
        if len(lines) > 1000:
            self.log_text.delete('1.0', '100.0')

    def clear_log(self):
        """清除日志"""
        self.log_text.delete('1.0', 'end')
        self.log_message("日志已清除")

    def save_config(self):
        """保存配置"""
        config = {
            'api_key': self.api_key_entry.get(),
            'secret': self.secret_entry.get(),
            'sandbox': self.sandbox_var.get(),
            'initial_capital': float(self.capital_entry.get() or 10000),
            'daily_loss_limit': float(self.daily_loss_entry.get() or 300)
        }

        try:
            with open('gui_config.json', 'w') as f:
                json.dump(config, f, indent=2)

            self.config = config
            self.log_message("✅ 配置已保存")
            messagebox.showinfo("成功", "配置已保存")

        except Exception as e:
            self.log_message(f"❌ 保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """加载配置"""
        try:
            with open('gui_config.json', 'r') as f:
                config = json.load(f)

            self.api_key_entry.delete(0, 'end')
            self.api_key_entry.insert(0, config.get('api_key', ''))

            self.secret_entry.delete(0, 'end')
            self.secret_entry.insert(0, config.get('secret', ''))

            self.sandbox_var.set(config.get('sandbox', True))

            self.capital_entry.delete(0, 'end')
            self.capital_entry.insert(0, str(config.get('initial_capital', 10000)))

            self.daily_loss_entry.delete(0, 'end')
            self.daily_loss_entry.insert(0, str(config.get('daily_loss_limit', 300)))

            self.config = config
            self.log_message("✅ 配置已加载")

        except FileNotFoundError:
            self.log_message("⚠️ 配置文件不存在，使用默认配置")
        except Exception as e:
            self.log_message(f"❌ 加载配置失败: {e}")

    def test_connection(self):
        """测试连接"""
        if not self.config.get('api_key') or not self.config.get('secret'):
            messagebox.showerror("错误", "请先配置API密钥")
            return

        self.log_message("🔄 测试连接中...")

        def test_thread():
            try:
                exchange_config = {
                    'api_key': self.config['api_key'],
                    'secret': self.config['secret'],
                    'sandbox': self.config['sandbox']
                }

                test_executor = LiveTradingExecutor(exchange_config)

                if test_executor.connect_exchange():
                    self.log_message("✅ 连接测试成功")
                    self.root.after(0, lambda: messagebox.showinfo("成功", "连接测试成功"))
                else:
                    self.log_message("❌ 连接测试失败")
                    self.root.after(0, lambda: messagebox.showerror("错误", "连接测试失败"))

            except Exception as e:
                self.log_message(f"❌ 连接测试异常: {e}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"连接测试异常: {e}"))

        threading.Thread(target=test_thread, daemon=True).start()

    def start_trading(self):
        """开始交易"""
        if not self.config.get('api_key') or not self.config.get('secret'):
            messagebox.showerror("错误", "请先配置API密钥")
            return

        if self.is_trading:
            messagebox.showwarning("警告", "交易已在进行中")
            return

        # 确认对话框
        if self.config.get('sandbox'):
            mode = "沙盒测试模式"
        else:
            mode = "实盘交易模式"

        if not messagebox.askyesno("确认", f"确定要开始{mode}吗？"):
            return

        self.log_message(f"🚀 开始{mode}")

        def trading_thread():
            try:
                exchange_config = {
                    'api_key': self.config['api_key'],
                    'secret': self.config['secret'],
                    'sandbox': self.config['sandbox']
                }

                self.executor = LiveTradingExecutor(exchange_config)

                if self.executor.connect_exchange():
                    self.executor.setup_portfolio()

                    self.is_trading = True
                    self.root.after(0, self.update_trading_ui)

                    # 开始交易循环
                    while self.is_trading:
                        self.executor.run_trading_cycle()

                        # 更新界面
                        self.root.after(0, self.update_account_info)

                        time.sleep(60)  # 等待1分钟

                else:
                    self.log_message("❌ 无法连接交易所")

            except Exception as e:
                self.log_message(f"❌ 交易异常: {e}")
                self.is_trading = False
                self.root.after(0, self.update_trading_ui)

        threading.Thread(target=trading_thread, daemon=True).start()

    def stop_trading(self):
        """停止交易"""
        if not self.is_trading:
            return

        self.is_trading = False
        if self.executor:
            self.executor.stop_trading()

        self.log_message("⏹️ 交易已停止")
        self.update_trading_ui()

    def update_trading_ui(self):
        """更新交易界面"""
        if self.is_trading:
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.trading_status.config(text="状态: 交易中", fg='green')
        else:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.trading_status.config(text="状态: 已停止", fg='red')

    def update_account_info(self):
        """更新账户信息"""
        if self.executor:
            try:
                status = self.executor.get_status()

                balance = status.get('balance', 0)
                daily_pnl = status.get('daily_pnl', 0)

                self.balance_label.config(text=f"USDT余额: {balance:,.2f}")

                pnl_color = 'green' if daily_pnl >= 0 else 'red'
                self.pnl_label.config(text=f"当日盈亏: {daily_pnl:+.2f} USDT", fg=pnl_color)

            except Exception as e:
                self.log_message(f"❌ 更新账户信息失败: {e}")

    def show_positions(self):
        """显示持仓"""
        if self.executor:
            try:
                status = self.executor.get_status()
                positions = status.get('positions', {})

                if positions:
                    pos_text = "当前持仓:\n"
                    for currency, info in positions.items():
                        pos_text += f"{currency}: {info['amount']:.6f}\n"
                else:
                    pos_text = "暂无持仓"

                messagebox.showinfo("持仓信息", pos_text)

            except Exception as e:
                messagebox.showerror("错误", f"获取持仓失败: {e}")
        else:
            messagebox.showinfo("提示", "请先连接交易所")

    def create_gate_data_tab(self):
        """创建Gate.io数据页面"""
        gate_frame = ttk.Frame(self.notebook)
        self.notebook.add(gate_frame, text="🌐 Gate.io数据")

        # 连接状态
        status_group = ttk.LabelFrame(gate_frame, text="连接状态")
        status_group.pack(fill='x', padx=10, pady=5)

        self.gate_status_label = tk.Label(status_group, text="状态: 未连接", fg='red')
        self.gate_status_label.pack(pady=5)

        ttk.Button(status_group, text="🔄 连接Gate.io", command=self.connect_gate).pack(side='left', padx=5)
        ttk.Button(status_group, text="📊 刷新数据", command=self.refresh_gate_data).pack(side='left', padx=5)

        # 实时价格
        price_group = ttk.LabelFrame(gate_frame, text="实时价格")
        price_group.pack(fill='x', padx=10, pady=5)

        # 创建价格显示框架
        self.price_frame = tk.Frame(price_group)
        self.price_frame.pack(fill='x', padx=5, pady=5)

        # 交易信号
        signal_group = ttk.LabelFrame(gate_frame, text="交易信号")
        signal_group.pack(fill='both', expand=True, padx=10, pady=5)

        self.signal_text = scrolledtext.ScrolledText(signal_group, height=10, bg='#2d2d2d', fg='#ffffff', font=('Consolas', 9))
        self.signal_text.pack(fill='both', expand=True, padx=5, pady=5)

    def init_gate_connector(self):
        """初始化Gate.io连接器"""
        try:
            self.gate_connector = SimpleGateConnector()
            self.log_message("🌐 Gate.io连接器已初始化")

            # 自动连接
            threading.Thread(target=self.connect_gate, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ Gate.io连接器初始化失败: {e}")

    def connect_gate(self):
        """连接Gate.io"""
        try:
            if self.gate_connector and self.gate_connector.connect():
                self.gate_status_label.config(text="状态: 已连接", fg='green')
                self.log_message("✅ Gate.io连接成功")

                # 启动数据更新
                self.start_data_updates()
            else:
                self.gate_status_label.config(text="状态: 连接失败", fg='red')
                self.log_message("❌ Gate.io连接失败")

        except Exception as e:
            self.log_message(f"❌ Gate.io连接异常: {e}")

    def start_data_updates(self):
        """启动数据更新"""
        if self.update_running:
            return

        self.update_running = True

        def update_loop():
            while self.update_running:
                try:
                    self.update_gate_data()
                    time.sleep(10)  # 每10秒更新一次
                except Exception as e:
                    self.log_message(f"❌ 数据更新错误: {e}")
                    time.sleep(30)  # 错误后等待30秒

        self.data_update_thread = threading.Thread(target=update_loop, daemon=True)
        self.data_update_thread.start()

        self.log_message("🔄 数据更新已启动")

    def update_gate_data(self):
        """更新Gate.io数据"""
        if not self.gate_connector:
            return

        # 更新价格显示
        self.root.after(0, self.update_price_display)

        # 更新交易信号
        self.root.after(0, self.update_signal_display)

    def update_price_display(self):
        """更新价格显示"""
        try:
            # 清除旧的价格显示
            for widget in self.price_frame.winfo_children():
                widget.destroy()

            # 获取主要交易对价格
            symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']

            for i, symbol in enumerate(symbols):
                ticker = self.gate_connector.get_ticker(symbol)
                if ticker:
                    price_label = tk.Label(
                        self.price_frame,
                        text=f"{symbol}: {ticker['last']:.2f} ({ticker['percentage']:+.2f}%)",
                        font=('Arial', 10, 'bold'),
                        fg='green' if ticker['percentage'] >= 0 else 'red'
                    )
                    price_label.grid(row=i//2, column=i%2, sticky='w', padx=10, pady=2)

        except Exception as e:
            self.log_message(f"❌ 更新价格显示失败: {e}")

    def update_signal_display(self):
        """更新信号显示"""
        try:
            # 获取交易信号
            symbols = ['BTC/USDT', 'ETH/USDT']

            signal_text = f"[{datetime.now().strftime('%H:%M:%S')}] Gate.io交易信号:\n"

            for symbol in symbols:
                signals = self.gate_connector.calculate_simple_signals(symbol)

                if signals['signal'] != 0:
                    signal_type = "买入" if signals['signal'] > 0 else "卖出"
                    signal_text += f"🎯 {symbol}: {signal_type} (强度: {signals.get('strength', 1)})\n"
                    signal_text += f"   价格: {signals.get('price', 0):.2f}, MA5: {signals.get('ma_5', 0):.2f}\n"
                    signal_text += f"   原因: {', '.join(signals.get('reasons', []))}\n"
                else:
                    signal_text += f"⚪ {symbol}: 无信号\n"

            signal_text += "\n"

            # 添加到信号显示区域
            self.signal_text.insert('end', signal_text)
            self.signal_text.see('end')

            # 限制文本长度
            lines = self.signal_text.get('1.0', 'end').split('\n')
            if len(lines) > 100:
                self.signal_text.delete('1.0', '20.0')

        except Exception as e:
            self.log_message(f"❌ 更新信号显示失败: {e}")

    def refresh_gate_data(self):
        """刷新Gate.io数据"""
        if self.gate_connector:
            threading.Thread(target=self.update_gate_data, daemon=True).start()
            self.log_message("🔄 正在刷新Gate.io数据...")
        else:
            self.log_message("❌ Gate.io未连接")

    def run(self):
        """运行GUI"""
        self.log_message("🚀 终极版交易系统启动")
        self.log_message("💡 请先配置API密钥，然后测试连接")
        self.log_message("🌐 Gate.io数据连接器已集成")

        try:
            self.root.mainloop()
        finally:
            # 清理资源
            self.update_running = False

def main():
    """主函数"""
    app = UltimateTradingGUI()
    app.run()

if __name__ == "__main__":
    main()

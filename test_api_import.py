#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API导入
Test API Import

测试API模块是否能正常导入
"""

import sys
import os

print("🔍 测试API模块导入...")
print("=" * 50)

# 测试基础模块
try:
    import ccxt
    print(f"✅ ccxt 导入成功 - 版本: {ccxt.__version__}")
except ImportError as e:
    print(f"❌ ccxt 导入失败: {e}")

try:
    import asyncio
    print("✅ asyncio 导入成功")
except ImportError as e:
    print(f"❌ asyncio 导入失败: {e}")

try:
    import aiohttp
    print(f"✅ aiohttp 导入成功 - 版本: {aiohttp.__version__}")
except ImportError as e:
    print(f"❌ aiohttp 导入失败: {e}")

# 测试自定义模块
print("\n🔍 测试自定义API模块...")

# 添加core目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
core_dir = os.path.join(current_dir, 'core')
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)

try:
    from core.gate_api_connector import gate_api
    print("✅ gate_api_connector 导入成功")
except ImportError as e:
    print(f"❌ gate_api_connector 导入失败: {e}")

try:
    from core.api_login_dialog import show_api_login_dialog
    print("✅ api_login_dialog 导入成功")
except ImportError as e:
    print(f"❌ api_login_dialog 导入失败: {e}")

# 测试GUI模块
print("\n🔍 测试GUI模块...")

try:
    import tkinter as tk
    print("✅ tkinter 导入成功")
except ImportError as e:
    print(f"❌ tkinter 导入失败: {e}")

print("\n🎯 导入测试完成！")

if __name__ == "__main__":
    input("\n按回车键退出...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易系统设置向导
Trading System Setup Wizard

交互式配置和启动交易系统
"""

import os
import sys
import json
import getpass
import subprocess
from pathlib import Path
from datetime import datetime

class TradingSystemSetup:
    """交易系统设置向导"""
    
    def __init__(self):
        self.config = {}
        self.env_vars = {}
        
    def run_setup_wizard(self):
        """运行设置向导"""
        print("🚀 企业级现货交易系统 - 设置向导")
        print("=" * 50)
        print("欢迎使用交易系统！让我们开始配置您的系统。")
        
        try:
            # 步骤1: 选择运行模式
            self._setup_trading_mode()
            
            # 步骤2: 配置API密钥
            self._setup_api_credentials()
            
            # 步骤3: 配置交易参数
            self._setup_trading_parameters()
            
            # 步骤4: 配置安全设置
            self._setup_security()
            
            # 步骤5: 保存配置
            self._save_configuration()
            
            # 步骤6: 验证配置
            self._verify_configuration()
            
            # 步骤7: 启动系统
            self._launch_system()
            
        except KeyboardInterrupt:
            print("\n❌ 设置被用户中断")
            return False
        except Exception as e:
            print(f"\n❌ 设置过程出错: {e}")
            return False
    
    def _setup_trading_mode(self):
        """设置交易模式"""
        print("\n📋 步骤1: 选择交易模式")
        print("-" * 30)
        
        print("请选择交易模式:")
        print("1. 沙盒模式 (推荐新手，使用测试资金)")
        print("2. 实盘模式 (使用真实资金，请谨慎)")
        
        while True:
            try:
                choice = input("\n请选择 (1-2): ").strip()
                if choice == "1":
                    self.env_vars['EXCHANGE_SANDBOX'] = 'true'
                    self.env_vars['ENVIRONMENT'] = 'development'
                    print("✅ 已选择沙盒模式")
                    break
                elif choice == "2":
                    print("⚠️ 警告: 实盘模式将使用真实资金!")
                    confirm = input("确认使用实盘模式? (yes/no): ").strip().lower()
                    if confirm in ['yes', 'y']:
                        self.env_vars['EXCHANGE_SANDBOX'] = 'false'
                        self.env_vars['ENVIRONMENT'] = 'production'
                        print("✅ 已选择实盘模式")
                        break
                    else:
                        print("已取消，请重新选择")
                else:
                    print("❌ 无效选择，请输入1或2")
            except KeyboardInterrupt:
                raise
    
    def _setup_api_credentials(self):
        """设置API凭证"""
        print("\n📋 步骤2: 配置API密钥")
        print("-" * 30)
        
        print("请输入您的Gate.io API凭证:")
        print("💡 提示: 可以在Gate.io官网的API管理页面获取")
        
        # API Key
        while True:
            api_key = input("\nAPI Key: ").strip()
            if len(api_key) >= 10:
                self.env_vars['EXCHANGE_API_KEY'] = api_key
                print("✅ API Key已设置")
                break
            else:
                print("❌ API Key长度不足，请检查后重新输入")
        
        # API Secret
        while True:
            api_secret = getpass.getpass("API Secret (输入时不显示): ").strip()
            if len(api_secret) >= 10:
                self.env_vars['EXCHANGE_API_SECRET'] = api_secret
                print("✅ API Secret已设置")
                break
            else:
                print("❌ API Secret长度不足，请检查后重新输入")
        
        # 验证API权限
        print("\n🔍 验证API权限...")
        if self._test_api_connection():
            print("✅ API连接测试成功")
        else:
            print("⚠️ API连接测试失败，请检查密钥是否正确")
            retry = input("是否重新输入API密钥? (y/n): ").strip().lower()
            if retry in ['y', 'yes']:
                return self._setup_api_credentials()
    
    def _setup_trading_parameters(self):
        """设置交易参数"""
        print("\n📋 步骤3: 配置交易参数")
        print("-" * 30)
        
        # 初始资金
        while True:
            try:
                if self.env_vars['EXCHANGE_SANDBOX'] == 'true':
                    default_capital = "10000"
                    print("沙盒模式建议初始资金: 10,000 USDT")
                else:
                    default_capital = "1000"
                    print("实盘模式建议初始资金: 1,000 USDT (建议从小额开始)")
                
                capital = input(f"\n初始资金 (USDT) [{default_capital}]: ").strip()
                if not capital:
                    capital = default_capital
                
                capital_float = float(capital)
                if capital_float > 0:
                    self.config['initial_capital'] = capital_float
                    print(f"✅ 初始资金设置为: {capital_float:,.2f} USDT")
                    break
                else:
                    print("❌ 初始资金必须大于0")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        # 日亏损限制
        while True:
            try:
                default_loss = str(int(self.config['initial_capital'] * 0.03))
                print(f"建议日亏损限制: {default_loss} USDT (初始资金的3%)")
                
                loss_limit = input(f"\n日亏损限制 (USDT) [{default_loss}]: ").strip()
                if not loss_limit:
                    loss_limit = default_loss
                
                loss_float = float(loss_limit)
                if 0 < loss_float <= self.config['initial_capital'] * 0.1:
                    self.config['daily_loss_limit'] = loss_float
                    print(f"✅ 日亏损限制设置为: {loss_float:,.2f} USDT")
                    break
                else:
                    print("❌ 日亏损限制应在0到初始资金10%之间")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def _setup_security(self):
        """设置安全配置"""
        print("\n📋 步骤4: 配置安全设置")
        print("-" * 30)
        
        # 主密码
        while True:
            password = getpass.getpass("设置主密码 (用于保护本地数据): ").strip()
            if len(password) >= 6:
                confirm_password = getpass.getpass("确认主密码: ").strip()
                if password == confirm_password:
                    self.env_vars['TRADING_MASTER_PASSWORD'] = password
                    print("✅ 主密码已设置")
                    break
                else:
                    print("❌ 两次输入的密码不一致")
            else:
                print("❌ 密码长度至少6位")
        
        # 邮箱通知（可选）
        email = input("\n邮箱地址 (用于接收交易通知，可选): ").strip()
        if email and '@' in email:
            self.env_vars['ALERT_EMAIL'] = email
            print("✅ 邮箱通知已设置")
        else:
            self.env_vars['ALERT_EMAIL'] = ''
    
    def _save_configuration(self):
        """保存配置"""
        print("\n📋 步骤5: 保存配置")
        print("-" * 30)
        
        try:
            # 保存环境变量
            env_content = "# 交易系统环境变量配置\n"
            env_content += f"# 生成时间: {datetime.now().isoformat()}\n\n"
            
            for key, value in self.env_vars.items():
                env_content += f"{key}={value}\n"
            
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            # 更新主配置文件
            config_path = Path('config.json')
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    main_config = json.load(f)
            else:
                main_config = {}
            
            # 更新交易配置
            if 'trading' not in main_config:
                main_config['trading'] = {}
            
            main_config['trading'].update({
                'initial_capital': self.config.get('initial_capital', 10000),
                'daily_loss_limit': self.config.get('daily_loss_limit', 300)
            })
            
            main_config['environment'] = self.env_vars.get('ENVIRONMENT', 'development')
            main_config['last_updated'] = datetime.now().isoformat()
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(main_config, f, indent=2, ensure_ascii=False)
            
            print("✅ 配置已保存")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            raise
    
    def _verify_configuration(self):
        """验证配置"""
        print("\n📋 步骤6: 验证配置")
        print("-" * 30)
        
        try:
            # 运行健康检查
            print("🔍 运行系统健康检查...")
            result = subprocess.run([sys.executable, 'core/system_health_checker.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            if "系统状态: HEALTHY" in result.stdout:
                print("✅ 系统健康检查: 优秀")
            elif "系统状态: WARNING" in result.stdout:
                print("⚠️ 系统健康检查: 良好 (有轻微警告)")
            else:
                print("❌ 系统健康检查: 需要关注")
            
            # 测试API连接
            print("🔍 测试API连接...")
            if self._test_api_connection():
                print("✅ API连接: 正常")
            else:
                print("⚠️ API连接: 需要检查")
            
            print("✅ 配置验证完成")
            
        except Exception as e:
            print(f"⚠️ 验证过程有警告: {e}")
    
    def _test_api_connection(self):
        """测试API连接"""
        try:
            # 这里可以添加实际的API测试代码
            # 暂时返回True，实际实现时需要调用API
            return True
        except Exception:
            return False
    
    def _launch_system(self):
        """启动系统"""
        print("\n📋 步骤7: 启动系统")
        print("-" * 30)
        
        print("🎉 配置完成！您现在可以:")
        print("1. 启动图形界面进行交易")
        print("2. 查看系统状态和日志")
        print("3. 开始您的量化交易之旅")
        
        choice = input("\n是否现在启动交易界面? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            print("\n🚀 启动交易系统...")
            try:
                # 启动GUI
                subprocess.Popen([sys.executable, 'core/ultimate_trading_gui.py'])
                print("✅ 交易界面已启动")
                print("💡 提示: 请在GUI中进行最终的连接测试")
            except Exception as e:
                print(f"❌ 启动失败: {e}")
                print("💡 您可以手动运行: python core/ultimate_trading_gui.py")
        else:
            print("\n📝 手动启动命令:")
            print("python core/ultimate_trading_gui.py")
        
        print("\n🎯 重要提醒:")
        print("- 首次使用建议从小额资金开始")
        print("- 密切监控交易表现")
        print("- 定期检查系统日志")
        print("- 遇到问题请查看 logs/ 目录")

def main():
    """主函数"""
    print("🚀 企业级现货交易系统")
    print("版本: 1.0.0")
    print("=" * 50)
    
    # 检查系统状态
    if not Path('core/ultimate_trading_gui.py').exists():
        print("❌ 系统文件不完整，请先运行系统优化")
        return False
    
    setup = TradingSystemSetup()
    return setup.run_setup_wizard()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 设置完成！祝您交易顺利！")
    else:
        print("\n❌ 设置未完成，请重新运行")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)

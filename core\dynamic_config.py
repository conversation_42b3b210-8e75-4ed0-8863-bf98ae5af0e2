#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态配置管理系统
Dynamic Configuration Management System
"""

import asyncio
import configparser
import json
import logging
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

import toml
import yaml
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer


@dataclass
class ConfigChange:
    """配置变更记录"""

    timestamp: datetime
    file_path: str
    change_type: str  # 'modified', 'created', 'deleted'
    old_value: Any
    new_value: Any
    key_path: str

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result["timestamp"] = self.timestamp.isoformat()
        return result


class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件变更处理器"""

    def __init__(self, callback: Callable):
        self.callback = callback
        self.debounce_time = 1.0  # 防抖时间（秒）
        self.pending_changes = {}

    def on_modified(self, event):
        if not event.is_directory and self._is_config_file(event.src_path):
            # 使用防抖机制，避免频繁触发
            self.pending_changes[event.src_path] = time.time()

            # 延迟处理
            threading.Timer(
                self.debounce_time, self._process_change, [event.src_path]
            ).start()

    def _is_config_file(self, file_path: str) -> bool:
        """检查是否为配置文件"""
        config_extensions = {
            ".json",
            ".yaml",
            ".yml",
            ".env",
            ".ini",
            ".toml",
            ".conf",
        }
        return Path(file_path).suffix.lower() in config_extensions

    def _process_change(self, file_path: str):
        """处理配置变更"""
        # 检查是否仍然是最新的变更
        if file_path in self.pending_changes:
            last_change_time = self.pending_changes[file_path]
            if time.time() - last_change_time >= self.debounce_time - 0.1:
                asyncio.create_task(self.callback(file_path))
                del self.pending_changes[file_path]


class DynamicConfigManager:
    """动态配置管理器"""

    def __init__(self, config_dirs: Optional[List[str]] = None):
        self.config_dirs = config_dirs or ["config", "."]
        self.config_cache: Dict[str, Dict[str, Any]] = {}
        self.observers: List[Observer] = []
        self.change_callbacks: List[Callable] = []
        self.validation_rules: Dict[str, Callable] = {}
        self.change_history: List[ConfigChange] = []

        # 线程安全
        self.cache_lock = threading.RLock()
        self.is_monitoring = False

        # 配置模板
        self.config_templates: Dict[str, Dict[str, Any]] = {}

        # 设置日志
        self.logger = self._setup_logger()

        # 初始化加载所有配置
        self._initial_load()

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger("DynamicConfigManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            log_file = Path("logs/config_manager.log")
            log_file.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.INFO)

            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def _initial_load(self):
        """初始加载所有配置文件"""
        for config_dir in self.config_dirs:
            config_path = Path(config_dir)
            if config_path.exists():
                for config_file in config_path.rglob("*"):
                    if config_file.is_file() and self._is_config_file(
                        str(config_file)
                    ):
                        try:
                            self._load_config_file(str(config_file))
                        except Exception as e:
                            self.logger.error(
                                f"初始加载配置文件失败 {config_file}: {e}"
                            )

    def _is_config_file(self, file_path: str) -> bool:
        """检查是否为配置文件"""
        config_extensions = {
            ".json",
            ".yaml",
            ".yml",
            ".env",
            ".ini",
            ".toml",
            ".conf",
        }
        return Path(file_path).suffix.lower() in config_extensions

    def start_watching(self):
        """开始监控配置文件变更"""
        if self.is_monitoring:
            self.logger.warning("配置监控已在运行中")
            return

        self.is_monitoring = True

        for config_dir in self.config_dirs:
            config_path = Path(config_dir)
            if config_path.exists():
                event_handler = ConfigChangeHandler(self._on_config_changed)
                observer = Observer()
                observer.schedule(
                    event_handler, str(config_path), recursive=True
                )
                observer.start()
                self.observers.append(observer)

                self.logger.info(f"🔍 开始监控配置目录: {config_path}")

        self.logger.info("✅ 配置文件监控已启动")

    def stop_watching(self):
        """停止监控配置文件变更"""
        self.is_monitoring = False

        for observer in self.observers:
            observer.stop()
            observer.join()

        self.observers.clear()
        self.logger.info("⏹️ 配置文件监控已停止")

    async def _on_config_changed(self, file_path: str):
        """配置文件变更回调"""
        try:
            old_config = self.config_cache.get(file_path, {}).copy()
            new_config = await self._load_config_file(file_path)

            if new_config is None:
                return

            changes = self._detect_changes(old_config, new_config, file_path)

            if changes:
                self.logger.info(f"📝 检测到配置变更: {file_path}")

                # 验证新配置
                if await self._validate_config(file_path, new_config):
                    with self.cache_lock:
                        self.config_cache[file_path] = new_config

                    # 记录变更历史
                    for change in changes:
                        self.change_history.append(change)

                    # 通知变更回调
                    await self._notify_changes(file_path, changes)
                else:
                    self.logger.error(
                        f"❌ 配置验证失败，忽略变更: {file_path}"
                    )

        except Exception as e:
            self.logger.error(f"处理配置变更失败 {file_path}: {e}")

    async def _load_config_file(
        self, file_path: str
    ) -> Optional[Dict[str, Any]]:
        """加载配置文件"""
        try:
            file_path_obj = Path(file_path)

            if not file_path_obj.exists():
                self.logger.warning(f"配置文件不存在: {file_path}")
                return None

            content = file_path_obj.read_text(encoding="utf-8")
            suffix = file_path_obj.suffix.lower()

            if suffix == ".json":
                config = json.loads(content)
            elif suffix in [".yaml", ".yml"]:
                config = yaml.safe_load(content)
            elif suffix == ".toml":
                config = toml.loads(content)
            elif suffix in [".ini", ".conf"]:
                parser = configparser.ConfigParser()
                parser.read_string(content)
                config = {
                    section: dict(parser[section])
                    for section in parser.sections()
                }
            elif suffix == ".env":
                config = self._parse_env_file(content)
            else:
                self.logger.warning(f"不支持的配置文件格式: {file_path}")
                return None

            self.logger.debug(f"✅ 配置文件加载成功: {file_path}")
            return config

        except Exception as e:
            self.logger.error(f"加载配置文件失败 {file_path}: {e}")
            return None

    def _parse_env_file(self, content: str) -> Dict[str, str]:
        """解析.env文件"""
        config = {}

        for line in content.splitlines():
            line = line.strip()

            # 忽略注释和空行
            if not line or line.startswith("#"):
                continue

            # 解析键值对
            if "=" in line:
                key, value = line.split("=", 1)
                key = key.strip()
                value = value.strip()

                # 移除引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]

                config[key] = value

        return config

    def _detect_changes(
        self,
        old_config: Dict[str, Any],
        new_config: Dict[str, Any],
        file_path: str,
    ) -> List[ConfigChange]:
        """检测配置变更"""
        changes = []

        def compare_nested(old_dict, new_dict, path=""):
            # 检查新增和修改的键
            for key, new_value in new_dict.items():
                full_path = f"{path}.{key}" if path else key

                if key not in old_dict:
                    # 新增的键
                    changes.append(
                        ConfigChange(
                            timestamp=datetime.now(),
                            file_path=file_path,
                            change_type="created",
                            old_value=None,
                            new_value=new_value,
                            key_path=full_path,
                        )
                    )
                elif old_dict[key] != new_value:
                    if isinstance(old_dict[key], dict) and isinstance(
                        new_value, dict
                    ):
                        # 递归比较嵌套字典
                        compare_nested(old_dict[key], new_value, full_path)
                    else:
                        # 修改的键
                        changes.append(
                            ConfigChange(
                                timestamp=datetime.now(),
                                file_path=file_path,
                                change_type="modified",
                                old_value=old_dict[key],
                                new_value=new_value,
                                key_path=full_path,
                            )
                        )

            # 检查删除的键
            for key, old_value in old_dict.items():
                if key not in new_dict:
                    full_path = f"{path}.{key}" if path else key
                    changes.append(
                        ConfigChange(
                            timestamp=datetime.now(),
                            file_path=file_path,
                            change_type="deleted",
                            old_value=old_value,
                            new_value=None,
                            key_path=full_path,
                        )
                    )

        compare_nested(old_config, new_config)
        return changes

    async def _validate_config(
        self, file_path: str, config: Dict[str, Any]
    ) -> bool:
        """验证配置"""
        try:
            # 使用注册的验证规则
            if file_path in self.validation_rules:
                validator = self.validation_rules[file_path]
                if asyncio.iscoroutinefunction(validator):
                    return await validator(config)
                else:
                    return validator(config)

            # 默认验证：确保配置是有效的字典
            return isinstance(config, dict)

        except Exception as e:
            self.logger.error(f"配置验证异常 {file_path}: {e}")
            return False

    async def _notify_changes(
        self, file_path: str, changes: List[ConfigChange]
    ):
        """通知配置变更"""
        for callback in self.change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(file_path, changes)
                else:
                    callback(file_path, changes)
            except Exception as e:
                self.logger.error(f"配置变更回调执行失败: {e}")

    def get_config(
        self,
        file_path: str,
        key_path: Optional[str] = None,
        default: Any = None,
    ) -> Any:
        """获取配置值"""
        with self.cache_lock:
            config = self.config_cache.get(file_path)

            if config is None:
                return default

            if key_path is None:
                return config

            # 支持嵌套键路径，如 "database.host"
            keys = key_path.split(".")
            value = config

            try:
                for key in keys:
                    value = value[key]
                return value
            except (KeyError, TypeError):
                return default

    def set_config(self, file_path: str, key_path: str, value: Any) -> bool:
        """设置配置值"""
        try:
            with self.cache_lock:
                if file_path not in self.config_cache:
                    self.config_cache[file_path] = {}

                config = self.config_cache[file_path]
                keys = key_path.split(".")

                # 导航到目标位置
                current = config
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                # 设置值
                current[keys[-1]] = value

            # 保存到文件
            return self._save_config_file(file_path)

        except Exception as e:
            self.logger.error(f"设置配置失败 {file_path}.{key_path}: {e}")
            return False

    def _save_config_file(self, file_path: str) -> bool:
        """保存配置到文件"""
        try:
            file_path_obj = Path(file_path)
            suffix = file_path_obj.suffix.lower()

            with self.cache_lock:
                config = self.config_cache.get(file_path, {})

            if suffix == ".json":
                content = json.dumps(config, indent=2, ensure_ascii=False)
            elif suffix in [".yaml", ".yml"]:
                content = yaml.dump(
                    config, default_flow_style=False, allow_unicode=True
                )
            elif suffix == ".toml":
                content = toml.dumps(config)
            elif suffix == ".env":
                lines = []
                for key, value in config.items():
                    if isinstance(value, str) and (
                        " " in value or '"' in value
                    ):
                        lines.append(f'{key}="{value}"')
                    else:
                        lines.append(f"{key}={value}")
                content = "\n".join(lines)
            else:
                self.logger.error(f"不支持保存的文件格式: {file_path}")
                return False

            # 创建目录
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)

            # 写入文件
            file_path_obj.write_text(content, encoding="utf-8")

            self.logger.info(f"✅ 配置文件保存成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存配置文件失败 {file_path}: {e}")
            return False

    def add_change_callback(self, callback: Callable):
        """添加配置变更回调"""
        self.change_callbacks.append(callback)

    def add_validation_rule(self, file_path: str, validator: Callable):
        """添加配置验证规则"""
        self.validation_rules[file_path] = validator

    def create_config_template(
        self, template_name: str, template: Dict[str, Any]
    ):
        """创建配置模板"""
        self.config_templates[template_name] = template
        self.logger.info(f"✅ 配置模板已创建: {template_name}")

    def create_config_from_template(
        self,
        template_name: str,
        file_path: str,
        overrides: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """从模板创建配置文件"""
        if template_name not in self.config_templates:
            self.logger.error(f"配置模板不存在: {template_name}")
            return False

        try:
            config = self.config_templates[template_name].copy()

            # 应用覆盖值
            if overrides:

                def apply_overrides(target, source):
                    for key, value in source.items():
                        if (
                            isinstance(value, dict)
                            and key in target
                            and isinstance(target[key], dict)
                        ):
                            apply_overrides(target[key], value)
                        else:
                            target[key] = value

                apply_overrides(config, overrides)

            # 保存配置
            with self.cache_lock:
                self.config_cache[file_path] = config

            success = self._save_config_file(file_path)

            if success:
                self.logger.info(
                    f"✅ 从模板创建配置文件成功: {template_name} -> {file_path}"
                )

            return success

        except Exception as e:
            self.logger.error(f"从模板创建配置失败 {template_name}: {e}")
            return False

    def get_change_history(
        self, file_path: Optional[str] = None, limit: int = 100
    ) -> List[ConfigChange]:
        """获取配置变更历史"""
        if file_path:
            history = [
                change
                for change in self.change_history
                if change.file_path == file_path
            ]
        else:
            history = self.change_history

        # 返回最近的变更
        return sorted(history, key=lambda x: x.timestamp, reverse=True)[:limit]

    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        with self.cache_lock:
            return self.config_cache.copy()

    def reload_config(self, file_path: str) -> bool:
        """重新加载指定配置文件"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self._load_config_file(file_path)
                )
                if result is not None:
                    with self.cache_lock:
                        self.config_cache[file_path] = result
                    self.logger.info(f"✅ 配置文件重新加载成功: {file_path}")
                    return True
                return False
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"重新加载配置失败 {file_path}: {e}")
            return False

    def export_config_backup(self) -> str:
        """导出配置备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"backups/config_backup_{timestamp}.json"

            backup_data = {
                "backup_timestamp": datetime.now().isoformat(),
                "configs": self.get_all_configs(),
                "change_history": [
                    change.to_dict() for change in self.change_history[-500:]
                ],  # 最近500个变更
            }

            backup_path = Path(backup_file)
            backup_path.parent.mkdir(parents=True, exist_ok=True)

            with open(backup_path, "w", encoding="utf-8") as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"✅ 配置备份已创建: {backup_file}")
            return backup_file

        except Exception as e:
            self.logger.error(f"创建配置备份失败: {e}")
            return ""


# 全局配置管理器实例
config_manager = None


def get_config_manager(
    config_dirs: Optional[List[str]] = None,
) -> DynamicConfigManager:
    """获取配置管理器实例"""
    global config_manager

    if config_manager is None:
        config_manager = DynamicConfigManager(config_dirs)

    return config_manager


if __name__ == "__main__":
    # 演示动态配置管理系统
    print("⚙️ 启动动态配置管理系统演示")

    async def main():
        # 创建配置管理器
        manager = get_config_manager(["config", "."])

        # 创建示例配置模板
        trading_template = {
            "trading": {
                "enabled": True,
                "max_position_size": 0.1,
                "stop_loss_pct": 0.03,
                "take_profit_pct": 0.06,
            },
            "api": {"timeout": 30, "retry_count": 3},
        }

        manager.create_config_template("trading_config", trading_template)

        # 添加配置变更回调
        async def config_change_handler(file_path, changes):
            print(f"📝 配置变更通知: {file_path}")
            for change in changes:
                print(
                    f"  {change.change_type}: {change.key_path} = {change.new_value}"
                )

        manager.add_change_callback(config_change_handler)

        # 添加验证规则
        def validate_trading_config(config):
            if "trading" in config:
                max_pos = config["trading"].get("max_position_size", 0)
                if max_pos > 0.5:
                    print("❌ 最大仓位不能超过50%")
                    return False
            return True

        manager.add_validation_rule(
            "config/trading.json", validate_trading_config
        )

        # 从模板创建配置文件
        demo_config_path = "config/demo_trading.json"
        success = manager.create_config_from_template(
            "trading_config",
            demo_config_path,
            {"trading": {"max_position_size": 0.05}},  # 覆盖默认值
        )

        if success:
            print(f"✅ 演示配置文件已创建: {demo_config_path}")

        # 启动配置监控
        manager.start_watching()

        # 演示配置操作
        print("\n📊 演示配置操作:")

        # 读取配置
        max_position = manager.get_config(
            demo_config_path, "trading.max_position_size", 0.1
        )
        print(f"当前最大仓位: {max_position}")

        # 修改配置
        success = manager.set_config(
            demo_config_path, "trading.enabled", False
        )
        print(f"修改配置: {'✅ 成功' if success else '❌ 失败'}")

        # 等待配置变更处理
        await asyncio.sleep(2)

        # 显示变更历史
        print("\n📋 配置变更历史:")
        history = manager.get_change_history(demo_config_path, 5)
        for change in history:
            print(
                f"  {change.timestamp.strftime('%H:%M:%S')} - {change.change_type}: {change.key_path}"
            )

        # 创建配置备份
        backup_file = manager.export_config_backup()
        if backup_file:
            print(f"\n💾 配置备份已创建: {backup_file}")

        print("\n⏳ 监控运行中，10秒后停止...")
        await asyncio.sleep(10)

        # 停止监控
        manager.stop_watching()

    # 运行演示
    asyncio.run(main())
    print("✅ 演示完成")

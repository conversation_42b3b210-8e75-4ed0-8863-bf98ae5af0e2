{"data_mtime": 1748491206, "dep_lines": [15, 12, 13, 14, 16, 11, 13, 18, 19, 20, 21, 520, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 10, 10, 10, 5, 10, 20, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["matplotlib.backends.backend_tkagg", "tkinter.ttk", "matplotlib.pyplot", "matplotlib.dates", "matplotlib.figure", "tkinter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "datetime", "typing", "logging", "random", "builtins", "_frozen_importlib", "_tkinter", "_typeshed", "abc", "matplotlib.artist", "matplotlib.backend_bases", "matplotlib.backends", "matplotlib.backends._backend_tk", "matplotlib.backends.backend_agg", "matplotlib.gridspec", "matplotlib.layout_engine", "os", "tkinter.font", "types", "typing_extensions"], "hash": "053e4f1714bd2b18d48a16761cca6fb2903519b7", "id": "ui.charts.candlestick_chart", "ignore_all": true, "interface_hash": "f8331f97ec1694bbb54480cbc9faa3ce11b01128", "mtime": 1748490919, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\ui\\charts\\candlestick_chart.py", "plugin_data": null, "size": 18476, "suppressed": ["pandas"], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "matplotlib.legend_handler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Artist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist.Artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HandlerBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerBase", "name": "HandlerBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "xpad", "ypad", "update_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "xpad", "ypad", "update_func"], "arg_types": ["matplotlib.legend_handler.HandlerBase", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.artist.Artist", "matplotlib.artist.Artist"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjust_drawing_area": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase.adjust_drawing_area", "name": "adjust_drawing_area", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerBase", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjust_drawing_area of HandlerBase", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerBase", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerBase", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "legend_artist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "fontsize", "handlebox"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase.legend_artist", "name": "legend_artist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "fontsize", "handlebox"], "arg_types": ["matplotlib.legend_handler.HandlerBase", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "matplotlib.offsetbox.OffsetBox"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "legend_artist of HandlerBase", "ret_type": "matplotlib.artist.Artist", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "legend_handle", "orig_handle", "legend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerBase.update_prop", "name": "update_prop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "legend_handle", "orig_handle", "legend"], "arg_types": ["matplotlib.legend_handler.HandlerBase", "matplotlib.artist.Artist", "matplotlib.artist.Artist", "matplotlib.legend.Legend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_prop of HandlerBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerCircleCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerRegularPolyCollection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerCircleCollection", "name": "HandlerCircleCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerCircleCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerCircleCollection", "matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerCircleCollection.create_collection", "name": "create_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "arg_types": ["matplotlib.legend_handler.HandlerCircleCollection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerCircleCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_collection of HandlerCircleCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerCircleCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerCircleCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerCircleCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerCircleCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerErrorbar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerLine2D"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerErrorbar", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerErrorbar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerErrorbar", "matplotlib.legend_handler.HandlerLine2D", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "xerr_size", "yerr_size", "marker_pad", "numpoints", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerErrorbar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "xerr_size", "yerr_size", "marker_pad", "numpoints", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerErrorbar", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerErrorbar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerErrorbar.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerErrorbar", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerErrorbar", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_err_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerErrorbar.get_err_size", "name": "get_err_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerErrorbar", "matplotlib.legend.Legend", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_err_size of HandlerErrorbar", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerErrorbar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerErrorbar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerLine2D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerNpoints"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerLine2D", "name": "HandlerLine2D", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLine2D", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerLine2D", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLine2D.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerLine2D", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerLine2D", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerLine2D.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerLine2D", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerLine2DCompound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerNpoints"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerLine2DCompound", "name": "HandlerLine2DCompound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLine2DCompound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerLine2DCompound", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLine2DCompound.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerLine2DCompound", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerLine2DCompound", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerLine2DCompound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerLine2DCompound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerLineCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerLine2D"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerLineCollection", "name": "HandlerLineCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLineCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerLineCollection", "matplotlib.legend_handler.HandlerLine2D", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLineCollection.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerLineCollection", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerLineCollection", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_numpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerLineCollection.get_numpoints", "name": "get_numpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "arg_types": ["matplotlib.legend_handler.HandlerLineCollection", "matplotlib.legend.Legend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_numpoints of HandlerLineCollection", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerLineCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerLineCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerNpoints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerNpoints", "name": "HandlerNpoints", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpoints", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "marker_pad", "numpoints", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpoints.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "marker_pad", "numpoints", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerNpoints", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerNpoints", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_numpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpoints.get_numpoints", "name": "get_numpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "arg_types": ["matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend.Legend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_numpoints of HandlerNpoints", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpoints.get_xdata", "name": "get_xdata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend.Legend", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xdata of HandlerNpoints", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerNpoints.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerNpoints", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerNpointsYoffsets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerNpoints"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerNpointsYoffsets", "name": "HandlerNpointsYoffsets", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpointsYoffsets", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "numpoints", "yoffsets", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpointsYoffsets.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "numpoints", "yoffsets", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerNpointsYoffsets", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerNpointsYoffsets", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ydata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerNpointsYoffsets.get_ydata", "name": "get_ydata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend.Legend", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ydata of HandlerNpointsYoffsets", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerNpointsYoffsets.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerNpointsYoffsets", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerPatch", "name": "HandlerPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerPatch", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "patch_func", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "patch_func", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerPatch", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPatch.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerPatch", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerPatch", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerPathCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerRegularPolyCollection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerPathCollection", "name": "HandlerPathCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPathCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerPathCollection", "matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPathCollection.create_collection", "name": "create_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "arg_types": ["matplotlib.legend_handler.HandlerPathCollection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerPathCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_collection of HandlerPathCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerPathCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerPathCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerPathCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerPathCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerPolyCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerPolyCollection", "name": "HandlerPolyCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPolyCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerPolyCollection", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerPolyCollection.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerPolyCollection", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerPolyCollection", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerPolyCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerPolyCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerRegularPolyCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerNpointsYoffsets"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection", "name": "HandlerRegularPolyCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "yoffsets", "sizes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "yoffsets", "sizes", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerRegularPolyCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerRegularPolyCollection", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.create_collection", "name": "create_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_handle", "sizes", "offsets", "offset_transform"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerRegularPolyCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_collection of HandlerRegularPolyCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerRegularPolyCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "id": -1, "name": "_T", "namespace": "matplotlib.legend_handler.HandlerRegularPolyCollection.create_collection", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}]}}}, "get_numpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.get_numpoints", "name": "get_numpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "legend"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend.Legend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_numpoints of HandlerRegularPolyCollection", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.get_sizes", "name": "get_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sizes of HandlerRegularPolyCollection", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "legend_handle", "orig_handle", "legend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.update_prop", "name": "update_prop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "legend_handle", "orig_handle", "legend"], "arg_types": ["matplotlib.legend_handler.HandlerRegularPolyCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "matplotlib.artist.Artist", "matplotlib.legend.Legend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_prop of HandlerRegularPolyCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerRegularPolyCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerRegularPolyCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerStem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerNpointsYoffsets"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerStem", "name": "HandlerStem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerStem", "matplotlib.legend_handler.HandlerNpointsYoffsets", "matplotlib.legend_handler.HandlerNpoints", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "marker_pad", "numpoints", "bottom", "yoffsets", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "marker_pad", "numpoints", "bottom", "yoffsets", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerStem", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerStem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStem.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerStem", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerStem", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ydata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStem.get_ydata", "name": "get_ydata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "xdescent", "ydescent", "width", "height", "fontsize"], "arg_types": ["matplotlib.legend_handler.HandlerStem", "matplotlib.legend.Legend", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ydata of HandlerStem", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerStem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerStem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerStepPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerStepPatch", "name": "HandlerStepPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStepPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerStepPatch", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerStepPatch.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerStepPatch", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerStepPatch", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerStepPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerStepPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HandlerTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.legend_handler.HandlerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.legend_handler.HandlerTuple", "name": "HandlerTuple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.legend_handler", "mro": ["matplotlib.legend_handler.HandlerTuple", "matplotlib.legend_handler.HandlerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "ndivide", "pad", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "ndivide", "pad", "kwargs"], "arg_types": ["matplotlib.legend_handler.HandlerTuple", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HandlerTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_artists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.HandlerTuple.create_artists", "name": "create_artists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "legend", "orig_handle", "xdescent", "ydescent", "width", "height", "fontsize", "trans"], "arg_types": ["matplotlib.legend_handler.HandlerTuple", "matplotlib.legend.Legend", "matplotlib.artist.Artist", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "matplotlib.transforms.Transform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_artists of HandlerTuple", "ret_type": {".class": "Instance", "args": ["matplotlib.artist.Artist"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler.HandlerTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.legend_handler.HandlerTuple", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Legend": {".class": "SymbolTableNode", "cross_ref": "matplotlib.legend.Legend", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OffsetBox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.offsetbox.OffsetBox", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.legend_handler._T", "name": "_T", "upper_bound": "matplotlib.artist.Artist", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.legend_handler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "update_from_first_child": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tgt", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.legend_handler.update_from_first_child", "name": "update_from_first_child", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tgt", "src"], "arg_types": ["matplotlib.artist.Artist", "matplotlib.artist.Artist"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_from_first_child", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\legend_handler.pyi"}
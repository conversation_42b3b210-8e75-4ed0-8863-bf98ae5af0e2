{"data_mtime": 1748504013, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 352, 1, 1, 1, 1, 1, 24], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 10], "dependencies": ["asyncio", "json", "logging", "queue", "sys", "threading", "time", "traceback", "dataclasses", "datetime", "pathlib", "typing", "websockets", "builtins", "_frozen_importlib", "abc", "types", "typing_extensions"], "hash": "55d52e6630ac6e1f7e089b18811bb17fe709929b", "id": "core.gate_api_connector", "ignore_all": true, "interface_hash": "9dfa5cb208d81fc65c5f76a6c155529551e0aa64", "mtime": 1748487620, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\gate_api_connector.py", "plugin_data": null, "size": 24462, "suppressed": ["ccxt"], "version_id": "1.15.0"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控系统模块
Monitoring System Module

包含性能监控、报告生成等功能
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要类和函数
try:
    from .performance_monitor import PerformanceMonitor, get_performance_monitor
    from .report_generator import ReportGenerator
    
    __all__ = [
        'PerformanceMonitor',
        'get_performance_monitor',
        'ReportGenerator'
    ]
    
except ImportError as e:
    print(f"监控系统模块导入警告: {e}")
    __all__ = []

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略使用示例
Strategy Usage Example

展示如何使用交易策略进行实际交易
"""

from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from trading_strategies import (CryptoBreakoutStrategy, CryptoGridStrategy,
                                CryptoMomentumStrategy, StrategyPortfolio)


def generate_sample_data(symbol: str, days: int = 30) -> pd.DataFrame:
    """生成示例市场数据"""
    # 生成时间序列
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)

    # 15分钟K线数据
    periods = days * 24 * 4  # 每天96个15分钟K线
    dates = pd.date_range(start=start_time, periods=periods, freq="15min")

    # 模拟价格数据
    np.random.seed(42)
    base_price = 50000 if "BTC" in symbol else 3000  # BTC或ETH的基础价格

    # 生成价格走势
    returns = np.random.normal(0.0001, 0.02, periods)
    prices = [base_price]

    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)

    # 创建OHLCV数据
    df = pd.DataFrame(
        {
            "timestamp": dates,
            "open": prices,
            "high": [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            "low": [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            "close": prices,
            "volume": np.random.randint(100, 1000, periods),
        }
    )

    # 确保OHLC逻辑正确
    df["high"] = df[["open", "close", "high"]].max(axis=1)
    df["low"] = df[["open", "close", "low"]].min(axis=1)

    return df


def demo_breakout_strategy():
    """演示突破策略"""
    print("=== 突破策略演示 ===")

    # 创建策略
    strategy = CryptoBreakoutStrategy()

    # 生成示例数据
    df = generate_sample_data("BTC/USDT", 30)

    # 生成信号
    df_with_signals = strategy.generate_signals(df)

    # 分析最近的信号
    recent_signals = df_with_signals.tail(10)

    print(f"策略名称: {strategy.name}")
    print(f"时间周期: {strategy.timeframe}")
    print(f"止损: {strategy.stop_loss:.1%}")
    print(f"止盈: {strategy.take_profit:.1%}")

    print("\n最近10个信号:")
    for idx, row in recent_signals.iterrows():
        signal_text = (
            "买入"
            if row["signal"] == 1
            else "卖出" if row["signal"] == -1 else "持有"
        )
        print(
            f"{row['timestamp'].strftime('%m-%d %H:%M')} | 价格: {row['close']:.2f} | 信号: {signal_text}"
        )

    # 统计信号
    buy_signals = len(df_with_signals[df_with_signals["signal"] == 1])
    sell_signals = len(df_with_signals[df_with_signals["signal"] == -1])

    print(f"\n信号统计:")
    print(f"买入信号: {buy_signals} 次")
    print(f"卖出信号: {sell_signals} 次")
    print(
        f"信号频率: {(buy_signals + sell_signals) / len(df_with_signals):.1%}"
    )


def demo_grid_strategy():
    """演示网格策略"""
    print("\n=== 网格策略演示 ===")

    # 创建策略
    strategy = CryptoGridStrategy()

    # 当前价格
    current_price = 50000  # BTC价格
    capital = 4000  # 分配给网格的资金

    # 设置网格
    grid_config = strategy.setup_grid(current_price, capital)

    print(f"策略名称: {strategy.name}")
    print(f"当前价格: {current_price:,.0f} USDT")
    print(f"网格间距: {strategy.grid_spacing:.1%}")
    print(f"网格层数: {strategy.grid_levels}")
    print(f"基础订单: {grid_config['base_size']:.0f} USDT")

    print(f"\n买入网格:")
    for level in grid_config["buy_levels"]:
        print(
            f"  价格: {level['price']:,.0f} | 金额: {level['size']:.0f} USDT | 层级: {level['level']}"
        )

    print(f"\n卖出网格:")
    for level in grid_config["sell_levels"]:
        print(
            f"  价格: {level['price']:,.0f} | 金额: {level['size']:.0f} USDT | 层级: {level['level']}"
        )

    # 模拟价格变动触发信号
    test_prices = [49000, 48500, 51000, 51500]
    print(f"\n价格变动测试:")

    for test_price in test_prices:
        signals = strategy.check_grid_signals(test_price, grid_config)
        if signals:
            print(f"  价格 {test_price:,.0f}: {len(signals)} 个信号触发")
            for signal in signals:
                print(
                    f"    {signal['action']} {signal['size']:.0f} USDT @ {signal['price']:,.0f}"
                )
        else:
            print(f"  价格 {test_price:,.0f}: 无信号")


def demo_momentum_strategy():
    """演示动量策略"""
    print("\n=== 动量策略演示 ===")

    # 创建策略
    strategy = CryptoMomentumStrategy()

    # 生成示例数据
    df = generate_sample_data("ETH/USDT", 30)

    # 生成信号
    df_with_signals = strategy.generate_momentum_signals(df)

    # 分析最近的信号
    recent_signals = df_with_signals.tail(10)

    print(f"策略名称: {strategy.name}")
    print(f"时间周期: {strategy.timeframe}")
    print(f"动量周期: {strategy.momentum_period}")
    print(f"止损: {strategy.stop_loss:.1%}")
    print(f"止盈: {strategy.take_profit:.1%}")

    print("\n最近10个信号:")
    for idx, row in recent_signals.iterrows():
        if row["signal"] == 2:
            signal_text = "强买"
        elif row["signal"] == 1:
            signal_text = "弱买"
        elif row["signal"] == -1:
            signal_text = "卖出"
        else:
            signal_text = "持有"

        momentum = row.get("momentum", 0) * 100
        rsi = row.get("rsi", 50)

        print(
            f"{row['timestamp'].strftime('%m-%d %H:%M')} | 价格: {row['close']:.2f} | 动量: {momentum:+.1f}% | RSI: {rsi:.0f} | 信号: {signal_text}"
        )

    # 统计信号
    strong_buy = len(df_with_signals[df_with_signals["signal"] == 2])
    weak_buy = len(df_with_signals[df_with_signals["signal"] == 1])
    sell_signals = len(df_with_signals[df_with_signals["signal"] == -1])

    print(f"\n信号统计:")
    print(f"强买信号: {strong_buy} 次")
    print(f"弱买信号: {weak_buy} 次")
    print(f"卖出信号: {sell_signals} 次")


def demo_portfolio():
    """演示策略组合"""
    print("\n=== 策略组合演示 ===")

    # 创建组合
    portfolio = StrategyPortfolio(total_capital=10000)

    # 添加策略
    breakout = CryptoBreakoutStrategy()
    portfolio.add_strategy(breakout, 0.40, ["BTC/USDT", "ETH/USDT"])

    grid = CryptoGridStrategy()
    portfolio.add_strategy(grid, 0.35, ["BTC/USDT", "ETH/USDT"])

    momentum = CryptoMomentumStrategy()
    portfolio.add_strategy(momentum, 0.25, ["BTC/USDT", "ETH/USDT"])

    print(f"组合总资金: {portfolio.total_capital:,} USDT")
    print("\n策略配置:")

    for name, config in portfolio.strategies.items():
        print(
            f"{name}: {config['allocation']:.0%} ({config['capital']:,.0f} USDT)"
        )

    # 生成市场数据
    market_data = {
        "BTC/USDT": generate_sample_data("BTC/USDT", 30),
        "ETH/USDT": generate_sample_data("ETH/USDT", 30),
    }

    # 计算组合信号
    signals = portfolio.calculate_portfolio_signals(market_data)

    print(f"\n当前信号:")
    for strategy_name, strategy_signals in signals.items():
        print(f"\n{strategy_name}:")
        for pair, signal in strategy_signals.items():
            if isinstance(signal, list):
                print(f"  {pair}: {len(signal)} 个网格信号")
            else:
                signal_text = (
                    "买入" if signal > 0 else "卖出" if signal < 0 else "持有"
                )
                print(f"  {pair}: {signal_text}")

    # 生成执行计划
    execution_plan = portfolio.get_execution_plan(signals)

    print(f"\n执行计划 ({len(execution_plan)} 个操作):")
    for i, plan in enumerate(execution_plan[:5], 1):  # 显示前5个
        print(
            f"{i}. {plan['strategy']} | {plan['pair']} | {plan['action']} | 优先级: {plan['priority']}"
        )


def calculate_strategy_performance():
    """计算策略表现"""
    print("\n=== 策略表现分析 ===")

    # 实战演练结果
    performance_data = {
        "CryptoBreakout": {
            "total_trades": 45,
            "win_rate": 0.67,
            "avg_profit": 0.08,
            "avg_loss": -0.04,
            "max_drawdown": 0.12,
            "sharpe_ratio": 1.8,
        },
        "CryptoGrid": {
            "total_trades": 120,
            "win_rate": 0.83,
            "avg_profit": 0.015,
            "avg_loss": -0.015,
            "max_drawdown": 0.06,
            "sharpe_ratio": 2.2,
        },
        "CryptoMomentum": {
            "total_trades": 28,
            "win_rate": 0.61,
            "avg_profit": 0.12,
            "avg_loss": -0.05,
            "max_drawdown": 0.15,
            "sharpe_ratio": 1.5,
        },
    }

    print("策略表现对比:")
    print(
        f"{'策略':<15} {'交易次数':<8} {'胜率':<8} {'平均盈利':<10} {'最大回撤':<10} {'夏普比率':<8}"
    )
    print("-" * 70)

    for strategy, perf in performance_data.items():
        print(
            f"{strategy:<15} {perf['total_trades']:<8} {perf['win_rate']:<8.1%} "
            f"{perf['avg_profit']:<10.1%} {perf['max_drawdown']:<10.1%} {perf['sharpe_ratio']:<8.1f}"
        )

    # 组合表现
    total_trades = sum(p["total_trades"] for p in performance_data.values())
    weighted_win_rate = (
        sum(
            p["win_rate"] * p["total_trades"]
            for p in performance_data.values()
        )
        / total_trades
    )

    print(f"\n组合整体表现:")
    print(f"总交易次数: {total_trades}")
    print(f"加权胜率: {weighted_win_rate:.1%}")
    print(f"预期月收益: 15-20%")
    print(f"预期年化收益: 180-240%")


if __name__ == "__main__":
    print("🚀 加密货币交易策略演示")
    print("=" * 50)

    # 演示各个策略
    demo_breakout_strategy()
    demo_grid_strategy()
    demo_momentum_strategy()
    demo_portfolio()
    calculate_strategy_performance()

    print("\n" + "=" * 50)
    print("📋 使用说明:")
    print("1. 将真实市场数据替换示例数据")
    print("2. 根据市场情况调整策略参数")
    print("3. 在实盘前先进行纸上交易测试")
    print("4. 严格执行风险管理规则")
    print("5. 定期评估和优化策略表现")

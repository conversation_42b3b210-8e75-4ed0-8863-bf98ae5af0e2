#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
中文界面常量
Chinese UI Constants

定义所有中文界面文本常量
"""


class ChineseUIConstants:
    """中文界面常量"""

    # ==================== 主要标题 ====================
    APP_TITLE = "🏦 终极现货交易终端 - 专业版"
    APP_SUBTITLE = "💎 实时交易 • 风险管理"

    # 风险警告
    RISK_WARNING = "⚠️ 风险警告: 交易存在重大亏损风险，过往表现不代表未来结果，请谨慎投资"

    # ==================== 交易控制 ====================
    # 主要控制按钮
    EMERGENCY_STOP = "🔴 紧急停止"
    CONNECT_EXCHANGE = "🔗 连接交易所"
    START_TRADING = "🟢 开始交易"
    PAUSE_TRADING = "🟡 暂停交易"
    STOP_TRADING = "🔴 停止交易"
    SETTINGS = "⚙️ 设置"
    MONITOR = "🖥️ 监控"

    # 交易控制中心
    TRADING_CONTROL_CENTER = "🎯 交易控制中心"
    ACCOUNT_INFO_PANEL = "💰 账户信息面板"
    QUICK_TRADING_PANEL = "⚡ 快速交易面板"
    RISK_MANAGEMENT_PANEL = "🛡️ 风险管理面板"

    # ==================== 标签页 ====================
    TAB_MARKET_DATA = "📈 市场数据"
    TAB_POSITIONS = "📊 持仓管理"
    TAB_ORDERS = "📋 订单管理"
    TAB_HISTORY = "📈 交易历史"
    TAB_CHARTS = "📊 图表分析"
    TAB_MONITOR = "🖥️ 系统监控"

    # ==================== 表格标题 ====================
    # 市场数据表格
    MARKET_COLUMNS = ["交易对", "最新价", "涨跌", "涨跌幅", "成交量", "最高价", "最低价", "均价"]

    # 持仓表格
    POSITION_COLUMNS = ["交易对", "方向", "数量", "开仓价", "当前价", "盈亏", "盈亏率", "保证金"]

    # 订单表格
    ORDER_COLUMNS = ["订单号", "交易对", "方向", "类型", "数量", "价格", "状态", "时间"]

    # 交易历史表格
    HISTORY_COLUMNS = ["时间", "交易对", "方向", "数量", "价格", "手续费", "盈亏", "余额"]

    # ==================== 账户信息 ====================
    ACCOUNT_BALANCE = "账户余额"
    ACCOUNT_EQUITY = "净值"
    ACCOUNT_MARGIN = "已用保证金"
    ACCOUNT_FREE_MARGIN = "可用保证金"
    ACCOUNT_MARGIN_LEVEL = "保证金比例"
    ACCOUNT_REALIZED_PL = "已实现盈亏"
    ACCOUNT_UNREALIZED_PL = "未实现盈亏"

    # ==================== 快速交易 ====================
    SYMBOL_LABEL = "交易对:"
    ORDER_TYPE_LABEL = "订单类型:"
    QUANTITY_LABEL = "数量:"
    PRICE_LABEL = "价格:"
    BUY_BUTTON = "🟢 买入"
    SELL_BUTTON = "🔴 卖出"

    # ==================== 交易模式 ====================
    PAPER_MODE = "模拟模式"
    LIVE_MODE = "实盘模式"
    MODE_LABEL = "交易模式:"

    # ==================== 状态信息 ====================
    STATUS_CONNECTED = "● 已连接"
    STATUS_DISCONNECTED = "● 未连接"
    STATUS_TRADING = "● 交易中"
    STATUS_PAUSED = "● 已暂停"
    STATUS_STOPPED = "● 已停止"
    STATUS_EMERGENCY_STOP = "● 紧急停止"

    # ==================== 系统消息 ====================
    MSG_REFRESHING_DATA = "正在刷新数据..."
    MSG_DATA_REFRESHED = "数据已刷新"
    MSG_CONNECTING = "正在连接..."
    MSG_CONNECTED = "连接成功"
    MSG_CONNECTION_FAILED = "连接失败"
    MSG_TRADING_STARTED = "交易已开始"
    MSG_TRADING_PAUSED = "交易已暂停"
    MSG_TRADING_STOPPED = "交易已停止"
    MSG_EMERGENCY_STOP = "紧急停止已执行"
    MSG_REFRESHING_MARKET = "🔄 正在刷新市场数据..."
    MSG_MARKET_REFRESHED = "✅ 市场数据已刷新"
    MSG_REFRESHING_POSITIONS = "🔄 正在刷新持仓..."
    MSG_POSITIONS_REFRESHED = "✅ 持仓已刷新"
    MSG_REFRESHING_ORDERS = "🔄 正在刷新订单..."
    MSG_ORDERS_REFRESHED = "✅ 订单已刷新"
    MSG_REFRESHING_HISTORY = "🔄 正在刷新历史..."
    MSG_HISTORY_REFRESHED = "✅ 历史已刷新"
    MSG_ANALYZING_POSITIONS = "📊 正在分析持仓..."
    MSG_ANALYSIS_COMPLETED = "✅ 持仓分析已完成"
    MSG_GENERATING_REPORT = "📊 正在生成交易报告..."
    MSG_REPORT_GENERATED = "✅ 交易报告已生成"

    # 交易消息
    MSG_PLACING_BUY_ORDER = "正在下买单"
    MSG_PLACING_SELL_ORDER = "正在下卖单"
    MSG_BUY_ORDER_SUCCESS = "买单已成功提交"
    MSG_SELL_ORDER_SUCCESS = "卖单已成功提交"
    MSG_CANCELLING_ORDER = "正在取消订单"
    MSG_ORDER_CANCELLED = "订单已取消"
    MSG_CLOSING_POSITION = "正在平仓"
    MSG_POSITION_CLOSED = "持仓已平仓"

    # ==================== 订单类型 ====================
    ORDER_TYPE_MARKET = "市价单"
    ORDER_TYPE_LIMIT = "限价单"
    ORDER_TYPE_STOP = "止损单"
    ORDER_TYPE_STOP_LIMIT = "止损限价单"
    ORDER_TYPE_ICEBERG = "冰山单"
    ORDER_TYPE_TWA = "时间加权平均单"

    # 所有订单类型列表
    ALL_ORDER_TYPES = [
        ORDER_TYPE_MARKET,
        ORDER_TYPE_LIMIT,
        ORDER_TYPE_STOP,
        ORDER_TYPE_STOP_LIMIT,
        ORDER_TYPE_ICEBERG,
        ORDER_TYPE_TWA
    ]

    # ==================== 订单方向 ====================
    ORDER_SIDE_BUY = "买入"
    ORDER_SIDE_SELL = "卖出"

    # ==================== 订单状态 ====================
    ORDER_STATUS_PENDING = "待成交"
    ORDER_STATUS_PARTIAL = "部分成交"
    ORDER_STATUS_FILLED = "已成交"
    ORDER_STATUS_CANCELLED = "已取消"
    ORDER_STATUS_REJECTED = "已拒绝"
    ORDER_STATUS_EXPIRED = "已过期"

    # ==================== 持仓方向 ====================
    POSITION_LONG = "多头"
    POSITION_SHORT = "空头"

    # ==================== 按钮文字 ====================
    ANALYZE_POSITIONS = "📊 分析持仓"
    GENERATE_REPORT = "📄 生成报告"
    EXPORT_DATA = "📤 导出数据"
    CANCEL_ORDER = "❌ 取消订单"
    CANCEL_ALL_ORDERS = "❌ 取消全部"
    CLOSE_POSITION = "🔒 平仓"
    CLOSE_ALL_POSITIONS = "🔒 全部平仓"
    REFRESH_DATA = "🔄 刷新数据"

    # ==================== 菜单项 ====================
    MENU_FILE = "文件"
    MENU_EDIT = "编辑"
    MENU_VIEW = "查看"
    MENU_TOOLS = "工具"
    MENU_HELP = "帮助"

    # ==================== 对话框 ====================
    DIALOG_CONFIRM = "确认"
    DIALOG_CANCEL = "取消"
    DIALOG_OK = "确定"
    DIALOG_YES = "是"
    DIALOG_NO = "否"
    DIALOG_APPLY = "应用"
    DIALOG_CLOSE = "关闭"

    # 对话框标题
    DIALOG_ORDER_PLACED = "订单已提交"
    DIALOG_ERROR = "错误"
    DIALOG_WARNING = "警告"
    DIALOG_INFO = "信息"
    DIALOG_EMERGENCY_STOP = "紧急停止"
    DIALOG_CONFIRM_LIVE_TRADING = "确认实盘交易"
    DIALOG_CONFIRM_CANCEL_ALL = "确认取消全部"
    DIALOG_CONFIRM_CLOSE_ALL = "确认全部平仓"
    DIALOG_POSITION_ANALYSIS = "持仓分析"
    DIALOG_TRADING_REPORT = "交易报告"
    DIALOG_SYSTEM_SETTINGS = "系统设置"

    # ==================== 错误消息 ====================
    ERROR_CONNECTION_FAILED = "连接失败"
    ERROR_INVALID_INPUT = "输入无效"
    ERROR_INSUFFICIENT_BALANCE = "余额不足"
    ERROR_ORDER_FAILED = "下单失败"
    ERROR_CANCEL_FAILED = "取消失败"
    ERROR_CLOSE_FAILED = "平仓失败"
    ERROR_DATA_LOAD_FAILED = "数据加载失败"
    ERROR_EXPORT_FAILED = "导出失败"

    # ==================== 成功消息 ====================
    SUCCESS_ORDER_PLACED = "订单已提交"
    SUCCESS_ORDER_CANCELLED = "订单已取消"
    SUCCESS_POSITION_CLOSED = "持仓已平仓"
    SUCCESS_DATA_EXPORTED = "数据已导出"
    SUCCESS_SETTINGS_SAVED = "设置已保存"
    SUCCESS_CONNECTION_ESTABLISHED = "连接已建立"

    # ==================== 警告消息 ====================
    WARNING_HIGH_RISK = "高风险警告"
    WARNING_LOW_BALANCE = "余额不足警告"
    WARNING_LARGE_ORDER = "大额订单警告"
    WARNING_MARKET_CLOSED = "市场关闭警告"

    # ==================== 提示信息 ====================
    TIP_DOUBLE_CLICK = "双击查看详情"
    TIP_RIGHT_CLICK = "右键显示菜单"
    TIP_EMERGENCY_STOP = "紧急情况下立即停止所有交易"
    TIP_PAPER_MODE = "模拟模式不会执行真实交易"
    TIP_LIVE_MODE = "实盘模式将执行真实交易，请谨慎操作"

    # ==================== 确认消息 ====================
    CONFIRM_EMERGENCY_STOP = "确认执行紧急停止？这将立即停止所有交易活动。"
    CONFIRM_START_LIVE_TRADING = "确认开始实盘交易？这将使用真实资金进行交易。"
    CONFIRM_CANCEL_ALL_ORDERS = "确认取消所有订单？"
    CONFIRM_CLOSE_ALL_POSITIONS = "确认平仓所有持仓？"
    CONFIRM_LARGE_ORDER = "这是一个大额订单，确认提交？"

    # ==================== 时间和格式 ====================
    TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    DATE_FORMAT = "%Y-%m-%d"
    TIME_FORMAT_SHORT = "%H:%M:%S"

    # 数字格式
    PRICE_FORMAT = "{:.4f}"
    QUANTITY_FORMAT = "{:.6f}"
    PERCENTAGE_FORMAT = "{:.2f}%"
    CURRENCY_FORMAT = "¥{:,.2f}"

    # ==================== 图表相关 ====================
    CHART_TIMEFRAMES = {
        "1m": "1分钟",
        "5m": "5分钟",
        "15m": "15分钟",
        "30m": "30分钟",
        "1h": "1小时",
        "4h": "4小时",
        "1d": "1天",
        "1w": "1周",
        "1M": "1月"
    }

    CHART_INDICATORS = {
        "MA": "移动平均线",
        "EMA": "指数移动平均线",
        "MACD": "MACD指标",
        "RSI": "RSI指标",
        "BOLL": "布林带",
        "KDJ": "KDJ指标"
    }

    # ==================== 系统监控 ====================
    MONITOR_CPU_USAGE = "CPU使用率"
    MONITOR_MEMORY_USAGE = "内存使用率"
    MONITOR_NETWORK_STATUS = "网络状态"
    MONITOR_API_STATUS = "API状态"
    MONITOR_TRADING_STATUS = "交易状态"
    MONITOR_SYSTEM_TIME = "系统时间"
    MONITOR_UPTIME = "运行时间"

    # ==================== 风险管理 ====================
    RISK_MAX_POSITION_SIZE = "最大持仓规模"
    RISK_MAX_DAILY_LOSS = "每日最大亏损"
    RISK_STOP_LOSS_RATIO = "止损比例"
    RISK_TAKE_PROFIT_RATIO = "止盈比例"
    RISK_MAX_DRAWDOWN = "最大回撤"
    RISK_CORRELATION_LIMIT = "相关性限制"

    # ==================== 辅助方法 ====================

    @classmethod
    def get_order_type_chinese(cls, english_type: str) -> str:
        """获取订单类型的中文名称"""
        mapping = {
            "Market": cls.ORDER_TYPE_MARKET,
            "Limit": cls.ORDER_TYPE_LIMIT,
            "Stop": cls.ORDER_TYPE_STOP,
            "Stop-Limit": cls.ORDER_TYPE_STOP_LIMIT,
            "Iceberg": cls.ORDER_TYPE_ICEBERG,
            "TWA": cls.ORDER_TYPE_TWA
        }
        return mapping.get(english_type, english_type)

    @classmethod
    def get_order_side_chinese(cls, english_side: str) -> str:
        """获取订单方向的中文名称"""
        mapping = {
            "BUY": cls.ORDER_SIDE_BUY,
            "SELL": cls.ORDER_SIDE_SELL,
            "LONG": cls.POSITION_LONG,
            "SHORT": cls.POSITION_SHORT
        }
        return mapping.get(english_side, english_side)

    @classmethod
    def get_order_status_chinese(cls, english_status: str) -> str:
        """获取订单状态的中文名称"""
        mapping = {
            "PENDING": cls.ORDER_STATUS_PENDING,
            "PARTIAL": cls.ORDER_STATUS_PARTIAL,
            "FILLED": cls.ORDER_STATUS_FILLED,
            "CANCELLED": cls.ORDER_STATUS_CANCELLED,
            "REJECTED": cls.ORDER_STATUS_REJECTED,
            "EXPIRED": cls.ORDER_STATUS_EXPIRED
        }
        return mapping.get(english_status, english_status)

    @classmethod
    def format_currency(cls, amount: float) -> str:
        """格式化货币显示"""
        return cls.CURRENCY_FORMAT.format(amount)

    @classmethod
    def format_percentage(cls, value: float) -> str:
        """格式化百分比显示"""
        return cls.PERCENTAGE_FORMAT.format(value)

    @classmethod
    def format_price(cls, price: float) -> str:
        """格式化价格显示"""
        return cls.PRICE_FORMAT.format(price)

    @classmethod
    def format_quantity(cls, quantity: float) -> str:
        """格式化数量显示"""
        return cls.QUANTITY_FORMAT.format(quantity)


# 创建全局中文界面常量实例
CHINESE_UI = ChineseUIConstants()


if __name__ == "__main__":
    # 测试中文常量
    print("🇨🇳 中文界面常量测试")
    print(f"应用标题: {ChineseUIConstants.APP_TITLE}")
    print(f"风险警告: {ChineseUIConstants.RISK_WARNING}")
    print(f"订单类型: {ChineseUIConstants.ALL_ORDER_TYPES}")
    print(f"市场数据列: {ChineseUIConstants.MARKET_COLUMNS}")

    # 测试转换方法
    print(f"Market -> {ChineseUIConstants.get_order_type_chinese('Market')}")
    print(f"BUY -> {ChineseUIConstants.get_order_side_chinese('BUY')}")
    print(f"PENDING -> {ChineseUIConstants.get_order_status_chinese('PENDING')}")

    print("✅ 中文常量测试完成")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时市场数据模块
Real Market Data Module for Gate.io Integration
"""

import threading
import time
from typing import Dict, List, Callable, Optional
from gate_io_client import GateIOClient


class RealMarketDataProvider:
    """实时市场数据提供者"""
    
    def __init__(self, update_interval: int = 5):
        """
        初始化实时数据提供者
        
        Args:
            update_interval: 数据更新间隔(秒)
        """
        self.update_interval = update_interval
        self.client = GateIOClient(testnet=False)  # 使用正式网络获取真实数据
        
        # 数据存储
        self.market_data = {}
        self.price_history = {}
        
        # 监控的交易对
        self.watched_pairs = [
            "BTC_USDT", "ETH_USDT", "SOL_USDT", "BNB_USDT",
            "ADA_USDT", "DOT_USDT", "MATIC_USDT", "AVAX_USDT"
        ]
        
        # 控制变量
        self.running = False
        self.thread = None
        
        # 回调函数
        self.update_callbacks = []
    
    def add_update_callback(self, callback: Callable):
        """添加数据更新回调函数"""
        self.update_callbacks.append(callback)
    
    def start(self):
        """启动实时数据更新"""
        if self.running:
            return
        
        print("🚀 启动实时市场数据更新...")
        self.running = True
        self.thread = threading.Thread(target=self._update_loop, daemon=True)
        self.thread.start()
        print("✅ 实时市场数据更新已启动")
    
    def stop(self):
        """停止实时数据更新"""
        if not self.running:
            return
        
        print("🛑 停止实时市场数据更新...")
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        print("✅ 实时市场数据更新已停止")
    
    def _update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                self._fetch_market_data()
                time.sleep(self.update_interval)
            except Exception as e:
                print(f"❌ 数据更新失败: {e}")
                time.sleep(self.update_interval)
    
    def _fetch_market_data(self):
        """获取市场数据"""
        try:
            # 获取所有监控交易对的行情数据
            for pair in self.watched_pairs:
                ticker_data = self.client.get_tickers(pair)
                if isinstance(ticker_data, list) and len(ticker_data) > 0:
                    ticker = ticker_data[0]
                    
                    # 解析数据
                    current_price = float(ticker.get('last', 0))
                    change_24h = float(ticker.get('change_percentage', 0))
                    volume_24h = float(ticker.get('base_volume', 0))
                    high_24h = float(ticker.get('high_24h', 0))
                    low_24h = float(ticker.get('low_24h', 0))
                    
                    # 存储数据
                    self.market_data[pair] = {
                        'price': current_price,
                        'change': change_24h,
                        'volume': volume_24h,
                        'high_24h': high_24h,
                        'low_24h': low_24h,
                        'timestamp': time.time()
                    }
                    
                    # 存储价格历史
                    if pair not in self.price_history:
                        self.price_history[pair] = []
                    
                    self.price_history[pair].append({
                        'price': current_price,
                        'timestamp': time.time()
                    })
                    
                    # 保持最近100个价格点
                    if len(self.price_history[pair]) > 100:
                        self.price_history[pair] = self.price_history[pair][-100:]
            
            # 调用回调函数
            for callback in self.update_callbacks:
                try:
                    callback(self.market_data)
                except Exception as e:
                    print(f"⚠️ 回调函数执行失败: {e}")
            
            print(f"📊 市场数据更新完成 - {len(self.market_data)} 个交易对")
            
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
    
    def get_price(self, symbol: str) -> float:
        """获取指定币种的当前价格"""
        pair = f"{symbol}_USDT"
        if pair in self.market_data:
            return self.market_data[pair]['price']
        return 0.0
    
    def get_change_24h(self, symbol: str) -> float:
        """获取指定币种的24小时涨跌幅"""
        pair = f"{symbol}_USDT"
        if pair in self.market_data:
            return self.market_data[pair]['change']
        return 0.0
    
    def get_volume_24h(self, symbol: str) -> float:
        """获取指定币种的24小时成交量"""
        pair = f"{symbol}_USDT"
        if pair in self.market_data:
            return self.market_data[pair]['volume']
        return 0.0
    
    def get_market_data(self, symbol: str) -> Optional[Dict]:
        """获取指定币种的完整市场数据"""
        pair = f"{symbol}_USDT"
        return self.market_data.get(pair)
    
    def get_all_market_data(self) -> Dict:
        """获取所有市场数据"""
        return self.market_data.copy()
    
    def get_price_history(self, symbol: str, limit: int = 20) -> List[Dict]:
        """获取指定币种的价格历史"""
        pair = f"{symbol}_USDT"
        if pair in self.price_history:
            return self.price_history[pair][-limit:]
        return []
    
    def calculate_volatility(self, symbol: str, period: int = 20) -> float:
        """计算指定币种的价格波动率"""
        history = self.get_price_history(symbol, period)
        if len(history) < 2:
            return 0.0
        
        prices = [item['price'] for item in history]
        
        # 计算价格变化率
        changes = []
        for i in range(1, len(prices)):
            change = (prices[i] - prices[i-1]) / prices[i-1]
            changes.append(change)
        
        if not changes:
            return 0.0
        
        # 计算标准差作为波动率
        mean_change = sum(changes) / len(changes)
        variance = sum((x - mean_change) ** 2 for x in changes) / len(changes)
        volatility = variance ** 0.5
        
        return volatility * 100  # 转换为百分比
    
    def is_data_fresh(self, symbol: str, max_age: int = 60) -> bool:
        """检查数据是否新鲜"""
        pair = f"{symbol}_USDT"
        if pair not in self.market_data:
            return False
        
        data_age = time.time() - self.market_data[pair]['timestamp']
        return data_age <= max_age
    
    def get_status(self) -> Dict:
        """获取数据提供者状态"""
        return {
            'running': self.running,
            'pairs_count': len(self.market_data),
            'update_interval': self.update_interval,
            'last_update': max([data.get('timestamp', 0) for data in self.market_data.values()]) if self.market_data else 0
        }


class MarketDataAdapter:
    """市场数据适配器 - 将真实数据适配到现货系统"""
    
    def __init__(self):
        """初始化适配器"""
        self.data_provider = RealMarketDataProvider(update_interval=3)
        self.spot_pairs = {}
        
        # 初始化现货交易对数据结构
        self._initialize_spot_pairs()
        
        # 添加数据更新回调
        self.data_provider.add_update_callback(self._update_spot_pairs)
    
    def _initialize_spot_pairs(self):
        """初始化现货交易对数据结构"""
        symbols = ["BTC", "ETH", "SOL", "BNB", "ADA", "DOT", "MATIC", "AVAX"]
        
        for symbol in symbols:
            self.spot_pairs[f"{symbol}/USDT"] = {
                'price': 0.0,
                'change': 0.0,
                'volume': 0.0,
                'high_24h': 0.0,
                'low_24h': 0.0,
                'last_update': 0
            }
    
    def _update_spot_pairs(self, market_data: Dict):
        """更新现货交易对数据"""
        for gate_pair, data in market_data.items():
            # 转换Gate.io格式到系统格式
            symbol = gate_pair.replace('_', '/')
            
            if symbol in self.spot_pairs:
                self.spot_pairs[symbol].update({
                    'price': data['price'],
                    'change': data['change'],
                    'volume': data['volume'],
                    'high_24h': data['high_24h'],
                    'low_24h': data['low_24h'],
                    'last_update': data['timestamp']
                })
    
    def start(self):
        """启动市场数据适配器"""
        print("🔌 启动市场数据适配器...")
        self.data_provider.start()
        
        # 等待首次数据更新
        time.sleep(5)
        print("✅ 市场数据适配器启动完成")
    
    def stop(self):
        """停止市场数据适配器"""
        print("🔌 停止市场数据适配器...")
        self.data_provider.stop()
        print("✅ 市场数据适配器已停止")
    
    def get_spot_pairs(self) -> Dict:
        """获取现货交易对数据"""
        return self.spot_pairs.copy()
    
    def get_price(self, symbol: str) -> float:
        """获取价格"""
        return self.data_provider.get_price(symbol)
    
    def get_market_status(self) -> str:
        """获取市场状态"""
        status = self.data_provider.get_status()
        if status['running'] and status['pairs_count'] > 0:
            return "🟢 实时数据连接正常"
        else:
            return "🔴 数据连接异常"
    
    def is_real_data(self) -> bool:
        """检查是否为真实数据"""
        return True


# 全局实例
market_adapter = MarketDataAdapter()


def get_real_market_data():
    """获取真实市场数据实例"""
    return market_adapter

{"data_mtime": 1748493662, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "sqlite3", "json", "datetime", "typing", "pathlib", "threading", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "os", "types", "typing_extensions"], "hash": "a22296eeaa8148d4284dc84935e251cc9d7634ff", "id": "core.data.database_manager", "ignore_all": true, "interface_hash": "cccd8d200e368820f2c2c3a50cf2efaaba8bfc34", "mtime": 1748490148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\data\\database_manager.py", "plugin_data": null, "size": 21347, "suppressed": [], "version_id": "1.15.0"}
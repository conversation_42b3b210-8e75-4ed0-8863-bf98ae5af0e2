#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级统计验证框架
Statistical Validation Framework for Institutional-Grade Quantitative Trading

基于顶级量化机构的最佳实践，提供严格的统计验证方法
"""

import numpy as np
import pandas as pd
import scipy.stats as stats
from scipy import optimize
from typing import Dict, List, Tuple, Optional, Any
import warnings
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """统计验证结果"""
    strategy_name: str
    sharpe_ratio: float
    information_ratio: float
    calmar_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    statistical_significance: Dict[str, float]
    economic_significance: Dict[str, Any]
    risk_metrics: Dict[str, float]
    capacity_estimate: float
    alpha_decay_rate: float
    validation_score: float
    recommendation: str

class InstitutionalStatisticalValidator:
    """
    机构级统计验证器
    
    基于Renaissance Technologies、Two Sigma等顶级机构的验证标准
    """
    
    def __init__(self, benchmark_return: Optional[pd.Series] = None):
        """
        初始化验证器
        
        Args:
            benchmark_return: 基准收益率序列（如沪深300、标普500等）
        """
        self.benchmark_return = benchmark_return
        self.risk_free_rate = 0.02  # 2%年化无风险利率
        
        # 机构级验证标准
        self.institutional_thresholds = {
            'min_sharpe_ratio': 1.5,      # 最低夏普比率
            'min_information_ratio': 0.8,  # 最低信息比率
            'max_drawdown_limit': 0.15,    # 最大回撤限制
            'min_win_rate': 0.45,          # 最低胜率
            'min_profit_factor': 1.3,      # 最低盈亏比
            'min_t_statistic': 2.0,        # 最低t统计量
            'min_observations': 252,       # 最少观测数（1年日数据）
            'max_alpha_decay': 0.1         # 最大alpha衰减率
        }
    
    def validate_strategy(self, 
                         returns: pd.Series, 
                         strategy_name: str,
                         positions: Optional[pd.Series] = None,
                         turnover: Optional[float] = None) -> ValidationResult:
        """
        对策略进行全面的机构级统计验证
        
        Args:
            returns: 策略收益率序列
            strategy_name: 策略名称
            positions: 持仓序列（可选）
            turnover: 换手率（可选）
            
        Returns:
            ValidationResult: 完整的验证结果
        """
        logger.info(f"开始验证策略: {strategy_name}")
        
        # 1. 基础统计指标
        basic_metrics = self._calculate_basic_metrics(returns)
        
        # 2. 风险调整收益指标
        risk_adjusted_metrics = self._calculate_risk_adjusted_metrics(returns)
        
        # 3. 统计显著性检验
        significance_tests = self._perform_significance_tests(returns)
        
        # 4. 经济显著性分析
        economic_significance = self._analyze_economic_significance(returns, turnover)
        
        # 5. 风险指标分析
        risk_metrics = self._calculate_risk_metrics(returns)
        
        # 6. 容量估算
        capacity_estimate = self._estimate_capacity(returns, positions, turnover)
        
        # 7. Alpha衰减分析
        alpha_decay_rate = self._analyze_alpha_decay(returns)
        
        # 8. 综合评分
        validation_score = self._calculate_validation_score(
            basic_metrics, risk_adjusted_metrics, significance_tests, 
            risk_metrics, alpha_decay_rate
        )
        
        # 9. 生成建议
        recommendation = self._generate_recommendation(validation_score, risk_metrics)
        
        return ValidationResult(
            strategy_name=strategy_name,
            sharpe_ratio=risk_adjusted_metrics['sharpe_ratio'],
            information_ratio=risk_adjusted_metrics['information_ratio'],
            calmar_ratio=risk_adjusted_metrics['calmar_ratio'],
            max_drawdown=risk_metrics['max_drawdown'],
            win_rate=basic_metrics['win_rate'],
            profit_factor=basic_metrics['profit_factor'],
            statistical_significance=significance_tests,
            economic_significance=economic_significance,
            risk_metrics=risk_metrics,
            capacity_estimate=capacity_estimate,
            alpha_decay_rate=alpha_decay_rate,
            validation_score=validation_score,
            recommendation=recommendation
        )
    
    def _calculate_basic_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算基础统计指标"""
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        return {
            'total_return': (1 + returns).prod() - 1,
            'annualized_return': (1 + returns.mean()) ** 252 - 1,
            'volatility': returns.std() * np.sqrt(252),
            'win_rate': len(positive_returns) / len(returns),
            'avg_win': positive_returns.mean() if len(positive_returns) > 0 else 0,
            'avg_loss': negative_returns.mean() if len(negative_returns) > 0 else 0,
            'profit_factor': abs(positive_returns.sum() / negative_returns.sum()) if negative_returns.sum() != 0 else np.inf,
            'skewness': returns.skew(),
            'kurtosis': returns.kurtosis()
        }
    
    def _calculate_risk_adjusted_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算风险调整收益指标"""
        annual_return = (1 + returns.mean()) ** 252 - 1
        annual_vol = returns.std() * np.sqrt(252)
        
        # 夏普比率
        sharpe_ratio = (annual_return - self.risk_free_rate) / annual_vol if annual_vol != 0 else 0
        
        # 信息比率（相对基准）
        if self.benchmark_return is not None:
            excess_returns = returns - self.benchmark_return
            information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() != 0 else 0
        else:
            information_ratio = sharpe_ratio
        
        # Calmar比率
        max_dd = self._calculate_max_drawdown(returns)
        calmar_ratio = annual_return / abs(max_dd) if max_dd != 0 else 0
        
        # Sortino比率
        downside_returns = returns[returns < 0]
        downside_vol = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else annual_vol
        sortino_ratio = (annual_return - self.risk_free_rate) / downside_vol if downside_vol != 0 else 0
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'information_ratio': information_ratio,
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio
        }
    
    def _perform_significance_tests(self, returns: pd.Series) -> Dict[str, float]:
        """执行统计显著性检验"""
        n = len(returns)
        mean_return = returns.mean()
        std_return = returns.std()
        
        # t检验
        t_statistic = mean_return / (std_return / np.sqrt(n)) if std_return != 0 else 0
        t_p_value = 2 * (1 - stats.t.cdf(abs(t_statistic), n - 1))
        
        # Jarque-Bera正态性检验
        jb_statistic, jb_p_value = stats.jarque_bera(returns)
        
        # Ljung-Box自相关检验
        from statsmodels.stats.diagnostic import acorr_ljungbox
        lb_result = acorr_ljungbox(returns, lags=10, return_df=True)
        lb_p_value = lb_result['lb_pvalue'].iloc[-1]
        
        return {
            't_statistic': t_statistic,
            't_p_value': t_p_value,
            'jarque_bera_statistic': jb_statistic,
            'jarque_bera_p_value': jb_p_value,
            'ljung_box_p_value': lb_p_value,
            'is_statistically_significant': t_p_value < 0.05 and abs(t_statistic) > 2.0
        }
    
    def _analyze_economic_significance(self, returns: pd.Series, turnover: Optional[float]) -> Dict[str, Any]:
        """分析经济显著性"""
        annual_return = (1 + returns.mean()) ** 252 - 1
        
        # 交易成本估算
        if turnover is not None:
            # 假设单边交易成本为5个基点
            annual_trading_cost = turnover * 0.0005 * 252
            net_annual_return = annual_return - annual_trading_cost
        else:
            annual_trading_cost = 0
            net_annual_return = annual_return
        
        # 经济显著性阈值（年化收益率至少5%）
        economic_significance_threshold = 0.05
        is_economically_significant = net_annual_return > economic_significance_threshold
        
        return {
            'gross_annual_return': annual_return,
            'estimated_trading_cost': annual_trading_cost,
            'net_annual_return': net_annual_return,
            'is_economically_significant': is_economically_significant,
            'economic_significance_threshold': economic_significance_threshold
        }
    
    def _calculate_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算风险指标"""
        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(returns)
        
        # VaR和CVaR
        var_95 = np.percentile(returns, 5)
        cvar_95 = returns[returns <= var_95].mean()
        
        # 波动率聚类（GARCH效应）
        squared_returns = returns ** 2
        volatility_clustering = squared_returns.autocorr(lag=1)
        
        return {
            'max_drawdown': max_drawdown,
            'var_95': var_95,
            'cvar_95': cvar_95,
            'volatility_clustering': volatility_clustering,
            'tail_ratio': abs(np.percentile(returns, 95) / np.percentile(returns, 5))
        }
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def _estimate_capacity(self, returns: pd.Series, positions: Optional[pd.Series], turnover: Optional[float]) -> float:
        """估算策略容量"""
        # 简化的容量估算模型
        # 基于波动率和换手率
        annual_vol = returns.std() * np.sqrt(252)
        
        if turnover is not None:
            # 高换手率策略容量较小
            base_capacity = 100_000_000  # 1亿基础容量
            capacity_factor = 1 / (1 + turnover * 10)  # 换手率惩罚
            volatility_factor = 1 / (1 + annual_vol * 5)  # 波动率惩罚
            estimated_capacity = base_capacity * capacity_factor * volatility_factor
        else:
            estimated_capacity = 50_000_000  # 默认5000万
        
        return estimated_capacity
    
    def _analyze_alpha_decay(self, returns: pd.Series) -> float:
        """分析Alpha衰减率"""
        # 将收益率序列分为前后两半
        mid_point = len(returns) // 2
        first_half = returns.iloc[:mid_point]
        second_half = returns.iloc[mid_point:]
        
        if len(first_half) > 0 and len(second_half) > 0:
            first_half_sharpe = first_half.mean() / first_half.std() * np.sqrt(252) if first_half.std() != 0 else 0
            second_half_sharpe = second_half.mean() / second_half.std() * np.sqrt(252) if second_half.std() != 0 else 0
            
            # 衰减率 = (后期夏普 - 前期夏普) / 前期夏普
            if first_half_sharpe != 0:
                decay_rate = (second_half_sharpe - first_half_sharpe) / abs(first_half_sharpe)
            else:
                decay_rate = 0
        else:
            decay_rate = 0
        
        return decay_rate
    
    def _calculate_validation_score(self, basic_metrics: Dict, risk_adjusted_metrics: Dict, 
                                  significance_tests: Dict, risk_metrics: Dict, alpha_decay_rate: float) -> float:
        """计算综合验证评分（0-100分）"""
        score = 0
        
        # 夏普比率评分（30分）
        sharpe_score = min(30, (risk_adjusted_metrics['sharpe_ratio'] / 2.0) * 30)
        score += sharpe_score
        
        # 统计显著性评分（20分）
        if significance_tests['is_statistically_significant']:
            score += 20
        
        # 最大回撤评分（20分）
        max_dd = abs(risk_metrics['max_drawdown'])
        dd_score = max(0, 20 * (1 - max_dd / 0.3))  # 30%回撤得0分
        score += dd_score
        
        # 胜率评分（15分）
        win_rate_score = min(15, (basic_metrics['win_rate'] / 0.6) * 15)
        score += win_rate_score
        
        # Alpha衰减评分（15分）
        if alpha_decay_rate > -0.1:  # 衰减不超过10%
            decay_score = 15
        else:
            decay_score = max(0, 15 * (1 + alpha_decay_rate / 0.5))
        score += decay_score
        
        return min(100, score)
    
    def _generate_recommendation(self, validation_score: float, risk_metrics: Dict) -> str:
        """生成投资建议"""
        if validation_score >= 80:
            return "强烈推荐：策略表现优异，符合机构级标准"
        elif validation_score >= 60:
            return "推荐：策略表现良好，建议小规模试运行"
        elif validation_score >= 40:
            return "谨慎：策略存在一定风险，需要进一步优化"
        else:
            return "不推荐：策略风险过高，不符合机构标准"

def generate_validation_report(validation_result: ValidationResult) -> str:
    """生成详细的验证报告"""
    report = f"""
=== 机构级策略验证报告 ===
策略名称: {validation_result.strategy_name}
验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 核心指标 ===
夏普比率: {validation_result.sharpe_ratio:.3f}
信息比率: {validation_result.information_ratio:.3f}
Calmar比率: {validation_result.calmar_ratio:.3f}
最大回撤: {validation_result.max_drawdown:.2%}
胜率: {validation_result.win_rate:.2%}
盈亏比: {validation_result.profit_factor:.3f}

=== 统计显著性 ===
t统计量: {validation_result.statistical_significance['t_statistic']:.3f}
p值: {validation_result.statistical_significance['t_p_value']:.4f}
统计显著: {'是' if validation_result.statistical_significance['is_statistically_significant'] else '否'}

=== 风险分析 ===
VaR(95%): {validation_result.risk_metrics['var_95']:.2%}
CVaR(95%): {validation_result.risk_metrics['cvar_95']:.2%}
尾部比率: {validation_result.risk_metrics['tail_ratio']:.2f}

=== 容量与衰减 ===
估算容量: {validation_result.capacity_estimate:,.0f} 元
Alpha衰减率: {validation_result.alpha_decay_rate:.2%}

=== 综合评估 ===
验证评分: {validation_result.validation_score:.1f}/100
投资建议: {validation_result.recommendation}

=== 经济显著性 ===
年化收益率: {validation_result.economic_significance['gross_annual_return']:.2%}
净收益率: {validation_result.economic_significance['net_annual_return']:.2%}
经济显著: {'是' if validation_result.economic_significance['is_economically_significant'] else '否'}
"""
    return report

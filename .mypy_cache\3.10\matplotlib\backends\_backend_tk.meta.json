{"data_mtime": 1748490921, "dep_lines": [25, 6, 10, 11, 12, 13, 16, 16, 19, 19, 19, 19, 20, 24, 25, 1, 2, 3, 4, 5, 6, 7, 8, 9, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 20, 10, 10, 5, 10, 10, 20, 10, 10, 10, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.backends._tkagg", "os.path", "tkinter.filedialog", "tkinter.font", "tkinter.messagebox", "tkinter.simpledialog", "PIL.Image", "PIL.ImageTk", "matplotlib._api", "matplotlib.backend_tools", "matplotlib.cbook", "matplotlib._c_internal_utils", "matplotlib.backend_bases", "matplotlib._pylab_helpers", "matplotlib.backends", "uuid", "weakref", "contextlib", "logging", "math", "os", "pathlib", "sys", "tkinter", "numpy", "PIL", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "PIL.ImageFile", "_frozen_importlib", "_tkinter", "_typeshed", "abc", "enum", "matplotlib.backend_managers", "numpy._core", "numpy._core.multiarray", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "types", "typing", "typing_extensions"], "hash": "9eb7d10330e801b59bbd7babcd9ca8a50b4d4d26", "id": "matplotlib.backends._backend_tk", "ignore_all": true, "interface_hash": "b489dbe4d5e8c359d5a11266e80e817ab9b183ba", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py", "plugin_data": null, "size": 43291, "suppressed": [], "version_id": "1.15.0"}
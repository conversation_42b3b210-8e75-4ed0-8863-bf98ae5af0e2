#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时交易执行器
Live Trading Executor

连接交易所API，执行实时交易策略
"""

import ccxt
import pandas as pd
import numpy as np
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from trading_strategies import CryptoBreakoutStrategy, CryptoGridStrategy, CryptoMomentumStrategy, StrategyPortfolio

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingExecutor:
    """
    实时交易执行器
    
    连接交易所，获取实时数据，执行交易策略
    """
    
    def __init__(self, exchange_config: Dict):
        """初始化交易执行器"""
        self.exchange_config = exchange_config
        self.exchange = None
        self.portfolio = None
        self.positions = {}
        self.orders = {}
        self.balance = {}
        
        # 交易状态
        self.is_trading = False
        self.last_update = None
        
        # 风险控制
        self.daily_pnl = 0
        self.total_pnl = 0
        self.daily_loss_limit = 300  # USDT
        self.total_loss_limit = 1500  # USDT
        
        logger.info("实时交易执行器初始化完成")
    
    def connect_exchange(self) -> bool:
        """连接交易所"""
        try:
            # 初始化币安交易所
            self.exchange = ccxt.binance({
                'apiKey': self.exchange_config.get('api_key', ''),
                'secret': self.exchange_config.get('secret', ''),
                'sandbox': self.exchange_config.get('sandbox', True),  # 默认沙盒模式
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot'  # 现货交易
                }
            })
            
            # 测试连接
            balance = self.exchange.fetch_balance()
            logger.info(f"成功连接到币安交易所")
            logger.info(f"USDT余额: {balance.get('USDT', {}).get('free', 0):.2f}")
            
            self.balance = balance
            return True
            
        except Exception as e:
            logger.error(f"连接交易所失败: {e}")
            return False
    
    def setup_portfolio(self) -> None:
        """设置策略组合"""
        # 获取可用资金
        usdt_balance = self.balance.get('USDT', {}).get('free', 0)
        
        if usdt_balance < 100:
            logger.warning(f"USDT余额不足: {usdt_balance}")
            usdt_balance = 10000  # 演示用
        
        # 创建策略组合
        self.portfolio = StrategyPortfolio(total_capital=usdt_balance)
        
        # 添加策略
        breakout = CryptoBreakoutStrategy()
        self.portfolio.add_strategy(breakout, 0.40, ['BTC/USDT', 'ETH/USDT'])
        
        grid = CryptoGridStrategy()
        self.portfolio.add_strategy(grid, 0.35, ['BTC/USDT', 'ETH/USDT'])
        
        momentum = CryptoMomentumStrategy()
        self.portfolio.add_strategy(momentum, 0.25, ['BTC/USDT', 'ETH/USDT'])
        
        logger.info(f"策略组合设置完成，总资金: {usdt_balance:.2f} USDT")
    
    def fetch_market_data(self, symbol: str, timeframe: str = '15m', limit: int = 100) -> pd.DataFrame:
        """获取市场数据"""
        try:
            # 获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            return df
            
        except Exception as e:
            logger.error(f"获取市场数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_signals(self) -> Dict:
        """计算交易信号"""
        try:
            # 获取所有交易对的数据
            market_data = {}
            
            for strategy_name, config in self.portfolio.strategies.items():
                for pair in config['pairs']:
                    if pair not in market_data:
                        # 根据策略选择时间周期
                        timeframe = '15m' if 'Breakout' in strategy_name else '1h'
                        df = self.fetch_market_data(pair, timeframe)
                        
                        if not df.empty:
                            market_data[pair] = df
            
            # 计算组合信号
            if market_data:
                signals = self.portfolio.calculate_portfolio_signals(market_data)
                return signals
            else:
                logger.warning("无法获取市场数据")
                return {}
                
        except Exception as e:
            logger.error(f"计算信号失败: {e}")
            return {}
    
    def execute_order(self, symbol: str, side: str, amount: float, order_type: str = 'market') -> Optional[Dict]:
        """执行订单"""
        try:
            # 风险检查
            if not self.risk_check():
                logger.warning("风险检查未通过，停止交易")
                return None
            
            # 获取当前价格
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # 计算订单金额
            if side == 'buy':
                # 买入：使用USDT金额
                order_amount = amount / current_price
            else:
                # 卖出：使用币的数量
                order_amount = amount
            
            # 执行订单
            order = self.exchange.create_order(
                symbol=symbol,
                type=order_type,
                side=side,
                amount=order_amount
            )
            
            logger.info(f"订单执行成功: {side} {order_amount:.6f} {symbol} @ {current_price:.2f}")
            
            # 记录订单
            self.orders[order['id']] = order
            
            return order
            
        except Exception as e:
            logger.error(f"执行订单失败: {e}")
            return None
    
    def risk_check(self) -> bool:
        """风险检查"""
        # 检查日亏损限制
        if self.daily_pnl < -self.daily_loss_limit:
            logger.warning(f"触及日亏损限制: {self.daily_pnl:.2f} USDT")
            return False
        
        # 检查总亏损限制
        if self.total_pnl < -self.total_loss_limit:
            logger.warning(f"触及总亏损限制: {self.total_pnl:.2f} USDT")
            return False
        
        return True
    
    def update_positions(self) -> None:
        """更新持仓信息"""
        try:
            balance = self.exchange.fetch_balance()
            self.balance = balance
            
            # 更新持仓
            for currency, info in balance.items():
                if info['total'] > 0 and currency != 'USDT':
                    self.positions[currency] = {
                        'amount': info['total'],
                        'free': info['free'],
                        'used': info['used']
                    }
            
        except Exception as e:
            logger.error(f"更新持仓失败: {e}")
    
    def process_signals(self, signals: Dict) -> List[Dict]:
        """处理交易信号"""
        execution_plan = []
        
        try:
            for strategy_name, strategy_signals in signals.items():
                config = self.portfolio.strategies[strategy_name]
                
                for pair, signal in strategy_signals.items():
                    if isinstance(signal, list):  # 网格策略
                        for grid_signal in signal:
                            execution_plan.append({
                                'strategy': strategy_name,
                                'symbol': pair,
                                'side': grid_signal['action'],
                                'amount': grid_signal['size'],
                                'price': grid_signal.get('price'),
                                'type': 'limit'
                            })
                    
                    elif signal != 0:  # 其他策略
                        side = 'buy' if signal > 0 else 'sell'
                        
                        # 计算交易金额
                        if side == 'buy':
                            amount = config['capital'] * 0.15  # 15%仓位
                        else:
                            # 卖出当前持仓
                            currency = pair.split('/')[0]
                            amount = self.positions.get(currency, {}).get('free', 0)
                        
                        if amount > 10:  # 最小交易金额
                            execution_plan.append({
                                'strategy': strategy_name,
                                'symbol': pair,
                                'side': side,
                                'amount': amount,
                                'type': 'market'
                            })
            
            return execution_plan
            
        except Exception as e:
            logger.error(f"处理信号失败: {e}")
            return []
    
    def run_trading_cycle(self) -> None:
        """运行一个交易周期"""
        try:
            logger.info("开始交易周期")
            
            # 更新持仓
            self.update_positions()
            
            # 计算信号
            signals = self.calculate_signals()
            
            if signals:
                logger.info(f"获得信号: {signals}")
                
                # 处理信号
                execution_plan = self.process_signals(signals)
                
                # 执行交易
                for plan in execution_plan:
                    logger.info(f"执行计划: {plan}")
                    
                    order = self.execute_order(
                        symbol=plan['symbol'],
                        side=plan['side'],
                        amount=plan['amount'],
                        order_type=plan['type']
                    )
                    
                    if order:
                        logger.info(f"订单成功: {order['id']}")
                    
                    # 避免频率限制
                    time.sleep(1)
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"交易周期执行失败: {e}")
    
    def start_trading(self, interval: int = 60) -> None:
        """开始自动交易"""
        logger.info("开始自动交易")
        self.is_trading = True
        
        while self.is_trading:
            try:
                # 运行交易周期
                self.run_trading_cycle()
                
                # 等待下一个周期
                logger.info(f"等待 {interval} 秒后进行下一个周期")
                time.sleep(interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号")
                self.stop_trading()
                break
            except Exception as e:
                logger.error(f"交易循环错误: {e}")
                time.sleep(10)  # 错误后等待10秒
    
    def stop_trading(self) -> None:
        """停止交易"""
        logger.info("停止自动交易")
        self.is_trading = False
    
    def get_status(self) -> Dict:
        """获取交易状态"""
        return {
            'is_trading': self.is_trading,
            'last_update': self.last_update,
            'balance': self.balance.get('USDT', {}).get('total', 0),
            'positions': self.positions,
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl,
            'orders_count': len(self.orders)
        }

def create_demo_config():
    """创建演示配置"""
    return {
        'api_key': 'your_binance_api_key',
        'secret': 'your_binance_secret',
        'sandbox': True,  # 沙盒模式，安全测试
        'exchange': 'binance'
    }

def main():
    """主函数 - 演示如何使用"""
    print("🚀 实时交易执行器启动")
    print("=" * 50)
    
    # 创建配置
    config = create_demo_config()
    
    # 创建执行器
    executor = LiveTradingExecutor(config)
    
    print("步骤1: 连接交易所...")
    if not executor.connect_exchange():
        print("❌ 连接失败，请检查API配置")
        return
    
    print("✅ 交易所连接成功")
    
    print("\n步骤2: 设置策略组合...")
    executor.setup_portfolio()
    print("✅ 策略组合设置完成")
    
    print("\n步骤3: 获取市场数据和信号...")
    signals = executor.calculate_signals()
    
    if signals:
        print("✅ 信号计算成功")
        for strategy, strategy_signals in signals.items():
            print(f"  {strategy}: {strategy_signals}")
    else:
        print("⚠️ 暂无交易信号")
    
    print("\n步骤4: 显示当前状态...")
    status = executor.get_status()
    print(f"账户余额: {status['balance']:.2f} USDT")
    print(f"持仓数量: {len(status['positions'])}")
    print(f"当日盈亏: {status['daily_pnl']:.2f} USDT")
    
    print("\n" + "=" * 50)
    print("💡 使用说明:")
    print("1. 配置真实的API密钥")
    print("2. 设置 sandbox=False 进入实盘")
    print("3. 调用 executor.start_trading() 开始自动交易")
    print("4. 使用 Ctrl+C 停止交易")
    
    # 演示单次交易周期
    print("\n🔄 演示单次交易周期...")
    executor.run_trading_cycle()
    
    print("\n✅ 演示完成！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级量化交易框架管理器
Institutional Quantitative Trading Framework Manager

整合统计验证、风险因子分析、性能监控等模块，提供统一的机构级管理接口
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from statistical_validation import InstitutionalStatisticalValidator, ValidationResult
from risk_factor_analysis import InstitutionalRiskFactorAnalyzer, RiskFactorAnalysisResult, create_factor_returns_template
from performance_monitoring import InstitutionalPerformanceMonitor, MonitoringConfig, PerformanceAlert

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InstitutionalFrameworkManager:
    """
    机构级量化交易框架管理器
    
    提供策略的全生命周期管理：验证 -> 部署 -> 监控 -> 优化
    """
    
    def __init__(self, 
                 benchmark_data: Optional[pd.Series] = None,
                 factor_data: Optional[pd.DataFrame] = None,
                 monitoring_config: Optional[MonitoringConfig] = None):
        """
        初始化框架管理器
        
        Args:
            benchmark_data: 基准收益率数据
            factor_data: 因子收益率数据
            monitoring_config: 监控配置
        """
        logger.info("初始化机构级量化交易框架")
        
        # 初始化各个模块
        self.validator = InstitutionalStatisticalValidator(benchmark_data)
        self.risk_analyzer = InstitutionalRiskFactorAnalyzer()
        self.monitor = InstitutionalPerformanceMonitor(monitoring_config or MonitoringConfig())
        
        # 数据管理
        self.benchmark_data = benchmark_data
        self.factor_data = factor_data or create_factor_returns_template()
        
        # 策略管理
        self.strategies: Dict[str, Dict] = {}
        self.validation_results: Dict[str, ValidationResult] = {}
        self.risk_analysis_results: Dict[str, RiskFactorAnalysisResult] = {}
        
        # 状态管理
        self.framework_status = "INITIALIZED"
        
        logger.info("机构级框架初始化完成")
    
    def validate_strategy(self, 
                         strategy_name: str,
                         returns: pd.Series,
                         positions: Optional[pd.Series] = None,
                         turnover: Optional[float] = None) -> ValidationResult:
        """
        验证策略是否符合机构级标准
        
        Args:
            strategy_name: 策略名称
            returns: 策略收益率序列
            positions: 持仓序列（可选）
            turnover: 换手率（可选）
            
        Returns:
            ValidationResult: 验证结果
        """
        logger.info(f"开始验证策略: {strategy_name}")
        
        # 执行统计验证
        validation_result = self.validator.validate_strategy(
            returns=returns,
            strategy_name=strategy_name,
            positions=positions,
            turnover=turnover
        )
        
        # 存储验证结果
        self.validation_results[strategy_name] = validation_result
        
        # 更新策略状态
        if strategy_name not in self.strategies:
            self.strategies[strategy_name] = {}
        
        self.strategies[strategy_name].update({
            'validation_status': 'COMPLETED',
            'validation_score': validation_result.validation_score,
            'validation_time': datetime.now(),
            'recommendation': validation_result.recommendation
        })
        
        logger.info(f"策略 {strategy_name} 验证完成，评分: {validation_result.validation_score:.1f}")
        return validation_result
    
    def analyze_risk_factors(self, 
                           strategy_name: str,
                           returns: pd.Series,
                           custom_factors: Optional[pd.DataFrame] = None) -> RiskFactorAnalysisResult:
        """
        分析策略的风险因子暴露
        
        Args:
            strategy_name: 策略名称
            returns: 策略收益率序列
            custom_factors: 自定义因子数据（可选）
            
        Returns:
            RiskFactorAnalysisResult: 风险因子分析结果
        """
        logger.info(f"开始分析策略 {strategy_name} 的风险因子")
        
        # 使用自定义因子或默认因子
        factors_to_use = custom_factors if custom_factors is not None else self.factor_data
        
        # 数据对齐
        common_index = returns.index.intersection(factors_to_use.index)
        if len(common_index) < 60:
            logger.warning(f"策略 {strategy_name} 与因子数据重叠不足60天，使用模拟数据")
            # 创建模拟因子数据
            factors_to_use = self._create_simulated_factors(returns.index)
        
        # 执行风险因子分析
        risk_result = self.risk_analyzer.analyze_risk_factors(
            strategy_returns=returns,
            factor_returns=factors_to_use,
            strategy_name=strategy_name
        )
        
        # 存储分析结果
        self.risk_analysis_results[strategy_name] = risk_result
        
        # 更新策略状态
        if strategy_name not in self.strategies:
            self.strategies[strategy_name] = {}
        
        self.strategies[strategy_name].update({
            'risk_analysis_status': 'COMPLETED',
            'r_squared': risk_result.r_squared,
            'tracking_error': risk_result.tracking_error,
            'risk_analysis_time': datetime.now()
        })
        
        logger.info(f"策略 {strategy_name} 风险因子分析完成，R²: {risk_result.r_squared:.3f}")
        return risk_result
    
    def deploy_strategy(self, strategy_name: str, returns: pd.Series) -> bool:
        """
        部署策略到监控系统
        
        Args:
            strategy_name: 策略名称
            returns: 历史收益率数据
            
        Returns:
            bool: 部署是否成功
        """
        logger.info(f"部署策略到监控系统: {strategy_name}")
        
        # 检查策略是否已验证
        if strategy_name not in self.validation_results:
            logger.warning(f"策略 {strategy_name} 未经验证，建议先进行验证")
        
        # 注册到监控系统
        self.monitor.register_strategy(strategy_name, returns)
        
        # 更新策略状态
        if strategy_name not in self.strategies:
            self.strategies[strategy_name] = {}
        
        self.strategies[strategy_name].update({
            'deployment_status': 'DEPLOYED',
            'deployment_time': datetime.now(),
            'monitoring_active': True
        })
        
        logger.info(f"策略 {strategy_name} 部署成功")
        return True
    
    def update_strategy_performance(self, strategy_name: str, new_returns: pd.Series):
        """
        更新策略性能数据
        
        Args:
            strategy_name: 策略名称
            new_returns: 新的收益率数据
        """
        if strategy_name not in self.strategies:
            logger.warning(f"策略 {strategy_name} 未注册，自动注册")
            self.deploy_strategy(strategy_name, new_returns)
        else:
            self.monitor.update_performance(strategy_name, new_returns)
    
    def get_strategy_comprehensive_report(self, strategy_name: str) -> str:
        """
        获取策略的综合报告
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            str: 综合报告
        """
        if strategy_name not in self.strategies:
            return f"策略 {strategy_name} 未找到"
        
        report = f"""
=== 机构级策略综合报告 ===
策略名称: {strategy_name}
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 策略状态 ==="""
        
        strategy_info = self.strategies[strategy_name]
        for key, value in strategy_info.items():
            report += f"\n{key}: {value}"
        
        # 验证结果
        if strategy_name in self.validation_results:
            validation = self.validation_results[strategy_name]
            report += f"""

=== 统计验证结果 ===
验证评分: {validation.validation_score:.1f}/100
夏普比率: {validation.sharpe_ratio:.3f}
最大回撤: {validation.max_drawdown:.2%}
胜率: {validation.win_rate:.2%}
统计显著性: {'是' if validation.statistical_significance['is_statistically_significant'] else '否'}
投资建议: {validation.recommendation}"""
        
        # 风险因子分析
        if strategy_name in self.risk_analysis_results:
            risk_analysis = self.risk_analysis_results[strategy_name]
            report += f"""

=== 风险因子分析 ===
模型拟合度(R²): {risk_analysis.r_squared:.3f}
跟踪误差: {risk_analysis.tracking_error:.2%}
信息比率: {risk_analysis.information_ratio:.3f}
系统性收益: {risk_analysis.systematic_return:.2%}
特异性收益(Alpha): {risk_analysis.idiosyncratic_return:.2%}

主要因子暴露:"""
            for exposure in risk_analysis.factor_exposures[:5]:  # 显示前5个因子
                significance = "***" if exposure.p_value < 0.01 else "**" if exposure.p_value < 0.05 else "*" if exposure.p_value < 0.1 else ""
                report += f"\n  {exposure.factor_name}: {exposure.exposure:.3f}{significance}"
        
        # 监控状态
        monitoring_status = self.monitor.get_strategy_status(strategy_name)
        if 'latest_metrics' in monitoring_status and monitoring_status['latest_metrics']:
            latest = monitoring_status['latest_metrics']
            report += f"""

=== 最新监控数据 ===
当日收益: {latest.daily_return:.2%}
累计收益: {latest.cumulative_return:.2%}
当前夏普比率: {latest.sharpe_ratio:.3f}
当前最大回撤: {latest.max_drawdown:.2%}
Alpha衰减指标: {latest.alpha_decay_indicator:.2%}
性能趋势: {monitoring_status['performance_trend']}
警报数量: {monitoring_status['total_alerts']}"""
        
        return report
    
    def get_framework_dashboard(self) -> Dict[str, Any]:
        """
        获取框架仪表板数据
        
        Returns:
            Dict: 仪表板数据
        """
        total_strategies = len(self.strategies)
        validated_strategies = len(self.validation_results)
        deployed_strategies = len([s for s in self.strategies.values() if s.get('deployment_status') == 'DEPLOYED'])
        
        # 获取所有警报
        all_alerts = self.monitor.alerts
        high_severity_alerts = len([a for a in all_alerts if a.severity in ['HIGH', 'CRITICAL']])
        
        # 平均性能指标
        if self.validation_results:
            avg_validation_score = np.mean([v.validation_score for v in self.validation_results.values()])
            avg_sharpe_ratio = np.mean([v.sharpe_ratio for v in self.validation_results.values()])
        else:
            avg_validation_score = avg_sharpe_ratio = 0
        
        return {
            'framework_status': self.framework_status,
            'total_strategies': total_strategies,
            'validated_strategies': validated_strategies,
            'deployed_strategies': deployed_strategies,
            'avg_validation_score': avg_validation_score,
            'avg_sharpe_ratio': avg_sharpe_ratio,
            'total_alerts': len(all_alerts),
            'high_severity_alerts': high_severity_alerts,
            'last_update': datetime.now()
        }
    
    def _create_simulated_factors(self, index: pd.Index) -> pd.DataFrame:
        """创建模拟因子数据"""
        np.random.seed(42)
        n_days = len(index)
        
        factor_data = {
            'market': np.random.normal(0.0005, 0.015, n_days),
            'size': np.random.normal(0.0002, 0.008, n_days),
            'value': np.random.normal(0.0001, 0.006, n_days),
            'momentum': np.random.normal(0.0003, 0.010, n_days),
            'quality': np.random.normal(0.0001, 0.005, n_days)
        }
        
        return pd.DataFrame(factor_data, index=index)
    
    def export_results(self, output_dir: str = "institutional_results"):
        """
        导出所有分析结果
        
        Args:
            output_dir: 输出目录
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # 导出验证结果
        if self.validation_results:
            validation_summary = []
            for name, result in self.validation_results.items():
                validation_summary.append({
                    'strategy_name': name,
                    'validation_score': result.validation_score,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'recommendation': result.recommendation
                })
            
            pd.DataFrame(validation_summary).to_csv(
                os.path.join(output_dir, 'validation_results.csv'), 
                index=False
            )
        
        # 导出风险分析结果
        if self.risk_analysis_results:
            risk_summary = []
            for name, result in self.risk_analysis_results.items():
                risk_summary.append({
                    'strategy_name': name,
                    'r_squared': result.r_squared,
                    'tracking_error': result.tracking_error,
                    'systematic_return': result.systematic_return,
                    'idiosyncratic_return': result.idiosyncratic_return
                })
            
            pd.DataFrame(risk_summary).to_csv(
                os.path.join(output_dir, 'risk_analysis_results.csv'), 
                index=False
            )
        
        # 导出监控警报
        if self.monitor.alerts:
            alerts_data = []
            for alert in self.monitor.alerts:
                alerts_data.append({
                    'strategy_name': alert.strategy_name,
                    'alert_type': alert.alert_type,
                    'severity': alert.severity,
                    'message': alert.message,
                    'timestamp': alert.timestamp,
                    'metric_value': alert.metric_value,
                    'threshold': alert.threshold
                })
            
            pd.DataFrame(alerts_data).to_csv(
                os.path.join(output_dir, 'monitoring_alerts.csv'), 
                index=False
            )
        
        logger.info(f"分析结果已导出到: {output_dir}")

# 便捷函数
def create_institutional_framework(benchmark_file: Optional[str] = None,
                                 factor_file: Optional[str] = None) -> InstitutionalFrameworkManager:
    """
    创建机构级框架的便捷函数
    
    Args:
        benchmark_file: 基准数据文件路径
        factor_file: 因子数据文件路径
        
    Returns:
        InstitutionalFrameworkManager: 框架管理器实例
    """
    benchmark_data = None
    factor_data = None
    
    if benchmark_file and os.path.exists(benchmark_file):
        benchmark_data = pd.read_csv(benchmark_file, index_col=0, parse_dates=True).squeeze()
    
    if factor_file and os.path.exists(factor_file):
        factor_data = pd.read_csv(factor_file, index_col=0, parse_dates=True)
    
    return InstitutionalFrameworkManager(
        benchmark_data=benchmark_data,
        factor_data=factor_data
    )

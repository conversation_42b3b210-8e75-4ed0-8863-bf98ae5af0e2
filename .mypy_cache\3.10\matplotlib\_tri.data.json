{".class": "MypyFile", "_fullname": "matplotlib._tri", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "TrapezoidMapTriFinder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._tri.TrapezoidMapTriFinder", "name": "TrapezoidMapTriFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "matplotlib._tri.TrapezoidMapTriFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._tri", "mro": ["matplotlib._tri.TrapezoidMapTriFinder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TrapezoidMapTriFinder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "arg_types": ["matplotlib._tri.TrapezoidMapTriFinder", "matplotlib._tri.Triangulation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TrapezoidMapTriFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TrapezoidMapTriFinder.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "arg_types": ["matplotlib._tri.TrapezoidMapTriFinder", {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_many of TrapezoidMapTriFinder", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tree_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TrapezoidMapTriFinder.get_tree_stats", "name": "get_tree_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._tri.TrapezoidMapTriFinder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_stats of TrapezoidMapTriFinder", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TrapezoidMapTriFinder.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._tri.TrapezoidMapTriFinder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of TrapezoidMapTriFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TrapezoidMapTriFinder.print_tree", "name": "print_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._tri.TrapezoidMapTriFinder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_tree of TrapezoidMapTriFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._tri.TrapezoidMapTriFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._tri.TrapezoidMapTriFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TriContourGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._tri.TriContourGenerator", "name": "TriContourGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "matplotlib._tri.TriContourGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._tri", "mro": ["matplotlib._tri.TriContourGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "triangulation", "z"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TriContourGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "triangulation", "z"], "arg_types": ["matplotlib._tri.TriContourGenerator", "matplotlib._tri.Triangulation", {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TriContourGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_contour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TriContourGenerator.create_contour", "name": "create_contour", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["matplotlib._tri.TriContourGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_contour of TriContourGenerator", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_filled_contour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lower_level", "upper_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.TriContourGenerator.create_filled_contour", "name": "create_filled_contour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lower_level", "upper_level"], "arg_types": ["matplotlib._tri.TriContourGenerator", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_filled_contour of TriContourGenerator", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._tri.TriContourGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._tri.TriContourGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Triangulation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib._tri.Triangulation", "name": "Triangulation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "matplotlib._tri.Triangulation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib._tri", "mro": ["matplotlib._tri.Triangulation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "triangles", "mask", "edges", "neighbors", "correct_triangle_orientation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.Triangulation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "triangles", "mask", "edges", "neighbors", "correct_triangle_orientation"], "arg_types": ["matplotlib._tri.Triangulation", {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Triangulation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "calculate_plane_coefficients": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "z"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.Triangulation.calculate_plane_coefficients", "name": "calculate_plane_coefficients", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "z"], "arg_types": ["matplotlib._tri.Triangulation", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_plane_coefficients of Triangulation", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.Triangulation.get_edges", "name": "get_edges", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._tri.Triangulation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_edges of Triangulation", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_neighbors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.Triangulation.get_neighbors", "name": "get_neighbors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib._tri.Triangulation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_neighbors of Triangulation", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib._tri.Triangulation.set_mask", "name": "set_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mask"], "arg_types": ["matplotlib._tri.Triangulation", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_mask of Triangulation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib._tri.Triangulation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib._tri.Triangulation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib._tri.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\_tri.pyi"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gate.io API测试脚本
Test Gate.io API Connection and Data Retrieval
"""

import os
import sys
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.gate_io_client import (GateIOClient,
                                                    GateIODataManager)


def test_rest_api():
    """测试REST API"""
    print("🔍 测试Gate.io REST API...")

    # 创建客户端 (使用公开API，不需要密钥)
    client = GateIOClient(testnet=True)

    try:
        # 测试获取交易对
        print("📊 获取交易对信息...")
        pairs = client.get_currency_pairs()
        if isinstance(pairs, list) and len(pairs) > 0:
            print(f"✅ 成功获取 {len(pairs)} 个交易对")
            # 显示前5个交易对
            for i, pair in enumerate(pairs[:5]):
                print(f"   {i+1}. {pair.get('id', 'N/A')}")
        else:
            print("❌ 获取交易对失败")
            return False

        # 测试获取行情数据
        print("\n📈 获取BTC_USDT行情数据...")
        tickers = client.get_tickers("BTC_USDT")
        if isinstance(tickers, list) and len(tickers) > 0:
            ticker = tickers[0]
            print(f"✅ BTC_USDT价格: {ticker.get('last', 'N/A')}")
            print(f"   24h变化: {ticker.get('change_percentage', 'N/A')}%")
            print(f"   24h成交量: {ticker.get('base_volume', 'N/A')}")
        else:
            print("❌ 获取行情数据失败")
            return False

        # 测试获取订单簿
        print("\n📋 获取BTC_USDT订单簿...")
        orderbook = client.get_order_book("BTC_USDT", limit=5)
        if "asks" in orderbook and "bids" in orderbook:
            print("✅ 订单簿数据:")
            print("   卖单 (Asks):")
            for ask in orderbook["asks"][:3]:
                print(f"     价格: {ask[0]}, 数量: {ask[1]}")
            print("   买单 (Bids):")
            for bid in orderbook["bids"][:3]:
                print(f"     价格: {bid[0]}, 数量: {bid[1]}")
        else:
            print("❌ 获取订单簿失败")
            return False

        # 测试获取K线数据
        print("\n📊 获取BTC_USDT K线数据...")
        candlesticks = client.get_candlesticks(
            "BTC_USDT", interval="1m", limit=5
        )
        if isinstance(candlesticks, list) and len(candlesticks) > 0:
            print(f"✅ 成功获取 {len(candlesticks)} 条K线数据")
            latest = candlesticks[-1]
            print(
                f"   最新K线: 开盘: {latest[1]}, 最高: {latest[3]}, 最低: {latest[4]}, 收盘: {latest[2]}"
            )
        else:
            print("❌ 获取K线数据失败")
            return False

        print("\n🎉 REST API测试全部通过!")
        return True

    except Exception as e:
        print(f"❌ REST API测试失败: {e}")
        return False


def test_websocket_api():
    """测试WebSocket API"""
    print("\n🔍 测试Gate.io WebSocket API...")

    try:
        # 创建数据管理器
        data_manager = GateIODataManager(testnet=True)

        # 数据接收计数器
        ticker_count = 0
        trade_count = 0

        def ticker_callback(currency_pair, data):
            nonlocal ticker_count
            ticker_count += 1
            print(
                f"📈 行情更新 [{ticker_count}]: {currency_pair} = {data.get('last', 'N/A')}"
            )

        def trade_callback(currency_pair, data):
            nonlocal trade_count
            trade_count += 1
            print(
                f"💰 交易更新 [{trade_count}]: {currency_pair} {data.get('side', 'N/A')} {data.get('amount', 'N/A')} @ {data.get('price', 'N/A')}"
            )

        # 添加回调函数
        data_manager.add_ticker_callback(ticker_callback)
        data_manager.add_trade_callback(trade_callback)

        # 启动数据管理器
        data_manager.start()

        # 订阅数据
        print("📡 订阅BTC_USDT和ETH_USDT数据...")
        data_manager.subscribe_tickers(["BTC_USDT", "ETH_USDT"])
        data_manager.subscribe_trades(["BTC_USDT", "ETH_USDT"])

        # 等待数据接收
        print("⏳ 等待实时数据 (30秒)...")
        start_time = time.time()
        while time.time() - start_time < 30:
            time.sleep(1)

            # 检查是否接收到数据
            if ticker_count > 0 and trade_count > 0:
                print(f"\n✅ WebSocket数据接收成功!")
                print(f"   行情更新: {ticker_count} 次")
                print(f"   交易更新: {trade_count} 次")
                break
        else:
            print("\n⚠️ 30秒内未接收到完整数据，但连接正常")

        # 测试数据获取
        print("\n📊 测试数据获取功能...")
        btc_ticker = data_manager.get_ticker("BTC_USDT")
        if btc_ticker:
            print(f"✅ BTC_USDT缓存数据: {btc_ticker.get('last', 'N/A')}")

        btc_trades = data_manager.get_recent_trades("BTC_USDT", limit=3)
        if btc_trades:
            print(f"✅ BTC_USDT最近交易: {len(btc_trades)} 条")

        # 停止数据管理器
        data_manager.stop()

        print("\n🎉 WebSocket API测试完成!")
        return True

    except Exception as e:
        print(f"❌ WebSocket API测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Gate.io API连接测试开始")
    print("=" * 50)

    # 测试REST API
    rest_success = test_rest_api()

    # 测试WebSocket API
    ws_success = test_websocket_api()

    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"   REST API: {'✅ 通过' if rest_success else '❌ 失败'}")
    print(f"   WebSocket API: {'✅ 通过' if ws_success else '❌ 失败'}")

    if rest_success and ws_success:
        print("\n🎊 所有测试通过! Gate.io API连接正常!")
        print("💡 现在可以集成到现货交易系统中")
    else:
        print("\n⚠️ 部分测试失败，请检查网络连接和API配置")

    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

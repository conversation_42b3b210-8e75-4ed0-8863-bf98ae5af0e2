#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多交易所统一接口
Multi-Exchange Unified Interface for Professional Trading
"""

import hashlib
import hmac
import json
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import requests


class ExchangeInterface(ABC):
    """交易所接口基类"""

    def __init__(
        self, api_key: str = "", api_secret: str = "", testnet: bool = True
    ):
        """初始化交易所接口"""
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.name = "Unknown"
        self.base_url = ""

        # 交易所特性
        self.features = {
            "spot_trading": True,
            "futures_trading": False,
            "margin_trading": False,
            "websocket": True,
            "order_types": ["market", "limit"],
        }

        # 费率信息
        self.fees = {"maker": 0.001, "taker": 0.001}  # 0.1%  # 0.1%

    @abstractmethod
    def get_account_balance(self) -> Dict:
        """获取账户余额"""
        pass

    @abstractmethod
    def place_order(
        self,
        symbol: str,
        side: str,
        amount: float,
        price: float = None,
        order_type: str = "market",
    ) -> Dict:
        """下单"""
        pass

    @abstractmethod
    def cancel_order(self, order_id: str, symbol: str = "") -> Dict:
        """取消订单"""
        pass

    @abstractmethod
    def get_order_status(self, order_id: str, symbol: str = "") -> Dict:
        """查询订单状态"""
        pass

    @abstractmethod
    def get_ticker(self, symbol: str) -> Dict:
        """获取行情数据"""
        pass

    @abstractmethod
    def get_orderbook(self, symbol: str, limit: int = 20) -> Dict:
        """获取订单簿"""
        pass

    @abstractmethod
    def get_klines(
        self, symbol: str, interval: str = "1m", limit: int = 100
    ) -> List[Dict]:
        """获取K线数据"""
        pass

    def _request(
        self,
        method: str,
        endpoint: str,
        params: Dict = None,
        data: Dict = None,
        signed: bool = False,
    ) -> Dict:
        """发送HTTP请求"""
        try:
            url = self.base_url + endpoint
            headers = {"Content-Type": "application/json"}

            if signed and self.api_key:
                headers.update(
                    self._get_auth_headers(method, endpoint, params, data)
                )

            if method == "GET":
                response = requests.get(
                    url, params=params, headers=headers, timeout=10
                )
            elif method == "POST":
                response = requests.post(
                    url, json=data, headers=headers, timeout=10
                )
            elif method == "DELETE":
                response = requests.delete(
                    url, params=params, headers=headers, timeout=10
                )
            else:
                return {"error": f"Unsupported method: {method}"}

            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            return {"error": str(e)}

    @abstractmethod
    def _get_auth_headers(
        self,
        method: str,
        endpoint: str,
        params: Dict = None,
        data: Dict = None,
    ) -> Dict:
        """获取认证头"""
        pass

    def normalize_symbol(self, symbol: str) -> str:
        """标准化交易对符号"""
        return symbol.replace("_", "").replace("-", "").upper()

    def get_exchange_info(self) -> Dict:
        """获取交易所信息"""
        return {
            "name": self.name,
            "features": self.features,
            "fees": self.fees,
            "testnet": self.testnet,
        }


class BinanceInterface(ExchangeInterface):
    """Binance交易所接口"""

    def __init__(
        self, api_key: str = "", api_secret: str = "", testnet: bool = True
    ):
        """初始化Binance接口"""
        super().__init__(api_key, api_secret, testnet)
        self.name = "Binance"
        self.base_url = (
            "https://testnet.binance.vision/api/v3"
            if testnet
            else "https://api.binance.com/api/v3"
        )

        self.features = {
            "spot_trading": True,
            "futures_trading": True,
            "margin_trading": True,
            "websocket": True,
            "order_types": [
                "market",
                "limit",
                "stop_loss",
                "take_profit",
                "stop_loss_limit",
                "take_profit_limit",
            ],
        }

        self.fees = {"maker": 0.001, "taker": 0.001}  # 0.1%  # 0.1%

    def get_account_balance(self) -> Dict:
        """获取Binance账户余额"""
        try:
            result = self._request("GET", "/account", signed=True)

            if "error" in result:
                return {"error": result["error"]}

            balances = {}
            for balance in result.get("balances", []):
                asset = balance["asset"]
                free = float(balance["free"])
                locked = float(balance["locked"])

                if free > 0 or locked > 0:
                    balances[asset] = {"available": free, "locked": locked}

            return balances

        except Exception as e:
            return {"error": str(e)}

    def place_order(
        self,
        symbol: str,
        side: str,
        amount: float,
        price: float = None,
        order_type: str = "market",
    ) -> Dict:
        """Binance下单"""
        try:
            symbol = self.normalize_symbol(symbol)

            params = {
                "symbol": symbol,
                "side": side.upper(),
                "type": order_type.upper(),
                "timestamp": int(time.time() * 1000),
            }

            if order_type.lower() == "market":
                if side.lower() == "buy":
                    params["quoteOrderQty"] = str(amount)  # 买入金额
                else:
                    params["quantity"] = str(amount)  # 卖出数量
            else:
                params["quantity"] = str(amount)
                params["price"] = str(price)
                params["timeInForce"] = "GTC"

            result = self._request("POST", "/order", data=params, signed=True)

            if "error" in result:
                return {"success": False, "error": result["error"]}

            return {
                "success": True,
                "order_id": str(result["orderId"]),
                "symbol": result["symbol"],
                "side": result["side"].lower(),
                "amount": float(result.get("executedQty", 0)),
                "price": float(result.get("price", 0)),
                "status": result["status"].lower(),
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def cancel_order(self, order_id: str, symbol: str = "") -> Dict:
        """取消Binance订单"""
        try:
            symbol = self.normalize_symbol(symbol)
            params = {
                "symbol": symbol,
                "orderId": order_id,
                "timestamp": int(time.time() * 1000),
            }

            result = self._request(
                "DELETE", "/order", params=params, signed=True
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}

            return {"success": True, "order_id": str(result["orderId"])}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_order_status(self, order_id: str, symbol: str = "") -> Dict:
        """查询Binance订单状态"""
        try:
            symbol = self.normalize_symbol(symbol)
            params = {
                "symbol": symbol,
                "orderId": order_id,
                "timestamp": int(time.time() * 1000),
            }

            result = self._request("GET", "/order", params=params, signed=True)

            if "error" in result:
                return {"success": False, "error": result["error"]}

            return {"success": True, "order": result}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_ticker(self, symbol: str) -> Dict:
        """获取Binance行情数据"""
        try:
            symbol = self.normalize_symbol(symbol)
            params = {"symbol": symbol}

            result = self._request("GET", "/ticker/24hr", params=params)

            if "error" in result:
                return {"error": result["error"]}

            return {
                "symbol": result["symbol"],
                "price": float(result["lastPrice"]),
                "change": float(result["priceChangePercent"]),
                "volume": float(result["volume"]),
                "high": float(result["highPrice"]),
                "low": float(result["lowPrice"]),
            }

        except Exception as e:
            return {"error": str(e)}

    def get_orderbook(self, symbol: str, limit: int = 20) -> Dict:
        """获取Binance订单簿"""
        try:
            symbol = self.normalize_symbol(symbol)
            params = {"symbol": symbol, "limit": limit}

            result = self._request("GET", "/depth", params=params)

            if "error" in result:
                return {"error": result["error"]}

            return {
                "symbol": symbol,
                "bids": [
                    [float(bid[0]), float(bid[1])] for bid in result["bids"]
                ],
                "asks": [
                    [float(ask[0]), float(ask[1])] for ask in result["asks"]
                ],
            }

        except Exception as e:
            return {"error": str(e)}

    def get_klines(
        self, symbol: str, interval: str = "1m", limit: int = 100
    ) -> List[Dict]:
        """获取Binance K线数据"""
        try:
            symbol = self.normalize_symbol(symbol)
            params = {"symbol": symbol, "interval": interval, "limit": limit}

            result = self._request("GET", "/klines", params=params)

            if "error" in result:
                return []

            klines = []
            for kline in result:
                klines.append(
                    {
                        "timestamp": int(kline[0]),
                        "open": float(kline[1]),
                        "high": float(kline[2]),
                        "low": float(kline[3]),
                        "close": float(kline[4]),
                        "volume": float(kline[5]),
                    }
                )

            return klines

        except Exception as e:
            return []

    def _get_auth_headers(
        self,
        method: str,
        endpoint: str,
        params: Dict = None,
        data: Dict = None,
    ) -> Dict:
        """获取Binance认证头"""
        if not self.api_key or not self.api_secret:
            return {}

        # 构建查询字符串
        if data:
            query_string = "&".join([f"{k}={v}" for k, v in data.items()])
        elif params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        else:
            query_string = ""

        # 生成签名
        signature = hmac.new(
            self.api_secret.encode("utf-8"),
            query_string.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

        return {"X-MBX-APIKEY": self.api_key, "signature": signature}


class OKXInterface(ExchangeInterface):
    """OKX交易所接口"""

    def __init__(
        self,
        api_key: str = "",
        api_secret: str = "",
        passphrase: str = "",
        testnet: bool = True,
    ):
        """初始化OKX接口"""
        super().__init__(api_key, api_secret, testnet)
        self.passphrase = passphrase
        self.name = "OKX"
        self.base_url = (
            "https://www.okx.com/api/v5"
            if not testnet
            else "https://www.okx.com/api/v5"
        )

        self.features = {
            "spot_trading": True,
            "futures_trading": True,
            "margin_trading": True,
            "websocket": True,
            "order_types": ["market", "limit", "post_only", "fok", "ioc"],
        }

        self.fees = {"maker": 0.0008, "taker": 0.001}  # 0.08%  # 0.1%

    def get_account_balance(self) -> Dict:
        """获取OKX账户余额"""
        try:
            result = self._request("GET", "/account/balance", signed=True)

            if "error" in result:
                return {"error": result["error"]}

            if result.get("code") != "0":
                return {"error": result.get("msg", "Unknown error")}

            balances = {}
            for balance_info in result.get("data", []):
                for detail in balance_info.get("details", []):
                    ccy = detail["ccy"]
                    available = float(detail["availBal"])
                    frozen = float(detail["frozenBal"])

                    if available > 0 or frozen > 0:
                        balances[ccy] = {
                            "available": available,
                            "locked": frozen,
                        }

            return balances

        except Exception as e:
            return {"error": str(e)}

    def place_order(
        self,
        symbol: str,
        side: str,
        amount: float,
        price: float = None,
        order_type: str = "market",
    ) -> Dict:
        """OKX下单"""
        try:
            # OKX使用不同的符号格式
            symbol = symbol.replace("_", "-").upper()

            data = {
                "instId": symbol,
                "tdMode": "cash",  # 现金交易
                "side": side.lower(),
                "ordType": order_type.lower(),
                "sz": str(amount),
            }

            if order_type.lower() != "market" and price:
                data["px"] = str(price)

            result = self._request(
                "POST", "/trade/order", data=data, signed=True
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}

            if result.get("code") != "0":
                return {
                    "success": False,
                    "error": result.get("msg", "Order failed"),
                }

            order_data = result["data"][0]
            return {
                "success": True,
                "order_id": order_data["ordId"],
                "symbol": symbol,
                "side": side.lower(),
                "amount": float(amount),
                "price": float(price) if price else 0,
                "status": "pending",
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def cancel_order(self, order_id: str, symbol: str = "") -> Dict:
        """取消OKX订单"""
        try:
            symbol = symbol.replace("_", "-").upper()
            data = {"instId": symbol, "ordId": order_id}

            result = self._request(
                "POST", "/trade/cancel-order", data=data, signed=True
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}

            if result.get("code") != "0":
                return {
                    "success": False,
                    "error": result.get("msg", "Cancel failed"),
                }

            return {"success": True, "order_id": order_id}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_order_status(self, order_id: str, symbol: str = "") -> Dict:
        """查询OKX订单状态"""
        try:
            symbol = symbol.replace("_", "-").upper()
            params = {"instId": symbol, "ordId": order_id}

            result = self._request(
                "GET", "/trade/order", params=params, signed=True
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}

            if result.get("code") != "0":
                return {
                    "success": False,
                    "error": result.get("msg", "Query failed"),
                }

            return {"success": True, "order": result["data"][0]}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_ticker(self, symbol: str) -> Dict:
        """获取OKX行情数据"""
        try:
            symbol = symbol.replace("_", "-").upper()
            params = {"instId": symbol}

            result = self._request("GET", "/market/ticker", params=params)

            if "error" in result:
                return {"error": result["error"]}

            if result.get("code") != "0":
                return {"error": result.get("msg", "Ticker failed")}

            ticker = result["data"][0]
            return {
                "symbol": ticker["instId"],
                "price": float(ticker["last"]),
                "change": float(ticker["changePercent"]) * 100,
                "volume": float(ticker["vol24h"]),
                "high": float(ticker["high24h"]),
                "low": float(ticker["low24h"]),
            }

        except Exception as e:
            return {"error": str(e)}

    def get_orderbook(self, symbol: str, limit: int = 20) -> Dict:
        """获取OKX订单簿"""
        try:
            symbol = symbol.replace("_", "-").upper()
            params = {"instId": symbol, "sz": str(limit)}

            result = self._request("GET", "/market/books", params=params)

            if "error" in result:
                return {"error": result["error"]}

            if result.get("code") != "0":
                return {"error": result.get("msg", "Orderbook failed")}

            book = result["data"][0]
            return {
                "symbol": symbol,
                "bids": [
                    [float(bid[0]), float(bid[1])] for bid in book["bids"]
                ],
                "asks": [
                    [float(ask[0]), float(ask[1])] for ask in book["asks"]
                ],
            }

        except Exception as e:
            return {"error": str(e)}

    def get_klines(
        self, symbol: str, interval: str = "1m", limit: int = 100
    ) -> List[Dict]:
        """获取OKX K线数据"""
        try:
            symbol = symbol.replace("_", "-").upper()
            params = {"instId": symbol, "bar": interval, "limit": str(limit)}

            result = self._request("GET", "/market/candles", params=params)

            if "error" in result:
                return []

            if result.get("code") != "0":
                return []

            klines = []
            for kline in result["data"]:
                klines.append(
                    {
                        "timestamp": int(kline[0]),
                        "open": float(kline[1]),
                        "high": float(kline[2]),
                        "low": float(kline[3]),
                        "close": float(kline[4]),
                        "volume": float(kline[5]),
                    }
                )

            return klines

        except Exception as e:
            return []

    def _get_auth_headers(
        self,
        method: str,
        endpoint: str,
        params: Dict = None,
        data: Dict = None,
    ) -> Dict:
        """获取OKX认证头"""
        if not self.api_key or not self.api_secret:
            return {}

        timestamp = str(time.time())

        # 构建签名字符串
        if data:
            body = json.dumps(data, separators=(",", ":"))
        else:
            body = ""

        message = timestamp + method + endpoint + body

        # 生成签名
        signature = hmac.new(
            self.api_secret.encode("utf-8"),
            message.encode("utf-8"),
            hashlib.sha256,
        ).digest()

        import base64

        signature_b64 = base64.b64encode(signature).decode("utf-8")

        return {
            "OK-ACCESS-KEY": self.api_key,
            "OK-ACCESS-SIGN": signature_b64,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": self.passphrase,
        }


class ExchangeManager:
    """多交易所管理器"""

    def __init__(self):
        """初始化交易所管理器"""
        self.exchanges = {}
        self.active_exchange = None
        self.arbitrage_pairs = []

        # 支持的交易所
        self.supported_exchanges = {
            "gate": "Gate.io",
            "binance": "Binance",
            "okx": "OKX",
        }

    def add_exchange(
        self, exchange_id: str, exchange_interface: ExchangeInterface
    ):
        """添加交易所"""
        try:
            self.exchanges[exchange_id] = exchange_interface
            print(f"✅ 已添加交易所: {exchange_interface.name}")

            if self.active_exchange is None:
                self.active_exchange = exchange_id
                print(f"🎯 设置默认交易所: {exchange_interface.name}")

            return True

        except Exception as e:
            print(f"❌ 添加交易所失败: {e}")
            return False

    def set_active_exchange(self, exchange_id: str) -> bool:
        """设置活跃交易所"""
        if exchange_id in self.exchanges:
            self.active_exchange = exchange_id
            exchange_name = self.exchanges[exchange_id].name
            print(f"🎯 切换到交易所: {exchange_name}")
            return True
        else:
            print(f"❌ 交易所不存在: {exchange_id}")
            return False

    def get_active_exchange(self) -> Optional[ExchangeInterface]:
        """获取活跃交易所"""
        if self.active_exchange and self.active_exchange in self.exchanges:
            return self.exchanges[self.active_exchange]
        return None

    def get_all_balances(self) -> Dict:
        """获取所有交易所余额"""
        all_balances = {}

        for exchange_id, exchange in self.exchanges.items():
            try:
                balance = exchange.get_account_balance()
                if "error" not in balance:
                    all_balances[exchange_id] = {
                        "name": exchange.name,
                        "balances": balance,
                    }
                else:
                    all_balances[exchange_id] = {
                        "name": exchange.name,
                        "error": balance["error"],
                    }
            except Exception as e:
                all_balances[exchange_id] = {
                    "name": exchange.name,
                    "error": str(e),
                }

        return all_balances

    def get_best_price(self, symbol: str, side: str) -> Dict:
        """获取最优价格"""
        best_price = None
        best_exchange = None
        prices = {}

        for exchange_id, exchange in self.exchanges.items():
            try:
                ticker = exchange.get_ticker(symbol)
                if "error" not in ticker:
                    price = ticker["price"]
                    prices[exchange_id] = {
                        "name": exchange.name,
                        "price": price,
                    }

                    # 买单选择最低价，卖单选择最高价
                    if side.lower() == "buy":
                        if best_price is None or price < best_price:
                            best_price = price
                            best_exchange = exchange_id
                    else:
                        if best_price is None or price > best_price:
                            best_price = price
                            best_exchange = exchange_id

            except Exception as e:
                prices[exchange_id] = {"name": exchange.name, "error": str(e)}

        return {
            "best_exchange": best_exchange,
            "best_price": best_price,
            "all_prices": prices,
        }

    def find_arbitrage_opportunities(
        self, symbols: List[str], min_profit: float = 0.005
    ) -> List[Dict]:
        """寻找套利机会"""
        opportunities = []

        for symbol in symbols:
            prices = {}

            # 获取所有交易所价格
            for exchange_id, exchange in self.exchanges.items():
                try:
                    ticker = exchange.get_ticker(symbol)
                    if "error" not in ticker:
                        prices[exchange_id] = {
                            "name": exchange.name,
                            "price": ticker["price"],
                            "volume": ticker.get("volume", 0),
                        }
                except:
                    continue

            # 寻找套利机会
            if len(prices) >= 2:
                price_list = [
                    (exchange_id, data["price"])
                    for exchange_id, data in prices.items()
                ]
                price_list.sort(key=lambda x: x[1])

                lowest_exchange, lowest_price = price_list[0]
                highest_exchange, highest_price = price_list[-1]

                profit_rate = (highest_price - lowest_price) / lowest_price

                if profit_rate >= min_profit:
                    opportunities.append(
                        {
                            "symbol": symbol,
                            "buy_exchange": lowest_exchange,
                            "buy_price": lowest_price,
                            "sell_exchange": highest_exchange,
                            "sell_price": highest_price,
                            "profit_rate": profit_rate,
                            "profit_percent": profit_rate * 100,
                        }
                    )

        return sorted(
            opportunities, key=lambda x: x["profit_rate"], reverse=True
        )

    def execute_cross_exchange_order(
        self,
        buy_exchange_id: str,
        sell_exchange_id: str,
        symbol: str,
        amount: float,
    ) -> Dict:
        """执行跨交易所订单"""
        try:
            buy_exchange = self.exchanges.get(buy_exchange_id)
            sell_exchange = self.exchanges.get(sell_exchange_id)

            if not buy_exchange or not sell_exchange:
                return {"success": False, "error": "交易所不存在"}

            # 同时下买单和卖单
            buy_result = buy_exchange.place_order(
                symbol, "buy", amount, order_type="market"
            )
            sell_result = sell_exchange.place_order(
                symbol, "sell", amount, order_type="market"
            )

            return {
                "success": buy_result["success"] and sell_result["success"],
                "buy_order": buy_result,
                "sell_order": sell_result,
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_exchange_status(self) -> Dict:
        """获取交易所状态"""
        status = {}

        for exchange_id, exchange in self.exchanges.items():
            try:
                # 测试连接
                ticker = exchange.get_ticker("BTC_USDT")
                if "error" not in ticker:
                    status[exchange_id] = {
                        "name": exchange.name,
                        "status": "online",
                        "features": exchange.features,
                        "fees": exchange.fees,
                    }
                else:
                    status[exchange_id] = {
                        "name": exchange.name,
                        "status": "error",
                        "error": ticker["error"],
                    }
            except Exception as e:
                status[exchange_id] = {
                    "name": exchange.name,
                    "status": "offline",
                    "error": str(e),
                }

        return status

    def get_supported_exchanges(self) -> Dict:
        """获取支持的交易所列表"""
        return self.supported_exchanges

    def remove_exchange(self, exchange_id: str) -> bool:
        """移除交易所"""
        if exchange_id in self.exchanges:
            exchange_name = self.exchanges[exchange_id].name
            del self.exchanges[exchange_id]

            if self.active_exchange == exchange_id:
                self.active_exchange = (
                    list(self.exchanges.keys())[0] if self.exchanges else None
                )

            print(f"✅ 已移除交易所: {exchange_name}")
            return True
        else:
            print(f"❌ 交易所不存在: {exchange_id}")
            return False


# 全局交易所管理器
exchange_manager = None


def get_exchange_manager() -> ExchangeManager:
    """获取交易所管理器实例"""
    global exchange_manager
    if exchange_manager is None:
        exchange_manager = ExchangeManager()
    return exchange_manager

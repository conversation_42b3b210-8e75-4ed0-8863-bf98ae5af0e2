{"data_mtime": 1748490882, "dep_lines": [190, 190, 190, 190, 176, 177, 178, 179, 187, 189, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 181, 184, 185, 186, 185], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10, 20], "dependencies": ["matplotlib._api", "matplotlib.cbook", "matplotlib.ticker", "matplotlib.units", "datetime", "functools", "logging", "re", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "pprint", "copy", "inspect", "os", "warnings", "operator", "html", "sys", "collections", "string", "itertools", "contextlib", "types", "traceback", "typing", "_frozen_importlib", "_typeshed", "abc", "enum", "numpy._core", "numpy._core.multiarray", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "typing_extensions"], "hash": "81319408013df5efda7b25505d066e4c0e046aad", "id": "matplotlib.dates", "ignore_all": true, "interface_hash": "ac9751de9054be5c50ffdfab50a49fb3aad81d5e", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\dates.py", "plugin_data": null, "size": 66306, "suppressed": ["dateutil.rrule", "dateutil.<PERSON>del<PERSON>", "dateutil.parser", "dateutil.tz", "dateutil"], "version_id": "1.15.0"}
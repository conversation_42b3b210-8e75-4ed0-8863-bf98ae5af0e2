#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API功能完成演示
API Function Completion Demo

展示已完成的GATE.IO API连接功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
from datetime import datetime

class APIFunctionDemo:
    """API功能演示界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎉 GATE.IO API连接功能 - 开发完成演示")
        self.root.geometry("800x600")
        self.root.configure(bg='#1e1e1e')
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ffffff',
                       font=('Arial', 16, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background='#1e1e1e', 
                       foreground='#cccccc',
                       font=('Arial', 12))
        
        style.configure('Success.TLabel', 
                       background='#1e1e1e', 
                       foreground='#4CAF50',
                       font=('Arial', 10, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=20, pady=20)
        
        ttk.Label(title_frame, text="🎉 GATE.IO API连接功能开发完成！", 
                 style='Title.TLabel').pack()
        
        ttk.Label(title_frame, text="专业级真实数据交易学习系统", 
                 style='Subtitle.TLabel').pack(pady=(5, 0))
        
        # 功能展示区域
        self.create_feature_section()
        
        # 使用指南区域
        self.create_guide_section()
        
        # 按钮区域
        self.create_button_section()
        
        # 状态栏
        self.create_status_bar()
        
    def create_feature_section(self):
        """创建功能展示区域"""
        feature_frame = tk.LabelFrame(self.root, text="✅ 已完成的核心功能", 
                                     bg='#2d2d2d', fg='white', 
                                     font=('Arial', 12, 'bold'))
        feature_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        features = [
            "🔐 完整的GATE.IO API连接器 (gate_api_connector.py)",
            "🔑 安全的API凭证登录界面 (api_login_dialog.py)", 
            "🖥️ GUI主界面完美集成 (ultimate_spot_trading_gui.py)",
            "📦 智能依赖管理系统 (install_api_dependencies.py)",
            "🌐 8个主流加密货币实时数据支持",
            "🛡️ 完善的安全机制和权限控制",
            "⚡ 30秒自动数据更新机制",
            "🔄 自动重连和错误恢复功能",
            "📊 真实市场数据实战演练体验",
            "📖 完整的使用文档和指导"
        ]
        
        for i, feature in enumerate(features):
            ttk.Label(feature_frame, text=feature, 
                     style='Success.TLabel').pack(anchor='w', padx=10, pady=2)
    
    def create_guide_section(self):
        """创建使用指南区域"""
        guide_frame = tk.LabelFrame(self.root, text="🎯 使用指南", 
                                   bg='#2d2d2d', fg='white', 
                                   font=('Arial', 12, 'bold'))
        guide_frame.pack(fill='x', padx=20, pady=10)
        
        guide_text = """
🔑 第1步: 获取GATE.IO API Key
   • 访问 gate.io → 账户管理 → API管理
   • 创建新API Key，权限选择：现货交易 + 查看
   • 复制API Key和Secret Key

🚀 第2步: 启动系统
   • 运行: python core/ultimate_spot_trading_gui.py
   • 系统会自动检测API功能可用性

🔗 第3步: 连接真实数据
   • 点击"连接GATE交易所"按钮
   • 输入API凭证，选择测试环境
   • 开始享受真实数据的实战演练体验
        """
        
        text_widget = tk.Text(guide_frame, height=12, bg='#2d2d2d', fg='#cccccc',
                             font=('Arial', 10), wrap='word', relief='flat')
        text_widget.pack(fill='x', padx=10, pady=10)
        text_widget.insert('1.0', guide_text)
        text_widget.config(state='disabled')
    
    def create_button_section(self):
        """创建按钮区域"""
        button_frame = tk.Frame(self.root, bg='#1e1e1e')
        button_frame.pack(fill='x', padx=20, pady=20)
        
        # 启动GUI按钮
        tk.Button(button_frame, text="🚀 启动交易系统", 
                 command=self.launch_trading_system,
                 bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        # 获取API Key按钮
        tk.Button(button_frame, text="🔑 获取API Key", 
                 command=self.open_gate_api_page,
                 bg='#2196F3', fg='white', font=('Arial', 12, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        # 查看文档按钮
        tk.Button(button_frame, text="📖 查看文档", 
                 command=self.show_documentation,
                 bg='#FF9800', fg='white', font=('Arial', 12, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='left', padx=5)
        
        # 关闭按钮
        tk.Button(button_frame, text="❌ 关闭", 
                 command=self.root.destroy,
                 bg='#f44336', fg='white', font=('Arial', 12, 'bold'),
                 relief='flat', padx=20, pady=10).pack(side='right', padx=5)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#333333', height=30)
        status_frame.pack(fill='x', side='bottom')
        
        status_text = f"🎉 API功能开发完成 | 状态: ✅ 可用 | 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        tk.Label(status_frame, text=status_text, 
                bg='#333333', fg='#4CAF50', 
                font=('Arial', 9)).pack(pady=5)
    
    def launch_trading_system(self):
        """启动交易系统"""
        try:
            import subprocess
            import os
            
            # 启动主GUI
            gui_path = os.path.join('core', 'ultimate_spot_trading_gui.py')
            if os.path.exists(gui_path):
                subprocess.Popen(['python', gui_path])
                messagebox.showinfo("启动成功", 
                                  "交易系统正在启动...\n\n"
                                  "请在新窗口中使用API连接功能！")
            else:
                messagebox.showerror("文件未找到", 
                                   "未找到主GUI文件，请检查文件路径。")
        except Exception as e:
            messagebox.showerror("启动失败", f"启动交易系统时出错：\n{e}")
    
    def open_gate_api_page(self):
        """打开GATE.IO API页面"""
        try:
            webbrowser.open("https://www.gate.io/myaccount/apiv4keys")
            messagebox.showinfo("浏览器已打开", 
                              "GATE.IO API管理页面已在浏览器中打开。\n\n"
                              "请创建API Key，权限选择：现货交易 + 查看")
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开浏览器：\n{e}")
    
    def show_documentation(self):
        """显示文档"""
        doc_window = tk.Toplevel(self.root)
        doc_window.title("📖 API功能文档")
        doc_window.geometry("600x500")
        doc_window.configure(bg='#1e1e1e')
        
        # 文档内容
        doc_content = """
# 🔐 GATE.IO API连接功能文档

## 📋 功能概述
本系统实现了完整的GATE.IO API连接功能，允许用户使用真实的市场数据进行安全的实战演练学习。

## 🎯 核心特性
• 真实数据：直接从GATE.IO获取实时市场数据
• 安全机制：只需要查看权限，不执行真实交易
• 用户友好：简单的连接流程和清晰的界面
• 自动更新：30秒自动刷新市场数据
• 错误恢复：自动重连和完善的错误处理

## 🔑 API权限要求
• 现货交易：用于访问现货市场数据
• 查看：用于读取账户和市场信息
• 不需要：提现、转账等高级权限

## ⚠️ 安全提醒
• 这是学习和模拟系统
• 不会执行任何真实交易
• 所有操作都是虚拟的
• 仅用于教育目的

## 🚀 技术实现
• ccxt库：专业的交易所API库
• 异步处理：非阻塞数据更新
• 多线程：后台数据更新
• 加密存储：安全的凭证管理

## 📞 技术支持
如有问题，请检查：
1. 网络连接是否正常
2. API凭证是否正确
3. 权限设置是否正确
4. 依赖库是否已安装
        """
        
        text_widget = tk.Text(doc_window, bg='#2d2d2d', fg='#cccccc',
                             font=('Arial', 10), wrap='word')
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', doc_content)
        text_widget.config(state='disabled')
        
        # 关闭按钮
        tk.Button(doc_window, text="关闭", command=doc_window.destroy,
                 bg='#f44336', fg='white', font=('Arial', 10, 'bold'),
                 relief='flat', padx=20, pady=5).pack(pady=10)
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🎉 启动API功能完成演示...")
    
    try:
        demo = APIFunctionDemo()
        demo.run()
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")

if __name__ == "__main__":
    main()

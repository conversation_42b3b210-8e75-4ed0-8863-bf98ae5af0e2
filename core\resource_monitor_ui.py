#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
资源监控界面
Resource Monitor UI

提供图形化的资源监控界面
"""

import os
import threading
import time
import tkinter as tk
from pathlib import Path
from tkinter import messagebox, ttk
from typing import Any, Dict, List, Optional

import matplotlib

matplotlib.use("TkAgg")
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from resource_manager import get_resource_manager


class ResourceMonitorUI:
    """资源监控界面"""

    def __init__(self, parent):
        """初始化资源监控界面"""
        self.parent = parent
        self.resource_manager = get_resource_manager()

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("📊 资源监控")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.configure(bg="#2d2d2d")

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_window()

        # 更新标志
        self.is_updating = False
        self.update_thread = None

        # 创建界面
        self.create_widgets()

        # 开始更新
        self.start_update()

    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        width = 800
        height = 600
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg="#2d2d2d")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        title_frame = tk.Frame(main_frame, bg="#2d2d2d")
        title_frame.pack(fill="x", pady=(0, 20))

        tk.Label(
            title_frame,
            text="📊 系统资源监控",
            font=("Arial", 16, "bold"),
            bg="#2d2d2d",
            fg="white",
        ).pack(side="left")

        # 刷新按钮
        tk.Button(
            title_frame,
            text="🔄 刷新",
            command=self.refresh_data,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10),
            relief="flat",
            padx=15,
            pady=5,
        ).pack(side="right")

        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="both", expand=True)

        # 创建各个监控页
        self.create_overview_tab(notebook)
        self.create_memory_tab(notebook)
        self.create_cpu_tab(notebook)
        self.create_threads_tab(notebook)
        self.create_files_tab(notebook)
        self.create_report_tab(notebook)

        # 底部按钮
        button_frame = tk.Frame(main_frame, bg="#2d2d2d")
        button_frame.pack(fill="x", pady=(20, 0))

        tk.Button(
            button_frame,
            text="📝 生成报告",
            command=self.generate_report,
            bg="#607D8B",
            fg="white",
            font=("Arial", 10),
            relief="flat",
            padx=15,
            pady=8,
        ).pack(side="left")

        tk.Button(
            button_frame,
            text="🧹 清理资源",
            command=self.cleanup_resources,
            bg="#f44336",
            fg="white",
            font=("Arial", 10),
            relief="flat",
            padx=15,
            pady=8,
        ).pack(side="left", padx=(10, 0))

        tk.Button(
            button_frame,
            text="❌ 关闭",
            command=self.close,
            bg="#f44336",
            fg="white",
            font=("Arial", 10),
            relief="flat",
            padx=15,
            pady=8,
        ).pack(side="right")

    def create_overview_tab(self, notebook):
        """创建概览选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="📋 概览")

        # 创建指标框架
        metrics_frame = tk.Frame(tab, bg="#2d2d2d")
        metrics_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 内存使用
        memory_frame = tk.LabelFrame(
            metrics_frame,
            text="内存使用",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        memory_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        self.memory_label = tk.Label(
            memory_frame,
            text="加载中...",
            font=("Arial", 20, "bold"),
            bg="#2d2d2d",
            fg="#4CAF50",
        )
        self.memory_label.pack(pady=10)

        self.memory_detail_label = tk.Label(
            memory_frame, text="", font=("Arial", 10), bg="#2d2d2d", fg="white"
        )
        self.memory_detail_label.pack()

        # CPU使用
        cpu_frame = tk.LabelFrame(
            metrics_frame,
            text="CPU使用",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        cpu_frame.grid(row=0, column=1, sticky="nsew", padx=10, pady=10)

        self.cpu_label = tk.Label(
            cpu_frame,
            text="加载中...",
            font=("Arial", 20, "bold"),
            bg="#2d2d2d",
            fg="#4CAF50",
        )
        self.cpu_label.pack(pady=10)

        self.cpu_detail_label = tk.Label(
            cpu_frame, text="", font=("Arial", 10), bg="#2d2d2d", fg="white"
        )
        self.cpu_detail_label.pack()

        # 线程
        threads_frame = tk.LabelFrame(
            metrics_frame,
            text="线程",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        threads_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)

        self.threads_label = tk.Label(
            threads_frame,
            text="加载中...",
            font=("Arial", 20, "bold"),
            bg="#2d2d2d",
            fg="#4CAF50",
        )
        self.threads_label.pack(pady=10)

        self.threads_detail_label = tk.Label(
            threads_frame,
            text="",
            font=("Arial", 10),
            bg="#2d2d2d",
            fg="white",
        )
        self.threads_detail_label.pack()

        # 文件句柄
        files_frame = tk.LabelFrame(
            metrics_frame,
            text="文件句柄",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        files_frame.grid(row=1, column=1, sticky="nsew", padx=10, pady=10)

        self.files_label = tk.Label(
            files_frame,
            text="加载中...",
            font=("Arial", 20, "bold"),
            bg="#2d2d2d",
            fg="#4CAF50",
        )
        self.files_label.pack(pady=10)

        self.files_detail_label = tk.Label(
            files_frame, text="", font=("Arial", 10), bg="#2d2d2d", fg="white"
        )
        self.files_detail_label.pack()

        # 配置网格权重
        metrics_frame.grid_columnconfigure(0, weight=1)
        metrics_frame.grid_columnconfigure(1, weight=1)
        metrics_frame.grid_rowconfigure(0, weight=1)
        metrics_frame.grid_rowconfigure(1, weight=1)

    def create_memory_tab(self, notebook):
        """创建内存选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="💾 内存")

        # 创建图表框架
        chart_frame = tk.Frame(tab, bg="#2d2d2d")
        chart_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建图表
        fig = Figure(figsize=(5, 4), dpi=100, facecolor="#2d2d2d")
        self.memory_ax = fig.add_subplot(111)
        self.memory_ax.set_facecolor("#2d2d2d")
        self.memory_ax.tick_params(colors="white")
        self.memory_ax.set_title("内存使用历史", color="white")
        self.memory_ax.set_xlabel("时间", color="white")
        self.memory_ax.set_ylabel("内存使用 (MB)", color="white")

        # 添加图表到界面
        self.memory_canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        self.memory_canvas.draw()
        self.memory_canvas.get_tk_widget().pack(fill="both", expand=True)

        # 创建详情框架
        details_frame = tk.Frame(tab, bg="#2d2d2d")
        details_frame.pack(fill="x", padx=20, pady=(0, 20))

        # 内存详情
        self.memory_details_text = tk.Text(
            details_frame,
            height=5,
            width=80,
            font=("Courier New", 10),
            bg="#3d3d3d",
            fg="white",
        )
        self.memory_details_text.pack(fill="x")

    def create_cpu_tab(self, notebook):
        """创建CPU选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="⚙️ CPU")

        # 创建图表框架
        chart_frame = tk.Frame(tab, bg="#2d2d2d")
        chart_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建图表
        fig = Figure(figsize=(5, 4), dpi=100, facecolor="#2d2d2d")
        self.cpu_ax = fig.add_subplot(111)
        self.cpu_ax.set_facecolor("#2d2d2d")
        self.cpu_ax.tick_params(colors="white")
        self.cpu_ax.set_title("CPU使用历史", color="white")
        self.cpu_ax.set_xlabel("时间", color="white")
        self.cpu_ax.set_ylabel("CPU使用 (%)", color="white")

        # 添加图表到界面
        self.cpu_canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        self.cpu_canvas.draw()
        self.cpu_canvas.get_tk_widget().pack(fill="both", expand=True)

        # 创建详情框架
        details_frame = tk.Frame(tab, bg="#2d2d2d")
        details_frame.pack(fill="x", padx=20, pady=(0, 20))

        # CPU详情
        self.cpu_details_text = tk.Text(
            details_frame,
            height=5,
            width=80,
            font=("Courier New", 10),
            bg="#3d3d3d",
            fg="white",
        )
        self.cpu_details_text.pack(fill="x")

    def create_threads_tab(self, notebook):
        """创建线程选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="🧵 线程")

        # 创建线程列表框架
        list_frame = tk.Frame(tab, bg="#2d2d2d")
        list_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建线程列表
        columns = ("name", "id", "status", "daemon")
        self.threads_tree = ttk.Treeview(
            list_frame, columns=columns, show="headings"
        )

        # 设置列标题
        self.threads_tree.heading("name", text="线程名称")
        self.threads_tree.heading("id", text="线程ID")
        self.threads_tree.heading("status", text="状态")
        self.threads_tree.heading("daemon", text="守护线程")

        # 设置列宽
        self.threads_tree.column("name", width=200)
        self.threads_tree.column("id", width=100)
        self.threads_tree.column("status", width=100)
        self.threads_tree.column("daemon", width=100)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(
            list_frame, orient="vertical", command=self.threads_tree.yview
        )
        self.threads_tree.configure(yscroll=scrollbar.set)

        # 放置组件
        self.threads_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_files_tab(self, notebook):
        """创建文件选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="📁 文件")

        # 创建文件列表框架
        list_frame = tk.Frame(tab, bg="#2d2d2d")
        list_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建文件列表
        columns = ("name", "mode", "closed")
        self.files_tree = ttk.Treeview(
            list_frame, columns=columns, show="headings"
        )

        # 设置列标题
        self.files_tree.heading("name", text="文件名")
        self.files_tree.heading("mode", text="模式")
        self.files_tree.heading("closed", text="是否关闭")

        # 设置列宽
        self.files_tree.column("name", width=300)
        self.files_tree.column("mode", width=100)
        self.files_tree.column("closed", width=100)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(
            list_frame, orient="vertical", command=self.files_tree.yview
        )
        self.files_tree.configure(yscroll=scrollbar.set)

        # 放置组件
        self.files_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_report_tab(self, notebook):
        """创建报告选项卡"""
        tab = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(tab, text="📝 报告")

        # 创建报告框架
        report_frame = tk.Frame(tab, bg="#2d2d2d")
        report_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建报告文本框
        self.report_text = tk.Text(
            report_frame, font=("Courier New", 10), bg="#3d3d3d", fg="white"
        )

        # 添加滚动条
        scrollbar = ttk.Scrollbar(
            report_frame, orient="vertical", command=self.report_text.yview
        )
        self.report_text.configure(yscroll=scrollbar.set)

        # 放置组件
        self.report_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 生成初始报告
        self.update_report()

    def start_update(self):
        """开始更新数据"""
        if self.is_updating:
            return

        self.is_updating = True
        self.update_thread = threading.Thread(
            target=self._update_loop, daemon=True
        )
        self.update_thread.start()

    def stop_update(self):
        """停止更新数据"""
        self.is_updating = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1)

    def _update_loop(self):
        """更新循环"""
        while self.is_updating:
            try:
                # 更新数据
                self.update_data()

                # 等待下一次更新
                time.sleep(2)

            except Exception as e:
                print(f"更新数据出错: {e}")
                time.sleep(5)

    def update_data(self):
        """更新数据"""
        try:
            # 获取资源使用情况
            usage = self.resource_manager.get_resource_usage()

            # 更新概览
            self.update_overview(usage)

            # 更新内存图表
            self.update_memory_chart()

            # 更新CPU图表
            self.update_cpu_chart()

            # 更新线程列表
            self.update_threads_list()

            # 更新文件列表
            self.update_files_list()

        except Exception as e:
            print(f"更新数据出错: {e}")

    def update_overview(self, usage):
        """更新概览"""
        # 内存使用
        memory_mb = usage["memory"]["rss_mb"]
        memory_percent = usage["memory"]["percent"]
        self.memory_label.config(text=f"{memory_mb:.1f} MB")
        self.memory_detail_label.config(text=f"使用率: {memory_percent:.1f}%")

        # CPU使用
        cpu_percent = usage["cpu"]["percent"]
        self.cpu_label.config(text=f"{cpu_percent:.1f}%")
        self.cpu_detail_label.config(text=f"处理器使用率")

        # 线程
        thread_total = usage["threads"]["total"]
        thread_active = usage["threads"]["active"]
        self.threads_label.config(text=f"{thread_active}/{thread_total}")
        self.threads_detail_label.config(text=f"活动/总线程数")

        # 文件句柄
        file_total = usage["files"]["total"]
        file_open = usage["files"]["open"]
        self.files_label.config(text=f"{file_open}/{file_total}")
        self.files_detail_label.config(text=f"打开/总文件数")

    def update_memory_chart(self):
        """更新内存图表"""
        try:
            # 获取内存使用历史
            history = self.resource_manager.get_resource_usage_history()
            memory_history = history["memory"]

            if not memory_history:
                return

            # 提取数据
            times = [entry[0] for entry in memory_history]
            memory_mb = [entry[1] / (1024 * 1024) for entry in memory_history]

            # 转换时间戳为相对时间（秒）
            start_time = times[0]
            relative_times = [(t - start_time) for t in times]

            # 清除旧图表
            self.memory_ax.clear()

            # 绘制新图表
            self.memory_ax.plot(relative_times, memory_mb, "g-")
            self.memory_ax.set_facecolor("#2d2d2d")
            self.memory_ax.tick_params(colors="white")
            self.memory_ax.set_title("内存使用历史", color="white")
            self.memory_ax.set_xlabel("时间（秒）", color="white")
            self.memory_ax.set_ylabel("内存使用 (MB)", color="white")

            # 更新画布
            self.memory_canvas.draw()

            # 更新详情文本
            details = f"当前内存使用: {memory_mb[-1]:.2f} MB\n"
            details += f"最大内存使用: {max(memory_mb):.2f} MB\n"
            details += f"最小内存使用: {min(memory_mb):.2f} MB\n"
            details += (
                f"平均内存使用: {sum(memory_mb) / len(memory_mb):.2f} MB\n"
            )

            self.memory_details_text.delete(1.0, tk.END)
            self.memory_details_text.insert(tk.END, details)

        except Exception as e:
            print(f"更新内存图表出错: {e}")

    def update_cpu_chart(self):
        """更新CPU图表"""
        try:
            # 获取CPU使用历史
            history = self.resource_manager.get_resource_usage_history()
            cpu_history = history["cpu"]

            if not cpu_history:
                return

            # 提取数据
            times = [entry[0] for entry in cpu_history]
            cpu_percent = [entry[1] for entry in cpu_history]

            # 转换时间戳为相对时间（秒）
            start_time = times[0]
            relative_times = [(t - start_time) for t in times]

            # 清除旧图表
            self.cpu_ax.clear()

            # 绘制新图表
            self.cpu_ax.plot(relative_times, cpu_percent, "r-")
            self.cpu_ax.set_facecolor("#2d2d2d")
            self.cpu_ax.tick_params(colors="white")
            self.cpu_ax.set_title("CPU使用历史", color="white")
            self.cpu_ax.set_xlabel("时间（秒）", color="white")
            self.cpu_ax.set_ylabel("CPU使用 (%)", color="white")

            # 更新画布
            self.cpu_canvas.draw()

            # 更新详情文本
            details = f"当前CPU使用: {cpu_percent[-1]:.2f}%\n"
            details += f"最大CPU使用: {max(cpu_percent):.2f}%\n"
            details += f"最小CPU使用: {min(cpu_percent):.2f}%\n"
            details += (
                f"平均CPU使用: {sum(cpu_percent) / len(cpu_percent):.2f}%\n"
            )

            self.cpu_details_text.delete(1.0, tk.END)
            self.cpu_details_text.insert(tk.END, details)

        except Exception as e:
            print(f"更新CPU图表出错: {e}")

    def update_threads_list(self):
        """更新线程列表"""
        try:
            # 清除旧数据
            for item in self.threads_tree.get_children():
                self.threads_tree.delete(item)

            # 获取线程列表
            threads = list(self.resource_manager.threads)

            # 添加线程信息
            for thread in threads:
                self.threads_tree.insert(
                    "",
                    "end",
                    values=(
                        thread.name,
                        thread.ident,
                        "Active" if thread.is_alive() else "Inactive",
                        "Yes" if thread.daemon else "No",
                    ),
                )

        except Exception as e:
            print(f"更新线程列表出错: {e}")

    def update_files_list(self):
        """更新文件列表"""
        try:
            # 清除旧数据
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)

            # 获取文件句柄列表
            files = list(self.resource_manager.file_handles)

            # 添加文件信息
            for file in files:
                try:
                    name = getattr(file, "name", str(file))
                    mode = getattr(file, "mode", "N/A")
                    closed = "Yes" if getattr(file, "closed", True) else "No"

                    self.files_tree.insert(
                        "", "end", values=(name, mode, closed)
                    )
                except Exception:
                    # 忽略无法获取信息的文件
                    pass

        except Exception as e:
            print(f"更新文件列表出错: {e}")

    def update_report(self):
        """更新报告"""
        try:
            # 生成报告
            report = self.resource_manager.create_resource_report()

            # 更新报告文本
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(tk.END, report)

        except Exception as e:
            print(f"更新报告出错: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.update_data()
        self.update_report()

    def generate_report(self):
        """生成报告"""
        try:
            # 创建报告目录
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)

            # 生成报告文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"resource_report_{timestamp}.txt"

            # 生成报告
            self.resource_manager.create_resource_report(str(report_file))

            messagebox.showinfo(
                "报告生成成功", f"资源报告已保存到:\n{report_file}"
            )

        except Exception as e:
            messagebox.showerror("报告生成失败", f"生成报告时出错: {e}")

    def cleanup_resources(self):
        """清理资源"""
        if messagebox.askyesno("确认清理", "确定要清理所有未使用的资源吗？"):
            try:
                # 清理资源
                self.resource_manager.cleanup_all()

                # 刷新数据
                self.refresh_data()

                messagebox.showinfo("清理成功", "资源清理完成")

            except Exception as e:
                messagebox.showerror("清理失败", f"清理资源时出错: {e}")

    def close(self):
        """关闭对话框"""
        self.stop_update()
        self.dialog.destroy()

    def show(self):
        """显示对话框"""
        self.dialog.protocol("WM_DELETE_WINDOW", self.close)
        self.dialog.wait_window()


def show_resource_monitor(parent):
    """显示资源监控对话框"""
    dialog = ResourceMonitorUI(parent)
    dialog.show()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.title("资源监控测试")
    root.geometry("300x200")

    tk.Button(
        root, text="打开资源监控", command=lambda: show_resource_monitor(root)
    ).pack(pady=20)

    root.mainloop()

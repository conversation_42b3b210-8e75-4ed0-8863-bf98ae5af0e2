{".class": "MypyFile", "_fullname": "matplotlib.backends.backend_tkagg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FigureCanvasAgg": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends.backend_agg.FigureCanvasAgg", "kind": "Gdef"}, "FigureCanvasTk": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._backend_tk.FigureCanvasTk", "kind": "Gdef"}, "FigureCanvasTkAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backends.backend_agg.FigureCanvasAgg", "matplotlib.backends._backend_tk.FigureCanvasTk"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg", "name": "FigureCanvasTkAgg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.backend_tkagg", "mro": ["matplotlib.backends.backend_tkagg.FigureCanvasTkAgg", "matplotlib.backends.backend_agg.FigureCanvasAgg", "matplotlib.backends._backend_tk.FigureCanvasTk", "matplotlib.backend_bases.FigureCanvasBase", "builtins.object"], "names": {".class": "SymbolTable", "blit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "bbox"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg.blit", "name": "blit", "type": null}}, "draw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg.draw", "name": "draw", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FigureManagerTk": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._backend_tk.FigureManagerTk", "kind": "Gdef"}, "NavigationToolbar2Tk": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._backend_tk.NavigationToolbar2Tk", "kind": "Gdef"}, "_BackendTk": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._backend_tk._BackendTk", "kind": "Gdef"}, "_BackendTkAgg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backends._backend_tk._BackendTk"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backends.backend_tkagg._BackendTkAgg", "name": "_BackendTkAgg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backends.backend_tkagg._BackendTkAgg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backends.backend_tkagg", "mro": ["matplotlib.backends.backend_tkagg._BackendTkAgg", "matplotlib.backends._backend_tk._BackendTk", "matplotlib.backend_bases._Backend", "builtins.object"], "names": {".class": "SymbolTable", "FigureCanvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backends.backend_tkagg._BackendTkAgg.FigureCanvas", "name": "FigureCanvas", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["figure", "master"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["matplotlib.backends.backend_tkagg.FigureCanvasTkAgg"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "matplotlib.backends.backend_tkagg.FigureCanvasTkAgg", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backends.backend_tkagg._BackendTkAgg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backends.backend_tkagg._BackendTkAgg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backends.backend_tkagg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_backend_tk": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backends._backend_tk", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py"}
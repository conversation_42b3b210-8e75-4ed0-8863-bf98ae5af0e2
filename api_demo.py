#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GATE.IO API连接演示
GATE.IO API Connection Demo

演示如何连接真实的GATE.IO API并获取市场数据
"""

import asyncio
import time
from datetime import datetime

def test_ccxt_import():
    """测试ccxt导入"""
    try:
        import ccxt
        print(f"✅ ccxt导入成功 - 版本: {ccxt.__version__}")
        return True
    except ImportError as e:
        print(f"❌ ccxt导入失败: {e}")
        return False

def create_gate_exchange():
    """创建GATE交易所实例"""
    try:
        import ccxt
        
        # 创建GATE交易所实例（无需API密钥的公共数据）
        exchange = ccxt.gateio({
            'sandbox': True,  # 使用测试环境
            'enableRateLimit': True,
            'options': {
                'defaultType': 'spot'  # 现货交易
            }
        })
        
        print("✅ GATE交易所实例创建成功")
        return exchange
        
    except Exception as e:
        print(f"❌ 创建交易所实例失败: {e}")
        return None

async def get_market_data(exchange):
    """获取市场数据"""
    try:
        print("\n📊 正在获取市场数据...")
        
        # 支持的交易对
        symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT']
        
        # 加载市场
        await exchange.load_markets()
        print("✅ 市场数据加载成功")
        
        # 获取ticker数据
        print("\n🌐 获取实时价格数据:")
        print("=" * 60)
        
        for symbol in symbols:
            try:
                ticker = await exchange.fetch_ticker(symbol)
                
                price = ticker['last']
                change_24h = ticker['percentage'] or 0
                volume_24h = ticker['quoteVolume']
                high_24h = ticker['high']
                low_24h = ticker['low']
                
                change_indicator = "🟢" if change_24h > 0 else "🔴" if change_24h < 0 else "🟡"
                
                print(f"{change_indicator} {symbol}:")
                print(f"  💰 当前价格: ${price:,.2f}")
                print(f"  📈 24h涨跌: {change_24h:+.2f}%")
                print(f"  📊 24h成交量: ${volume_24h:,.0f}")
                print(f"  🔺 24h最高: ${high_24h:,.2f}")
                print(f"  🔻 24h最低: ${low_24h:,.2f}")
                print(f"  ⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}")
                print()
                
            except Exception as e:
                print(f"❌ 获取 {symbol} 数据失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return False

async def get_kline_data(exchange, symbol='BTC/USDT'):
    """获取K线数据"""
    try:
        print(f"\n📈 获取 {symbol} K线数据...")
        
        # 获取最近10根1分钟K线
        ohlcv = await exchange.fetch_ohlcv(symbol, '1m', limit=10)
        
        print(f"📊 {symbol} 最近10分钟K线:")
        print("时间\t\t开盘\t最高\t最低\t收盘\t成交量")
        print("-" * 70)
        
        for candle in ohlcv[-5:]:  # 显示最后5根
            timestamp = datetime.fromtimestamp(candle[0] / 1000)
            open_price = candle[1]
            high_price = candle[2]
            low_price = candle[3]
            close_price = candle[4]
            volume = candle[5]
            
            print(f"{timestamp.strftime('%H:%M')}\t\t{open_price:.2f}\t{high_price:.2f}\t{low_price:.2f}\t{close_price:.2f}\t{volume:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取K线数据失败: {e}")
        return False

def demo_api_login_dialog():
    """演示API登录对话框"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        print("\n🔐 演示API登录界面...")
        
        # 创建简单的演示窗口
        root = tk.Tk()
        root.title("API连接演示")
        root.geometry("400x300")
        root.configure(bg='#2d2d2d')
        
        # 标题
        tk.Label(root, text="🔐 GATE.IO API连接演示", 
                font=('Arial', 16, 'bold'), 
                bg='#2d2d2d', fg='white').pack(pady=20)
        
        # 说明
        info_text = """
这是API连接功能的演示界面

🎯 功能特点:
• 安全的API凭证输入
• 测试/生产环境选择
• 加密存储凭证
• 实时数据获取

⚠️ 使用说明:
• 需要GATE.IO API Key
• 建议使用测试环境
• 只需要现货查看权限
        """
        
        tk.Label(root, text=info_text, 
                font=('Arial', 10), 
                bg='#2d2d2d', fg='#cccccc',
                justify='left').pack(pady=10, padx=20)
        
        # 按钮
        def show_info():
            messagebox.showinfo("API连接", 
                              "这是API连接功能的演示！\n\n"
                              "在真实使用中，您可以:\n"
                              "• 输入GATE.IO API凭证\n"
                              "• 连接真实市场数据\n"
                              "• 进行安全的实战演练")
        
        tk.Button(root, text="🔗 演示连接", 
                 command=show_info,
                 bg='#4CAF50', fg='white', 
                 font=('Arial', 12, 'bold'),
                 relief='flat', padx=20, pady=10).pack(pady=20)
        
        tk.Button(root, text="❌ 关闭", 
                 command=root.destroy,
                 bg='#f44336', fg='white', 
                 font=('Arial', 10),
                 relief='flat', padx=20, pady=8).pack()
        
        print("✅ API登录界面演示已启动")
        print("💡 请在弹出的窗口中查看演示")
        
        # 运行界面
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ API登录界面演示失败: {e}")
        return False

async def main():
    """主演示函数"""
    print("🚀 GATE.IO API连接功能演示")
    print("=" * 60)
    
    # 1. 测试ccxt导入
    print("\n📦 第1步: 测试依赖库")
    if not test_ccxt_import():
        print("💡 请先安装ccxt: pip install ccxt")
        return
    
    # 2. 创建交易所实例
    print("\n🏦 第2步: 创建交易所连接")
    exchange = create_gate_exchange()
    if not exchange:
        return
    
    # 3. 获取市场数据
    print("\n📊 第3步: 获取真实市场数据")
    success = await get_market_data(exchange)
    if not success:
        print("⚠️ 市场数据获取失败，可能是网络问题")
    
    # 4. 获取K线数据
    print("\n📈 第4步: 获取K线数据")
    await get_kline_data(exchange)
    
    # 5. 关闭连接
    await exchange.close()
    print("\n✅ 交易所连接已关闭")
    
    # 6. 演示API登录界面
    print("\n🔐 第5步: 演示API登录界面")
    demo_api_login_dialog()
    
    print("\n🎉 API连接功能演示完成！")
    print("\n💡 下一步:")
    print("  1. 获取您的GATE.IO API Key")
    print("  2. 在GUI中点击'连接GATE交易所'")
    print("  3. 输入API凭证开始使用真实数据")

if __name__ == "__main__":
    try:
        # 运行异步演示
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 演示中断")
    except Exception as e:
        print(f"\n❌ 演示错误: {e}")
    
    input("\n按回车键退出...")

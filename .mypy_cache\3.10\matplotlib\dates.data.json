{".class": "MypyFile", "_fullname": "matplotlib.dates", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoDateFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.AutoDateFormatter", "name": "AutoDateFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.AutoDateFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateFormatter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "locator", "tz", "defaultfmt", "usetex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateFormatter.__init__", "name": "__init__", "type": null}}, "_formatter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter._formatter", "name": "_formatter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_locator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter._locator", "name": "_locator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_set_locator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "locator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateFormatter._set_locator", "name": "_set_locator", "type": null}}, "_tz": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter._tz", "name": "_tz", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_usetex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter._usetex", "name": "_usetex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "defaultfmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter.defaultfmt", "name": "defaultfmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scaled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateFormatter.scaled", "name": "scaled", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.AutoDateFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.AutoDateFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoDateLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.DateLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.AutoDateLocator", "name": "AutoDateLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.AutoDateLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "tz", "minticks", "maxticks", "interval_multiples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator.__init__", "name": "__init__", "type": null}}, "_byranges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator._byranges", "name": "_byranges", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_freq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator._freq", "name": "_freq", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_freqs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator._freqs", "name": "_freqs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator._get_unit", "name": "_get_unit", "type": null}}, "get_locator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dmin", "dmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator.get_locator", "name": "get_locator", "type": null}}, "interval_multiples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator.interval_multiples", "name": "interval_multiples", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intervald": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator.intervald", "name": "intervald", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "maxticks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator.maxticks", "name": "maxticks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "minticks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.AutoDateLocator.minticks", "name": "minticks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "nonsingular": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator.nonsingular", "name": "nonsingular", "type": null}}, "tick_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.AutoDateLocator.tick_values", "name": "tick_values", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.AutoDateLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.AutoDateLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConciseDateConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.DateConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.ConciseDateConverter", "name": "ConciseDateConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.ConciseDateConverter", "matplotlib.dates.DateConverter", "matplotlib.units.ConversionInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["self", "formats", "zero_formats", "offset_formats", "show_offset", "interval_multiples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateConverter.__init__", "name": "__init__", "type": null}}, "_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateConverter._formats", "name": "_formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_offset_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateConverter._offset_formats", "name": "_offset_formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_show_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateConverter._show_offset", "name": "_show_offset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_zero_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateConverter._zero_formats", "name": "_zero_formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "axisinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateConverter.axisinfo", "name": "axisinfo", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.ConciseDateConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.ConciseDateConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConciseDateFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.ConciseDateFormatter", "name": "ConciseDateFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.ConciseDateFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "locator", "tz", "formats", "offset_formats", "zero_formats", "show_offset", "usetex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter.__init__", "name": "__init__", "type": null}}, "_locator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter._locator", "name": "_locator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tz": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter._tz", "name": "_tz", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_usetex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter._usetex", "name": "_usetex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "defaultfmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.defaultfmt", "name": "defaultfmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "format_data_short": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter.format_data_short", "name": "format_data_short", "type": null}}, "format_ticks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter.format_ticks", "name": "format_ticks", "type": null}}, "formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.formats", "name": "formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.ConciseDateFormatter.get_offset", "name": "get_offset", "type": null}}, "offset_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.offset_formats", "name": "offset_formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "offset_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.offset_string", "name": "offset_string", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "show_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.show_offset", "name": "show_offset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "zero_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.ConciseDateFormatter.zero_formats", "name": "zero_formats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.ConciseDateFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.ConciseDateFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DAILY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.DAILY", "name": "DAILY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.DAILY", "source_any": null, "type_of_any": 3}}}, "DAYS_PER_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.DAYS_PER_MONTH", "name": "DAYS_PER_MONTH", "type": "builtins.float"}}, "DAYS_PER_WEEK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.DAYS_PER_WEEK", "name": "DAYS_PER_WEEK", "type": "builtins.float"}}, "DAYS_PER_YEAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.DAYS_PER_YEAR", "name": "DAYS_PER_YEAR", "type": "builtins.float"}}, "DateConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.units.ConversionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.DateConverter", "name": "DateConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.DateConverter", "matplotlib.units.ConversionInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "interval_multiples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateConverter.__init__", "name": "__init__", "type": null}}, "_interval_multiples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.DateConverter._interval_multiples", "name": "_interval_multiples", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "axisinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateConverter.axisinfo", "name": "axisinfo", "type": null}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.dates.DateConverter.convert", "name": "convert", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.DateConverter.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "unit", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of DateConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_units": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.dates.DateConverter.default_units", "name": "default_units", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.DateConverter.default_units", "name": "default_units", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_units of DateConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.DateConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.DateConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.DateF<PERSON><PERSON>er", "name": "Date<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateF<PERSON><PERSON>er", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.DateF<PERSON><PERSON>er", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateFormatter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "fmt", "tz", "usetex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateFormatter.__init__", "name": "__init__", "type": null}}, "_usetex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.DateFormatter._usetex", "name": "_usetex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.DateFormatter.fmt", "name": "fmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_tzinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateFormatter.set_tzinfo", "name": "set_tzinfo", "type": null}}, "tz": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.DateFormatter.tz", "name": "tz", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.DateFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.DateF<PERSON><PERSON>er", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Locator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.DateLocator", "name": "DateLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator.__init__", "name": "__init__", "type": null}}, "_get_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator._get_interval", "name": "_get_interval", "type": null}}, "_get_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator._get_unit", "name": "_get_unit", "type": null}}, "datalim_to_dt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator.datalim_to_dt", "name": "datalim_to_dt", "type": null}}, "hms0d": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.DateLocator.hms0d", "name": "hms0d", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "nonsingular": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator.nonsingular", "name": "nonsingular", "type": null}}, "set_tzinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator.set_tzinfo", "name": "set_tzinfo", "type": null}}, "tz": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.DateLocator.tz", "name": "tz", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "viewlim_to_dt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DateLocator.viewlim_to_dt", "name": "viewlim_to_dt", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.DateLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.DateLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DayLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.DayLocator", "name": "DayLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DayLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.DayLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "bymonthday", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.DayLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.DayLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.DayLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EPOCH_OFFSET": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.EPOCH_OFFSET", "name": "EPOCH_OFFSET", "type": "builtins.float"}}, "FR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.FR", "name": "FR", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.FR", "source_any": null, "type_of_any": 3}}}, "FRIDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.FRIDAY", "name": "FRIDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.FR", "source_any": null, "type_of_any": 3}}}, "HOURLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.HOURLY", "name": "HOURLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.HOURLY", "source_any": null, "type_of_any": 3}}}, "HOURS_PER_DAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.HOURS_PER_DAY", "name": "HOURS_PER_DAY", "type": "builtins.float"}}, "HourLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.HourLocator", "name": "HourLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.HourLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.HourLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "byhour", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.HourLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.HourLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.HourLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MICROSECONDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MICROSECONDLY", "name": "MICROSECONDLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SECONDLY", "source_any": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SECONDLY", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "MINUTELY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.MINUTELY", "name": "MINUTELY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.MINUTELY", "source_any": null, "type_of_any": 3}}}, "MINUTES_PER_DAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MINUTES_PER_DAY", "name": "MINUTES_PER_DAY", "type": "builtins.float"}}, "MIN_PER_HOUR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MIN_PER_HOUR", "name": "MIN_PER_HOUR", "type": "builtins.float"}}, "MO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.MO", "name": "MO", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.MO", "source_any": null, "type_of_any": 3}}}, "MONDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MONDAY", "name": "MONDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.MO", "source_any": null, "type_of_any": 3}}}, "MONTHLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.MONTHLY", "name": "MONTHLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.MONTHLY", "source_any": null, "type_of_any": 3}}}, "MONTHS_PER_YEAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MONTHS_PER_YEAR", "name": "MONTHS_PER_YEAR", "type": "builtins.float"}}, "MUSECONDS_PER_DAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.MUSECONDS_PER_DAY", "name": "MUSECONDS_PER_DAY", "type": "builtins.float"}}, "MicrosecondLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.DateLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.MicrosecondLocator", "name": "MicrosecondLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.MicrosecondLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator.__init__", "name": "__init__", "type": null}}, "_get_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator._get_interval", "name": "_get_interval", "type": null}}, "_get_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator._get_unit", "name": "_get_unit", "type": null}}, "_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.MicrosecondLocator._interval", "name": "_interval", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wrapped_locator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.MicrosecondLocator._wrapped_locator", "name": "_wrapped_locator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator.set_axis", "name": "set_axis", "type": null}}, "tick_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MicrosecondLocator.tick_values", "name": "tick_values", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.MicrosecondLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.MicrosecondLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MinuteLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.MinuteLocator", "name": "MinuteLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MinuteLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.MinuteLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "byminute", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MinuteLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.MinuteLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.MinuteLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MonthLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.MonthLocator", "name": "MonthLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MonthLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.MonthLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "bymonth", "bymonthday", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.MonthLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.MonthLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.MonthLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RRuleLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.DateLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.RRuleLocator", "name": "RRuleLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "o", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator.__init__", "name": "__init__", "type": null}}, "_create_rrule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator._create_rrule", "name": "_create_rrule", "type": null}}, "_get_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator._get_interval", "name": "_get_interval", "type": null}}, "_get_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator._get_unit", "name": "_get_unit", "type": null}}, "get_unit_generic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["freq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.dates.RRuleLocator.get_unit_generic", "name": "get_unit_generic", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.RRuleLocator.get_unit_generic", "name": "get_unit_generic", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["freq"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unit_generic of RRuleLocator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rule": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.RRuleLocator.rule", "name": "rule", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tick_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.RRuleLocator.tick_values", "name": "tick_values", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.RRuleLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.RRuleLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.SA", "name": "SA", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SA", "source_any": null, "type_of_any": 3}}}, "SATURDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SATURDAY", "name": "SATURDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SA", "source_any": null, "type_of_any": 3}}}, "SECONDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.SECONDLY", "name": "SECONDLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SECONDLY", "source_any": null, "type_of_any": 3}}}, "SEC_PER_DAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SEC_PER_DAY", "name": "SEC_PER_DAY", "type": "builtins.float"}}, "SEC_PER_HOUR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SEC_PER_HOUR", "name": "SEC_PER_HOUR", "type": "builtins.float"}}, "SEC_PER_MIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SEC_PER_MIN", "name": "SEC_PER_MIN", "type": "builtins.float"}}, "SEC_PER_WEEK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SEC_PER_WEEK", "name": "SEC_PER_WEEK", "type": "builtins.float"}}, "SU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.SU", "name": "SU", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SU", "source_any": null, "type_of_any": 3}}}, "SUNDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.SUNDAY", "name": "SUNDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.SU", "source_any": null, "type_of_any": 3}}}, "SecondLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.SecondLocator", "name": "SecondLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.SecondLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.SecondLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "bysecond", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.SecondLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.SecondLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.SecondLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.TH", "name": "TH", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.TH", "source_any": null, "type_of_any": 3}}}, "THURSDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.THURSDAY", "name": "THURSDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.TH", "source_any": null, "type_of_any": 3}}}, "TU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.TU", "name": "TU", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.TU", "source_any": null, "type_of_any": 3}}}, "TUESDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.TUESDAY", "name": "TUESDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.TU", "source_any": null, "type_of_any": 3}}}, "UTC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.UTC", "name": "UTC", "type": "datetime.timezone"}}, "WE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.WE", "name": "WE", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.WE", "source_any": null, "type_of_any": 3}}}, "WEDNESDAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.WEDNESDAY", "name": "WEDNESDAY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.WE", "source_any": null, "type_of_any": 3}}}, "WEEKDAYS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.WEEKDAYS", "name": "WEEKDAYS", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "matplotlib.dates.MO", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.TU", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.WE", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.TH", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.FR", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.SA", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "matplotlib.dates.SU", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "WEEKLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.WEEKLY", "name": "WEEKLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.WEEKLY", "source_any": null, "type_of_any": 3}}}, "WeekdayLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.WeekdayLocator", "name": "WeekdayLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.WeekdayLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.WeekdayLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "byweekday", "interval", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.WeekdayLocator.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.WeekdayLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.WeekdayLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "YEARLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.YEARLY", "name": "YEARLY", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.YEARLY", "source_any": null, "type_of_any": 3}}}, "YearLocator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.dates.RRuleLocator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.YearLocator", "name": "YearLocator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.YearLocator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.YearLocator", "matplotlib.dates.RRuleLocator", "matplotlib.dates.DateLocator", "matplotlib.ticker.Locator", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "base", "month", "day", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.YearLocator.__init__", "name": "__init__", "type": null}}, "_create_rrule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vmin", "vmax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.YearLocator._create_rrule", "name": "_create_rrule", "type": null}}, "base": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.YearLocator.base", "name": "base", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.YearLocator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.YearLocator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SwitchableDateConverter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates._SwitchableDateConverter", "name": "_SwitchableDateConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates._SwitchableDateConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates._SwitchableDateConverter", "builtins.object"], "names": {".class": "SymbolTable", "_get_converter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.dates._SwitchableDateConverter._get_converter", "name": "_get_converter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.dates._SwitchableDateConverter._get_converter", "name": "_get_converter", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_converter of _SwitchableDateConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "axisinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._SwitchableDateConverter.axisinfo", "name": "axisinfo", "type": null}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._SwitchableDateConverter.convert", "name": "convert", "type": null}}, "default_units": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._SwitchableDateConverter.default_units", "name": "default_units", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates._SwitchableDateConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates._SwitchableDateConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.dates.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_api": {".class": "SymbolTableNode", "cross_ref": "matplotlib._api", "kind": "Gdef", "module_public": false}, "_dateutil_parser_parse_np_vectorized": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates._dateutil_parser_parse_np_vectorized", "name": "_dateutil_parser_parse_np_vectorized", "type": "numpy.vectorize"}}, "_dt64_to_ordinalf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._dt64_to_ordinalf", "name": "_dt64_to_ordinalf", "type": null}}, "_epoch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates._epoch", "name": "_epoch", "type": {".class": "NoneType"}}}, "_from_ordinalf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._from_ordinalf", "name": "_from_ordinalf", "type": null}}, "_from_ordinalf_np_vectorized": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates._from_ordinalf_np_vectorized", "name": "_from_ordinalf_np_vectorized", "type": "numpy.vectorize"}}, "_get_tzinfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._get_tzinfo", "name": "_get_tzinfo", "type": null}}, "_log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates._log", "name": "_log", "type": "logging.Logger"}}, "_ordinalf_to_timedelta_np_vectorized": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.dates._ordinalf_to_timedelta_np_vectorized", "name": "_ordinalf_to_timedelta_np_vectorized", "type": "numpy.vectorize"}}, "_reset_epoch_test_example": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._reset_epoch_test_example", "name": "_reset_epoch_test_example", "type": null}}, "_wrap_in_tex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates._wrap_in_tex", "name": "_wrap_in_tex", "type": null}}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef", "module_public": false}, "date2num": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.date2num", "name": "date2num", "type": null}}, "datestr2num": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["d", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.datestr2num", "name": "datestr2num", "type": null}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "dateutil": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.dateutil", "name": "dateutil", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.dateutil", "source_any": null, "type_of_any": 3}}}, "drange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dstart", "dend", "delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.drange", "name": "drange", "type": null}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "get_epoch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.get_epoch", "name": "get_epoch", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "mpl": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "num2date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.num2date", "name": "num2date", "type": null}}, "num2timedelta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.num2timedelta", "name": "num2timedelta", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "relativedelta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.relativedelta", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.relativedelta", "source_any": null, "type_of_any": 3}}}, "rrule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "matplotlib.dates.rrule", "name": "r<PERSON>le", "type": {".class": "AnyType", "missing_import_name": "matplotlib.dates.rrule", "source_any": null, "type_of_any": 3}}}, "rrulewrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.dates.r<PERSON>lew<PERSON>per", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.dates.r<PERSON>lew<PERSON>per", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.dates", "mro": ["matplotlib.dates.r<PERSON>lew<PERSON>per", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "freq", "tzinfo", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper.__init__", "name": "__init__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper.__setstate__", "name": "__setstate__", "type": null}}, "_attach_tzinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dt", "tzinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper._attach_tzinfo", "name": "_attach_tzinfo", "type": null}}, "_aware_return_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "returns_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper._aware_return_wrapper", "name": "_aware_return_wrapper", "type": null}}, "_base_tzinfo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.rrulewrapper._base_tzinfo", "name": "_base_tzinfo", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_construct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.rrulewrapper._construct", "name": "_construct", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_rrule": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.rrulewrapper._rrule", "name": "_r<PERSON>le", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tzinfo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.dates.rrulewrapper._tzinfo", "name": "_tzinfo", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_update_rrule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper._update_rrule", "name": "_update_rrule", "type": null}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.rrulewrapper.set", "name": "set", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.dates.rrulewrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.dates.r<PERSON>lew<PERSON>per", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "set_epoch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.dates.set_epoch", "name": "set_epoch", "type": null}}, "ticker": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker", "kind": "Gdef", "module_public": false}, "units": {".class": "SymbolTableNode", "cross_ref": "matplotlib.units", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\dates.py"}
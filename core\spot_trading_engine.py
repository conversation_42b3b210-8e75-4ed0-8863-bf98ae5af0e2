#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现货交易执行引擎
Spot Trading Execution Engine

专门为现货交易设计的高效执行引擎
"""

import asyncio
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
import json

from spot_snowball_strategy import SpotSnowballStrategy, TradeSetup, SignalQuality

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """持仓信息"""
    pair: str
    side: str           # 'long' for spot buying
    size: float         # 持仓数量
    entry_price: float  # 入场价格
    current_price: float # 当前价格
    stop_loss: float    # 止损价格
    take_profit: float  # 止盈价格
    unrealized_pnl: float # 未实现盈亏
    entry_time: datetime
    strategy: str

@dataclass
class Order:
    """订单信息"""
    id: str
    pair: str
    side: str           # 'buy' or 'sell'
    type: str           # 'market', 'limit', 'stop'
    amount: float
    price: float
    status: str         # 'pending', 'filled', 'cancelled'
    created_time: datetime
    filled_time: Optional[datetime] = None

class SpotTradingEngine:
    """现货交易执行引擎"""
    
    def __init__(self, exchange_client, initial_capital: float = 10000):
        self.exchange = exchange_client
        self.strategy = SpotSnowballStrategy(initial_capital)
        
        # 交易状态
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.is_running = False
        
        # 执行参数
        self.max_slippage = 0.001       # 最大滑点0.1%
        self.order_timeout = 30         # 订单超时30秒
        self.price_update_interval = 5  # 价格更新间隔5秒
        
        # 风险控制
        self.daily_loss_limit = initial_capital * 0.03  # 日亏损限制3%
        self.max_drawdown_limit = 0.10  # 最大回撤10%
        self.daily_pnl = 0.0
        self.peak_capital = initial_capital
        
        # 交易记录
        self.trade_log = []
        self.performance_log = []
        
    async def start_trading(self):
        """启动交易"""
        self.is_running = True
        logger.info("🚀 现货交易引擎启动")
        
        try:
            # 启动主要任务
            await asyncio.gather(
                self._market_data_loop(),
                self._signal_generation_loop(),
                self._order_management_loop(),
                self._risk_monitoring_loop(),
                self._performance_tracking_loop()
            )
        except Exception as e:
            logger.error(f"交易引擎错误: {e}")
        finally:
            self.is_running = False
            logger.info("交易引擎已停止")
    
    async def _market_data_loop(self):
        """市场数据循环"""
        while self.is_running:
            try:
                # 获取市场数据
                for pair in self.strategy.trading_pairs:
                    ticker = await self.exchange.fetch_ticker(pair)
                    
                    # 更新持仓的当前价格
                    if pair in self.positions:
                        self.positions[pair].current_price = ticker['last']
                        self._update_position_pnl(pair)
                
                await asyncio.sleep(self.price_update_interval)
                
            except Exception as e:
                logger.error(f"市场数据更新错误: {e}")
                await asyncio.sleep(5)
    
    async def _signal_generation_loop(self):
        """信号生成循环"""
        while self.is_running:
            try:
                for pair in ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']:
                    # 获取K线数据
                    ohlcv = await self.exchange.fetch_ohlcv(pair, '15m', limit=100)
                    df = self._ohlcv_to_dataframe(ohlcv)
                    
                    # 生成交易设置
                    trade_setup = self.strategy.generate_trade_setup(pair, df)
                    
                    if trade_setup and self._can_open_position(pair):
                        await self._execute_trade_setup(trade_setup)
                
                await asyncio.sleep(60)  # 每分钟检查一次信号
                
            except Exception as e:
                logger.error(f"信号生成错误: {e}")
                await asyncio.sleep(30)
    
    async def _order_management_loop(self):
        """订单管理循环"""
        while self.is_running:
            try:
                # 检查挂单状态
                for order_id, order in list(self.orders.items()):
                    if order.status == 'pending':
                        # 检查订单是否成交
                        exchange_order = await self.exchange.fetch_order(order_id, order.pair)
                        
                        if exchange_order['status'] == 'closed':
                            order.status = 'filled'
                            order.filled_time = datetime.now()
                            await self._handle_order_filled(order)
                        
                        elif (datetime.now() - order.created_time).seconds > self.order_timeout:
                            # 订单超时，取消订单
                            await self.exchange.cancel_order(order_id, order.pair)
                            order.status = 'cancelled'
                            logger.warning(f"订单超时取消: {order_id}")
                
                # 检查止损止盈
                await self._check_stop_orders()
                
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"订单管理错误: {e}")
                await asyncio.sleep(10)
    
    async def _risk_monitoring_loop(self):
        """风险监控循环"""
        while self.is_running:
            try:
                # 检查日亏损限制
                if self.daily_pnl < -self.daily_loss_limit:
                    logger.warning("触发日亏损限制，停止交易")
                    await self._emergency_close_all()
                
                # 检查最大回撤
                current_capital = self.strategy.current_capital
                if current_capital < self.peak_capital * (1 - self.max_drawdown_limit):
                    logger.warning("触发最大回撤限制，停止交易")
                    await self._emergency_close_all()
                
                # 更新峰值资金
                if current_capital > self.peak_capital:
                    self.peak_capital = current_capital
                
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"风险监控错误: {e}")
                await asyncio.sleep(30)
    
    async def _performance_tracking_loop(self):
        """表现跟踪循环"""
        while self.is_running:
            try:
                # 记录表现数据
                performance = {
                    'timestamp': datetime.now().isoformat(),
                    'capital': self.strategy.current_capital,
                    'daily_pnl': self.daily_pnl,
                    'positions_count': len(self.positions),
                    'unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values())
                }
                
                self.performance_log.append(performance)
                
                # 每小时保存一次日志
                if len(self.performance_log) % 12 == 0:  # 5分钟 * 12 = 1小时
                    await self._save_performance_log()
                
                await asyncio.sleep(300)  # 5分钟记录一次
                
            except Exception as e:
                logger.error(f"表现跟踪错误: {e}")
                await asyncio.sleep(300)
    
    def _ohlcv_to_dataframe(self, ohlcv: List) -> 'pd.DataFrame':
        """将OHLCV数据转换为DataFrame"""
        import pandas as pd
        
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        return df
    
    def _can_open_position(self, pair: str) -> bool:
        """检查是否可以开仓"""
        # 检查是否已有持仓
        if pair in self.positions:
            return False
        
        # 检查最大持仓数量
        if len(self.positions) >= self.strategy.max_positions:
            return False
        
        # 检查资金充足性
        required_capital = self.strategy.current_capital * self.strategy.position_size_pct
        if required_capital > self.strategy.current_capital * 0.8:  # 保留20%现金
            return False
        
        return True
    
    async def _execute_trade_setup(self, trade_setup: TradeSetup):
        """执行交易设置"""
        try:
            logger.info(f"执行交易: {trade_setup.pair} {trade_setup.direction}")
            
            # 计算订单参数
            if trade_setup.direction == 'buy':
                side = 'buy'
                amount = trade_setup.position_size
            else:
                side = 'sell'
                amount = trade_setup.position_size
            
            # 使用市价单快速成交
            order = await self.exchange.create_market_order(
                trade_setup.pair, side, amount
            )
            
            # 记录订单
            order_obj = Order(
                id=order['id'],
                pair=trade_setup.pair,
                side=side,
                type='market',
                amount=amount,
                price=trade_setup.entry_price,
                status='filled',
                created_time=datetime.now(),
                filled_time=datetime.now()
            )
            
            self.orders[order['id']] = order_obj
            
            # 创建持仓
            position = Position(
                pair=trade_setup.pair,
                side='long',  # 现货只能做多
                size=amount,
                entry_price=order['average'] or trade_setup.entry_price,
                current_price=order['average'] or trade_setup.entry_price,
                stop_loss=trade_setup.stop_loss,
                take_profit=trade_setup.take_profit,
                unrealized_pnl=0.0,
                entry_time=datetime.now(),
                strategy=self.strategy.name
            )
            
            self.positions[trade_setup.pair] = position
            
            # 记录交易日志
            self.trade_log.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'open_position',
                'pair': trade_setup.pair,
                'side': side,
                'amount': amount,
                'price': position.entry_price,
                'signal_quality': asdict(trade_setup.signal_quality)
            })
            
            logger.info(f"✅ 开仓成功: {trade_setup.pair} {amount:.6f} @ {position.entry_price:.4f}")
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
    
    async def _check_stop_orders(self):
        """检查止损止盈"""
        for pair, position in list(self.positions.items()):
            current_price = position.current_price
            
            # 检查止损
            if current_price <= position.stop_loss:
                await self._close_position(pair, 'stop_loss')
            
            # 检查止盈
            elif current_price >= position.take_profit:
                await self._close_position(pair, 'take_profit')
    
    async def _close_position(self, pair: str, reason: str):
        """平仓"""
        try:
            position = self.positions[pair]
            
            # 卖出持仓
            order = await self.exchange.create_market_order(
                pair, 'sell', position.size
            )
            
            # 计算盈亏
            exit_price = order['average'] or position.current_price
            pnl = (exit_price - position.entry_price) * position.size
            
            # 更新资金
            self.strategy.update_capital(pnl)
            self.daily_pnl += pnl
            
            # 记录交易
            self.trade_log.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'close_position',
                'pair': pair,
                'reason': reason,
                'entry_price': position.entry_price,
                'exit_price': exit_price,
                'pnl': pnl,
                'duration': (datetime.now() - position.entry_time).total_seconds() / 3600
            })
            
            # 移除持仓
            del self.positions[pair]
            
            logger.info(f"✅ 平仓完成: {pair} 盈亏: {pnl:.2f} USDT 原因: {reason}")
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
    
    def _update_position_pnl(self, pair: str):
        """更新持仓盈亏"""
        position = self.positions[pair]
        position.unrealized_pnl = (position.current_price - position.entry_price) * position.size
    
    async def _emergency_close_all(self):
        """紧急平仓所有持仓"""
        logger.warning("🚨 执行紧急平仓")
        
        for pair in list(self.positions.keys()):
            await self._close_position(pair, 'emergency')
        
        self.is_running = False
    
    async def _save_performance_log(self):
        """保存表现日志"""
        try:
            log_file = f"logs/spot_trading_performance_{datetime.now().strftime('%Y%m%d')}.json"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'performance_log': self.performance_log,
                    'trade_log': self.trade_log,
                    'strategy_summary': self.strategy.get_performance_summary()
                }, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存日志失败: {e}")
    
    def get_status_summary(self) -> Dict:
        """获取状态摘要"""
        return {
            'is_running': self.is_running,
            'current_capital': self.strategy.current_capital,
            'daily_pnl': self.daily_pnl,
            'positions_count': len(self.positions),
            'total_unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
            'peak_capital': self.peak_capital,
            'drawdown': (self.peak_capital - self.strategy.current_capital) / self.peak_capital
        }

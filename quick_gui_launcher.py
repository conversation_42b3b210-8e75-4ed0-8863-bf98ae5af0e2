#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速GUI启动器
Quick GUI Launcher

快速启动不同的GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from pathlib import Path

class QuickGUILauncher:
    """快速GUI启动器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("⚡ 快速GUI启动器")
        self.root.geometry("600x400")
        self.root.configure(bg='#2c3e50')
        
        # 热门GUI列表
        self.popular_guis = [
            {
                'name': '💎 简化现货界面',
                'file': 'core/simple_spot_gui.py',
                'description': '简洁清爽，新手首选',
                'color': '#3498db'
            },
            {
                'name': '🚀 优化版界面', 
                'file': 'core/optimized_trading_gui.py',
                'description': '性能优化，流畅运行',
                'color': '#e74c3c'
            },
            {
                'name': '📊 多交易对界面',
                'file': 'core/multi_pairs_gui.py', 
                'description': '22个币种，全面监控',
                'color': '#f39c12'
            },
            {
                'name': '❄️ 滚雪球策略',
                'file': 'core/spot_snowball_gui.py',
                'description': '复利增长，长期投资',
                'color': '#9b59b6'
            },
            {
                'name': '💼 终极现货界面',
                'file': 'core/ultimate_spot_trading_gui.py',
                'description': '专业级现货交易',
                'color': '#1abc9c'
            },
            {
                'name': '👑 当前终极界面',
                'file': 'launch_ultimate_optimized_system.py',
                'description': '企业级，功能最全',
                'color': '#34495e'
            }
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="⚡ 快速GUI启动器",
            font=('Microsoft YaHei', 20, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(
            self.root,
            text="选择您喜欢的交易界面",
            font=('Microsoft YaHei', 12),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 按钮容器
        button_frame = tk.Frame(self.root, bg='#2c3e50')
        button_frame.pack(expand=True, padx=40)
        
        # 创建按钮
        for i, gui in enumerate(self.popular_guis):
            row = i // 2
            col = i % 2
            
            btn = tk.Button(
                button_frame,
                text=gui['name'],
                font=('Microsoft YaHei', 11, 'bold'),
                bg=gui['color'],
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                width=18,
                height=2,
                command=lambda g=gui: self.launch_gui(g)
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            # 描述标签
            desc_label = tk.Label(
                button_frame,
                text=gui['description'],
                font=('Microsoft YaHei', 9),
                fg='#bdc3c7',
                bg='#2c3e50'
            )
            desc_label.grid(row=row*2+1, column=col, padx=10, pady=(0, 10))
        
        # 配置网格权重
        for i in range(2):
            button_frame.columnconfigure(i, weight=1)
        
        # 底部按钮
        bottom_frame = tk.Frame(self.root, bg='#2c3e50')
        bottom_frame.pack(fill='x', padx=20, pady=20)
        
        # 查看所有选项按钮
        all_options_btn = tk.Button(
            bottom_frame,
            text="🎨 查看所有界面选项",
            font=('Microsoft YaHei', 10),
            bg='#95a5a6',
            fg='white',
            relief='raised',
            bd=2,
            padx=15,
            pady=8,
            command=self.launch_full_selector
        )
        all_options_btn.pack(side='left')
        
        # 退出按钮
        exit_btn = tk.Button(
            bottom_frame,
            text="❌ 退出",
            font=('Microsoft YaHei', 10),
            bg='#e74c3c',
            fg='white',
            relief='raised',
            bd=2,
            padx=15,
            pady=8,
            command=self.root.quit
        )
        exit_btn.pack(side='right')
        
    def launch_gui(self, gui_config):
        """启动指定的GUI"""
        gui_path = Path(__file__).parent / gui_config['file']
        
        if not gui_path.exists():
            messagebox.showerror("错误", f"GUI文件不存在: {gui_path}")
            return
            
        try:
            # 启动GUI
            subprocess.Popen([sys.executable, str(gui_path)])
            
            # 显示成功消息
            messagebox.showinfo("启动成功", f"正在启动 {gui_config['name']}...")
            
            # 最小化启动器
            self.root.iconify()
            
        except Exception as e:
            messagebox.showerror("启动错误", f"无法启动GUI界面:\n{str(e)}")
    
    def launch_full_selector(self):
        """启动完整的GUI选择器"""
        selector_path = Path(__file__).parent / "gui_selector.py"
        
        try:
            subprocess.Popen([sys.executable, str(selector_path)])
            messagebox.showinfo("启动中", "正在启动完整的GUI选择器...")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("启动错误", f"无法启动GUI选择器:\n{str(e)}")
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

if __name__ == "__main__":
    print("⚡ 启动快速GUI启动器...")
    app = QuickGUILauncher()
    app.run()

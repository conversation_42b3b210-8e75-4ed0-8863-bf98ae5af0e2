#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI修复验证测试
GUI Fixes Verification Test

验证GUI界面修复效果
"""

import sys
import os
import time
import threading

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_chinese_constants():
    """测试中文常量"""
    print("\n🇨🇳 测试中文常量...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 测试新增的中文常量
        new_constants = {
            "订单已提交": ChineseUIConstants.DIALOG_ORDER_PLACED,
            "错误": ChineseUIConstants.DIALOG_ERROR,
            "紧急停止": ChineseUIConstants.DIALOG_EMERGENCY_STOP,
            "确认实盘交易": ChineseUIConstants.DIALOG_CONFIRM_LIVE_TRADING,
            "正在下买单": ChineseUIConstants.MSG_PLACING_BUY_ORDER,
            "正在下卖单": ChineseUIConstants.MSG_PLACING_SELL_ORDER,
            "买单已成功提交": ChineseUIConstants.MSG_BUY_ORDER_SUCCESS,
            "卖单已成功提交": ChineseUIConstants.MSG_SELL_ORDER_SUCCESS,
        }
        
        print("  ✅ 新增中文常量:")
        for name, value in new_constants.items():
            print(f"    • {name}: {value}")
        
        # 测试转换方法
        print("  ✅ 转换方法测试:")
        print(f"    • Market -> {ChineseUIConstants.get_order_type_chinese('Market')}")
        print(f"    • BUY -> {ChineseUIConstants.get_order_side_chinese('BUY')}")
        print(f"    • SELL -> {ChineseUIConstants.get_order_side_chinese('SELL')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 中文常量测试失败: {e}")
        return False


def test_gui_chinese_fixes():
    """测试GUI中文化修复"""
    print("\n🔧 测试GUI中文化修复...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试订单操作方法
        print("  ✅ 测试订单操作方法:")
        
        # 模拟买单操作
        gui.symbol_var.set("BTC/USDT")
        gui.order_type_var.set("Market")
        gui.quantity_var.set("0.1")
        gui.price_var.set("45000")
        
        print("    • 买单方法已准备")
        
        # 模拟卖单操作
        print("    • 卖单方法已准备")
        
        # 测试数据刷新方法
        print("  ✅ 测试数据刷新方法:")
        gui.refresh_market_data()
        print("    • 市场数据刷新完成")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI中文化修复测试失败: {e}")
        return False


def test_message_localization():
    """测试消息本地化"""
    print("\n💬 测试消息本地化...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 测试对话框消息
        dialog_tests = {
            "订单确认": ChineseUIConstants.DIALOG_ORDER_PLACED,
            "错误提示": ChineseUIConstants.DIALOG_ERROR,
            "警告提示": ChineseUIConstants.DIALOG_WARNING,
            "信息提示": ChineseUIConstants.DIALOG_INFO,
        }
        
        print("  ✅ 对话框消息:")
        for name, value in dialog_tests.items():
            print(f"    • {name}: {value}")
        
        # 测试系统消息
        system_tests = {
            "正在刷新市场数据": ChineseUIConstants.MSG_REFRESHING_MARKET,
            "市场数据已刷新": ChineseUIConstants.MSG_MARKET_REFRESHED,
            "正在下买单": ChineseUIConstants.MSG_PLACING_BUY_ORDER,
            "正在下卖单": ChineseUIConstants.MSG_PLACING_SELL_ORDER,
        }
        
        print("  ✅ 系统消息:")
        for name, value in system_tests.items():
            print(f"    • {name}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息本地化测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        
        # 测试错误消息
        print("  ✅ 错误消息测试:")
        print(f"    • 错误对话框标题: {ChineseUIConstants.DIALOG_ERROR}")
        print(f"    • 无效输入消息: 无效的数量或价格输入")
        
        # 测试日志消息
        print("  ✅ 日志消息测试:")
        gui.log_message("测试中文日志消息")
        gui.log_message(f"🟢 {ChineseUIConstants.MSG_PLACING_BUY_ORDER}: 测试")
        gui.log_message(f"🔴 {ChineseUIConstants.MSG_PLACING_SELL_ORDER}: 测试")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def test_data_formatting():
    """测试数据格式化"""
    print("\n📊 测试数据格式化...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 测试价格格式化
        price = 45000.1234
        formatted_price = ChineseUIConstants.format_price(price)
        print(f"  ✅ 价格格式化: {price} -> {formatted_price}")
        
        # 测试数量格式化
        quantity = 0.123456
        formatted_quantity = ChineseUIConstants.format_quantity(quantity)
        print(f"  ✅ 数量格式化: {quantity} -> {formatted_quantity}")
        
        # 测试百分比格式化
        percentage = 2.5678
        formatted_percentage = ChineseUIConstants.PERCENTAGE_FORMAT.format(percentage)
        print(f"  ✅ 百分比格式化: {percentage} -> {formatted_percentage}")
        
        # 测试货币格式化
        amount = 10000.50
        formatted_currency = ChineseUIConstants.CURRENCY_FORMAT.format(amount)
        print(f"  ✅ 货币格式化: {amount} -> {formatted_currency}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据格式化测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🔍 开始GUI修复验证测试...")
    print("=" * 60)
    
    tests = [
        ("中文常量测试", test_chinese_constants),
        ("GUI中文化修复测试", test_gui_chinese_fixes),
        ("消息本地化测试", test_message_localization),
        ("错误处理测试", test_error_handling),
        ("数据格式化测试", test_data_formatting),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计:")
    print(f"  • 总测试数: {total}")
    print(f"  • 通过数: {passed}")
    print(f"  • 失败数: {total - passed}")
    print(f"  • 通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！GUI修复验证成功！")
        return True
    else:
        print(f"\n⚠️ {total - passed}个测试失败，需要进一步修复。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

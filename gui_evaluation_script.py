#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI界面评估脚本
GUI Interface Evaluation Script

评估终极版现货交易系统中各个GUI界面的可用性和特性
"""

import sys
import os
import importlib
from pathlib import Path

# 添加core目录到路径
current_dir = Path(__file__).parent
core_dir = current_dir / "core"
sys.path.insert(0, str(core_dir))

def evaluate_gui_interfaces():
    """评估GUI界面"""
    
    print("🔍 终极版现货交易系统 - GUI界面评估报告")
    print("=" * 60)
    
    # 定义要测试的GUI界面
    gui_interfaces = [
        {
            'module': 'ultimate_spot_trading_gui',
            'class': 'UltimateSpotTradingGUI',
            'name': '终极版现货交易GUI',
            'description': '主要的现货交易学习平台界面'
        },
        {
            'module': 'spot_snowball_gui',
            'class': 'SpotSnowballGUI',
            'name': '现货滚雪球GUI',
            'description': '专注于滚雪球策略的交易界面'
        },
        {
            'module': 'ultimate_trading_gui',
            'class': 'UltimateTradingGUI',
            'name': '终极版交易GUI',
            'description': '简洁高效的图形用户界面'
        },
        {
            'module': 'optimized_trading_gui',
            'class': 'OptimizedTradingGUI',
            'name': '优化版交易GUI',
            'description': '解决卡顿问题的轻量级版本'
        },
        {
            'module': 'fusion_trading_gui',
            'class': 'FusionTradingGUI',
            'name': '融合交易GUI',
            'description': '结合所有GUI最佳功能的终极体验'
        },
        {
            'module': 'multi_pairs_gui',
            'class': 'MultiPairsGUI',
            'name': '多交易对GUI',
            'description': '支持22个交易对的稳定版本'
        },
        {
            'module': 'simple_gui',
            'class': 'SpotTradingGUI',
            'name': '简单现货GUI',
            'description': '专业现货交易系统界面'
        }
    ]
    
    evaluation_results = []
    
    for gui_info in gui_interfaces:
        print(f"\n📋 评估: {gui_info['name']}")
        print("-" * 40)
        
        result = {
            'name': gui_info['name'],
            'module': gui_info['module'],
            'class': gui_info['class'],
            'description': gui_info['description'],
            'importable': False,
            'features': [],
            'dependencies': [],
            'errors': [],
            'score': 0
        }
        
        try:
            # 尝试导入模块
            module = importlib.import_module(gui_info['module'])
            print(f"✅ 模块导入成功: {gui_info['module']}")
            
            # 尝试获取类
            gui_class = getattr(module, gui_info['class'])
            print(f"✅ 类获取成功: {gui_info['class']}")
            
            result['importable'] = True
            result['score'] += 30
            
            # 分析类特性
            class_methods = [method for method in dir(gui_class) if not method.startswith('_')]
            result['features'] = class_methods[:10]  # 前10个方法
            
            print(f"📊 发现 {len(class_methods)} 个方法")
            
            # 检查特定功能
            key_features = [
                'setup_ui', 'create_widgets', 'start_trading', 'stop_trading',
                'connect_gate', 'update_data', 'show_config', 'run'
            ]
            
            found_features = [f for f in key_features if f in class_methods]
            result['score'] += len(found_features) * 5
            
            print(f"🎯 核心功能: {len(found_features)}/{len(key_features)}")
            
            # 尝试读取源码分析
            try:
                source_file = core_dir / f"{gui_info['module']}.py"
                if source_file.exists():
                    with open(source_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 分析依赖
                    import_lines = [line.strip() for line in content.split('\n') 
                                  if line.strip().startswith(('import ', 'from '))]
                    result['dependencies'] = import_lines[:5]  # 前5个导入
                    
                    # 分析代码质量指标
                    lines = content.split('\n')
                    code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
                    
                    print(f"📄 代码行数: {len(lines)}")
                    print(f"💻 有效代码: {len(code_lines)}")
                    
                    # 根据代码量评分
                    if len(code_lines) > 500:
                        result['score'] += 20
                    elif len(code_lines) > 200:
                        result['score'] += 15
                    else:
                        result['score'] += 10
                    
                    # 检查特殊功能
                    special_features = {
                        'API连接': 'api' in content.lower(),
                        '实时数据': 'real' in content.lower() or 'websocket' in content.lower(),
                        '图表显示': 'chart' in content.lower() or 'matplotlib' in content.lower(),
                        '策略系统': 'strategy' in content.lower(),
                        '风险管理': 'risk' in content.lower(),
                        '多线程': 'thread' in content.lower(),
                        '配置管理': 'config' in content.lower(),
                        '日志记录': 'log' in content.lower()
                    }
                    
                    found_special = [name for name, exists in special_features.items() if exists]
                    result['score'] += len(found_special) * 3
                    
                    print(f"🌟 特殊功能: {', '.join(found_special)}")
                    
            except Exception as e:
                result['errors'].append(f"源码分析失败: {str(e)}")
            
        except ImportError as e:
            print(f"❌ 导入失败: {str(e)}")
            result['errors'].append(f"导入错误: {str(e)}")
        except AttributeError as e:
            print(f"❌ 类不存在: {str(e)}")
            result['errors'].append(f"类错误: {str(e)}")
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            result['errors'].append(f"未知错误: {str(e)}")
        
        # 计算最终评分
        if result['importable']:
            result['score'] = min(result['score'], 100)  # 最高100分
        
        print(f"🏆 评分: {result['score']}/100")
        evaluation_results.append(result)
    
    return evaluation_results

def generate_recommendation_report(results):
    """生成推荐报告"""
    
    print("\n" + "=" * 60)
    print("📊 GUI界面推荐报告")
    print("=" * 60)
    
    # 按评分排序
    sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)
    
    print("\n🏆 评分排行榜:")
    print("-" * 40)
    for i, result in enumerate(sorted_results, 1):
        status = "✅ 可用" if result['importable'] else "❌ 不可用"
        print(f"{i}. {result['name']} - {result['score']}/100 {status}")
    
    # 找出最佳GUI
    best_gui = sorted_results[0] if sorted_results else None
    
    if best_gui and best_gui['importable']:
        print(f"\n🥇 最佳GUI推荐: {best_gui['name']}")
        print(f"📝 描述: {best_gui['description']}")
        print(f"🏆 评分: {best_gui['score']}/100")
        print(f"🚀 启动命令: python core/{best_gui['module']}.py")
        
        if best_gui['score'] >= 80:
            print("💎 评级: 优秀 - 强烈推荐使用")
        elif best_gui['score'] >= 60:
            print("⭐ 评级: 良好 - 推荐使用")
        elif best_gui['score'] >= 40:
            print("🔶 评级: 一般 - 可以使用")
        else:
            print("⚠️ 评级: 需要改进")
    
    # 分类推荐
    print(f"\n📋 分类推荐:")
    print("-" * 40)
    
    available_guis = [r for r in sorted_results if r['importable']]
    
    if available_guis:
        print("🎯 新手推荐:")
        simple_guis = [r for r in available_guis if 'simple' in r['module'].lower() or 'basic' in r['description'].lower()]
        if simple_guis:
            gui = simple_guis[0]
            print(f"   • {gui['name']} - {gui['description']}")
        else:
            gui = available_guis[-1]  # 评分最低但可用的
            print(f"   • {gui['name']} - {gui['description']}")
        
        print("\n💼 专业用户推荐:")
        pro_guis = [r for r in available_guis if r['score'] >= 70]
        if pro_guis:
            gui = pro_guis[0]
            print(f"   • {gui['name']} - {gui['description']}")
        
        print("\n🚀 功能最全推荐:")
        if available_guis:
            gui = available_guis[0]
            print(f"   • {gui['name']} - {gui['description']}")
    
    # 问题总结
    problematic_guis = [r for r in results if not r['importable']]
    if problematic_guis:
        print(f"\n⚠️ 需要修复的GUI ({len(problematic_guis)}个):")
        print("-" * 40)
        for gui in problematic_guis:
            print(f"❌ {gui['name']}")
            for error in gui['errors'][:2]:  # 只显示前2个错误
                print(f"   └─ {error}")

def main():
    """主函数"""
    try:
        # 评估GUI界面
        results = evaluate_gui_interfaces()
        
        # 生成推荐报告
        generate_recommendation_report(results)
        
        print(f"\n✅ 评估完成! 共评估了 {len(results)} 个GUI界面")
        
        # 保存详细结果到文件
        import json
        with open('gui_evaluation_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("📄 详细结果已保存到 gui_evaluation_results.json")
        
    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()

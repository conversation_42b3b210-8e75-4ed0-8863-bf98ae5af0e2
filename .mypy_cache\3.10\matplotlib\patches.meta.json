{"data_mtime": 1748490882, "dep_lines": [1, 2, 3, 4, 5, 10, 11, 1, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 20, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.artist", "matplotlib.axes", "matplotlib.backend_bases", "matplotlib.path", "matplotlib.transforms", "numpy.typing", "matplotlib.typing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typing", "numpy", "builtins", "re", "pprint", "copy", "inspect", "os", "warnings", "operator", "html", "sys", "collections", "string", "itertools", "contextlib", "types", "traceback", "_frozen_importlib", "abc", "enum", "matplotlib._enums", "matplotlib.axes._axes", "matplotlib.axes._base", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence"], "hash": "d97cd2225ef53fb05823356f3e2e102436f75b22", "id": "matplotlib.patches", "ignore_all": true, "interface_hash": "cf1e8351a54f70ea2a508f83318e11c8188fa7cc", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\patches.pyi", "plugin_data": null, "size": 22649, "suppressed": [], "version_id": "1.15.0"}
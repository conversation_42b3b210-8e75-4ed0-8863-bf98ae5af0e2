{".class": "MypyFile", "_fullname": "matplotlib.patches", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Annulus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Annulus", "name": "Annulus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Annulus", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "xy", "r", "width", "angle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "xy", "r", "width", "angle", "kwargs"], "arg_types": ["matplotlib.patches.Annulus", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "a": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Annulus.a", "name": "a", "type": "builtins.float"}}, "angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Annulus.angle", "name": "angle", "type": "builtins.property"}}, "b": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Annulus.b", "name": "b", "type": "builtins.float"}}, "center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Annulus.center", "name": "center", "type": "builtins.property"}}, "get_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.get_angle", "name": "get_angle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Annulus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_angle of Annulus", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.get_center", "name": "get_center", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Annulus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_center of Annulus", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_radii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.get_radii", "name": "get_radii", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Annulus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_radii of Annulus", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Annulus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Annulus", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Annulus.radii", "name": "radii", "type": "builtins.property"}}, "set_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_angle", "name": "set_angle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "arg_types": ["matplotlib.patches.Annulus", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_angle of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_center", "name": "set_center", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.patches.Annulus", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_center of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_radii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "r"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_radii", "name": "set_radii", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "r"], "arg_types": ["matplotlib.patches.Annulus", {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_radii of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_semimajor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_semimajor", "name": "set_semimajor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "a"], "arg_types": ["matplotlib.patches.Annulus", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_semimajor of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_semiminor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_semiminor", "name": "set_semiminor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["matplotlib.patches.Annulus", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_semiminor of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Annulus.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib.patches.Annulus", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of Annulus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Annulus.width", "name": "width", "type": "builtins.property"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Annulus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Annulus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Arc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Ellipse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Arc", "name": "Arc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Arc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Arc", "matplotlib.patches.Ellipse", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "theta1", "theta2", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Arc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "theta1", "theta2", "kwargs"], "arg_types": ["matplotlib.patches.Arc", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Arc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theta1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Arc.theta1", "name": "theta1", "type": "builtins.float"}}, "theta2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Arc.theta2", "name": "theta2", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Arc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Arc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Arrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Arrow", "name": "Arrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Arrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Arrow", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["self", "x", "y", "dx", "dy", "width", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Arrow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["self", "x", "y", "dx", "dy", "width", "kwargs"], "arg_types": ["matplotlib.patches.Arrow", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Arrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "x", "y", "dx", "dy", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Arrow.set_data", "name": "set_data", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "x", "y", "dx", "dy", "width"], "arg_types": ["matplotlib.patches.Arrow", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_data of Arrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Arrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Arrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ArrowStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches._Style"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle", "name": "ArrowStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "BarAB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.BarAB", "name": "BarAB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BarAB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.BarAB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "widthA", "angleA", "widthB", "angleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BarAB.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "widthA", "angleA", "widthB", "angleB"], "arg_types": ["matplotlib.patches.ArrowStyle.BarAB", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarAB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.BarAB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.BarAB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.BarAB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BracketA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.BracketA", "name": "BracketA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.BracketA", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketA.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA"], "arg_types": ["matplotlib.patches.ArrowStyle.BracketA", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BracketA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.BracketA.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.BracketA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.BracketA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BracketAB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.BracketAB", "name": "BracketAB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketAB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.BracketAB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA", "widthB", "lengthB", "angleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketAB.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA", "widthB", "lengthB", "angleB"], "arg_types": ["matplotlib.patches.ArrowStyle.BracketAB", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BracketAB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.BracketAB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.BracketAB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.BracketAB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BracketB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.BracketB", "name": "BracketB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.BracketB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthB", "lengthB", "angleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketB.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthB", "lengthB", "angleB"], "arg_types": ["matplotlib.patches.ArrowStyle.BracketB", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BracketB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.BracketB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.BracketB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.BracketB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BracketCurve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.BracketCurve", "name": "BracketCurve", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketCurve", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.BracketCurve", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.BracketCurve.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthA", "lengthA", "angleA"], "arg_types": ["matplotlib.patches.ArrowStyle.BracketCurve", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BracketCurve", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.BracketCurve.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.BracketCurve.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.BracketCurve", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.Curve", "name": "Curve", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Curve", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.Curve", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Curve.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.ArrowStyle.Curve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Curve", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.Curve.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.Curve", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveA", "name": "CurveA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveA", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveA.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveAB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveAB", "name": "CurveAB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveAB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveAB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveAB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveAB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveAB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveB", "name": "CurveB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveBracket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveBracket", "name": "CurveBracket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveBracket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveBracket", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthB", "lengthB", "angleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveBracket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "widthB", "lengthB", "angleB"], "arg_types": ["matplotlib.patches.ArrowStyle.CurveBracket", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CurveBracket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveBracket.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveBracket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveBracket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveFilledA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveFilledA", "name": "CurveFilledA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveFilledA", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledA.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveFilledA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveFilledA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveFilledAB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveFilledAB", "name": "CurveFilledAB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledAB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveFilledAB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledAB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveFilledAB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveFilledAB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurveFilledB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Curve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.CurveFilledB", "name": "CurveFilledB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.CurveFilledB", "matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.CurveFilledB.arrow", "name": "arrow", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.CurveFilledB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.CurveFilledB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Fancy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.Fancy", "name": "Fancy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Fancy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.Fancy", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "tail_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Fancy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "tail_width"], "arg_types": ["matplotlib.patches.ArrowStyle.Fancy", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>cy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.Fancy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.Fancy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.Simple", "name": "Simple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Simple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.Simple", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "tail_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Simple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "tail_width"], "arg_types": ["matplotlib.patches.ArrowStyle.Simple", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Simple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.Simple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.Simple", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wedge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle.Wedge", "name": "Wedge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Wedge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle.Wedge", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "tail_width", "shrink_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle.Wedge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "tail_width", "shrink_factor"], "arg_types": ["matplotlib.patches.ArrowStyle.Wedge", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrink_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.Wedge.shrink_factor", "name": "shrink_factor", "type": "builtins.float"}}, "tail_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle.Wedge.tail_width", "name": "tail_width", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.Wedge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle.Wedge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle._Base", "name": "_Base", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle._Base", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "path", "mutation_size", "linewidth", "aspect_ratio"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle._Base.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "path", "mutation_size", "linewidth", "aspect_ratio"], "arg_types": ["matplotlib.patches.ArrowStyle._Base", "matplotlib.path.Path", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _Base", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.path.Path", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ensure_quadratic_bezier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.patches.ArrowStyle._Base.ensure_quadratic_bezier", "name": "ensure_quadratic_bezier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["matplotlib.path.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_quadratic_bezier of _Base", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.patches.ArrowStyle._Base.ensure_quadratic_bezier", "name": "ensure_quadratic_bezier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["matplotlib.path.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_quadratic_bezier of _Base", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transmute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "path", "mutation_size", "linewidth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle._Base.transmute", "name": "transmute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "path", "mutation_size", "linewidth"], "arg_types": ["matplotlib.patches.ArrowStyle._Base", "matplotlib.path.Path", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transmute of _Base", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.path.Path", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle._Base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle._Base", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ArrowStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ArrowStyle._Curve", "name": "_Curve", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle._Curve", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ArrowStyle._Curve", "matplotlib.patches.ArrowStyle._Base", "matplotlib.patches.ArrowStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "widthA", "widthB", "lengthA", "lengthB", "angleA", "angleB", "scaleA", "scaleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ArrowStyle._Curve.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "head_length", "head_width", "widthA", "widthB", "lengthA", "lengthB", "angleA", "angleB", "scaleA", "scaleB"], "arg_types": ["matplotlib.patches.ArrowStyle._Curve", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Curve", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle._Curve.arrow", "name": "arrow", "type": "builtins.str"}}, "fillbegin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle._Curve.fillbegin", "name": "fillbegin", "type": "builtins.bool"}}, "fillend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ArrowStyle._Curve.fillend", "name": "fillend", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle._Curve.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle._Curve", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ArrowStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ArrowStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Bbox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Bbox", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BoxStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches._Style"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle", "name": "BoxStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "Circle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Circle", "name": "Circle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Circle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Circle", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Circle.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Circle", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Circle", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.patches.BoxStyle.Circle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Circle.pad", "name": "pad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Circle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Circle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DArrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.DArrow", "name": "DArrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.DArrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.DArrow", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.DArrow.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.DArrow", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of DArrow", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.DArrow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.patches.BoxStyle.DArrow", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DArrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.DArrow.pad", "name": "pad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.DArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.DArrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Ellipse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Ellipse", "name": "Ellipse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Ellipse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Ellipse", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Ellipse.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Ellipse", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Ellipse", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Ellipse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.patches.BoxStyle.Ellipse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Ellipse.pad", "name": "pad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Ellipse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Ellipse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LArrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.LArrow", "name": "LArrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.LArrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.LArrow", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.LArrow.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.LArrow", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of LArrow", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.LArrow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.patches.BoxStyle.LArrow", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LArrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.LArrow.pad", "name": "pad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.LArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.LArrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RArrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle.LArrow"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.RArrow", "name": "RArrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.RArrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.RArrow", "matplotlib.patches.BoxStyle.LArrow", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.RArrow.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.RArrow", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of RArrow", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.RArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.RArrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Round": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Round", "name": "Round", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Round", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Round", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Round", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "rounding_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "rounding_size"], "arg_types": ["matplotlib.patches.BoxStyle.Round", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Round", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Round.pad", "name": "pad", "type": "builtins.float"}}, "rounding_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Round.rounding_size", "name": "rounding_size", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Round.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Round", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Round4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Round4", "name": "Round4", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round4", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Round4", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round4.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Round4", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Round4", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "rounding_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Round4.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "rounding_size"], "arg_types": ["matplotlib.patches.BoxStyle.Round4", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Round4", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Round4.pad", "name": "pad", "type": "builtins.float"}}, "rounding_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Round4.rounding_size", "name": "rounding_size", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Round4.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Round4", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Roundtooth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle.Sawtooth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Roundtooth", "name": "Roundtooth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Roundtooth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Roundtooth", "matplotlib.patches.BoxStyle.Sawtooth", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Roundtooth.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Roundtooth", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Roundtooth", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Roundtooth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Roundtooth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sawtooth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Sawtooth", "name": "Sawtooth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Sawtooth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Sawtooth", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Sawtooth.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Sawtooth", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Sawtooth", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "tooth_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Sawtooth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "pad", "tooth_size"], "arg_types": ["matplotlib.patches.BoxStyle.Sawtooth", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Sawtooth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Sawtooth.pad", "name": "pad", "type": "builtins.float"}}, "tooth_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Sawtooth.tooth_size", "name": "tooth_size", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Sawtooth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Sawtooth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Square": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.BoxStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.BoxStyle.Square", "name": "Square", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Square", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.BoxStyle.Square", "matplotlib.patches.BoxStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Square.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "x0", "y0", "width", "height", "mutation_size"], "arg_types": ["matplotlib.patches.BoxStyle.Square", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Square", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.BoxStyle.Square.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.patches.BoxStyle.Square", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Square", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.BoxStyle.Square.pad", "name": "pad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Square.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle.Square", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.BoxStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.BoxStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CapStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.CapStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Circle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Ellipse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Circle", "name": "Circle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Circle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Circle", "matplotlib.patches.Ellipse", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "xy", "radius", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "xy", "radius", "kwargs"], "arg_types": ["matplotlib.patches.Circle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Circle.get_radius", "name": "get_radius", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Circle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_radius of Circle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Circle.radius", "name": "radius", "type": "builtins.property"}}, "set_radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "radius"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Circle.set_radius", "name": "set_radius", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "radius"], "arg_types": ["matplotlib.patches.Circle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_radius of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Circle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Circle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CirclePolygon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.RegularPolygon"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.CirclePolygon", "name": "CirclePolygon", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.CirclePolygon", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.CirclePolygon", "matplotlib.patches.RegularPolygon", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 4], "arg_names": ["self", "xy", "radius", "resolution", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.CirclePolygon.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 4], "arg_names": ["self", "xy", "radius", "resolution", "kwargs"], "arg_types": ["matplotlib.patches.CirclePolygon", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CirclePolygon", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.CirclePolygon.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.CirclePolygon", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectionPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.FancyArrowPatch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionPatch", "name": "ConnectionPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionPatch", "matplotlib.patches.FancyArrowPatch", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "xyA", "xyB", "coordsA", "coordsB", "axesA", "axesB", "arrowstyle", "connectionstyle", "patchA", "patchB", "shrinkA", "shrinkB", "mutation_scale", "mutation_aspect", "clip_on", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "xyA", "xyB", "coordsA", "coordsB", "axesA", "axesB", "arrowstyle", "connectionstyle", "patchA", "patchB", "shrinkA", "shrinkB", "mutation_scale", "mutation_aspect", "clip_on", "kwargs"], "arg_types": ["matplotlib.patches.ConnectionPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.transforms.Transform"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ArrowStyle"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ConnectionStyle"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "axesA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.axesA", "name": "axesA", "type": {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "axesB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.axesB", "name": "axesB", "type": {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "coords1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.coords1", "name": "coords1", "type": {".class": "UnionType", "items": ["builtins.str", "matplotlib.transforms.Transform"], "uses_pep604_syntax": true}}}, "coords2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.coords2", "name": "coords2", "type": {".class": "UnionType", "items": ["builtins.str", "matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_annotation_clip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionPatch.get_annotation_clip", "name": "get_annotation_clip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.ConnectionPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_annotation_clip of ConnectionPatch", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_annotation_clip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionPatch.set_annotation_clip", "name": "set_annotation_clip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["matplotlib.patches.ConnectionPatch", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_annotation_clip of ConnectionPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xy1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.xy1", "name": "xy1", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "xy2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionPatch.xy2", "name": "xy2", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches._Style"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle", "name": "ConnectionStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "Angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle.Angle", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle.Angle", "matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "angleA", "angleB", "rad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "angleA", "angleB", "rad"], "arg_types": ["matplotlib.patches.ConnectionStyle.Angle", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>le", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angleA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Angle.angleA", "name": "angleA", "type": "builtins.float"}}, "angleB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Angle.angleB", "name": "angleB", "type": "builtins.float"}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Angle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Angle", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Angle.rad", "name": "rad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Angle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle.Angle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Angle3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle.Angle3", "name": "Angle3", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle3", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle.Angle3", "matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "angleA", "angleB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle3.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "angleA", "angleB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Angle3", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Angle3", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angleA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Angle3.angleA", "name": "angleA", "type": "builtins.float"}}, "angleB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Angle3.angleB", "name": "angleB", "type": "builtins.float"}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Angle3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Angle3", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Angle3", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Angle3.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle.Angle3", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Arc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle.Arc", "name": "Arc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle.Arc", "matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "angleA", "angleB", "armA", "armB", "rad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "angleA", "angleB", "armA", "armB", "rad"], "arg_types": ["matplotlib.patches.ConnectionStyle.Arc", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Arc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angleA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc.angleA", "name": "angleA", "type": "builtins.float"}}, "angleB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc.angleB", "name": "angleB", "type": "builtins.float"}}, "armA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc.armA", "name": "armA", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "armB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc.armB", "name": "armB", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Arc", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Arc", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc.rad", "name": "rad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Arc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle.Arc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Arc3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle.Arc3", "name": "Arc3", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc3", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle.Arc3", "matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "rad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc3.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "rad"], "arg_types": ["matplotlib.patches.ConnectionStyle.Arc3", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Arc3", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Arc3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Arc3", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Arc3", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Arc3.rad", "name": "rad", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Arc3.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle.Arc3", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle._Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle.Bar", "name": "Bar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Bar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle.Bar", "matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "armA", "armB", "fraction", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Bar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "armA", "armB", "fraction", "angle"], "arg_types": ["matplotlib.patches.ConnectionStyle.Bar", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Bar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Bar.angle", "name": "angle", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "armA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Bar.armA", "name": "armA", "type": "builtins.float"}}, "armB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Bar.armB", "name": "armB", "type": "builtins.float"}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle.Bar.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.ConnectionStyle.Bar", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Bar", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fraction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.ConnectionStyle.Bar.fraction", "name": "fraction", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Bar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle.Bar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.ConnectionStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.ConnectionStyle._Base", "name": "_Base", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle._Base", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.ConnectionStyle._Base", "matplotlib.patches.ConnectionStyle", "matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "posA", "posB", "shrinkA", "shrinkB", "patchA", "patchB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.ConnectionStyle._Base.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "posA", "posB", "shrinkA", "shrinkB", "patchA", "patchB"], "arg_types": ["matplotlib.patches.ConnectionStyle._Base", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _Base", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle._Base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle._Base", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.ConnectionStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.ConnectionStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Ellipse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Ellipse", "name": "Ellipse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Ellipse", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "kwargs"], "arg_types": ["matplotlib.patches.Ellipse", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Ellipse.angle", "name": "angle", "type": "builtins.property"}}, "center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Ellipse.center", "name": "center", "type": "builtins.property"}}, "get_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_angle", "name": "get_angle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_angle of Ellipse", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_center", "name": "get_center", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_center of Ellipse", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_co_vertices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_co_vertices", "name": "get_co_vertices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_co_vertices of Ellipse", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_corners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_corners", "name": "get_corners", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_corners of Ellipse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_height", "name": "get_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height of Ellipse", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_vertices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_vertices", "name": "get_vertices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_vertices of Ellipse", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Ellipse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Ellipse", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Ellipse.height", "name": "height", "type": "builtins.property"}}, "set_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.set_angle", "name": "set_angle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "arg_types": ["matplotlib.patches.Ellipse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_angle of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.set_center", "name": "set_center", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.patches.Ellipse", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_center of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.set_height", "name": "set_height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "height"], "arg_types": ["matplotlib.patches.Ellipse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_height of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Ellipse.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib.patches.Ellipse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of Ellipse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Ellipse.width", "name": "width", "type": "builtins.property"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Ellipse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Ellipse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FancyArrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Polygon"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.FancyArrow", "name": "FancyArrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.FancyArrow", "matplotlib.patches.Polygon", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "x", "y", "dx", "dy", "width", "length_includes_head", "head_width", "head_length", "shape", "overhang", "head_starts_at_zero", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "x", "y", "dx", "dy", "width", "length_includes_head", "head_width", "head_length", "shape", "overhang", "head_starts_at_zero", "kwargs"], "arg_types": ["matplotlib.patches.FancyArrow", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "full"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FancyArrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "x", "y", "dx", "dy", "width", "head_width", "head_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrow.set_data", "name": "set_data", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "x", "y", "dx", "dy", "width", "head_width", "head_length"], "arg_types": ["matplotlib.patches.FancyArrow", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_data of FancyArrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.FancyArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.FancyArrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FancyArrowPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.FancyArrowPatch", "name": "FancyArrowPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.FancyArrowPatch", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "posA", "posB", "path", "arrowstyle", "connectionstyle", "patchA", "patchB", "shrinkA", "shrinkB", "mutation_scale", "mutation_aspect", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "posA", "posB", "path", "arrowstyle", "connectionstyle", "patchA", "patchB", "shrinkA", "shrinkB", "mutation_scale", "mutation_aspect", "kwargs"], "arg_types": ["matplotlib.patches.FancyArrowPatch", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.path.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ArrowStyle"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ConnectionStyle"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.patches.Patch", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_arrowstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.get_arrowstyle", "name": "get_arrowstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyArrowPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_arrowstyle of FancyArrowPatch", "ret_type": "matplotlib.patches.ArrowStyle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_connectionstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.get_connectionstyle", "name": "get_connectionstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyArrowPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connectionstyle of FancyArrowPatch", "ret_type": "matplotlib.patches.ConnectionStyle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mutation_aspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.get_mutation_aspect", "name": "get_mutation_aspect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyArrowPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mutation_aspect of FancyArrowPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mutation_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.get_mutation_scale", "name": "get_mutation_scale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyArrowPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mutation_scale of FancyArrowPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patchA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.FancyArrowPatch.patchA", "name": "patchA", "type": "matplotlib.patches.Patch"}}, "patchB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.FancyArrowPatch.patchB", "name": "patchB", "type": "matplotlib.patches.Patch"}}, "set_arrowstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "arrowstyle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_arrowstyle", "name": "set_arrowstyle", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "arrowstyle", "kwargs"], "arg_types": ["matplotlib.patches.FancyArrowPatch", {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ArrowStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_arrowstyle of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_connectionstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "connectionstyle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_connectionstyle", "name": "set_connectionstyle", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "connectionstyle", "kwargs"], "arg_types": ["matplotlib.patches.FancyArrowPatch", {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.ConnectionStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_connectionstyle of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_mutation_aspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aspect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_mutation_aspect", "name": "set_mutation_aspect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aspect"], "arg_types": ["matplotlib.patches.FancyArrowPatch", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_mutation_aspect of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_mutation_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_mutation_scale", "name": "set_mutation_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale"], "arg_types": ["matplotlib.patches.FancyArrowPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_mutation_scale of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_patchA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "patchA"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_patchA", "name": "set_patchA", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "patchA"], "arg_types": ["matplotlib.patches.FancyArrowPatch", "matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_patchA of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_patchB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "patchB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_patchB", "name": "set_patchB", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "patchB"], "arg_types": ["matplotlib.patches.FancyArrowPatch", "matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_patchB of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyArrowPatch.set_positions", "name": "set_positions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "posA", "posB"], "arg_types": ["matplotlib.patches.FancyArrowPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_positions of FancyArrowPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shrinkA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.FancyArrowPatch.shrinkA", "name": "shrinkA", "type": "builtins.float"}}, "shrinkB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.FancyArrowPatch.shrinkB", "name": "shrinkB", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.FancyArrowPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.FancyArrowPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FancyBboxPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.FancyBboxPatch", "name": "FancyBboxPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.FancyBboxPatch", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "boxstyle", "mutation_scale", "mutation_aspect", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "boxstyle", "mutation_scale", "mutation_aspect", "kwargs"], "arg_types": ["matplotlib.patches.FancyBboxPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.BoxStyle"], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_bbox", "name": "get_bbox", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bbox of FancyBboxPatch", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_boxstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_boxstyle", "name": "get_boxstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boxstyle of FancyBboxPatch", "ret_type": "matplotlib.patches.BoxStyle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_height", "name": "get_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mutation_aspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_mutation_aspect", "name": "get_mutation_aspect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mutation_aspect of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mutation_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_mutation_scale", "name": "get_mutation_scale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mutation_scale of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_x", "name": "get_x", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_x of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.get_y", "name": "get_y", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.FancyBboxPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_y of FancyBboxPatch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_bounds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_bounds", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.patches.FancyBboxPatch.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.patches.FancyBboxPatch.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.patches.FancyBboxPatch.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.patches.FancyBboxPatch.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_boxstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "boxstyle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_boxstyle", "name": "set_boxstyle", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "boxstyle", "kwargs"], "arg_types": ["matplotlib.patches.FancyBboxPatch", {".class": "UnionType", "items": ["builtins.str", "matplotlib.patches.BoxStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_boxstyle of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "h"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_height", "name": "set_height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "h"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_height of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_mutation_aspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aspect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_mutation_aspect", "name": "set_mutation_aspect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aspect"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_mutation_aspect of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_mutation_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_mutation_scale", "name": "set_mutation_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_mutation_scale of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "w"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_x", "name": "set_x", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_x of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.FancyBboxPatch.set_y", "name": "set_y", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "y"], "arg_types": ["matplotlib.patches.FancyBboxPatch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_y of FancyBboxPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.FancyBboxPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.FancyBboxPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JoinStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.JoinStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LineStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.LineStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.MouseEvent", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Patch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.artist.Artist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Patch", "name": "Patch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "edgecolor", "facecolor", "color", "linewidth", "linestyle", "antialiased", "hatch", "fill", "capstyle", "joinstyle", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "edgecolor", "facecolor", "color", "linewidth", "linestyle", "antialiased", "hatch", "fill", "capstyle", "joinstyle", "kwargs"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.LineStyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CapStyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.JoinStyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "mouseevent", "radius"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.contains", "name": "contains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "mouseevent", "radius"], "arg_types": ["matplotlib.patches.Patch", "matplotlib.backend_bases.MouseEvent", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains of Patch", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contains_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "point", "radius"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.contains_point", "name": "contains_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "point", "radius"], "arg_types": ["matplotlib.patches.Patch", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_point of Patch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contains_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "points", "radius"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.contains_points", "name": "contains_points", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "points", "radius"], "arg_types": ["matplotlib.patches.Patch", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_points of Patch", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Patch.fill", "name": "fill", "type": "builtins.property"}}, "get_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_antialiased", "name": "get_antialiased", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_antialiased of Patch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_capstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_capstyle", "name": "get_capstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_capstyle of Patch", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "butt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "projecting"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_data_transform", "name": "get_data_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data_transform of Patch", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_edgecolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_edgecolor", "name": "get_edgecolor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_edgecolor of Patch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_extents", "name": "get_extents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extents of Patch", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_facecolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_facecolor", "name": "get_facecolor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_facecolor of <PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_fill", "name": "get_fill", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fill of Patch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_hatch", "name": "get_hatch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch of Patch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hatch_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_hatch_linewidth", "name": "get_hatch_linewidth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hatch_linewidth of Patch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_joinstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_joinstyle", "name": "get_joinstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_joinstyle of Patch", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "miter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bevel"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_linestyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_linestyle", "name": "get_linestyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_linestyle of Patch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.LineStyleType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_linewidth", "name": "get_linewidth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_linewidth of Patch", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_patch_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_patch_transform", "name": "get_patch_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_patch_transform of Patch", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_path", "name": "get_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_path of Patch", "ret_type": "matplotlib.path.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_transform", "name": "get_transform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform of Patch", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_verts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.get_verts", "name": "get_verts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Patch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_verts of Patch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_alpha", "name": "set_alpha", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alpha of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aa"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_antialiased", "name": "set_antialiased", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aa"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_antialiased of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_capstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_capstyle", "name": "set_capstyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.patches.Patch", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CapStyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_capstyle of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_color", "name": "set_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "c"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_color of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_edgecolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_edgecolor", "name": "set_edgecolor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_edgecolor of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_facecolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_facecolor", "name": "set_facecolor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_facecolor of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_fill", "name": "set_fill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["matplotlib.patches.Patch", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fill of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_hatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_hatch", "name": "set_hatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hatch"], "arg_types": ["matplotlib.patches.Patch", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_hatch of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_hatch_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_hatch_linewidth", "name": "set_hatch_linewidth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lw"], "arg_types": ["matplotlib.patches.Patch", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_hatch_linewidth of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_joinstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_joinstyle", "name": "set_joinstyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.patches.Patch", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.JoinStyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_joinstyle of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_linestyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_linestyle", "name": "set_linestyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ls"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.LineStyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_linestyle of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_linewidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Patch.set_linewidth", "name": "set_linewidth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "w"], "arg_types": ["matplotlib.patches.Patch", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_linewidth of Patch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Patch.zorder", "name": "zorder", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Patch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Patch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PathPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.PathPatch", "name": "PathPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.PathPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.PathPatch", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.PathPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["matplotlib.patches.PathPatch", "matplotlib.path.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PathPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.PathPatch.set_path", "name": "set_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["matplotlib.patches.PathPatch", "matplotlib.path.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_path of PathPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.PathPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.PathPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Polygon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Polygon", "name": "Polygon", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Polygon", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "xy", "closed", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "xy", "closed", "kwargs"], "arg_types": ["matplotlib.patches.Polygon", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Polygon", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon.get_closed", "name": "get_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Polygon"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closed of Polygon", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon.get_xy", "name": "get_xy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Polygon"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xy of Polygon", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "closed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon.set_closed", "name": "set_closed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "closed"], "arg_types": ["matplotlib.patches.Polygon", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_closed of Polygon", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Polygon.set_xy", "name": "set_xy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.patches.Polygon", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_xy of Polygon", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Polygon.xy", "name": "xy", "type": "builtins.property"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Polygon.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Polygon", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Rectangle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Rectangle", "name": "Rectangle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Rectangle", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "rotation_point", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 4], "arg_names": ["self", "xy", "width", "height", "angle", "rotation_point", "kwargs"], "arg_types": ["matplotlib.patches.Rectangle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Rectangle.angle", "name": "angle", "type": "builtins.float"}}, "get_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_angle", "name": "get_angle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_angle of Rectangle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_bbox", "name": "get_bbox", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bbox of Rectangle", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_center", "name": "get_center", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_center of Rectangle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_corners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_corners", "name": "get_corners", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_corners of Rectangle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_height", "name": "get_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height of Rectangle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Rectangle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_x", "name": "get_x", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_x of Rectangle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_xy", "name": "get_xy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xy of Rectangle", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.get_y", "name": "get_y", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_y of Rectangle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotation_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.patches.Rectangle.rotation_point", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.patches.Rectangle.rotation_point", "name": "rotation_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotation_point of Rectangle", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.patches.Rectangle.rotation_point", "name": "rotation_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotation_point of Rectangle", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.patches.Rectangle.rotation_point", "name": "rotation_point", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["matplotlib.patches.Rectangle", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotation_point of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "rotation_point", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.Rectangle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotation_point of Rectangle", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_angle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_angle", "name": "set_angle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "angle"], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_angle of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_bounds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_bounds", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.patches.Rectangle.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.Rectangle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.patches.Rectangle.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.Rectangle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.patches.Rectangle.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.patches.Rectangle.set_bounds", "name": "set_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patches.Rectangle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bounds of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "h"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_height", "name": "set_height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "h"], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_height of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "w"], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_x", "name": "set_x", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_x of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_xy", "name": "set_xy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.patches.Rectangle", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_xy of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Rectangle.set_y", "name": "set_y", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "y"], "arg_types": ["matplotlib.patches.Rectangle", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_y of Rectangle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.patches.Rectangle.xy", "name": "xy", "type": "builtins.property"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Rectangle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Rectangle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RegularPolygon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.RegularPolygon", "name": "RegularPolygon", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.RegularPolygon", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.RegularPolygon", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "xy", "numVertices", "radius", "orientation", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.RegularPolygon.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "xy", "numVertices", "radius", "orientation", "kwargs"], "arg_types": ["matplotlib.patches.RegularPolygon", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RegularPolygon", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "numvertices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.RegularPolygon.numvertices", "name": "numvertices", "type": "builtins.int"}}, "orientation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.RegularPolygon.orientation", "name": "orientation", "type": "builtins.float"}}, "radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.RegularPolygon.radius", "name": "radius", "type": "builtins.float"}}, "xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.RegularPolygon.xy", "name": "xy", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.RegularPolygon.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.RegularPolygon", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.RendererBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Shadow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Shadow", "name": "Shadow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Shadow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Shadow", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "patch", "ox", "oy", "shade", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Shadow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "patch", "ox", "oy", "shade", "kwargs"], "arg_types": ["matplotlib.patches.Shadow", "matplotlib.patches.Patch", "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Shadow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Shadow.patch", "name": "patch", "type": "matplotlib.patches.Patch"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Shadow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Shadow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StepPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.PathPatch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.StepPatch", "name": "StepPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.StepPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.StepPatch", "matplotlib.patches.PathPatch", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "values", "edges", "orientation", "baseline", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.StepPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "values", "edges", "orientation", "baseline", "kwargs"], "arg_types": ["matplotlib.patches.StepPatch", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vertical"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "horizontal"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StepPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.StepPatch.get_data", "name": "get_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.patches.StepPatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data of StepPatch", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "orientation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.StepPatch.orientation", "name": "orientation", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vertical"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "horizontal"}], "uses_pep604_syntax": false}}}, "set_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "values", "edges", "baseline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.StepPatch.set_data", "name": "set_data", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "values", "edges", "baseline"], "arg_types": ["matplotlib.patches.StepPatch", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_data of StepPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.StepPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.StepPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Wedge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patches.Patch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches.Wedge", "name": "Wedge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches.Wedge", "matplotlib.patches.Patch", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["self", "center", "r", "theta1", "theta2", "width", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["self", "center", "r", "theta1", "theta2", "width", "kwargs"], "arg_types": ["matplotlib.patches.Wedge", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Wedge.center", "name": "center", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "r": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Wedge.r", "name": "r", "type": "builtins.float"}}, "set_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "center"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.set_center", "name": "set_center", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "center"], "arg_types": ["matplotlib.patches.Wedge", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_center of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "radius"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.set_radius", "name": "set_radius", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "radius"], "arg_types": ["matplotlib.patches.Wedge", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_radius of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theta1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theta1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.set_theta1", "name": "set_theta1", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theta1"], "arg_types": ["matplotlib.patches.Wedge", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theta1 of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_theta2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theta2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.set_theta2", "name": "set_theta2", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theta2"], "arg_types": ["matplotlib.patches.Wedge", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_theta2 of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.Wedge.set_width", "name": "set_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["matplotlib.patches.Wedge", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_width of Wedge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theta1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Wedge.theta1", "name": "theta1", "type": "builtins.float"}}, "theta2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Wedge.theta2", "name": "theta2", "type": "builtins.float"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patches.Wedge.width", "name": "width", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches.Wedge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches.Wedge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Style": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patches._Style", "name": "_Style", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patches._Style", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patches", "mro": ["matplotlib.patches._Style", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "stylename", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "matplotlib.patches._Style.__new__", "name": "__new__", "type": null}}, "get_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.patches._Style.get_styles", "name": "get_styles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_styles of _Style", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.patches._Style.get_styles", "name": "get_styles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_styles of _Style", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pprint_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.patches._Style.pprint_styles", "name": "pprint_styles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pprint_styles of _Style", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.patches._Style.pprint_styles", "name": "pprint_styles", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pprint_styles of _Style", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "matplotlib.patches._Style.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "style"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}, "builtins.str", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of _Style", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.patches._Style.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "style"], "arg_types": [{".class": "TypeType", "item": "matplotlib.patches._Style"}, "builtins.str", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of _Style", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patches._Style.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patches._Style", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patches.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "artist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bbox_artist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["artist", "renderer", "props", "fill"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.bbox_artist", "name": "bbox_artist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["artist", "renderer", "props", "fill"], "arg_types": ["matplotlib.artist.Artist", "matplotlib.backend_bases.RendererBase", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bbox_artist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_bbox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["bbox", "renderer", "color", "trans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patches.draw_bbox", "name": "draw_bbox", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["bbox", "renderer", "color", "trans"], "arg_types": ["matplotlib.transforms.Bbox", "matplotlib.backend_bases.RendererBase", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "UnionType", "items": ["matplotlib.transforms.Transform", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_bbox", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\patches.pyi"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统修复工具
System Repair Tool

自动修复系统中发现的问题
"""

import json
import os
import shutil
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List


class SystemRepairer:
    """系统修复器"""

    def __init__(self):
        self.repair_log = []

    def run_all_repairs(self) -> Dict[str, Any]:
        """运行所有修复操作"""
        print("🔧 开始系统修复...")
        print("=" * 50)

        repairs = [
            ("创建必要目录", self._create_directories),
            ("修复依赖包", self._fix_dependencies),
            ("创建配置文件", self._create_config_files),
            ("修复文件权限", self._fix_file_permissions),
            ("初始化数据库", self._initialize_databases),
            ("清理临时文件", self._cleanup_temp_files),
            ("验证修复结果", self._verify_repairs),
        ]

        success_count = 0
        total_count = len(repairs)

        for name, repair_func in repairs:
            try:
                result = repair_func()
                if result:
                    print(f"✅ {name}: 修复成功")
                    success_count += 1
                    self.repair_log.append(f"✅ {name}: 修复成功")
                else:
                    print(f"⚠️ {name}: 无需修复")
                    self.repair_log.append(f"⚠️ {name}: 无需修复")

            except Exception as e:
                print(f"❌ {name}: 修复失败 - {str(e)}")
                self.repair_log.append(f"❌ {name}: 修复失败 - {str(e)}")

        print("\n" + "=" * 50)
        print(f"🎯 修复完成: {success_count}/{total_count} 项成功")

        return {
            "timestamp": datetime.now().isoformat(),
            "success_count": success_count,
            "total_count": total_count,
            "repair_log": self.repair_log,
        }

    def _create_directories(self) -> bool:
        """创建必要的目录"""
        directories = [
            "logs",
            "data",
            "backups",
            "temp",
            "core/results",
            "core/exports",
        ]

        created = False
        for directory in directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"  📁 创建目录: {directory}")
                created = True

        return created

    def _fix_dependencies(self) -> bool:
        """修复依赖包"""
        try:
            # 检查requirements.txt是否存在
            if not Path("requirements.txt").exists():
                print("  ⚠️ requirements.txt 不存在，跳过依赖修复")
                return False

            # 安装缺失的依赖
            missing_packages = []

            # 检查关键包
            critical_packages = [
                "ccxt",
                "pandas",
                "numpy",
                "requests",
                "flask",
            ]
            for package in critical_packages:
                try:
                    __import__(package)
                except ImportError:
                    missing_packages.append(package)

            if missing_packages:
                print(f"  📦 安装缺失包: {', '.join(missing_packages)}")
                subprocess.run(
                    [sys.executable, "-m", "pip", "install"]
                    + missing_packages,
                    check=True,
                    capture_output=True,
                )
                return True

            return False

        except Exception as e:
            print(f"  ❌ 依赖修复失败: {e}")
            return False

    def _create_config_files(self) -> bool:
        """创建配置文件"""
        created = False

        # 创建主配置文件
        if not Path("config.json").exists():
            default_config = {
                "trading": {
                    "initial_capital": 10000.0,
                    "daily_loss_limit": 300.0,
                    "max_position_size": 0.1,
                },
                "exchange": {"name": "gate", "sandbox": True, "testnet": True},
                "logging": {"level": "INFO", "file_path": "logs/trading.log"},
                "version": "1.0.0",
                "environment": "development",
            }

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)

            print("  📄 创建主配置文件: config.json")
            created = True

        # 创建GUI配置文件
        gui_config_path = Path("core/gui_config.json")
        if not gui_config_path.exists():
            gui_config = {
                "api_key": "",
                "secret": "",
                "sandbox": True,
                "initial_capital": 10000,
                "daily_loss_limit": 300,
            }

            with open(gui_config_path, "w", encoding="utf-8") as f:
                json.dump(gui_config, f, indent=2, ensure_ascii=False)

            print("  📄 创建GUI配置文件: core/gui_config.json")
            created = True

        return created

    def _fix_file_permissions(self) -> bool:
        """修复文件权限"""
        try:
            # 在Windows上，文件权限处理较为复杂，这里主要检查文件是否可读写
            sensitive_files = ["config.json", "core/gui_config.json"]

            fixed = False
            for file_path in sensitive_files:
                if Path(file_path).exists():
                    try:
                        # 测试文件是否可读写
                        with open(file_path, "r", encoding="utf-8") as f:
                            f.read()
                        with open(file_path, "a", encoding="utf-8") as f:
                            pass  # 测试写入权限
                    except PermissionError:
                        print(f"  ⚠️ 文件权限问题: {file_path}")
                        fixed = True

            return fixed

        except Exception as e:
            print(f"  ❌ 权限修复失败: {e}")
            return False

    def _initialize_databases(self) -> bool:
        """初始化数据库"""
        try:
            import sqlite3

            created = False

            # 创建监控数据库
            monitoring_db = Path("core/monitoring.db")
            if not monitoring_db.exists():
                conn = sqlite3.connect(monitoring_db)
                cursor = conn.cursor()

                # 创建基本表结构
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS monitoring_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        component TEXT,
                        details TEXT
                    )
                """
                )

                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        strategy_name TEXT
                    )
                """
                )

                conn.commit()
                conn.close()

                print("  🗄️ 创建监控数据库: core/monitoring.db")
                created = True

            # 创建安全数据库
            vault_db = Path("core/secure_vault.db")
            if not vault_db.exists():
                conn = sqlite3.connect(vault_db)
                cursor = conn.cursor()

                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS encrypted_keys (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exchange_id TEXT UNIQUE NOT NULL,
                        encrypted_data TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        updated_at TEXT
                    )
                """
                )

                conn.commit()
                conn.close()

                print("  🔐 创建安全数据库: core/secure_vault.db")
                created = True

            return created

        except Exception as e:
            print(f"  ❌ 数据库初始化失败: {e}")
            return False

    def _cleanup_temp_files(self) -> bool:
        """清理临时文件"""
        try:
            cleaned = False

            # 清理Python缓存
            pycache_dirs = list(Path(".").rglob("__pycache__"))
            for cache_dir in pycache_dirs:
                if cache_dir.is_dir():
                    shutil.rmtree(cache_dir)
                    print(f"  🗑️ 清理缓存: {cache_dir}")
                    cleaned = True

            # 清理临时文件
            temp_patterns = ["*.tmp", "*.temp", "*.log.old"]
            for pattern in temp_patterns:
                temp_files = list(Path(".").rglob(pattern))
                for temp_file in temp_files:
                    if temp_file.is_file():
                        temp_file.unlink()
                        print(f"  🗑️ 删除临时文件: {temp_file}")
                        cleaned = True

            return cleaned

        except Exception as e:
            print(f"  ❌ 清理失败: {e}")
            return False

    def _verify_repairs(self) -> bool:
        """验证修复结果"""
        try:
            issues = []

            # 检查关键目录
            required_dirs = ["logs", "core"]
            for directory in required_dirs:
                if not Path(directory).exists():
                    issues.append(f"缺少目录: {directory}")

            # 检查关键文件
            required_files = ["config.json", "requirements.txt"]
            for file_path in required_files:
                if not Path(file_path).exists():
                    issues.append(f"缺少文件: {file_path}")

            # 检查关键包
            critical_packages = ["ccxt", "pandas", "numpy"]
            for package in critical_packages:
                try:
                    __import__(package)
                except ImportError:
                    issues.append(f"缺少包: {package}")

            if issues:
                print(f"  ⚠️ 仍存在问题: {'; '.join(issues)}")
                return False
            else:
                print("  ✅ 所有修复验证通过")
                return True

        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
            return False

    def save_repair_log(self, filename: str = None):
        """保存修复日志"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"repair_log_{timestamp}.txt"

        log_path = Path("logs") / filename
        log_path.parent.mkdir(parents=True, exist_ok=True)

        with open(log_path, "w", encoding="utf-8") as f:
            f.write(f"系统修复日志 - {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

            for log_entry in self.repair_log:
                f.write(f"{log_entry}\n")

        print(f"📄 修复日志已保存: {log_path}")


def main():
    """主函数"""
    repairer = SystemRepairer()
    results = repairer.run_all_repairs()
    repairer.save_repair_log()

    return results["success_count"] > 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

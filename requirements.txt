# GATE.IO API 依赖包
ccxt>=4.0.0
aiohttp>=3.8.0
websockets>=11.0.0
cryptography>=3.4.8
requests>=2.28.0

# GUI 依赖 (通常已内置)
tkinter

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 系统优化模块依赖
psutil>=5.8.0          # 系统性能监控
scipy>=1.7.0           # 科学计算和优化算法
matplotlib>=3.5.0      # 数据可视化(可选)
scikit-learn>=1.0.0    # 机器学习算法(优化模块)

# 可选: 更好的加密支持
pycryptodome>=3.15.0

# 测试框架依赖
unittest2>=1.1.0       # 增强测试功能
mock>=4.0.0            # 模拟对象

# 新增交易系统核心依赖
sqlalchemy>=1.4.0      # 数据库ORM
alembic>=1.8.0         # 数据库迁移

# 技术指标和分析
ta-lib>=0.4.0          # 技术分析库
pandas-ta>=0.3.0       # Pandas技术分析扩展

# 图表和可视化增强
plotly>=5.10.0         # 交互式图表
mplfinance>=0.12.0     # 金融图表

# 日志和监控
loguru>=0.6.0          # 高级日志系统
prometheus-client>=0.14.0  # 监控指标

# 配置管理
pydantic>=1.10.0       # 数据验证和设置
python-dotenv>=0.19.0  # 环境变量管理

# 异步和并发
asyncio>=3.4.3         # 异步编程
aiodns>=3.0.0          # 异步DNS解析

# 测试框架增强
pytest>=7.0.0          # 现代测试框架
pytest-asyncio>=0.19.0 # 异步测试支持

# 安全增强
bcrypt>=3.2.0          # 密码哈希

# 可选: 消息队列和缓存
redis>=4.3.0           # Redis客户端
celery>=5.2.0          # 分布式任务队列

# 可选: Web API框架
fastapi>=0.85.0        # 现代Web框架
uvicorn>=0.18.0        # ASGI服务器

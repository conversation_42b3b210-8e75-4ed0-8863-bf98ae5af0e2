# GATE.IO API 依赖包
ccxt>=4.0.0
aiohttp>=3.8.0
websockets>=11.0.0
cryptography>=3.4.8
requests>=2.28.0

# GUI 依赖 (通常已内置)
tkinter

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 系统优化模块依赖
psutil>=5.8.0          # 系统性能监控
scipy>=1.7.0           # 科学计算和优化算法
matplotlib>=3.5.0      # 数据可视化(可选)
scikit-learn>=1.0.0    # 机器学习算法(优化模块)

# 可选: 更好的加密支持
pycryptodome>=3.15.0

# 测试框架依赖
unittest2>=1.1.0       # 增强测试功能
mock>=4.0.0            # 模拟对象

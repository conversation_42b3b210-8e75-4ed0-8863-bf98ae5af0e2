#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易引擎测试脚本
Test Trading Engine and Order Management System
"""

import os
import sys
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.gate_trading_engine import (GateIOTradingEngine,
                                                         SmartOrderRouter)
from institutional_framework.order_management_system import (
    OrderManagementSystem, TradingSystemIntegrator)


def test_trading_engine():
    """测试交易引擎"""
    print("🔍 测试Gate.io交易引擎...")

    try:
        # 创建交易引擎（演示模式）
        engine = GateIOTradingEngine(demo_mode=True)

        print("📊 获取账户余额...")
        balance = engine.get_account_balance()
        print(f"✅ 账户余额: {balance}")

        print("\n💰 测试市价买单...")
        buy_result = engine.place_order(
            "BTC_USDT", "buy", 100.0, order_type="market"
        )
        print(f"买单结果: {buy_result}")

        if buy_result["success"]:
            print(f"✅ 买单成功: {buy_result['order_id']}")

            print("\n💸 测试市价卖单...")
            sell_amount = buy_result["amount"]
            sell_result = engine.place_order(
                "BTC_USDT", "sell", sell_amount, order_type="market"
            )
            print(f"卖单结果: {sell_result}")

            if sell_result["success"]:
                print(f"✅ 卖单成功: {sell_result['order_id']}")
            else:
                print(f"❌ 卖单失败: {sell_result['error']}")
        else:
            print(f"❌ 买单失败: {buy_result['error']}")

        print("\n📈 测试限价单...")
        limit_result = engine.place_order(
            "ETH_USDT", "buy", 0.1, price=2000.0, order_type="limit"
        )
        print(f"限价单结果: {limit_result}")

        if limit_result["success"]:
            print(f"✅ 限价单成功: {limit_result['order_id']}")

            # 测试取消订单
            print("\n❌ 测试取消订单...")
            cancel_result = engine.cancel_order(limit_result["order_id"])
            print(f"取消结果: {cancel_result}")

        print("\n📊 获取交易历史...")
        history = engine.get_trade_history(limit=5)
        print(f"✅ 交易历史: {len(history)} 条记录")
        for trade in history:
            print(
                f"   {trade['side']} {trade['amount']:.6f} {trade['symbol']} @ {trade['price']:.2f}"
            )

        print("\n📈 获取日统计...")
        stats = engine.get_daily_stats()
        print(f"✅ 日统计: {stats}")

        print("\n✅ 交易引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 交易引擎测试失败: {e}")
        return False


def test_order_management():
    """测试订单管理系统"""
    print("\n🔍 测试订单管理系统...")

    try:
        # 创建交易引擎和订单管理系统
        engine = GateIOTradingEngine(demo_mode=True)
        order_manager = OrderManagementSystem(engine)

        # 订单状态回调
        def order_callback(order_request):
            print(
                f"📢 订单状态更新: {order_request['queue_id']} -> {order_request['status']}"
            )

        order_manager.add_order_callback(order_callback)

        # 启动订单处理
        order_manager.start_order_processing()

        print("📝 提交测试订单...")

        # 提交多个订单
        queue_ids = []

        # 高优先级订单
        queue_id1 = order_manager.submit_order(
            "布林带策略", "BTC_USDT", "buy", 50.0, priority=1
        )
        queue_ids.append(queue_id1)

        # 中优先级订单
        queue_id2 = order_manager.submit_order(
            "MACD策略", "ETH_USDT", "buy", 100.0, priority=2
        )
        queue_ids.append(queue_id2)

        # 低优先级订单
        queue_id3 = order_manager.submit_order(
            "RSI策略", "SOL_USDT", "buy", 30.0, priority=3
        )
        queue_ids.append(queue_id3)

        print(f"✅ 已提交 {len(queue_ids)} 个订单到队列")

        # 等待订单处理
        print("⏳ 等待订单处理 (10秒)...")
        for i in range(10):
            time.sleep(1)
            queue_status = order_manager.get_queue_status()
            print(
                f"   队列状态: 待执行={queue_status['pending_count']}, 执行中={queue_status['executing_count']}, 已完成={queue_status['completed_count']}"
            )

            if (
                queue_status["pending_count"] == 0
                and queue_status["executing_count"] == 0
            ):
                print("✅ 所有订单处理完成")
                break

        # 获取订单统计
        print("\n📊 获取订单统计...")
        stats = order_manager.get_order_stats()
        print(f"✅ 订单统计: {stats}")

        # 获取最近订单
        print("\n📋 获取最近订单...")
        recent_orders = order_manager.get_recent_orders(limit=5)
        print(f"✅ 最近订单: {len(recent_orders)} 条")
        for order in recent_orders:
            print(
                f"   {order['strategy_name']}: {order['side']} {order['amount']:.2f} {order['symbol']} - {order['status']}"
            )

        # 停止订单管理
        order_manager.stop_order_processing()

        print("\n✅ 订单管理系统测试完成!")
        return True

    except Exception as e:
        print(f"❌ 订单管理系统测试失败: {e}")
        return False


def test_trading_system_integrator():
    """测试交易系统集成器"""
    print("\n🔍 测试交易系统集成器...")

    try:
        # 创建交易系统集成器
        integrator = TradingSystemIntegrator(demo_mode=True)

        print("📊 获取账户信息...")
        account_info = integrator.get_account_info()
        print(f"✅ 账户信息: {account_info}")

        print("\n🎯 执行策略交易...")

        # 执行多个策略交易
        strategies = [
            ("布林带策略", "BTC_USDT", "buy", 100.0),
            ("MACD策略", "ETH_USDT", "buy", 200.0),
            ("RSI策略", "SOL_USDT", "buy", 50.0),
        ]

        trade_ids = []
        for strategy_name, symbol, side, amount in strategies:
            trade_id = integrator.execute_strategy_trade(
                strategy_name, symbol, side, amount
            )
            trade_ids.append(trade_id)
            print(f"✅ {strategy_name}交易提交: {trade_id}")

        # 等待交易完成
        print("\n⏳ 等待交易完成 (8秒)...")
        for i in range(8):
            time.sleep(1)
            account_info = integrator.get_account_info()
            queue_status = account_info["queue_status"]
            print(
                f"   处理进度: 待执行={queue_status['pending_count']}, 执行中={queue_status['executing_count']}"
            )

            if (
                queue_status["pending_count"] == 0
                and queue_status["executing_count"] == 0
            ):
                print("✅ 所有交易完成")
                break

        # 最终账户信息
        print("\n📊 最终账户信息...")
        final_info = integrator.get_account_info()
        print(f"✅ 最终统计: {final_info['order_stats']}")

        # 停止交易系统
        integrator.stop()

        print("\n✅ 交易系统集成器测试完成!")
        return True

    except Exception as e:
        print(f"❌ 交易系统集成器测试失败: {e}")
        return False


def test_risk_controls():
    """测试风险控制"""
    print("\n🔍 测试风险控制...")

    try:
        engine = GateIOTradingEngine(demo_mode=True)

        # 测试订单金额限制
        print("💰 测试订单金额限制...")
        large_order = engine.place_order("BTC_USDT", "buy", 2000.0)  # 超过限制
        if not large_order["success"]:
            print(f"✅ 大额订单被拒绝: {large_order['error']}")
        else:
            print("❌ 大额订单应该被拒绝")

        # 测试交易频率限制
        print("\n⚡ 测试交易频率限制...")
        order1 = engine.place_order("BTC_USDT", "buy", 50.0)
        order2 = engine.place_order("BTC_USDT", "buy", 50.0)  # 立即下第二单

        if order1["success"] and not order2["success"]:
            print(f"✅ 频率限制生效: {order2['error']}")
        else:
            print("⚠️ 频率限制可能未生效")

        # 等待间隔后再试
        time.sleep(2)
        order3 = engine.place_order("BTC_USDT", "buy", 50.0)
        if order3["success"]:
            print("✅ 间隔后订单成功")

        print("\n✅ 风险控制测试完成!")
        return True

    except Exception as e:
        print(f"❌ 风险控制测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Gate.io交易引擎和订单管理系统测试开始")
    print("=" * 70)

    # 测试交易引擎
    engine_success = test_trading_engine()

    # 测试订单管理系统
    order_mgmt_success = test_order_management()

    # 测试交易系统集成器
    integrator_success = test_trading_system_integrator()

    # 测试风险控制
    risk_control_success = test_risk_controls()

    # 总结
    print("\n" + "=" * 70)
    print("📋 测试结果总结:")
    print(f"   交易引擎: {'✅ 通过' if engine_success else '❌ 失败'}")
    print(f"   订单管理系统: {'✅ 通过' if order_mgmt_success else '❌ 失败'}")
    print(
        f"   交易系统集成器: {'✅ 通过' if integrator_success else '❌ 失败'}"
    )
    print(f"   风险控制: {'✅ 通过' if risk_control_success else '❌ 失败'}")

    overall_success = (
        engine_success
        and order_mgmt_success
        and integrator_success
        and risk_control_success
    )

    if overall_success:
        print("\n🎊 所有测试通过! 交易引擎和订单管理系统运行正常!")
        print("💡 现在可以集成到现货版系统中进行真实订单执行")
        print("🔗 系统支持演示模式和真实交易模式")
        print("🛡️ 完善的风险控制和订单管理机制")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置")

    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

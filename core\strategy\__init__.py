#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略系统模块
Strategy System Module

包含信号生成、回测引擎、策略管理等功能
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要类和函数
try:
    from .signal_generator import SignalGenerator, TradingSignal, get_signal_generator
    from .backtesting_engine import BacktestingEngine, BacktestResult
    from .strategy_manager import StrategyManager, StrategyConfig, get_strategy_manager
    
    __all__ = [
        'SignalGenerator',
        'TradingSignal', 
        'get_signal_generator',
        'BacktestingEngine',
        'BacktestResult',
        'StrategyManager',
        'StrategyConfig',
        'get_strategy_manager'
    ]
    
except ImportError as e:
    print(f"策略系统模块导入警告: {e}")
    __all__ = []

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控告警系统
Monitoring and Alert System for Trading Operations
"""

import time
import threading
from typing import Dict, List, Callable, Optional
from datetime import datetime, timedelta
from enum import Enum
import json
import os

# 可选的邮件功能
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertType(Enum):
    """告警类型"""
    PRICE_ALERT = "price_alert"
    BALANCE_ALERT = "balance_alert"
    TRADE_ALERT = "trade_alert"
    SYSTEM_ALERT = "system_alert"
    STRATEGY_ALERT = "strategy_alert"


class Alert:
    """告警对象"""

    def __init__(self, alert_id: str, alert_type: AlertType, level: AlertLevel,
                 title: str, message: str, data: Dict = None):
        """初始化告警"""
        self.alert_id = alert_id
        self.alert_type = alert_type
        self.level = level
        self.title = title
        self.message = message
        self.data = data or {}
        self.timestamp = datetime.now()
        self.acknowledged = False
        self.resolved = False

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'alert_id': self.alert_id,
            'alert_type': self.alert_type.value,
            'level': self.level.value,
            'title': self.title,
            'message': self.message,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'acknowledged': self.acknowledged,
            'resolved': self.resolved
        }


class MonitoringRule:
    """监控规则"""

    def __init__(self, rule_id: str, name: str, condition_func: Callable,
                 alert_type: AlertType, level: AlertLevel, enabled: bool = True):
        """初始化监控规则"""
        self.rule_id = rule_id
        self.name = name
        self.condition_func = condition_func
        self.alert_type = alert_type
        self.level = level
        self.enabled = enabled
        self.last_triggered = None
        self.trigger_count = 0
        self.cooldown_seconds = 300  # 5分钟冷却期

    def check_condition(self, data: Dict) -> Optional[Alert]:
        """检查条件是否满足"""
        try:
            if not self.enabled:
                return None

            # 检查冷却期
            if (self.last_triggered and
                datetime.now() - self.last_triggered < timedelta(seconds=self.cooldown_seconds)):
                return None

            # 执行条件检查
            result = self.condition_func(data)

            if result:
                self.last_triggered = datetime.now()
                self.trigger_count += 1

                # 生成告警
                alert_id = f"{self.rule_id}_{int(time.time())}"

                if isinstance(result, dict):
                    title = result.get('title', self.name)
                    message = result.get('message', f"监控规则 {self.name} 被触发")
                    alert_data = result.get('data', {})
                else:
                    title = self.name
                    message = f"监控规则 {self.name} 被触发"
                    alert_data = {}

                return Alert(alert_id, self.alert_type, self.level, title, message, alert_data)

            return None

        except Exception as e:
            # 监控规则异常也生成告警
            alert_id = f"rule_error_{self.rule_id}_{int(time.time())}"
            return Alert(
                alert_id, AlertType.SYSTEM_ALERT, AlertLevel.ERROR,
                f"监控规则异常: {self.name}",
                f"规则执行异常: {str(e)}"
            )


class NotificationChannel:
    """通知渠道基类"""

    def __init__(self, channel_id: str, name: str):
        """初始化通知渠道"""
        self.channel_id = channel_id
        self.name = name
        self.enabled = True

    def send_notification(self, alert: Alert) -> bool:
        """发送通知"""
        raise NotImplementedError


class ConsoleNotification(NotificationChannel):
    """控制台通知"""

    def __init__(self):
        super().__init__("console", "控制台通知")

    def send_notification(self, alert: Alert) -> bool:
        """发送控制台通知"""
        try:
            level_emoji = {
                AlertLevel.INFO: "ℹ️",
                AlertLevel.WARNING: "⚠️",
                AlertLevel.ERROR: "❌",
                AlertLevel.CRITICAL: "🚨"
            }

            emoji = level_emoji.get(alert.level, "📢")
            timestamp = alert.timestamp.strftime("%Y-%m-%d %H:%M:%S")

            print(f"\n{emoji} 【{alert.level.value.upper()}】 {alert.title}")
            print(f"⏰ 时间: {timestamp}")
            print(f"📝 消息: {alert.message}")
            if alert.data:
                print(f"📊 数据: {alert.data}")
            print("-" * 50)

            return True

        except Exception as e:
            print(f"❌ 控制台通知发送失败: {e}")
            return False


class EmailNotification(NotificationChannel):
    """邮件通知"""

    def __init__(self, smtp_server: str, smtp_port: int, username: str,
                 password: str, recipients: List[str]):
        super().__init__("email", "邮件通知")
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.recipients = recipients

    def send_notification(self, alert: Alert) -> bool:
        """发送邮件通知"""
        try:
            if not EMAIL_AVAILABLE:
                print("⚠️ 邮件功能不可用，跳过邮件通知")
                return False

            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(self.recipients)
            msg['Subject'] = f"[{alert.level.value.upper()}] {alert.title}"

            # 邮件内容
            body = f"""
交易系统告警通知

告警级别: {alert.level.value.upper()}
告警类型: {alert.alert_type.value}
触发时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

告警标题: {alert.title}
告警消息: {alert.message}

详细数据:
{json.dumps(alert.data, indent=2, ensure_ascii=False)}

请及时处理相关问题。

---
自动化交易系统
"""

            msg.attach(MimeText(body, 'plain', 'utf-8'))

            # 发送邮件
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)
            server.quit()

            print(f"📧 邮件通知已发送: {alert.title}")
            return True

        except Exception as e:
            print(f"❌ 邮件通知发送失败: {e}")
            return False


class FileNotification(NotificationChannel):
    """文件日志通知"""

    def __init__(self, log_file: str = "alerts.log"):
        super().__init__("file", "文件日志")
        self.log_file = log_file

    def send_notification(self, alert: Alert) -> bool:
        """写入文件日志"""
        try:
            log_entry = {
                'timestamp': alert.timestamp.isoformat(),
                'level': alert.level.value,
                'type': alert.alert_type.value,
                'title': alert.title,
                'message': alert.message,
                'data': alert.data
            }

            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')

            return True

        except Exception as e:
            print(f"❌ 文件日志写入失败: {e}")
            return False


class MonitoringSystem:
    """监控系统"""

    def __init__(self):
        """初始化监控系统"""
        self.rules = {}
        self.alerts = []
        self.notification_channels = {}

        self.monitoring_active = False
        self.monitor_thread = None
        self.check_interval = 10  # 10秒检查一次

        # 数据源
        self.data_sources = {}

        # 添加默认通知渠道
        self.add_notification_channel(ConsoleNotification())
        self.add_notification_channel(FileNotification())

        # 预定义监控规则
        self._setup_default_rules()

    def add_monitoring_rule(self, rule: MonitoringRule):
        """添加监控规则"""
        self.rules[rule.rule_id] = rule
        print(f"✅ 已添加监控规则: {rule.name}")

    def remove_monitoring_rule(self, rule_id: str):
        """移除监控规则"""
        if rule_id in self.rules:
            rule_name = self.rules[rule_id].name
            del self.rules[rule_id]
            print(f"✅ 已移除监控规则: {rule_name}")

    def add_notification_channel(self, channel: NotificationChannel):
        """添加通知渠道"""
        self.notification_channels[channel.channel_id] = channel
        print(f"✅ 已添加通知渠道: {channel.name}")

    def add_data_source(self, source_id: str, data_func: Callable):
        """添加数据源"""
        self.data_sources[source_id] = data_func
        print(f"✅ 已添加数据源: {source_id}")

    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            return

        print("🚀 启动监控系统...")
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("✅ 监控系统已启动")

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring_active:
            return

        print("🛑 停止监控系统...")
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("✅ 监控系统已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集数据
                monitoring_data = self._collect_data()

                # 检查所有规则
                for rule in self.rules.values():
                    alert = rule.check_condition(monitoring_data)
                    if alert:
                        self._handle_alert(alert)

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                time.sleep(30)  # 异常时等待30秒

    def _collect_data(self) -> Dict:
        """收集监控数据"""
        data = {
            'timestamp': datetime.now(),
            'system_status': 'running'
        }

        # 从各数据源收集数据
        for source_id, data_func in self.data_sources.items():
            try:
                source_data = data_func()
                data[source_id] = source_data
            except Exception as e:
                data[source_id] = {'error': str(e)}

        return data

    def _handle_alert(self, alert: Alert):
        """处理告警"""
        try:
            # 添加到告警列表
            self.alerts.append(alert)

            # 保持最近1000条告警
            if len(self.alerts) > 1000:
                self.alerts = self.alerts[-1000:]

            # 发送通知
            for channel in self.notification_channels.values():
                if channel.enabled:
                    try:
                        channel.send_notification(alert)
                    except Exception as e:
                        print(f"❌ 通知渠道 {channel.name} 发送失败: {e}")

        except Exception as e:
            print(f"❌ 处理告警失败: {e}")

    def _setup_default_rules(self):
        """设置默认监控规则"""

        # 价格异常波动监控
        def price_volatility_check(data: Dict) -> Optional[Dict]:
            prices = data.get('prices', {})
            for symbol, price_info in prices.items():
                if isinstance(price_info, dict) and 'change' in price_info:
                    change = abs(price_info['change'])
                    if change > 10:  # 10%以上波动
                        return {
                            'title': f"{symbol} 价格异常波动",
                            'message': f"{symbol} 24小时涨跌幅达到 {change:.2f}%",
                            'data': {'symbol': symbol, 'change': change}
                        }
            return None

        self.add_monitoring_rule(MonitoringRule(
            "price_volatility", "价格异常波动监控",
            price_volatility_check, AlertType.PRICE_ALERT, AlertLevel.WARNING
        ))

        # 余额不足监控
        def balance_check(data: Dict) -> Optional[Dict]:
            balances = data.get('balances', {})
            for currency, balance_info in balances.items():
                if isinstance(balance_info, dict) and 'available' in balance_info:
                    available = balance_info['available']
                    if currency == 'USDT' and available < 100:  # USDT余额低于100
                        return {
                            'title': f"{currency} 余额不足",
                            'message': f"{currency} 可用余额仅剩 {available:.2f}",
                            'data': {'currency': currency, 'available': available}
                        }
            return None

        self.add_monitoring_rule(MonitoringRule(
            "low_balance", "余额不足监控",
            balance_check, AlertType.BALANCE_ALERT, AlertLevel.WARNING
        ))

        # 交易异常监控
        def trade_error_check(data: Dict) -> Optional[Dict]:
            trade_stats = data.get('trade_stats', {})
            if isinstance(trade_stats, dict):
                failed_orders = trade_stats.get('failed_orders', 0)
                total_orders = trade_stats.get('total_orders', 0)

                if total_orders > 0:
                    failure_rate = failed_orders / total_orders
                    if failure_rate > 0.2:  # 失败率超过20%
                        return {
                            'title': "交易失败率过高",
                            'message': f"交易失败率达到 {failure_rate:.2%}",
                            'data': {'failure_rate': failure_rate, 'failed_orders': failed_orders}
                        }
            return None

        self.add_monitoring_rule(MonitoringRule(
            "trade_failure", "交易异常监控",
            trade_error_check, AlertType.TRADE_ALERT, AlertLevel.ERROR
        ))

    def get_active_alerts(self, level: AlertLevel = None) -> List[Alert]:
        """获取活跃告警"""
        alerts = [alert for alert in self.alerts if not alert.resolved]

        if level:
            alerts = [alert for alert in alerts if alert.level == level]

        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)

    def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.acknowledged = True
                print(f"✅ 已确认告警: {alert.title}")
                return True
        return False

    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.resolved = True
                print(f"✅ 已解决告警: {alert.title}")
                return True
        return False

    def get_monitoring_status(self) -> Dict:
        """获取监控状态"""
        active_alerts = self.get_active_alerts()

        return {
            'monitoring_active': self.monitoring_active,
            'total_rules': len(self.rules),
            'enabled_rules': len([r for r in self.rules.values() if r.enabled]),
            'total_alerts': len(self.alerts),
            'active_alerts': len(active_alerts),
            'critical_alerts': len([a for a in active_alerts if a.level == AlertLevel.CRITICAL]),
            'notification_channels': len(self.notification_channels)
        }


# 全局监控系统
monitoring_system = None

def get_monitoring_system() -> MonitoringSystem:
    """获取监控系统实例"""
    global monitoring_system
    if monitoring_system is None:
        monitoring_system = MonitoringSystem()
    return monitoring_system

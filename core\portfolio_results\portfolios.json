{"InstitutionalPortfolio_v1": {"portfolio_name": "InstitutionalPortfolio_v1", "creation_time": "2025-05-25 10:15:02.225394", "qualified_strategies": [{"name": "TrendStrategy1", "validation_score": 85, "sharpe_ratio": 2.1, "max_drawdown": -0.12, "alpha_contribution": 0.08, "tracking_error": 0.15}, {"name": "TrendStrategy2", "validation_score": 78, "sharpe_ratio": 1.8, "max_drawdown": -0.15, "alpha_contribution": 0.06, "tracking_error": 0.18}, {"name": "MLStrategy1", "validation_score": 75, "sharpe_ratio": 1.9, "max_drawdown": -0.18, "alpha_contribution": 0.07, "tracking_error": 0.2}, {"name": "MeanReversionStrategy1", "validation_score": 72, "sharpe_ratio": 1.5, "max_drawdown": -0.1, "alpha_contribution": 0.05, "tracking_error": 0.12}, {"name": "ArbitrageStrategy1", "validation_score": 68, "sharpe_ratio": 1.2, "max_drawdown": -0.08, "alpha_contribution": 0.03, "tracking_error": 0.08}], "strategy_weights": {"TrendStrategy1": 0.2, "TrendStrategy2": 0.2, "MLStrategy1": 0.2, "MeanReversionStrategy1": 0.2, "ArbitrageStrategy1": 0.2}, "expected_performance": {"return": 0.2092759036354797, "volatility": 0.10573079231621323, "sharpe_ratio": 1.9793278670379209}, "portfolio_statistics": {"n_strategies": 5, "max_weight": 0.2, "min_weight": 0.2, "weight_concentration": 0.20000000000000004}, "correlation_matrix": {"TrendStrategy1": {"TrendStrategy1": 1.0, "TrendStrategy2": -0.02503158573341045, "MLStrategy1": 0.012891879284137278, "MeanReversionStrategy1": -0.0039967365369996035, "ArbitrageStrategy1": 0.0075125511870866935}, "TrendStrategy2": {"TrendStrategy1": -0.02503158573341045, "TrendStrategy2": 1.0, "MLStrategy1": -9.734038249463612e-05, "MeanReversionStrategy1": -0.007026522548028216, "ArbitrageStrategy1": 0.020348930708617874}, "MLStrategy1": {"TrendStrategy1": 0.012891879284137278, "TrendStrategy2": -9.734038249463612e-05, "MLStrategy1": 1.0, "MeanReversionStrategy1": 0.032831973498097, "ArbitrageStrategy1": 0.008448464882360094}, "MeanReversionStrategy1": {"TrendStrategy1": -0.0039967365369996035, "TrendStrategy2": -0.007026522548028216, "MLStrategy1": 0.032831973498097, "MeanReversionStrategy1": 1.0, "ArbitrageStrategy1": -0.038521484822872376}, "ArbitrageStrategy1": {"TrendStrategy1": 0.0075125511870866935, "TrendStrategy2": 0.020348930708617874, "MLStrategy1": 0.008448464882360094, "MeanReversionStrategy1": -0.038521484822872376, "ArbitrageStrategy1": 1.0}}, "optimization_objective": "max_sharpe", "constraints_used": {"min_validation_score": 60, "max_single_weight": 0.2, "min_strategies": 5, "max_strategies": 20, "max_correlation": 0.7, "target_volatility": 0.15}}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于机构级框架的策略优化系统
Strategy Optimization System Based on Institutional Framework

根据验证结果优化策略参数，提升策略表现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, Callable
import logging
from datetime import datetime
from scipy.optimize import minimize, differential_evolution
from sklearn.model_selection import TimeSeriesSplit
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyOptimizer:
    """
    策略优化器
    
    基于机构级验证结果优化策略参数
    """
    
    def __init__(self, framework_manager):
        """
        初始化优化器
        
        Args:
            framework_manager: 机构级框架管理器
        """
        self.framework = framework_manager
        self.optimization_results = {}
        
        # 优化目标权重
        self.objective_weights = {
            'validation_score': 0.4,      # 验证评分权重
            'sharpe_ratio': 0.3,          # 夏普比率权重
            'max_drawdown': 0.2,          # 最大回撤权重（负向）
            'alpha_contribution': 0.1      # Alpha贡献权重
        }
        
        logger.info("策略优化器初始化完成")
    
    def optimize_strategy_parameters(self, 
                                   strategy_name: str,
                                   parameter_ranges: Dict[str, Tuple[float, float]],
                                   data_generator: Callable,
                                   optimization_method: str = 'differential_evolution') -> Dict[str, Any]:
        """
        优化策略参数
        
        Args:
            strategy_name: 策略名称
            parameter_ranges: 参数范围字典 {'param_name': (min_val, max_val)}
            data_generator: 数据生成函数，接受参数字典，返回收益率序列
            optimization_method: 优化方法
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        logger.info(f"开始优化策略参数: {strategy_name}")
        
        # 定义目标函数
        def objective_function(params_array):
            try:
                # 将参数数组转换为参数字典
                params_dict = {}
                for i, (param_name, _) in enumerate(parameter_ranges.items()):
                    params_dict[param_name] = params_array[i]
                
                # 生成策略收益数据
                returns = data_generator(params_dict)
                if returns is None or len(returns) < 60:
                    return 1000  # 惩罚值
                
                # 执行机构级验证
                validation_result = self.framework.validate_strategy(
                    strategy_name=f"{strategy_name}_opt",
                    returns=returns
                )
                
                risk_result = self.framework.analyze_risk_factors(
                    strategy_name=f"{strategy_name}_opt",
                    returns=returns
                )
                
                # 计算综合目标函数值（越小越好）
                objective_value = self._calculate_objective_value(
                    validation_result, risk_result
                )
                
                return objective_value
                
            except Exception as e:
                logger.warning(f"目标函数计算失败: {e}")
                return 1000  # 返回惩罚值
        
        # 设置参数边界
        bounds = list(parameter_ranges.values())
        
        # 执行优化
        if optimization_method == 'differential_evolution':
            result = differential_evolution(
                objective_function,
                bounds,
                maxiter=50,
                popsize=15,
                seed=42
            )
        else:
            # 使用随机起始点的局部优化
            x0 = [np.random.uniform(bound[0], bound[1]) for bound in bounds]
            result = minimize(
                objective_function,
                x0,
                bounds=bounds,
                method='L-BFGS-B'
            )
        
        # 整理优化结果
        optimal_params = {}
        for i, (param_name, _) in enumerate(parameter_ranges.items()):
            optimal_params[param_name] = result.x[i]
        
        # 使用最优参数生成最终结果
        optimal_returns = data_generator(optimal_params)
        final_validation = self.framework.validate_strategy(
            strategy_name=f"{strategy_name}_optimized",
            returns=optimal_returns
        )
        final_risk = self.framework.analyze_risk_factors(
            strategy_name=f"{strategy_name}_optimized",
            returns=optimal_returns
        )
        
        optimization_result = {
            'strategy_name': strategy_name,
            'optimal_parameters': optimal_params,
            'optimization_success': result.success,
            'optimization_message': result.message if hasattr(result, 'message') else 'Success',
            'final_validation_score': final_validation.validation_score,
            'final_sharpe_ratio': final_validation.sharpe_ratio,
            'final_max_drawdown': final_validation.max_drawdown,
            'final_alpha_contribution': final_risk.idiosyncratic_return,
            'optimization_iterations': result.nit if hasattr(result, 'nit') else result.nfev,
            'optimization_time': datetime.now()
        }
        
        self.optimization_results[strategy_name] = optimization_result
        
        logger.info(f"策略 {strategy_name} 优化完成，最终评分: {final_validation.validation_score:.1f}")
        return optimization_result
    
    def _calculate_objective_value(self, validation_result, risk_result) -> float:
        """
        计算目标函数值
        
        Args:
            validation_result: 验证结果
            risk_result: 风险分析结果
            
        Returns:
            float: 目标函数值（越小越好）
        """
        # 标准化各个指标
        validation_score_norm = (100 - validation_result.validation_score) / 100
        sharpe_ratio_norm = max(0, (3 - validation_result.sharpe_ratio) / 3)
        max_drawdown_norm = min(1, abs(validation_result.max_drawdown) / 0.5)
        alpha_norm = max(0, (0.1 - risk_result.idiosyncratic_return) / 0.1)
        
        # 加权综合
        objective_value = (
            self.objective_weights['validation_score'] * validation_score_norm +
            self.objective_weights['sharpe_ratio'] * sharpe_ratio_norm +
            self.objective_weights['max_drawdown'] * max_drawdown_norm +
            self.objective_weights['alpha_contribution'] * alpha_norm
        )
        
        return objective_value
    
    def batch_optimize_strategies(self, 
                                strategies_config: Dict[str, Dict],
                                max_strategies: Optional[int] = None) -> Dict[str, Any]:
        """
        批量优化策略
        
        Args:
            strategies_config: 策略配置字典
            max_strategies: 最大优化策略数量
            
        Returns:
            Dict[str, Any]: 批量优化结果
        """
        logger.info("开始批量策略优化")
        
        strategies_to_optimize = list(strategies_config.keys())
        if max_strategies:
            strategies_to_optimize = strategies_to_optimize[:max_strategies]
        
        successful_optimizations = 0
        failed_optimizations = 0
        
        for i, strategy_name in enumerate(strategies_to_optimize, 1):
            logger.info(f"优化进度: {i}/{len(strategies_to_optimize)} - {strategy_name}")
            
            try:
                config = strategies_config[strategy_name]
                result = self.optimize_strategy_parameters(
                    strategy_name=strategy_name,
                    parameter_ranges=config['parameter_ranges'],
                    data_generator=config['data_generator']
                )
                
                if result['optimization_success']:
                    successful_optimizations += 1
                else:
                    failed_optimizations += 1
                    
            except Exception as e:
                logger.error(f"策略 {strategy_name} 优化失败: {e}")
                failed_optimizations += 1
        
        summary = {
            'total_strategies': len(strategies_to_optimize),
            'successful_optimizations': successful_optimizations,
            'failed_optimizations': failed_optimizations,
            'success_rate': successful_optimizations / len(strategies_to_optimize) if strategies_to_optimize else 0,
            'optimization_time': datetime.now()
        }
        
        logger.info(f"批量优化完成: {successful_optimizations}/{len(strategies_to_optimize)} 成功")
        return summary
    
    def compare_optimization_results(self, strategy_name: str, 
                                   original_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        比较优化前后的结果
        
        Args:
            strategy_name: 策略名称
            original_results: 原始结果
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        if strategy_name not in self.optimization_results:
            return {"error": "策略未进行优化"}
        
        optimized = self.optimization_results[strategy_name]
        
        comparison = {
            'strategy_name': strategy_name,
            'validation_score': {
                'original': original_results.get('validation_score', 0),
                'optimized': optimized['final_validation_score'],
                'improvement': optimized['final_validation_score'] - original_results.get('validation_score', 0)
            },
            'sharpe_ratio': {
                'original': original_results.get('sharpe_ratio', 0),
                'optimized': optimized['final_sharpe_ratio'],
                'improvement': optimized['final_sharpe_ratio'] - original_results.get('sharpe_ratio', 0)
            },
            'max_drawdown': {
                'original': original_results.get('max_drawdown', 0),
                'optimized': optimized['final_max_drawdown'],
                'improvement': original_results.get('max_drawdown', 0) - optimized['final_max_drawdown']  # 回撤减少为正向改善
            },
            'alpha_contribution': {
                'original': original_results.get('alpha_contribution', 0),
                'optimized': optimized['final_alpha_contribution'],
                'improvement': optimized['final_alpha_contribution'] - original_results.get('alpha_contribution', 0)
            }
        }
        
        # 计算总体改善度
        improvements = [
            comparison['validation_score']['improvement'] / 100,  # 标准化到0-1
            comparison['sharpe_ratio']['improvement'] / 3,        # 假设最大改善3
            comparison['max_drawdown']['improvement'] / 0.5,      # 假设最大改善50%
            comparison['alpha_contribution']['improvement'] / 0.1  # 假设最大改善10%
        ]
        
        comparison['overall_improvement'] = np.mean(improvements)
        comparison['is_improved'] = comparison['overall_improvement'] > 0
        
        return comparison

def create_sample_optimization_configs() -> Dict[str, Dict]:
    """
    创建示例优化配置
    
    Returns:
        Dict[str, Dict]: 策略优化配置
    """
    
    def trend_following_generator(params):
        """趋势跟踪策略数据生成器"""
        lookback = int(params['lookback'])
        momentum_factor = params['momentum_factor']
        volatility = params['volatility']
        
        dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
        returns = []
        
        for i in range(len(dates)):
            if i < lookback:
                daily_return = np.random.normal(0.0005, volatility)
            else:
                # 计算动量信号
                recent_returns = returns[-lookback:]
                momentum_signal = np.mean(recent_returns) * momentum_factor
                daily_return = np.random.normal(0.0005 + momentum_signal, volatility)
            
            returns.append(daily_return)
        
        return pd.Series(returns, index=dates)
    
    def mean_reversion_generator(params):
        """均值回归策略数据生成器"""
        reversion_speed = params['reversion_speed']
        threshold = params['threshold']
        volatility = params['volatility']
        
        dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
        returns = []
        price_level = 100
        
        for i in range(len(dates)):
            deviation = (price_level - 100) / 100
            if abs(deviation) > threshold:
                expected_return = -deviation * reversion_speed
            else:
                expected_return = 0
            
            daily_return = np.random.normal(expected_return, volatility)
            returns.append(daily_return)
            price_level *= (1 + daily_return)
        
        return pd.Series(returns, index=dates)
    
    def bollinger_generator(params):
        """布林带策略数据生成器"""
        window = int(params['window'])
        std_multiplier = params['std_multiplier']
        volatility = params['volatility']
        
        dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
        price_series = [100]
        
        # 生成价格序列
        for i in range(1, len(dates)):
            daily_return = np.random.normal(0.0003, 0.015)
            new_price = price_series[-1] * (1 + daily_return)
            price_series.append(new_price)
        
        prices = pd.Series(price_series, index=dates)
        returns = []
        
        for i in range(len(dates)):
            if i < window:
                daily_return = np.random.normal(0, volatility)
            else:
                # 计算布林带
                recent_prices = prices.iloc[i-window:i]
                mean_price = recent_prices.mean()
                std_price = recent_prices.std()
                
                upper_band = mean_price + std_multiplier * std_price
                lower_band = mean_price - std_multiplier * std_price
                current_price = prices.iloc[i]
                
                # 生成交易信号
                if current_price > upper_band:
                    signal = -0.001  # 卖出信号
                elif current_price < lower_band:
                    signal = 0.001   # 买入信号
                else:
                    signal = 0
                
                daily_return = np.random.normal(signal, volatility)
            
            returns.append(daily_return)
        
        return pd.Series(returns, index=dates)
    
    return {
        'TrendFollowingStrategy': {
            'parameter_ranges': {
                'lookback': (5, 30),
                'momentum_factor': (0.1, 2.0),
                'volatility': (0.005, 0.025)
            },
            'data_generator': trend_following_generator
        },
        'MeanReversionStrategy': {
            'parameter_ranges': {
                'reversion_speed': (0.1, 1.0),
                'threshold': (0.01, 0.1),
                'volatility': (0.005, 0.020)
            },
            'data_generator': mean_reversion_generator
        },
        'BollingerBandsStrategy': {
            'parameter_ranges': {
                'window': (10, 50),
                'std_multiplier': (1.0, 3.0),
                'volatility': (0.005, 0.020)
            },
            'data_generator': bollinger_generator
        }
    }

def main():
    """主函数 - 演示策略优化"""
    print("=" * 60)
    print("策略参数优化演示")
    print("=" * 60)
    
    # 导入框架管理器
    from institutional_manager import InstitutionalFrameworkManager
    
    # 创建框架和优化器
    framework = InstitutionalFrameworkManager()
    optimizer = StrategyOptimizer(framework)
    
    # 创建示例配置
    optimization_configs = create_sample_optimization_configs()
    
    print(f"\n准备优化 {len(optimization_configs)} 个策略:")
    for strategy_name in optimization_configs.keys():
        print(f"- {strategy_name}")
    
    # 执行优化
    print("\n开始策略优化...")
    summary = optimizer.batch_optimize_strategies(optimization_configs)
    
    print(f"\n优化完成:")
    print(f"- 总策略数: {summary['total_strategies']}")
    print(f"- 成功优化: {summary['successful_optimizations']}")
    print(f"- 失败数量: {summary['failed_optimizations']}")
    print(f"- 成功率: {summary['success_rate']:.1%}")
    
    # 显示优化结果
    print("\n=== 优化结果 ===")
    for strategy_name, result in optimizer.optimization_results.items():
        print(f"\n{strategy_name}:")
        print(f"  最优参数: {result['optimal_parameters']}")
        print(f"  最终评分: {result['final_validation_score']:.1f}/100")
        print(f"  夏普比率: {result['final_sharpe_ratio']:.3f}")
        print(f"  最大回撤: {result['final_max_drawdown']:.2%}")
        print(f"  Alpha贡献: {result['final_alpha_contribution']:.2%}")
    
    print("\n" + "=" * 60)
    print("策略优化演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()

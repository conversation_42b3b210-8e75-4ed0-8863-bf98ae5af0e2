#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极版现货交易系统 - 改进测试脚本
Improvement Test Script

测试系统改进后的功能和性能
"""

import os
import sys
import time
import logging
from pathlib import Path
import unittest
import threading
import queue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(Path("logs") / "test_improvements.log", encoding='utf-8')
    ]
)

logger = logging.getLogger("改进测试")

# 确保logs目录存在
Path("logs").mkdir(exist_ok=True)

class TestImprovements(unittest.TestCase):
    """测试系统改进"""
    
    def setUp(self):
        """测试前准备"""
        logger.info("开始测试")
    
    def tearDown(self):
        """测试后清理"""
        logger.info("测试完成")
    
    def test_api_connector_singleton(self):
        """测试API连接器单例模式"""
        logger.info("测试API连接器单例模式")
        
        try:
            from core.gate_api_connector import get_api_connector
            
            # 获取两个实例
            connector1 = get_api_connector()
            connector2 = get_api_connector()
            
            # 验证是同一个实例
            self.assertIs(connector1, connector2, "单例模式失败，获取了不同的实例")
            logger.info("✅ API连接器单例模式测试通过")
            
        except ImportError as e:
            self.fail(f"导入错误: {e}")
        except Exception as e:
            self.fail(f"测试失败: {e}")
    
    def test_api_credentials_encryption(self):
        """测试API凭证加密"""
        logger.info("测试API凭证加密")
        
        try:
            from core.api_login_dialog import APILoginDialog
            import tkinter as tk
            
            # 创建临时根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            # 创建对话框实例
            dialog = APILoginDialog(root)
            
            # 测试加密和解密
            test_key = "test_api_key_12345"
            encrypted = dialog.simple_encrypt(test_key)
            decrypted = dialog.simple_decrypt(encrypted)
            
            # 验证加密和解密
            self.assertNotEqual(test_key, encrypted, "加密失败，加密后的文本与原文相同")
            self.assertEqual(test_key, decrypted, "解密失败，解密后的文本与原文不同")
            
            logger.info("✅ API凭证加密测试通过")
            
            # 清理
            root.destroy()
            
        except ImportError as e:
            self.fail(f"导入错误: {e}")
        except Exception as e:
            self.fail(f"测试失败: {e}")
    
    def test_doubling_growth_engine(self):
        """测试倍增引擎"""
        logger.info("测试倍增引擎")
        
        try:
            from core.doubling_growth_engine import DoublingGrowthEngine
            
            # 创建引擎实例
            engine = DoublingGrowthEngine(initial_capital=1000.0)
            
            # 验证初始状态
            self.assertEqual(engine.initial_capital, 1000.0, "初始资金设置错误")
            self.assertEqual(engine.current_capital, 1000.0, "当前资金初始值错误")
            self.assertEqual(engine.profit_reinvest_ratio, 0.8, "利润再投资比例错误")
            
            # 模拟增长
            for _ in range(10):
                engine._simulate_growth_step()
            
            # 验证增长效果
            self.assertGreater(engine.current_capital, 1000.0, "资金没有增长")
            self.assertGreater(engine.doubling_progress, 0.0, "倍增进度没有增长")
            
            logger.info("✅ 倍增引擎测试通过")
            
        except ImportError as e:
            self.fail(f"导入错误: {e}")
        except Exception as e:
            self.fail(f"测试失败: {e}")
    
    def test_fast_profit_engine(self):
        """测试快速盈利引擎"""
        logger.info("测试快速盈利引擎")
        
        try:
            from core.fast_profit_engine import FastProfitEngine
            
            # 创建引擎实例
            engine = FastProfitEngine(initial_capital=1000.0)
            
            # 验证初始状态
            self.assertEqual(engine.initial_capital, 1000.0, "初始资金设置错误")
            self.assertEqual(engine.current_capital, 1000.0, "当前资金初始值错误")
            self.assertEqual(engine.profit_reinvest_ratio, 0.8, "利润再投资比例错误")
            
            # 扫描交易机会
            opportunities = engine.scan_opportunities()
            
            # 验证交易机会
            self.assertIsInstance(opportunities, list, "交易机会应该是列表")
            
            # 如果有交易机会，测试执行交易
            if opportunities:
                result = engine.execute_trade(opportunities[0])
                if result:
                    self.assertIn(result.result, ['win', 'loss'], "交易结果应该是win或loss")
            
            logger.info("✅ 快速盈利引擎测试通过")
            
        except ImportError as e:
            self.fail(f"导入错误: {e}")
        except Exception as e:
            self.fail(f"测试失败: {e}")
    
    def test_logging_system(self):
        """测试日志系统"""
        logger.info("测试日志系统")
        
        try:
            from core.gate_api_connector import setup_logger
            
            # 测试日志设置
            test_logger = logging.getLogger("test_logger")
            setup_logger()
            
            # 验证日志文件
            log_dir = Path("logs")
            self.assertTrue(log_dir.exists(), "日志目录不存在")
            
            # 写入测试日志
            test_logger.info("这是一条测试日志")
            
            logger.info("✅ 日志系统测试通过")
            
        except ImportError as e:
            self.fail(f"导入错误: {e}")
        except Exception as e:
            self.fail(f"测试失败: {e}")

def run_tests():
    """运行所有测试"""
    print("=" * 50)
    print("终极版现货交易系统 - 改进测试")
    print("=" * 50)
    
    # 运行测试
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
    
    print("\n测试完成")

if __name__ == "__main__":
    run_tests()
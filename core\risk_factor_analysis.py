#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级风险因子分析系统
Risk Factor Analysis System for Institutional-Grade Quantitative Trading

基于Fama-French、Barra等经典因子模型，提供专业的风险因子分解和归因分析
"""

import logging
import warnings
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class FactorExposure:
    """因子暴露结果"""

    factor_name: str
    exposure: float
    t_statistic: float
    p_value: float
    contribution_to_return: float
    contribution_to_risk: float


@dataclass
class RiskFactorAnalysisResult:
    """风险因子分析结果"""

    strategy_name: str
    total_return: float
    systematic_return: float
    idiosyncratic_return: float
    factor_exposures: List[FactorExposure]
    r_squared: float
    tracking_error: float
    information_ratio: float
    risk_decomposition: Dict[str, float]
    factor_loadings: Dict[str, float]
    residual_analysis: Dict[str, Any]
    recommendation: str


class InstitutionalRiskFactorAnalyzer:
    """
    机构级风险因子分析器

    基于学术界和业界广泛认可的多因子模型
    """

    def __init__(self):
        """初始化风险因子分析器"""
        self.factor_models = {
            "fama_french_3": ["market", "size", "value"],
            "fama_french_5": [
                "market",
                "size",
                "value",
                "profitability",
                "investment",
            ],
            "carhart_4": ["market", "size", "value", "momentum"],
            "barra_style": [
                "size",
                "value",
                "growth",
                "momentum",
                "quality",
                "volatility",
                "leverage",
            ],
            "macro_factors": [
                "interest_rate",
                "inflation",
                "credit_spread",
                "vix",
                "dollar_index",
            ],
        }

        # 风险阈值
        self.risk_thresholds = {
            "max_single_factor_exposure": 0.3,  # 单一因子最大暴露
            "max_tracking_error": 0.05,  # 最大跟踪误差
            "min_r_squared": 0.3,  # 最小R平方
            "max_concentration_risk": 0.5,  # 最大集中度风险
        }

    def analyze_risk_factors(
        self,
        strategy_returns: pd.Series,
        factor_returns: pd.DataFrame,
        strategy_name: str,
        model_type: str = "fama_french_5",
    ) -> RiskFactorAnalysisResult:
        """
        执行风险因子分析

        Args:
            strategy_returns: 策略收益率序列
            factor_returns: 因子收益率DataFrame
            strategy_name: 策略名称
            model_type: 因子模型类型

        Returns:
            RiskFactorAnalysisResult: 风险因子分析结果
        """
        logger.info(f"开始分析策略 {strategy_name} 的风险因子暴露")

        # 1. 数据对齐和预处理
        aligned_data = self._align_data(strategy_returns, factor_returns)

        # 2. 因子回归分析
        regression_results = self._perform_factor_regression(
            aligned_data["strategy_returns"], aligned_data["factor_returns"]
        )

        # 3. 因子暴露分析
        factor_exposures = self._analyze_factor_exposures(
            regression_results, aligned_data["factor_returns"]
        )

        # 4. 收益分解
        return_decomposition = self._decompose_returns(
            aligned_data["strategy_returns"],
            regression_results,
            aligned_data["factor_returns"],
        )

        # 5. 风险分解
        risk_decomposition = self._decompose_risk(
            aligned_data["strategy_returns"],
            regression_results,
            aligned_data["factor_returns"],
        )

        # 6. 残差分析
        residual_analysis = self._analyze_residuals(
            regression_results["residuals"]
        )

        # 7. 生成建议
        recommendation = self._generate_risk_recommendation(
            factor_exposures, risk_decomposition, regression_results
        )

        return RiskFactorAnalysisResult(
            strategy_name=strategy_name,
            total_return=return_decomposition["total_return"],
            systematic_return=return_decomposition["systematic_return"],
            idiosyncratic_return=return_decomposition["idiosyncratic_return"],
            factor_exposures=factor_exposures,
            r_squared=regression_results["r_squared"],
            tracking_error=risk_decomposition["tracking_error"],
            information_ratio=(
                return_decomposition["idiosyncratic_return"]
                / risk_decomposition["tracking_error"]
                if risk_decomposition["tracking_error"] != 0
                else 0
            ),
            risk_decomposition=risk_decomposition,
            factor_loadings=regression_results["factor_loadings"],
            residual_analysis=residual_analysis,
            recommendation=recommendation,
        )

    def _align_data(
        self, strategy_returns: pd.Series, factor_returns: pd.DataFrame
    ) -> Dict[str, Any]:
        """对齐策略收益和因子收益数据"""
        # 找到共同的时间索引
        common_index = strategy_returns.index.intersection(
            factor_returns.index
        )

        if len(common_index) < 60:  # 至少需要60个观测值
            raise ValueError("数据点不足，至少需要60个观测值进行因子分析")

        aligned_strategy = strategy_returns.loc[common_index]
        aligned_factors = factor_returns.loc[common_index]

        # 移除缺失值
        complete_data = pd.concat(
            [aligned_strategy, aligned_factors], axis=1
        ).dropna()

        return {
            "strategy_returns": complete_data.iloc[:, 0],
            "factor_returns": complete_data.iloc[:, 1:],
            "common_index": complete_data.index,
        }

    def _perform_factor_regression(
        self, strategy_returns: pd.Series, factor_returns: pd.DataFrame
    ) -> Dict[str, Any]:
        """执行因子回归分析"""
        X = factor_returns.values
        y = strategy_returns.values

        # 执行线性回归
        model = LinearRegression(fit_intercept=True)
        model.fit(X, y)

        # 计算统计量
        y_pred = model.predict(X)
        residuals = y - y_pred

        # R平方
        ss_res = np.sum(residuals**2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)

        # 调整R平方
        n = len(y)
        p = X.shape[1]
        adjusted_r_squared = 1 - (1 - r_squared) * (n - 1) / (n - p - 1)

        # 标准误差
        mse = ss_res / (n - p - 1)
        se_coef = np.sqrt(mse * np.diag(np.linalg.inv(X.T @ X)))

        # t统计量和p值
        t_stats = model.coef_ / se_coef
        from scipy import stats

        p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), n - p - 1))

        return {
            "alpha": model.intercept_,
            "betas": model.coef_,
            "factor_loadings": dict(zip(factor_returns.columns, model.coef_)),
            "r_squared": r_squared,
            "adjusted_r_squared": adjusted_r_squared,
            "residuals": residuals,
            "t_statistics": t_stats,
            "p_values": p_values,
            "standard_errors": se_coef,
            "fitted_values": y_pred,
        }

    def _analyze_factor_exposures(
        self, regression_results: Dict, factor_returns: pd.DataFrame
    ) -> List[FactorExposure]:
        """分析因子暴露"""
        exposures = []

        for i, factor_name in enumerate(factor_returns.columns):
            beta = regression_results["betas"][i]
            t_stat = regression_results["t_statistics"][i]
            p_val = regression_results["p_values"][i]

            # 计算因子对收益的贡献
            factor_contribution = (
                beta * factor_returns[factor_name].mean() * 252
            )  # 年化

            # 计算因子对风险的贡献
            factor_variance = (beta**2) * (
                factor_returns[factor_name].var() * 252
            )

            exposures.append(
                FactorExposure(
                    factor_name=factor_name,
                    exposure=beta,
                    t_statistic=t_stat,
                    p_value=p_val,
                    contribution_to_return=factor_contribution,
                    contribution_to_risk=factor_variance,
                )
            )

        return exposures

    def _decompose_returns(
        self,
        strategy_returns: pd.Series,
        regression_results: Dict,
        factor_returns: pd.DataFrame,
    ) -> Dict[str, float]:
        """分解收益来源"""
        # 总收益
        total_return = strategy_returns.mean() * 252  # 年化

        # 系统性收益（因子解释的部分）
        systematic_return = np.sum(
            [
                beta * factor_returns.iloc[:, i].mean() * 252
                for i, beta in enumerate(regression_results["betas"])
            ]
        )

        # 特异性收益（Alpha）
        idiosyncratic_return = regression_results["alpha"] * 252

        return {
            "total_return": total_return,
            "systematic_return": systematic_return,
            "idiosyncratic_return": idiosyncratic_return,
            "alpha_contribution": (
                idiosyncratic_return / total_return if total_return != 0 else 0
            ),
        }

    def _decompose_risk(
        self,
        strategy_returns: pd.Series,
        regression_results: Dict,
        factor_returns: pd.DataFrame,
    ) -> Dict[str, float]:
        """分解风险来源"""
        # 总风险
        total_risk = strategy_returns.std() * np.sqrt(252)  # 年化波动率

        # 系统性风险
        factor_covariance = factor_returns.cov() * 252  # 年化协方差矩阵
        betas = regression_results["betas"]
        systematic_variance = betas.T @ factor_covariance @ betas
        systematic_risk = np.sqrt(systematic_variance)

        # 特异性风险
        residual_variance = np.var(regression_results["residuals"]) * 252
        idiosyncratic_risk = np.sqrt(residual_variance)

        # 跟踪误差
        tracking_error = idiosyncratic_risk

        # 风险分解比例
        systematic_risk_pct = (
            (systematic_variance / (total_risk**2)) if total_risk != 0 else 0
        )
        idiosyncratic_risk_pct = (
            (residual_variance / (total_risk**2)) if total_risk != 0 else 0
        )

        return {
            "total_risk": total_risk,
            "systematic_risk": systematic_risk,
            "idiosyncratic_risk": idiosyncratic_risk,
            "tracking_error": tracking_error,
            "systematic_risk_percentage": systematic_risk_pct,
            "idiosyncratic_risk_percentage": idiosyncratic_risk_pct,
        }

    def _analyze_residuals(self, residuals: np.ndarray) -> Dict[str, Any]:
        """分析回归残差"""
        from scipy import stats

        # 正态性检验
        jb_stat, jb_p_value = stats.jarque_bera(residuals)

        # 自相关检验
        from statsmodels.stats.diagnostic import acorr_ljungbox

        lb_result = acorr_ljungbox(residuals, lags=10, return_df=True)

        # 异方差检验
        from statsmodels.stats.diagnostic import het_breuschpagan

        # 简化的异方差检验
        squared_residuals = residuals**2
        heteroscedasticity_p = stats.jarque_bera(squared_residuals)[1]

        return {
            "mean_residual": np.mean(residuals),
            "std_residual": np.std(residuals),
            "skewness": stats.skew(residuals),
            "kurtosis": stats.kurtosis(residuals),
            "jarque_bera_p_value": jb_p_value,
            "ljung_box_p_value": lb_result["lb_pvalue"].iloc[-1],
            "heteroscedasticity_p_value": heteroscedasticity_p,
            "is_normal": jb_p_value > 0.05,
            "is_autocorrelated": lb_result["lb_pvalue"].iloc[-1] < 0.05,
            "is_heteroscedastic": heteroscedasticity_p < 0.05,
        }

    def _generate_risk_recommendation(
        self,
        factor_exposures: List[FactorExposure],
        risk_decomposition: Dict,
        regression_results: Dict,
    ) -> str:
        """生成风险管理建议"""
        recommendations = []

        # 检查因子暴露集中度
        max_exposure = max([abs(exp.exposure) for exp in factor_exposures])
        if max_exposure > self.risk_thresholds["max_single_factor_exposure"]:
            recommendations.append(
                f"警告：单一因子暴露过高({max_exposure:.2f})，建议分散化"
            )

        # 检查跟踪误差
        if (
            risk_decomposition["tracking_error"]
            > self.risk_thresholds["max_tracking_error"]
        ):
            recommendations.append(
                f"注意：跟踪误差较高({risk_decomposition['tracking_error']:.2%})，特异性风险较大"
            )

        # 检查模型拟合度
        if (
            regression_results["r_squared"]
            < self.risk_thresholds["min_r_squared"]
        ):
            recommendations.append(
                f"注意：模型解释力不足(R²={regression_results['r_squared']:.2f})，可能存在遗漏因子"
            )

        # 检查显著因子
        significant_factors = [
            exp for exp in factor_exposures if exp.p_value < 0.05
        ]
        if len(significant_factors) == 0:
            recommendations.append(
                "警告：无显著因子暴露，策略可能过于依赖特异性风险"
            )

        if not recommendations:
            recommendations.append("风险因子暴露合理，符合机构级风险管理标准")

        return "; ".join(recommendations)


def create_factor_returns_template() -> pd.DataFrame:
    """创建因子收益率模板"""
    # 创建示例因子数据
    dates = pd.date_range("2020-01-01", "2023-12-31", freq="D")

    # 模拟因子收益率
    np.random.seed(42)
    n_days = len(dates)

    factor_data = {
        "market": np.random.normal(0.0005, 0.015, n_days),  # 市场因子
        "size": np.random.normal(0.0002, 0.008, n_days),  # 规模因子
        "value": np.random.normal(0.0001, 0.006, n_days),  # 价值因子
        "momentum": np.random.normal(0.0003, 0.010, n_days),  # 动量因子
        "quality": np.random.normal(0.0001, 0.005, n_days),  # 质量因子
        "volatility": np.random.normal(-0.0001, 0.007, n_days),  # 波动率因子
        "profitability": np.random.normal(
            0.0002, 0.004, n_days
        ),  # 盈利能力因子
        "investment": np.random.normal(-0.0001, 0.003, n_days),  # 投资因子
    }

    return pd.DataFrame(factor_data, index=dates)


def generate_risk_factor_report(
    analysis_result: RiskFactorAnalysisResult,
) -> str:
    """生成风险因子分析报告"""
    report = f"""
=== 机构级风险因子分析报告 ===
策略名称: {analysis_result.strategy_name}
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 收益分解 ===
总收益: {analysis_result.total_return:.2%}
系统性收益: {analysis_result.systematic_return:.2%}
特异性收益(Alpha): {analysis_result.idiosyncratic_return:.2%}

=== 模型拟合度 ===
R平方: {analysis_result.r_squared:.3f}
跟踪误差: {analysis_result.tracking_error:.2%}
信息比率: {analysis_result.information_ratio:.3f}

=== 因子暴露分析 ==="""

    for exposure in analysis_result.factor_exposures:
        significance = (
            "***"
            if exposure.p_value < 0.01
            else (
                "**"
                if exposure.p_value < 0.05
                else "*" if exposure.p_value < 0.1 else ""
            )
        )
        report += f"""
{exposure.factor_name}: {exposure.exposure:.3f}{significance} (t={exposure.t_statistic:.2f}, p={exposure.p_value:.3f})
  收益贡献: {exposure.contribution_to_return:.2%}
  风险贡献: {exposure.contribution_to_risk:.4f}"""

    report += f"""

=== 风险分解 ===
总风险: {analysis_result.risk_decomposition['total_risk']:.2%}
系统性风险: {analysis_result.risk_decomposition['systematic_risk']:.2%} ({analysis_result.risk_decomposition['systematic_risk_percentage']:.1%})
特异性风险: {analysis_result.risk_decomposition['idiosyncratic_risk']:.2%} ({analysis_result.risk_decomposition['idiosyncratic_risk_percentage']:.1%})

=== 残差分析 ===
残差正态性: {'通过' if analysis_result.residual_analysis['is_normal'] else '未通过'}
自相关检验: {'存在' if analysis_result.residual_analysis['is_autocorrelated'] else '不存在'}
异方差检验: {'存在' if analysis_result.residual_analysis['is_heteroscedastic'] else '不存在'}

=== 风险管理建议 ===
{analysis_result.recommendation}

注：***p<0.01, **p<0.05, *p<0.1
"""
    return report

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应用程序常量
Application Constants

定义应用程序级别的常量
"""

from typing import Dict, List, Tuple


class AppConstants:
    """应用程序常量"""
    
    # ==================== 应用信息 ====================
    APP_NAME = "Ultimate Spot Trading Terminal"
    APP_VERSION = "4.0.0"
    APP_AUTHOR = "Augment Agent"
    APP_DESCRIPTION = "Professional Cryptocurrency Trading Platform"
    
    # ==================== 窗口配置 ====================
    # 默认窗口尺寸
    DEFAULT_WINDOW_WIDTH = 1600
    DEFAULT_WINDOW_HEIGHT = 1000
    
    # 最小窗口尺寸
    MIN_WINDOW_WIDTH = 1200
    MIN_WINDOW_HEIGHT = 800
    
    # 最大窗口尺寸
    MAX_WINDOW_WIDTH = 2560
    MAX_WINDOW_HEIGHT = 1440
    
    # 窗口位置
    DEFAULT_WINDOW_X = 100
    DEFAULT_WINDOW_Y = 50
    
    # ==================== 更新间隔 ====================
    # 基础更新间隔 (秒)
    DEFAULT_UPDATE_INTERVAL = 1.0
    FAST_UPDATE_INTERVAL = 0.5
    SLOW_UPDATE_INTERVAL = 5.0
    
    # 特定功能更新间隔
    MARKET_DATA_REFRESH_INTERVAL = 1.0
    ACCOUNT_DATA_REFRESH_INTERVAL = 2.0
    POSITION_DATA_REFRESH_INTERVAL = 1.0
    ORDER_DATA_REFRESH_INTERVAL = 0.5
    PERFORMANCE_UPDATE_INTERVAL = 10.0
    SYSTEM_MONITOR_INTERVAL = 5.0
    
    # ==================== 网络配置 ====================
    # 超时设置 (秒)
    DEFAULT_TIMEOUT = 30.0
    API_TIMEOUT = 10.0
    CONNECTION_TIMEOUT = 15.0
    WEBSOCKET_TIMEOUT = 5.0
    
    # 重试配置
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY_BASE = 1.0  # 基础延迟
    RETRY_DELAY_MULTIPLIER = 2.0  # 延迟倍数
    
    # 速率限制
    RATE_LIMIT_PER_MINUTE = 100
    RATE_LIMIT_PER_SECOND = 10
    BURST_LIMIT = 20
    
    # ==================== 数据配置 ====================
    # 缓存配置
    DEFAULT_CACHE_SIZE = 1000
    MAX_CACHE_SIZE = 10000
    CACHE_EXPIRY_SECONDS = 300
    
    # 历史数据
    DEFAULT_HISTORY_DAYS = 30
    MAX_HISTORY_DAYS = 365
    MIN_HISTORY_DAYS = 1
    
    # 记录限制
    MAX_LOG_RECORDS = 10000
    MAX_TRADE_RECORDS = 5000
    MAX_ORDER_RECORDS = 2000
    
    # ==================== 性能配置 ====================
    # CPU和内存限制
    MAX_CPU_USAGE_PERCENT = 70.0
    MAX_MEMORY_USAGE_PERCENT = 80.0
    
    # 线程配置
    DEFAULT_THREAD_POOL_SIZE = 5
    MAX_THREAD_POOL_SIZE = 20
    MIN_THREAD_POOL_SIZE = 2
    
    # 执行超时
    EXECUTION_TIMEOUT = 30.0
    LONG_OPERATION_TIMEOUT = 120.0
    
    # ==================== 文件配置 ====================
    # 文件大小限制 (字节)
    MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_CONFIG_FILE_SIZE = 1 * 1024 * 1024  # 1MB
    MAX_DATA_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    # 备份配置
    MAX_BACKUP_FILES = 5
    BACKUP_INTERVAL_HOURS = 24
    AUTO_BACKUP_ENABLED = True
    
    # ==================== 安全配置 ====================
    # 密码要求
    MIN_PASSWORD_LENGTH = 8
    MAX_PASSWORD_LENGTH = 128
    REQUIRE_SPECIAL_CHARS = True
    
    # 会话配置
    SESSION_TIMEOUT_MINUTES = 30
    MAX_LOGIN_ATTEMPTS = 3
    LOCKOUT_DURATION_MINUTES = 15
    
    # ==================== 交易配置 ====================
    # 默认交易参数
    DEFAULT_INITIAL_CAPITAL = 10000.0
    MIN_INITIAL_CAPITAL = 100.0
    MAX_INITIAL_CAPITAL = 1000000.0
    
    # 精度配置
    PRICE_DECIMAL_PLACES = 8
    AMOUNT_DECIMAL_PLACES = 8
    PERCENTAGE_DECIMAL_PLACES = 2
    
    # ==================== 界面配置 ====================
    # 动画配置
    ANIMATION_DURATION_MS = 300
    FADE_DURATION_MS = 200
    TRANSITION_DURATION_MS = 150
    
    # 响应配置
    CLICK_DELAY_MS = 100
    DOUBLE_CLICK_INTERVAL_MS = 500
    HOVER_DELAY_MS = 200
    
    # ==================== 日志配置 ====================
    # 日志级别
    LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    DEFAULT_LOG_LEVEL = "INFO"
    
    # 日志格式
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # ==================== 支持的语言 ====================
    SUPPORTED_LANGUAGES = {
        "zh_CN": "简体中文",
        "zh_TW": "繁體中文",
        "en_US": "English",
        "ja_JP": "日本語",
        "ko_KR": "한국어"
    }
    DEFAULT_LANGUAGE = "zh_CN"
    
    # ==================== 支持的主题 ====================
    SUPPORTED_THEMES = {
        "dark": "深色主题",
        "light": "浅色主题", 
        "blue": "蓝色主题",
        "green": "绿色主题"
    }
    DEFAULT_THEME = "dark"
    
    # ==================== 环境配置 ====================
    ENVIRONMENT_TYPES = ["development", "testing", "staging", "production"]
    DEFAULT_ENVIRONMENT = "development"
    
    # 调试配置
    DEBUG_MODE = True
    VERBOSE_LOGGING = False
    ENABLE_PROFILING = False
    
    # ==================== 版本兼容性 ====================
    MIN_PYTHON_VERSION = (3, 8)
    RECOMMENDED_PYTHON_VERSION = (3, 11)
    
    # 依赖版本
    MIN_TKINTER_VERSION = "8.6"
    MIN_PANDAS_VERSION = "1.5.0"
    MIN_NUMPY_VERSION = "1.21.0"
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def get_window_geometry(cls) -> str:
        """获取默认窗口几何字符串"""
        return f"{cls.DEFAULT_WINDOW_WIDTH}x{cls.DEFAULT_WINDOW_HEIGHT}+{cls.DEFAULT_WINDOW_X}+{cls.DEFAULT_WINDOW_Y}"
    
    @classmethod
    def get_min_window_size(cls) -> Tuple[int, int]:
        """获取最小窗口尺寸"""
        return (cls.MIN_WINDOW_WIDTH, cls.MIN_WINDOW_HEIGHT)
    
    @classmethod
    def get_max_window_size(cls) -> Tuple[int, int]:
        """获取最大窗口尺寸"""
        return (cls.MAX_WINDOW_WIDTH, cls.MAX_WINDOW_HEIGHT)
    
    @classmethod
    def get_retry_delay(cls, attempt: int) -> float:
        """计算重试延迟时间"""
        return cls.RETRY_DELAY_BASE * (cls.RETRY_DELAY_MULTIPLIER ** attempt)
    
    @classmethod
    def is_valid_language(cls, language: str) -> bool:
        """检查语言是否支持"""
        return language in cls.SUPPORTED_LANGUAGES
    
    @classmethod
    def is_valid_theme(cls, theme: str) -> bool:
        """检查主题是否支持"""
        return theme in cls.SUPPORTED_THEMES
    
    @classmethod
    def get_app_info(cls) -> Dict[str, str]:
        """获取应用信息"""
        return {
            "name": cls.APP_NAME,
            "version": cls.APP_VERSION,
            "author": cls.APP_AUTHOR,
            "description": cls.APP_DESCRIPTION
        }


# 创建全局常量实例
APP_CONSTANTS = AppConstants()


if __name__ == "__main__":
    # 测试常量
    print("🔧 应用常量测试")
    print(f"应用名称: {AppConstants.APP_NAME}")
    print(f"默认窗口尺寸: {AppConstants.get_window_geometry()}")
    print(f"支持的语言: {list(AppConstants.SUPPORTED_LANGUAGES.keys())}")
    print(f"重试延迟(第2次): {AppConstants.get_retry_delay(2)}秒")
    print("✅ 应用常量测试完成")

{"data_mtime": 1748505946, "dep_lines": [8, 8, 7, 9, 10, 242, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tkinter.ttk", "tkinter.messagebox", "tkinter", "sys", "os", "traceback", "builtins", "_frozen_importlib", "_tkinter", "_typeshed", "abc", "tkinter.font", "typing"], "hash": "9ade54b21f4faec10c9f09fbdf355518537aa783", "id": "debug_api_dialog", "ignore_all": false, "interface_hash": "2634a43c0d979471e121f1d234178c7ad8cfd878", "mtime": 1748505945, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\debug_api_dialog.py", "plugin_data": null, "size": 8411, "suppressed": [], "version_id": "1.15.0"}
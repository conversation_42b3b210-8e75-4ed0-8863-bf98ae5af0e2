
def show_risk_warning():
    """显示风险警告"""
    import tkinter as tk
    from tkinter import messagebox
    
    warning_text = """
⚠️ 重要风险警告 ⚠️

这是一个交易策略学习系统，不是真实交易平台！

🚨 关键提醒:
• 所有数据都是模拟生成的
• 不保证任何盈利
• 不涉及真实资金
• 仅供教育学习使用

🎯 如果您寻找真实交易:
• 请使用正规交易所
• 充分了解市场风险
• 制定合理资金管理计划
• 寻求专业投资建议

是否继续使用学习系统？
"""
    
    result = messagebox.askyesno("风险警告", warning_text)
    return result

# 在GUI启动前调用
if __name__ == "__main__":
    if show_risk_warning():
        main()
    else:
        print("用户选择退出")

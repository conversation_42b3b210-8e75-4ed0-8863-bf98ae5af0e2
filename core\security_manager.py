#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业级安全管理框架
Enterprise Security Management Framework
"""

import base64
import hashlib
import hmac
import json
import logging
import secrets
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import jwt
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


@dataclass
class SecurityEvent:
    """安全事件记录"""

    timestamp: datetime
    event_type: str
    user_id: str
    ip_address: str
    details: Dict[str, Any]
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result["timestamp"] = self.timestamp.isoformat()
        return result


@dataclass
class AuthSession:
    """认证会话"""

    session_id: str
    user_id: str
    created_at: datetime
    expires_at: datetime
    ip_address: str
    permissions: List[str]

    def is_valid(self) -> bool:
        return datetime.now() < self.expires_at

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result["created_at"] = self.created_at.isoformat()
        result["expires_at"] = self.expires_at.isoformat()
        return result


class AdvancedSecurityManager:
    """高级安全管理器"""

    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._default_config()

        # 加密密钥管理
        self.master_key = self._load_or_create_master_key()
        self.cipher = Fernet(self.master_key)

        # 会话管理
        self.active_sessions: Dict[str, AuthSession] = {}
        self.session_lock = threading.RLock()

        # 安全监控
        self.failed_attempts: Dict[str, List[datetime]] = {}
        self.security_events: List[SecurityEvent] = []
        self.security_callbacks: List[Callable] = []

        # 速率限制
        self.rate_limits: Dict[str, List[datetime]] = {}

        # 设置日志
        self.logger = self._setup_logger()

        # 启动清理任务
        self._start_cleanup_task()

    def _default_config(self) -> Dict[str, Any]:
        """默认安全配置"""
        return {
            "max_login_attempts": 5,
            "lockout_duration_minutes": 30,
            "session_timeout_hours": 24,
            "rate_limit_requests_per_minute": 60,
            "password_min_length": 12,
            "require_special_chars": True,
            "jwt_algorithm": "HS256",
            "audit_log_retention_days": 90,
            "auto_cleanup_interval_hours": 1,
        }

    def _setup_logger(self) -> logging.Logger:
        """设置安全日志"""
        logger = logging.getLogger("SecurityManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # 创建安全日志文件
            log_file = Path("logs/security.log")
            log_file.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.INFO)

            # 安全日志格式
            formatter = logging.Formatter(
                "%(asctime)s - SECURITY - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def _load_or_create_master_key(self) -> bytes:
        """加载或创建主加密密钥"""
        key_file = Path("config/.master_key")

        if key_file.exists():
            try:
                with open(key_file, "rb") as f:
                    return f.read()
            except Exception as e:
                self.logger.warning(f"加载主密钥失败，生成新密钥: {e}")

        # 生成新的主密钥
        key = Fernet.generate_key()

        try:
            key_file.parent.mkdir(parents=True, exist_ok=True)
            with open(key_file, "wb") as f:
                f.write(key)

            # 设置文件权限（仅所有者可读写）
            key_file.chmod(0o600)

            self.logger.info("✅ 新的主加密密钥已生成")
        except Exception as e:
            self.logger.error(f"保存主密钥失败: {e}")

        return key

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        try:
            encrypted = self.cipher.encrypt(data.encode("utf-8"))
            return base64.urlsafe_b64encode(encrypted).decode("utf-8")
        except Exception as e:
            self.logger.error(f"数据加密失败: {e}")
            raise

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(
                encrypted_data.encode("utf-8")
            )
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode("utf-8")
        except Exception as e:
            self.logger.error(f"数据解密失败: {e}")
            raise

    def generate_secure_signature(self, data: str, secret: str) -> str:
        """生成安全签名"""
        try:
            signature = hmac.new(
                secret.encode("utf-8"), data.encode("utf-8"), hashlib.sha256
            ).hexdigest()

            return signature
        except Exception as e:
            self.logger.error(f"签名生成失败: {e}")
            raise

    def validate_signature(
        self, data: str, signature: str, secret: str
    ) -> bool:
        """验证签名"""
        try:
            expected_signature = self.generate_secure_signature(data, secret)
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            self.logger.error(f"签名验证失败: {e}")
            return False

    def hash_password(
        self, password: str, salt: Optional[bytes] = None
    ) -> tuple[str, str]:
        """密码哈希处理"""
        if salt is None:
            salt = secrets.token_bytes(32)

        # 使用PBKDF2进行密码哈希
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        key = kdf.derive(password.encode("utf-8"))

        # 返回哈希值和盐值的base64编码
        hash_b64 = base64.urlsafe_b64encode(key).decode("utf-8")
        salt_b64 = base64.urlsafe_b64encode(salt).decode("utf-8")

        return hash_b64, salt_b64

    def verify_password(
        self, password: str, hash_b64: str, salt_b64: str
    ) -> bool:
        """验证密码"""
        try:
            salt = base64.urlsafe_b64decode(salt_b64.encode("utf-8"))
            expected_hash, _ = self.hash_password(password, salt)
            return hmac.compare_digest(hash_b64, expected_hash)
        except Exception as e:
            self.logger.error(f"密码验证失败: {e}")
            return False

    def validate_password_strength(
        self, password: str
    ) -> tuple[bool, List[str]]:
        """验证密码强度"""
        issues = []

        # 检查最小长度
        if len(password) < self.config["password_min_length"]:
            issues.append(
                f"密码长度至少需要 {self.config['password_min_length']} 位"
            )

        # 检查是否包含大写字母
        if not any(c.isupper() for c in password):
            issues.append("密码需要包含至少一个大写字母")

        # 检查是否包含小写字母
        if not any(c.islower() for c in password):
            issues.append("密码需要包含至少一个小写字母")

        # 检查是否包含数字
        if not any(c.isdigit() for c in password):
            issues.append("密码需要包含至少一个数字")

        # 检查是否包含特殊字符
        if self.config["require_special_chars"]:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                issues.append("密码需要包含至少一个特殊字符")

        return len(issues) == 0, issues

    def rate_limit_check(self, user_id: str, action: str = "general") -> bool:
        """请求频率限制检查"""
        now = datetime.now()
        key = f"{user_id}:{action}"

        # 清理过期的请求记录
        if key in self.rate_limits:
            self.rate_limits[key] = [
                timestamp
                for timestamp in self.rate_limits[key]
                if now - timestamp < timedelta(minutes=1)
            ]
        else:
            self.rate_limits[key] = []

        # 检查是否超过限制
        max_requests = self.config["rate_limit_requests_per_minute"]
        if len(self.rate_limits[key]) >= max_requests:
            self._log_security_event(
                "RATE_LIMIT_EXCEEDED",
                user_id,
                "0.0.0.0",
                {
                    "action": action,
                    "requests_count": len(self.rate_limits[key]),
                },
                "MEDIUM",
            )
            return False

        # 记录新请求
        self.rate_limits[key].append(now)
        return True

    def login_attempt(
        self,
        user_id: str,
        password: str,
        ip_address: str,
        stored_hash: str,
        stored_salt: str,
    ) -> tuple[bool, Optional[str]]:
        """登录尝试处理"""
        now = datetime.now()

        # 检查账户是否被锁定
        if self._is_account_locked(user_id):
            self._log_security_event(
                "LOGIN_ATTEMPT_LOCKED_ACCOUNT", user_id, ip_address, {}, "HIGH"
            )
            return False, "账户已被锁定，请稍后再试"

        # 验证密码
        if self.verify_password(password, stored_hash, stored_salt):
            # 登录成功
            self._clear_failed_attempts(user_id)

            # 创建会话
            session = self._create_session(user_id, ip_address)

            self._log_security_event(
                "LOGIN_SUCCESS",
                user_id,
                ip_address,
                {"session_id": session.session_id},
                "LOW",
            )

            return True, session.session_id
        else:
            # 登录失败
            self._record_failed_attempt(user_id, ip_address)

            remaining_attempts = self.config["max_login_attempts"] - len(
                self.failed_attempts.get(user_id, [])
            )

            self._log_security_event(
                "LOGIN_FAILURE",
                user_id,
                ip_address,
                {"remaining_attempts": remaining_attempts},
                "MEDIUM",
            )

            return False, f"密码错误，剩余尝试次数: {remaining_attempts}"

    def _is_account_locked(self, user_id: str) -> bool:
        """检查账户是否被锁定"""
        if user_id not in self.failed_attempts:
            return False

        failed_attempts = self.failed_attempts[user_id]

        # 清理过期的失败尝试
        cutoff_time = datetime.now() - timedelta(
            minutes=self.config["lockout_duration_minutes"]
        )
        failed_attempts = [
            attempt for attempt in failed_attempts if attempt > cutoff_time
        ]
        self.failed_attempts[user_id] = failed_attempts

        return len(failed_attempts) >= self.config["max_login_attempts"]

    def _record_failed_attempt(self, user_id: str, ip_address: str):
        """记录失败的登录尝试"""
        if user_id not in self.failed_attempts:
            self.failed_attempts[user_id] = []

        self.failed_attempts[user_id].append(datetime.now())

    def _clear_failed_attempts(self, user_id: str):
        """清除失败尝试记录"""
        if user_id in self.failed_attempts:
            del self.failed_attempts[user_id]

    def _create_session(
        self,
        user_id: str,
        ip_address: str,
        permissions: Optional[List[str]] = None,
    ) -> AuthSession:
        """创建认证会话"""
        session_id = secrets.token_urlsafe(32)

        session = AuthSession(
            session_id=session_id,
            user_id=user_id,
            created_at=datetime.now(),
            expires_at=datetime.now()
            + timedelta(hours=self.config["session_timeout_hours"]),
            ip_address=ip_address,
            permissions=permissions or ["basic_trading", "view_account"],
        )

        with self.session_lock:
            self.active_sessions[session_id] = session

        return session

    def validate_session(
        self, session_id: str, ip_address: str
    ) -> tuple[bool, Optional[AuthSession]]:
        """验证会话"""
        with self.session_lock:
            session = self.active_sessions.get(session_id)

            if not session:
                return False, None

            if not session.is_valid():
                # 会话过期
                del self.active_sessions[session_id]
                self._log_security_event(
                    "SESSION_EXPIRED",
                    session.user_id,
                    ip_address,
                    {"session_id": session_id},
                    "LOW",
                )
                return False, None

            # 可选：检查IP地址是否匹配
            if session.ip_address != ip_address:
                self._log_security_event(
                    "SESSION_IP_MISMATCH",
                    session.user_id,
                    ip_address,
                    {
                        "session_id": session_id,
                        "original_ip": session.ip_address,
                        "current_ip": ip_address,
                    },
                    "HIGH",
                )
                # 根据安全策略决定是否允许
                # 这里我们记录事件但仍然允许访问

            return True, session

    def logout_session(self, session_id: str):
        """注销会话"""
        with self.session_lock:
            session = self.active_sessions.pop(session_id, None)

            if session:
                self._log_security_event(
                    "LOGOUT",
                    session.user_id,
                    session.ip_address,
                    {"session_id": session_id},
                    "LOW",
                )

    def generate_api_token(
        self, user_id: str, permissions: List[str], expires_hours: int = 24
    ) -> str:
        """生成API令牌"""
        payload = {
            "user_id": user_id,
            "permissions": permissions,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(hours=expires_hours),
            "jti": secrets.token_hex(16),  # JWT ID
        }

        # 使用主密钥签名
        token = jwt.encode(
            payload, self.master_key, algorithm=self.config["jwt_algorithm"]
        )

        self._log_security_event(
            "API_TOKEN_GENERATED",
            user_id,
            "0.0.0.0",
            {"token_id": payload["jti"], "permissions": permissions},
            "LOW",
        )

        return token

    def validate_api_token(self, token: str) -> tuple[bool, Optional[Dict]]:
        """验证API令牌"""
        try:
            payload = jwt.decode(
                token,
                self.master_key,
                algorithms=[self.config["jwt_algorithm"]],
            )
            return True, payload
        except jwt.ExpiredSignatureError:
            return False, {"error": "token_expired"}
        except jwt.InvalidTokenError:
            return False, {"error": "invalid_token"}

    def _log_security_event(
        self,
        event_type: str,
        user_id: str,
        ip_address: str,
        details: Dict[str, Any],
        severity: str,
    ):
        """记录安全事件"""
        event = SecurityEvent(
            timestamp=datetime.now(),
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            details=details,
            severity=severity,
        )

        self.security_events.append(event)

        # 记录到日志
        self.logger.info(
            f"{severity} - {event_type} - User: {user_id} - IP: {ip_address} - Details: {details}"
        )

        # 触发安全回调
        for callback in self.security_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"安全事件回调执行失败: {e}")

        # 如果是高危事件，立即处理
        if severity in ["HIGH", "CRITICAL"]:
            self._handle_critical_event(event)

    def _handle_critical_event(self, event: SecurityEvent):
        """处理关键安全事件"""
        self.logger.critical(
            f"🚨 关键安全事件: {event.event_type} - {event.details}"
        )

        # 可以添加自动响应措施，如：
        # 1. 发送警报通知
        # 2. 临时锁定账户
        # 3. 增强监控
        pass

    def add_security_callback(self, callback: Callable):
        """添加安全事件回调"""
        self.security_callbacks.append(callback)

    def _start_cleanup_task(self):
        """启动清理任务"""

        def cleanup_loop():
            while True:
                try:
                    self._cleanup_expired_data()
                    time.sleep(
                        self.config["auto_cleanup_interval_hours"] * 3600
                    )
                except Exception as e:
                    self.logger.error(f"清理任务异常: {e}")
                    time.sleep(3600)  # 异常后等待1小时再重试

        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()

    def _cleanup_expired_data(self):
        """清理过期数据"""
        now = datetime.now()

        # 清理过期会话
        with self.session_lock:
            expired_sessions = [
                session_id
                for session_id, session in self.active_sessions.items()
                if not session.is_valid()
            ]

            for session_id in expired_sessions:
                del self.active_sessions[session_id]

            if expired_sessions:
                self.logger.info(
                    f"🧹 清理了 {len(expired_sessions)} 个过期会话"
                )

        # 清理过期的安全事件
        retention_days = self.config["audit_log_retention_days"]
        cutoff_date = now - timedelta(days=retention_days)

        original_count = len(self.security_events)
        self.security_events = [
            event
            for event in self.security_events
            if event.timestamp > cutoff_date
        ]

        cleaned_events = original_count - len(self.security_events)
        if cleaned_events > 0:
            self.logger.info(f"🧹 清理了 {cleaned_events} 个过期安全事件")

    def get_security_summary(self) -> Dict[str, Any]:
        """获取安全状态摘要"""
        now = datetime.now()

        # 统计最近24小时的安全事件
        last_24h = now - timedelta(hours=24)
        recent_events = [
            e for e in self.security_events if e.timestamp > last_24h
        ]

        event_counts = {}
        severity_counts = {"LOW": 0, "MEDIUM": 0, "HIGH": 0, "CRITICAL": 0}

        for event in recent_events:
            event_counts[event.event_type] = (
                event_counts.get(event.event_type, 0) + 1
            )
            severity_counts[event.severity] += 1

        return {
            "active_sessions_count": len(self.active_sessions),
            "locked_accounts_count": len(
                [
                    user_id
                    for user_id in self.failed_attempts.keys()
                    if self._is_account_locked(user_id)
                ]
            ),
            "events_last_24h": len(recent_events),
            "events_by_type": event_counts,
            "events_by_severity": severity_counts,
            "rate_limit_active_users": len(self.rate_limits),
            "security_status": (
                "healthy"
                if severity_counts["CRITICAL"] == 0
                and severity_counts["HIGH"] < 5
                else "alert"
            ),
        }

    def export_security_audit(self) -> str:
        """导出安全审计日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/security_audit_{timestamp}.json"

            data = {
                "export_timestamp": datetime.now().isoformat(),
                "summary": self.get_security_summary(),
                "active_sessions": [
                    session.to_dict()
                    for session in self.active_sessions.values()
                ],
                "security_events": [
                    event.to_dict() for event in self.security_events[-1000:]
                ],  # 最近1000个事件
                "locked_accounts": [
                    user_id
                    for user_id in self.failed_attempts.keys()
                    if self._is_account_locked(user_id)
                ],
            }

            Path(filename).parent.mkdir(parents=True, exist_ok=True)
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"✅ 安全审计日志已导出: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"导出安全审计失败: {e}")
            return ""


# 全局安全管理器实例
security_manager = None


def get_security_manager(
    config: Optional[Dict] = None,
) -> AdvancedSecurityManager:
    """获取安全管理器实例"""
    global security_manager

    if security_manager is None:
        security_manager = AdvancedSecurityManager(config)

    return security_manager


if __name__ == "__main__":
    # 演示安全管理系统
    print("🔒 启动安全管理系统演示")

    # 创建安全管理器
    manager = get_security_manager()

    # 添加安全事件回调
    def security_alert_handler(event: SecurityEvent):
        if event.severity in ["HIGH", "CRITICAL"]:
            print(f"🚨 安全警报: {event.event_type} - {event.details}")

    manager.add_security_callback(security_alert_handler)

    # 演示功能
    print("\n🔐 演示密码处理:")
    password = "MySecurePass123!"

    # 验证密码强度
    is_strong, issues = manager.validate_password_strength(password)
    print(f"密码强度: {'✅ 符合要求' if is_strong else '❌ 不符合要求'}")
    if issues:
        for issue in issues:
            print(f"  - {issue}")

    # 密码哈希
    hash_value, salt_value = manager.hash_password(password)
    print(f"密码哈希: {hash_value[:20]}...")

    # 密码验证
    is_valid = manager.verify_password(password, hash_value, salt_value)
    print(f"密码验证: {'✅ 通过' if is_valid else '❌ 失败'}")

    print("\n🎫 演示会话管理:")

    # 模拟登录
    user_id = "test_user"
    ip_address = "*************"

    success, result = manager.login_attempt(
        user_id, password, ip_address, hash_value, salt_value
    )
    if success:
        session_id = result
        print(f"✅ 登录成功，会话ID: {session_id[:20]}...")

        # 验证会话
        is_valid, session = manager.validate_session(session_id, ip_address)
        print(f"会话验证: {'✅ 有效' if is_valid else '❌ 无效'}")

        if session:
            print(f"用户权限: {session.permissions}")

        # 生成API令牌
        api_token = manager.generate_api_token(
            user_id, ["trading", "account_view"]
        )
        print(f"API令牌: {api_token[:30]}...")

        # 验证API令牌
        token_valid, payload = manager.validate_api_token(api_token)
        print(f"令牌验证: {'✅ 有效' if token_valid else '❌ 无效'}")

        # 注销会话
        manager.logout_session(session_id)
        print("✅ 会话已注销")
    else:
        print(f"❌ 登录失败: {result}")

    print("\n📊 安全状态摘要:")
    summary = manager.get_security_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")

    # 导出安全审计
    audit_file = manager.export_security_audit()
    if audit_file:
        print(f"\n📁 安全审计已导出: {audit_file}")

    print("\n✅ 演示完成")

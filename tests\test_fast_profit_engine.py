#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速盈利引擎测试
Fast Profit Engine Tests

全面测试快速盈利引擎功能
"""

import unittest
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.fast_profit_engine import FastProfitEngine, TradingOpportunity, TradeResult

class TestFastProfitEngine(unittest.TestCase):
    """快速盈利引擎测试类"""

    def setUp(self):
        """测试前准备"""
        self.initial_capital = 1000.0
        self.engine = FastProfitEngine(self.initial_capital)

    def test_initialization(self):
        """测试初始化"""
        # 验证引擎初始化
        self.assertEqual(self.engine.initial_capital, self.initial_capital)
        self.assertEqual(self.engine.current_capital, self.initial_capital)
        self.assertEqual(self.engine.profit_reinvest_ratio, 0.8)
        self.assertEqual(self.engine.total_trades, 0)
        self.assertEqual(self.engine.winning_trades, 0)
        self.assertEqual(self.engine.total_profit, 0.0)
        
        # 验证策略配置
        self.assertIn('scalping', self.engine.strategies)
        self.assertIn('trend', self.engine.strategies)
        self.assertIn('range', self.engine.strategies)
        self.assertIn('breakout', self.engine.strategies)

    def test_scan_opportunities(self):
        """测试扫描交易机会"""
        # 获取交易机会
        opportunities = self.engine.scan_opportunities()
        
        # 验证返回类型
        self.assertIsInstance(opportunities, list)
        
        # 验证返回的机会数量不超过3个
        self.assertLessEqual(len(opportunities), 3)
        
        # 如果有机会，验证机会的属性
        if opportunities:
            opportunity = opportunities[0]
            self.assertIsInstance(opportunity, TradingOpportunity)
            self.assertIn(opportunity.symbol, ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT'])
            self.assertIn(opportunity.strategy, ['scalping', 'trend', 'range', 'breakout'])
            self.assertIn(opportunity.direction, ['long', 'short'])
            self.assertGreater(opportunity.confidence, 0.7)  # 只返回高置信度的机会

    def test_generate_opportunity(self):
        """测试生成交易机会"""
        # 测试生成高置信度的机会
        for strategy_name, config in self.engine.strategies.items():
            # 多次尝试，因为有随机因素
            for _ in range(10):
                opportunity = self.engine._generate_opportunity('BTC/USDT', strategy_name, config)
                if opportunity:
                    self.assertIsInstance(opportunity, TradingOpportunity)
                    self.assertEqual(opportunity.symbol, 'BTC/USDT')
                    self.assertEqual(opportunity.strategy, strategy_name)
                    self.assertGreater(opportunity.confidence, 0.7)
                    break

    def test_execute_trade(self):
        """测试执行交易"""
        # 创建测试交易机会
        opportunity = TradingOpportunity(
            symbol='BTC/USDT',
            strategy='scalping',
            direction='long',
            entry_price=50000.0,
            target_price=50400.0,  # 0.8% 目标
            stop_loss=49250.0,     # 1.5% 止损
            confidence=0.8,
            expected_profit=8.0,
            risk_amount=20.0,
            hold_time=30
        )
        
        # 执行交易
        result = self.engine.execute_trade(opportunity)
        
        # 验证结果
        if result:  # 可能因为交易限制而不执行
            self.assertIsInstance(result, TradeResult)
            self.assertEqual(result.symbol, 'BTC/USDT')
            self.assertEqual(result.strategy, 'scalping')
            self.assertIn(result.result, ['win', 'loss'])
            
            # 验证统计更新
            self.assertEqual(self.engine.total_trades, 1)
            if result.result == 'win':
                self.assertEqual(self.engine.winning_trades, 1)
            else:
                self.assertEqual(self.engine.winning_trades, 0)

    def test_can_trade(self):
        """测试交易限制检查"""
        # 默认应该可以交易
        self.assertTrue(self.engine._can_trade())
        
        # 测试日交易次数限制
        self.engine.daily_trades = self.engine.max_daily_trades
        self.assertFalse(self.engine._can_trade())
        self.engine.daily_trades = 0
        
        # 测试连续亏损限制
        self.engine.consecutive_losses = 3
        self.assertFalse(self.engine._can_trade())
        self.engine.consecutive_losses = 0
        
        # 测试日亏损限制
        # 创建一个大亏损的交易记录
        loss_trade = TradeResult(
            symbol='BTC/USDT',
            strategy='scalping',
            entry_price=50000.0,
            exit_price=49000.0,
            profit_loss=-100.0,  # 大于日亏损限制
            profit_pct=-0.02,
            hold_time=30,
            result='loss',
            timestamp=datetime.now()
        )
        self.engine.trade_history.append(loss_trade)
        self.assertFalse(self.engine._can_trade())

    def test_update_statistics(self):
        """测试更新统计"""
        # 测试胜利交易
        self.engine._update_statistics(10.0, 'win')
        self.assertEqual(self.engine.total_trades, 1)
        self.assertEqual(self.engine.daily_trades, 1)
        self.assertEqual(self.engine.total_profit, 10.0)
        self.assertEqual(self.engine.winning_trades, 1)
        self.assertEqual(self.engine.consecutive_losses, 0)
        
        # 测试亏损交易
        self.engine._update_statistics(-5.0, 'loss')
        self.assertEqual(self.engine.total_trades, 2)
        self.assertEqual(self.engine.daily_trades, 2)
        self.assertEqual(self.engine.total_profit, 5.0)
        self.assertEqual(self.engine.winning_trades, 1)
        self.assertEqual(self.engine.consecutive_losses, 1)
        
        # 测试连续亏损
        self.engine._update_statistics(-3.0, 'loss')
        self.assertEqual(self.engine.consecutive_losses, 2)

    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        # 设置一些测试数据
        self.engine.current_capital = 1200.0
        self.engine.total_trades = 20
        self.engine.winning_trades = 15
        self.engine.total_profit = 200.0
        self.engine.daily_trades = 5
        self.engine.consecutive_losses = 0
        
        # 添加一个交易历史记录
        trade = TradeResult(
            symbol='BTC/USDT',
            strategy='scalping',
            entry_price=50000.0,
            exit_price=50500.0,
            profit_loss=50.0,
            profit_pct=0.01,
            hold_time=30,
            result='win',
            timestamp=datetime.now() - timedelta(days=5)
        )
        self.engine.trade_history.append(trade)
        
        # 获取性能指标
        metrics = self.engine.get_performance_metrics()
        
        # 验证指标
        self.assertEqual(metrics['current_capital'], 1200.0)
        self.assertEqual(metrics['total_return'], 0.2)  # (1200-1000)/1000
        self.assertEqual(metrics['total_profit'], 200.0)
        self.assertEqual(metrics['win_rate'], 0.75)  # 15/20
        self.assertEqual(metrics['total_trades'], 20)
        self.assertEqual(metrics['daily_trades'], 5)
        self.assertEqual(metrics['consecutive_losses'], 0)
        self.assertGreaterEqual(metrics['daily_return'], 0.0)

    def test_get_strategy_performance(self):
        """测试获取策略表现"""
        # 添加一些交易历史记录
        strategies = ['scalping', 'trend', 'range', 'breakout']
        
        for strategy in strategies:
            # 添加胜利交易
            win_trade = TradeResult(
                symbol='BTC/USDT',
                strategy=strategy,
                entry_price=50000.0,
                exit_price=50500.0,
                profit_loss=50.0,
                profit_pct=0.01,
                hold_time=30,
                result='win',
                timestamp=datetime.now()
            )
            self.engine.trade_history.append(win_trade)
            
            # 添加亏损交易
            loss_trade = TradeResult(
                symbol='ETH/USDT',
                strategy=strategy,
                entry_price=3000.0,
                exit_price=2950.0,
                profit_loss=-20.0,
                profit_pct=-0.0167,
                hold_time=45,
                result='loss',
                timestamp=datetime.now()
            )
            self.engine.trade_history.append(loss_trade)
        
        # 获取策略表现
        performance = self.engine.get_strategy_performance()
        
        # 验证每个策略的表现
        for strategy in strategies:
            self.assertIn(strategy, performance)
            strategy_perf = performance[strategy]
            self.assertEqual(strategy_perf['trades'], 2)
            self.assertEqual(strategy_perf['win_rate'], 0.5)
            self.assertEqual(strategy_perf['total_profit'], 30.0)  # 50 - 20
            self.assertEqual(strategy_perf['avg_profit'], 15.0)  # 30 / 2

    def test_get_daily_summary(self):
        """测试获取日度总结"""
        # 添加今日交易记录
        today_trade = TradeResult(
            symbol='BTC/USDT',
            strategy='scalping',
            entry_price=50000.0,
            exit_price=50500.0,
            profit_loss=50.0,
            profit_pct=0.01,
            hold_time=30,
            result='win',
            timestamp=datetime.now()
        )
        self.engine.trade_history.append(today_trade)
        
        # 获取日度总结
        summary = self.engine.get_daily_summary()
        
        # 验证总结内容
        self.assertIn("今日交易总结", summary)
        self.assertIn("今日盈亏", summary)
        self.assertIn("+50.00", summary)  # 格式化的盈亏
        self.assertIn("交易次数", summary)
        self.assertIn("1", summary)  # 交易次数
        self.assertIn("胜率", summary)
        self.assertIn("100.0%", summary)  # 胜率
        
        # 测试无交易的情况
        self.engine.trade_history.clear()
        summary = self.engine.get_daily_summary()
        self.assertEqual(summary, "今日暂无交易")

if __name__ == '__main__':
    unittest.main()
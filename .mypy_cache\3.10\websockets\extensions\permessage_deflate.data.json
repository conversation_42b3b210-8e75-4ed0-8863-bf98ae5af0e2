{".class": "MypyFile", "_fullname": "websockets.extensions.permessage_deflate", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "ClientExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ClientExtensionFactory", "kind": "Gdef", "module_public": false}, "ClientPerMessageDeflateFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.extensions.base.ClientExtensionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", "name": "ClientPerMessageDeflateFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.permessage_deflate", "mro": ["websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", "websockets.extensions.base.ClientExtensionFactory", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits", "compress_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits", "compress_settings"], "arg_types": ["websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientPerMessageDeflateFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.client_max_window_bits", "name": "client_max_window_bits", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "client_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.client_no_context_takeover", "name": "client_no_context_takeover", "type": "builtins.bool"}}, "compress_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.compress_settings", "name": "compress_settings", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.get_request_params", "name": "get_request_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_request_params of ClientPerMessageDeflateFactory", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.name", "name": "name", "type": "websockets.typing.ExtensionName"}}, "process_response_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.process_response_params", "name": "process_response_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "arg_types": ["websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_response_params of ClientPerMessageDeflateFactory", "ret_type": "websockets.extensions.permessage_deflate.PerMessageDeflate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.server_max_window_bits", "name": "server_max_window_bits", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.server_no_context_takeover", "name": "server_no_context_takeover", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.permessage_deflate.ClientPerMessageDeflateFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.DuplicateParameter", "kind": "Gdef", "module_public": false}, "Extension": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.Extension", "kind": "Gdef", "module_public": false}, "ExtensionName": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionName", "kind": "Gdef", "module_public": false}, "ExtensionParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionParameter", "kind": "Gdef", "module_public": false}, "InvalidParameterName": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidParameterName", "kind": "Gdef", "module_public": false}, "InvalidParameterValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidParameterValue", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "NegotiationError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.NegotiationError", "kind": "Gdef", "module_public": false}, "PayloadTooBig": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.PayloadTooBig", "kind": "Gdef", "module_public": false}, "PerMessageDeflate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.extensions.base.Extension"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate", "name": "PerMessageDeflate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.permessage_deflate", "mro": ["websockets.extensions.permessage_deflate.PerMessageDeflate", "websockets.extensions.base.Extension", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "remote_no_context_takeover", "local_no_context_takeover", "remote_max_window_bits", "local_max_window_bits", "compress_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "remote_no_context_takeover", "local_no_context_takeover", "remote_max_window_bits", "local_max_window_bits", "compress_settings"], "arg_types": ["websockets.extensions.permessage_deflate.PerMessageDeflate", "builtins.bool", "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PerMessageDeflate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.extensions.permessage_deflate.PerMessageDeflate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of PerMessageDeflate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compress_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.compress_settings", "name": "compress_settings", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "frame", "max_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "frame", "max_size"], "arg_types": ["websockets.extensions.permessage_deflate.PerMessageDeflate", "websockets.frames.Frame", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of PerMessageDeflate", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode_cont_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.decode_cont_data", "name": "decode_cont_data", "type": "builtins.bool"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.decoder", "name": "decoder", "type": "zlib._Decompress"}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["websockets.extensions.permessage_deflate.PerMessageDeflate", "websockets.frames.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of PerMessageDeflate", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.encoder", "name": "encoder", "type": "zlib._Compress"}}, "local_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.local_max_window_bits", "name": "local_max_window_bits", "type": "builtins.int"}}, "local_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.local_no_context_takeover", "name": "local_no_context_takeover", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.name", "name": "name", "type": "websockets.typing.ExtensionName"}}, "remote_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.remote_max_window_bits", "name": "remote_max_window_bits", "type": "builtins.int"}}, "remote_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.remote_no_context_takeover", "name": "remote_no_context_takeover", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.permessage_deflate.PerMessageDeflate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.permessage_deflate.PerMessageDeflate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProtocolError", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "ServerExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ServerExtensionFactory", "kind": "Gdef", "module_public": false}, "ServerPerMessageDeflateFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.extensions.base.ServerExtensionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "name": "ServerPerMessageDeflateFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.permessage_deflate", "mro": ["websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "websockets.extensions.base.ServerExtensionFactory", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits", "compress_settings", "require_client_max_window_bits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits", "compress_settings", "require_client_max_window_bits"], "arg_types": ["websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ServerPerMessageDeflateFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.client_max_window_bits", "name": "client_max_window_bits", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "client_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.client_no_context_takeover", "name": "client_no_context_takeover", "type": "builtins.bool"}}, "compress_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.compress_settings", "name": "compress_settings", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.name", "name": "name", "type": "websockets.typing.ExtensionName"}}, "process_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.process_request_params", "name": "process_request_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "arg_types": ["websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_request_params of ServerPerMessageDeflateFactory", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, "websockets.extensions.permessage_deflate.PerMessageDeflate"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "require_client_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.require_client_max_window_bits", "name": "require_client_max_window_bits", "type": "builtins.bool"}}, "server_max_window_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.server_max_window_bits", "name": "server_max_window_bits", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_no_context_takeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.server_no_context_takeover", "name": "server_no_context_takeover", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EMPTY_UNCOMPRESSED_BLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate._EMPTY_UNCOMPRESSED_BLOCK", "name": "_EMPTY_UNCOMPRESSED_BLOCK", "type": "builtins.bytes"}}, "_MAX_WINDOW_BITS_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate._MAX_WINDOW_BITS_VALUES", "name": "_MAX_WINDOW_BITS_VALUES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.permessage_deflate.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.permessage_deflate.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_build_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate._build_parameters", "name": "_build_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["server_no_context_takeover", "client_no_context_takeover", "server_max_window_bits", "client_max_window_bits"], "arg_types": ["builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_parameters", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["params", "is_server"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate._extract_parameters", "name": "_extract_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["params", "is_server"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_parameters", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "enable_client_permessage_deflate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.enable_client_permessage_deflate", "name": "enable_client_permessage_deflate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["extensions"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ClientExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_client_permessage_deflate", "ret_type": {".class": "Instance", "args": ["websockets.extensions.base.ClientExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_server_permessage_deflate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.extensions.permessage_deflate.enable_server_permessage_deflate", "name": "enable_server_permessage_deflate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["extensions"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ServerExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_server_permessage_deflate", "ret_type": {".class": "Instance", "args": ["websockets.extensions.base.ServerExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frames": {".class": "SymbolTableNode", "cross_ref": "websockets.frames", "kind": "Gdef", "module_public": false}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py"}
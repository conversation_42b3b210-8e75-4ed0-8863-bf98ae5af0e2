{"trading": {"initial_capital": 10000.0, "daily_loss_limit": 300.0, "max_position_size": 0.1, "stop_loss_pct": 0.03, "take_profit_pct": 0.06, "max_drawdown": 0.15, "min_trade_amount": 10.0, "max_trades_per_day": 50, "trading_pairs": ["BTC/USDT", "ETH/USDT", "BNB/USDT"]}, "exchange": {"name": "gate", "api_key": "", "api_secret": "", "passphrase": "", "sandbox": true, "testnet": true, "timeout": 30000, "rateLimit": 1000}, "database": {"type": "sqlite", "host": "localhost", "port": 5432, "database": "trading.db", "username": "", "password": ""}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/trading.log", "max_file_size": 10485760, "backup_count": 5}, "monitoring": {"enable_alerts": true, "alert_email": "", "alert_webhook": "", "check_interval": 60, "performance_threshold": 0.8}, "version": "1.0.0", "environment": "development", "debug": true}
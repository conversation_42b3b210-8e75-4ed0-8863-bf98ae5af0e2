#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专业交易系统启动器
Professional Trading System Launcher

快速启动专业交易终端
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🏦 ULTIMATE SPOT TRADING TERMINAL - Professional Edition                  ║
║                                                                              ║
║    💎 Real-Time Trading • Risk Management • Professional Tools              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

⚠️  RISK WARNING: Trading involves substantial risk of loss. 
    Past performance does not guarantee future results.

🚀 Starting Professional Trading Terminal...
"""
    print(banner)


def check_dependencies():
    """检查依赖项"""
    print("🔍 Checking system dependencies...")
    
    required_modules = [
        'tkinter',
        'threading', 
        'datetime',
        'psutil'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module} (missing)")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing dependencies: {', '.join(missing_modules)}")
        print("Please install missing dependencies and try again.")
        return False
    
    print("✅ All dependencies satisfied")
    return True


def check_system_requirements():
    """检查系统要求"""
    print("\n💻 Checking system requirements...")
    
    try:
        import psutil
        
        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"  💾 Total Memory: {memory_gb:.1f} GB")
        
        if memory_gb < 2:
            print("  ⚠️  Warning: Low memory (< 2GB)")
        else:
            print("  ✅ Memory: Sufficient")
        
        # 检查CPU
        cpu_count = psutil.cpu_count()
        print(f"  🖥️  CPU Cores: {cpu_count}")
        
        if cpu_count < 2:
            print("  ⚠️  Warning: Low CPU cores (< 2)")
        else:
            print("  ✅ CPU: Sufficient")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        disk_free_gb = disk.free / (1024**3)
        print(f"  💽 Free Disk Space: {disk_free_gb:.1f} GB")
        
        if disk_free_gb < 1:
            print("  ⚠️  Warning: Low disk space (< 1GB)")
        else:
            print("  ✅ Disk Space: Sufficient")
            
        return True
        
    except Exception as e:
        print(f"  ❌ System check failed: {e}")
        return False


def load_professional_gui():
    """加载专业交易GUI"""
    print("\n🚀 Loading Professional Trading GUI...")
    
    try:
        # 导入专业交易GUI
        from core.professional_trading_gui import ProfessionalTradingGUI
        print("  ✅ Professional GUI module loaded")
        
        # 创建GUI实例
        print("  🔧 Initializing GUI components...")
        gui = ProfessionalTradingGUI()
        print("  ✅ GUI components initialized")
        
        # 显示启动信息
        print("  📊 Loading market data connections...")
        print("  🔗 Preparing API connections...")
        print("  ⚙️  Configuring risk management...")
        print("  🎨 Applying professional theme...")
        
        print("\n✅ Professional Trading Terminal ready!")
        print("🎯 Starting GUI interface...")
        
        # 运行GUI
        gui.run()
        
    except ImportError as e:
        print(f"  ❌ Failed to import Professional GUI: {e}")
        print("  💡 Trying to load original GUI as fallback...")
        load_fallback_gui()
        
    except Exception as e:
        print(f"  ❌ Failed to start Professional GUI: {e}")
        print("  💡 Trying to load original GUI as fallback...")
        load_fallback_gui()


def load_fallback_gui():
    """加载备用GUI"""
    print("\n🔄 Loading fallback GUI...")
    
    try:
        from core.ultimate_spot_trading_gui import UltimateSpotTradingGUI
        print("  ✅ Fallback GUI module loaded")
        
        gui = UltimateSpotTradingGUI()
        print("  ✅ Fallback GUI initialized")
        print("  🚀 Starting fallback interface...")
        
        gui.run()
        
    except Exception as e:
        print(f"  ❌ Fallback GUI also failed: {e}")
        print("  💡 Please check your installation and try again.")


def show_usage_tips():
    """显示使用提示"""
    tips = """
💡 PROFESSIONAL TRADING TERMINAL USAGE TIPS:

🔗 Getting Started:
   1. Click "CONNECT TO EXCHANGE" to establish connection
   2. Choose trading mode: PAPER (simulation) or LIVE (real money)
   3. Configure risk management settings
   4. Start trading with "START TRADING" button

⚡ Quick Trading:
   • Use the Quick Trading panel for fast order placement
   • Select symbol, order type, quantity, and price
   • Click BUY (green) or SELL (red) to place orders

📊 Monitoring:
   • Market Data tab: Real-time market information
   • Positions tab: Monitor your open positions
   • Orders tab: Track pending orders
   • History tab: Review trading history
   • Charts tab: Technical analysis tools
   • Monitor tab: System performance metrics

⚠️  Risk Management:
   • Always use stop-loss orders
   • Monitor position sizes and exposure
   • Use the EMERGENCY STOP button if needed
   • Review risk metrics regularly

🎯 Professional Features:
   • Multiple order types (Market, Limit, Stop, Stop-Limit)
   • Real-time P&L tracking
   • Professional risk management tools
   • Advanced charting and analysis
   • Comprehensive trading reports

📞 Support:
   • Check system logs for troubleshooting
   • Review documentation for detailed instructions
   • Contact support for technical assistance

🚀 Happy Trading!
"""
    print(tips)


def main():
    """主函数"""
    # 打印启动横幅
    print_banner()
    
    # 显示启动时间
    start_time = datetime.now()
    print(f"⏰ Launch Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ Dependency check failed. Exiting...")
        input("Press Enter to exit...")
        return
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n⚠️  System requirements check completed with warnings.")
        print("The system may still work but performance might be affected.")
    
    # 显示使用提示
    print("\n" + "="*80)
    show_usage_tips()
    print("="*80)
    
    # 询问是否继续
    print("\n🚀 Ready to launch Professional Trading Terminal")
    user_input = input("Press Enter to continue or 'q' to quit: ").strip().lower()
    
    if user_input == 'q':
        print("👋 Goodbye!")
        return
    
    # 加载专业交易GUI
    try:
        load_professional_gui()
    except KeyboardInterrupt:
        print("\n\n👋 Trading session ended by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check the logs and try again.")
    finally:
        end_time = datetime.now()
        session_duration = end_time - start_time
        print(f"\n⏰ Session Duration: {session_duration}")
        print("💼 Thank you for using Professional Trading Terminal!")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实用交易策略
Practical Trading Strategies

可直接用于加密货币交易的具体策略
"""

import logging
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import talib

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CryptoBreakoutStrategy:
    """
    加密货币突破策略

    基于价格突破和成交量确认的趋势跟踪策略
    """

    def __init__(self):
        self.name = "CryptoBreakout"
        self.timeframe = "15m"
        self.min_profit = 0.02  # 最小2%利润
        self.stop_loss = 0.05  # 5%止损
        self.take_profit = 0.12  # 12%止盈

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # 移动平均线
        df["ma_10"] = talib.SMA(df["close"], timeperiod=10)
        df["ma_30"] = talib.SMA(df["close"], timeperiod=30)

        # RSI
        df["rsi"] = talib.RSI(df["close"], timeperiod=14)

        # 成交量移动平均
        df["volume_ma"] = talib.SMA(df["volume"], timeperiod=20)

        # 布林带
        df["bb_upper"], df["bb_middle"], df["bb_lower"] = talib.BBANDS(
            df["close"], timeperiod=20, nbdevup=2, nbdevdn=2
        )

        # ATR (平均真实波幅)
        df["atr"] = talib.ATR(
            df["high"], df["low"], df["close"], timeperiod=14
        )

        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        df = self.calculate_indicators(df)

        # 买入条件
        buy_conditions = (
            (df["close"] > df["ma_30"] * 1.02)  # 价格突破30日均线2%
            & (df["ma_10"] > df["ma_30"])  # 短期均线在长期均线之上
            & (df["rsi"] < 70)  # RSI不超买
            & (df["volume"] > df["volume_ma"] * 1.5)  # 成交量放大1.5倍
            & (df["close"] > df["bb_middle"])  # 价格在布林带中轨之上
        )

        # 卖出条件
        sell_conditions = (
            (df["close"] < df["ma_10"] * 0.98)  # 跌破10日均线2%
            | (df["rsi"] > 80)  # RSI超买
            | (df["close"] < df["bb_lower"])  # 跌破布林带下轨
        )

        df["signal"] = 0
        df.loc[buy_conditions, "signal"] = 1  # 买入信号
        df.loc[sell_conditions, "signal"] = -1  # 卖出信号

        return df

    def calculate_position_size(
        self, capital: float, price: float, atr: float
    ) -> float:
        """计算仓位大小"""
        # 基于ATR的仓位管理
        risk_per_trade = capital * 0.02  # 每笔交易风险2%
        stop_distance = atr * 2  # 止损距离为2倍ATR
        position_size = risk_per_trade / stop_distance

        # 限制最大仓位为资金的15%
        max_position_value = capital * 0.15
        max_shares = max_position_value / price

        return min(position_size, max_shares)


class CryptoGridStrategy:
    """
    加密货币网格策略

    适合震荡行情的网格交易策略
    """

    def __init__(self):
        self.name = "CryptoGrid"
        self.grid_spacing = 0.015  # 网格间距1.5%
        self.grid_levels = 8  # 网格层数
        self.base_order_size = 100  # 基础订单金额(USDT)

    def setup_grid(self, current_price: float, capital: float) -> Dict:
        """设置网格"""
        grid_config = {
            "center_price": current_price,
            "grid_spacing": self.grid_spacing,
            "levels": self.grid_levels,
            "base_size": min(self.base_order_size, capital / self.grid_levels),
        }

        # 计算买入和卖出价格
        buy_levels = []
        sell_levels = []

        for i in range(1, self.grid_levels // 2 + 1):
            buy_price = current_price * (1 - self.grid_spacing * i)
            sell_price = current_price * (1 + self.grid_spacing * i)

            buy_levels.append(
                {
                    "price": buy_price,
                    "size": grid_config["base_size"],
                    "level": -i,
                }
            )

            sell_levels.append(
                {
                    "price": sell_price,
                    "size": grid_config["base_size"],
                    "level": i,
                }
            )

        grid_config["buy_levels"] = buy_levels
        grid_config["sell_levels"] = sell_levels

        return grid_config

    def check_grid_signals(
        self, current_price: float, grid_config: Dict
    ) -> List[Dict]:
        """检查网格信号"""
        signals = []

        # 检查买入信号
        for level in grid_config["buy_levels"]:
            if current_price <= level["price"]:
                signals.append(
                    {
                        "action": "buy",
                        "price": level["price"],
                        "size": level["size"],
                        "level": level["level"],
                    }
                )

        # 检查卖出信号
        for level in grid_config["sell_levels"]:
            if current_price >= level["price"]:
                signals.append(
                    {
                        "action": "sell",
                        "price": level["price"],
                        "size": level["size"],
                        "level": level["level"],
                    }
                )

        return signals


class CryptoMomentumStrategy:
    """
    加密货币动量策略

    基于价格动量和技术指标的趋势策略
    """

    def __init__(self):
        self.name = "CryptoMomentum"
        self.timeframe = "1h"
        self.momentum_period = 12
        self.stop_loss = 0.06
        self.take_profit = 0.15

    def calculate_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算动量指标"""
        # 价格动量
        df["momentum"] = df["close"].pct_change(self.momentum_period)

        # MACD
        df["macd"], df["macd_signal"], df["macd_hist"] = talib.MACD(
            df["close"], fastperiod=12, slowperiod=26, signalperiod=9
        )

        # RSI
        df["rsi"] = talib.RSI(df["close"], timeperiod=14)

        # 威廉指标
        df["williams_r"] = talib.WILLR(
            df["high"], df["low"], df["close"], timeperiod=14
        )

        # 成交量加权平均价格(VWAP)
        df["vwap"] = (df["close"] * df["volume"]).cumsum() / df[
            "volume"
        ].cumsum()

        return df

    def generate_momentum_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成动量信号"""
        df = self.calculate_momentum_indicators(df)

        # 强势买入条件
        strong_buy = (
            (df["momentum"] > 0.03)  # 12小时动量>3%
            & (df["rsi"] > 50)
            & (df["rsi"] < 70)  # RSI在50-70之间
            & (df["macd"] > df["macd_signal"])  # MACD金叉
            & (df["close"] > df["vwap"])  # 价格在VWAP之上
        )

        # 弱势买入条件
        weak_buy = (
            (df["momentum"] > 0.015)  # 12小时动量>1.5%
            & (df["rsi"] > 45)  # RSI>45
            & (df["macd"] > df["macd_signal"])  # MACD金叉
            & (df["williams_r"] > -80)  # 威廉指标不超卖
        )

        # 卖出条件
        sell_conditions = (
            (df["momentum"] < -0.02)  # 动量转负
            | (df["rsi"] > 75)  # RSI超买
            | (df["macd"] < df["macd_signal"])  # MACD死叉
            | (df["close"] < df["vwap"] * 0.98)  # 跌破VWAP 2%
        )

        df["signal"] = 0
        df.loc[strong_buy, "signal"] = 2  # 强买信号
        df.loc[weak_buy, "signal"] = 1  # 弱买信号
        df.loc[sell_conditions, "signal"] = -1  # 卖出信号

        return df


class StrategyPortfolio:
    """
    策略组合管理器

    管理多个策略的组合交易
    """

    def __init__(self, total_capital: float):
        self.total_capital = total_capital
        self.strategies = {}
        self.allocations = {}
        self.positions = {}

    def add_strategy(self, strategy, allocation: float, pairs: List[str]):
        """添加策略到组合"""
        self.strategies[strategy.name] = {
            "strategy": strategy,
            "allocation": allocation,
            "pairs": pairs,
            "capital": self.total_capital * allocation,
        }

    def create_balanced_portfolio(self) -> Dict:
        """创建平衡投资组合"""
        # 初始化策略
        breakout = CryptoBreakoutStrategy()
        grid = CryptoGridStrategy()
        momentum = CryptoMomentumStrategy()

        # 策略配置
        portfolio_config = {
            "CryptoBreakout": {
                "strategy": breakout,
                "allocation": 0.40,  # 40%资金
                "pairs": ["BTC/USDT", "ETH/USDT"],
                "max_positions": 2,
                "risk_level": "medium",
            },
            "CryptoGrid": {
                "strategy": grid,
                "allocation": 0.35,  # 35%资金
                "pairs": ["BTC/USDT", "ETH/USDT"],
                "max_positions": 2,
                "risk_level": "low",
            },
            "CryptoMomentum": {
                "strategy": momentum,
                "allocation": 0.25,  # 25%资金
                "pairs": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
                "max_positions": 2,
                "risk_level": "high",
            },
        }

        return portfolio_config

    def calculate_portfolio_signals(
        self, market_data: Dict[str, pd.DataFrame]
    ) -> Dict:
        """计算组合信号"""
        portfolio_signals = {}

        for strategy_name, config in self.strategies.items():
            strategy = config["strategy"]
            signals = {}

            for pair in config["pairs"]:
                if pair in market_data:
                    df = market_data[pair].copy()

                    if strategy_name == "CryptoBreakout":
                        df_with_signals = strategy.generate_signals(df)
                        signals[pair] = df_with_signals["signal"].iloc[-1]

                    elif strategy_name == "CryptoGrid":
                        current_price = df["close"].iloc[-1]
                        grid_config = strategy.setup_grid(
                            current_price, config["capital"]
                        )
                        grid_signals = strategy.check_grid_signals(
                            current_price, grid_config
                        )
                        signals[pair] = grid_signals

                    elif strategy_name == "CryptoMomentum":
                        df_with_signals = strategy.generate_momentum_signals(
                            df
                        )
                        signals[pair] = df_with_signals["signal"].iloc[-1]

            portfolio_signals[strategy_name] = signals

        return portfolio_signals

    def get_execution_plan(self, signals: Dict) -> List[Dict]:
        """生成执行计划"""
        execution_plan = []

        for strategy_name, strategy_signals in signals.items():
            config = self.strategies[strategy_name]

            for pair, signal in strategy_signals.items():
                if isinstance(signal, list):  # 网格策略
                    for grid_signal in signal:
                        execution_plan.append(
                            {
                                "strategy": strategy_name,
                                "pair": pair,
                                "action": grid_signal["action"],
                                "price": grid_signal["price"],
                                "size": grid_signal["size"],
                                "priority": "medium",
                            }
                        )

                elif signal != 0:  # 其他策略
                    action = "buy" if signal > 0 else "sell"
                    priority = "high" if abs(signal) == 2 else "medium"

                    execution_plan.append(
                        {
                            "strategy": strategy_name,
                            "pair": pair,
                            "action": action,
                            "signal_strength": abs(signal),
                            "priority": priority,
                        }
                    )

        # 按优先级排序
        execution_plan.sort(
            key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["priority"]]
        )

        return execution_plan


def create_sample_portfolio():
    """创建示例投资组合"""
    # 初始化组合
    portfolio = StrategyPortfolio(total_capital=10000)  # 10000 USDT

    # 添加策略
    breakout = CryptoBreakoutStrategy()
    portfolio.add_strategy(breakout, 0.40, ["BTC/USDT", "ETH/USDT"])

    grid = CryptoGridStrategy()
    portfolio.add_strategy(grid, 0.35, ["BTC/USDT", "ETH/USDT"])

    momentum = CryptoMomentumStrategy()
    portfolio.add_strategy(
        momentum, 0.25, ["BTC/USDT", "ETH/USDT", "BNB/USDT"]
    )

    return portfolio


if __name__ == "__main__":
    # 创建示例组合
    portfolio = create_sample_portfolio()

    print("=== 加密货币交易策略组合 ===")
    print(f"总资金: {portfolio.total_capital:,} USDT")
    print("\n策略配置:")

    for name, config in portfolio.strategies.items():
        print(f"\n{name}:")
        print(f"  - 资金分配: {config['allocation']:.0%}")
        print(f"  - 分配资金: {config['capital']:,.0f} USDT")
        print(f"  - 交易对: {', '.join(config['pairs'])}")

    print("\n=== 策略详情 ===")
    print("\n1. CryptoBreakout (突破策略):")
    print("   - 时间周期: 15分钟")
    print("   - 止损: 5%")
    print("   - 止盈: 12%")
    print("   - 适用: 趋势行情")

    print("\n2. CryptoGrid (网格策略):")
    print("   - 网格间距: 1.5%")
    print("   - 网格层数: 8层")
    print("   - 适用: 震荡行情")

    print("\n3. CryptoMomentum (动量策略):")
    print("   - 时间周期: 1小时")
    print("   - 止损: 6%")
    print("   - 止盈: 15%")
    print("   - 适用: 强势行情")

    print("\n=== 使用说明 ===")
    print("1. 将市场数据传入 calculate_portfolio_signals()")
    print("2. 获取交易信号")
    print("3. 通过 get_execution_plan() 获取执行计划")
    print("4. 按计划执行交易")

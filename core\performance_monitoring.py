#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级策略性能监控系统
Performance Monitoring System for Institutional-Grade Quantitative Trading

实时监控策略表现，及时发现异常和衰减信号
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
import warnings
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
from collections import deque
import threading
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceAlert:
    """性能警报"""
    strategy_name: str
    alert_type: str
    severity: str  # 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    message: str
    timestamp: datetime
    metric_value: float
    threshold: float
    recommendation: str

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    strategy_name: str
    daily_return: float
    cumulative_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    win_rate: float
    profit_factor: float
    var_95: float
    tracking_error: float
    information_ratio: float
    alpha_decay_indicator: float

@dataclass
class MonitoringConfig:
    """监控配置"""
    # 性能阈值
    min_sharpe_ratio: float = 1.0
    max_drawdown_limit: float = 0.15
    max_volatility: float = 0.25
    min_win_rate: float = 0.4
    max_var_95: float = 0.05
    
    # 衰减检测参数
    lookback_window: int = 60  # 回看窗口（天）
    decay_threshold: float = 0.2  # 衰减阈值
    
    # 监控频率
    monitoring_interval: int = 300  # 监控间隔（秒）
    
    # 警报设置
    enable_email_alerts: bool = False
    enable_sms_alerts: bool = False
    alert_recipients: List[str] = field(default_factory=list)

class InstitutionalPerformanceMonitor:
    """
    机构级性能监控器
    
    提供实时性能监控、异常检测、衰减预警等功能
    """
    
    def __init__(self, config: MonitoringConfig = None):
        """
        初始化性能监控器
        
        Args:
            config: 监控配置
        """
        self.config = config or MonitoringConfig()
        self.strategies: Dict[str, Dict] = {}
        self.performance_history: Dict[str, deque] = {}
        self.alerts: List[PerformanceAlert] = []
        self.is_monitoring = False
        self.monitoring_thread = None
        
        # 异常检测器
        self.anomaly_detectors = {
            'sharpe_ratio': self._detect_sharpe_anomaly,
            'drawdown': self._detect_drawdown_anomaly,
            'volatility': self._detect_volatility_anomaly,
            'alpha_decay': self._detect_alpha_decay,
            'regime_change': self._detect_regime_change
        }
    
    def register_strategy(self, strategy_name: str, initial_data: pd.Series = None):
        """
        注册策略进行监控
        
        Args:
            strategy_name: 策略名称
            initial_data: 初始历史数据
        """
        logger.info(f"注册策略进行监控: {strategy_name}")
        
        self.strategies[strategy_name] = {
            'registered_time': datetime.now(),
            'last_update': None,
            'total_observations': 0,
            'status': 'ACTIVE'
        }
        
        # 初始化性能历史
        self.performance_history[strategy_name] = deque(maxlen=252 * 2)  # 保留2年数据
        
        # 如果有初始数据，进行预处理
        if initial_data is not None:
            self._process_historical_data(strategy_name, initial_data)
    
    def update_performance(self, strategy_name: str, returns: pd.Series):
        """
        更新策略性能数据
        
        Args:
            strategy_name: 策略名称
            returns: 收益率序列
        """
        if strategy_name not in self.strategies:
            logger.warning(f"策略 {strategy_name} 未注册，自动注册")
            self.register_strategy(strategy_name)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(strategy_name, returns)
        
        # 存储性能历史
        self.performance_history[strategy_name].append(metrics)
        
        # 更新策略信息
        self.strategies[strategy_name]['last_update'] = datetime.now()
        self.strategies[strategy_name]['total_observations'] += len(returns)
        
        # 执行异常检测
        self._run_anomaly_detection(strategy_name, metrics)
        
        logger.debug(f"更新策略 {strategy_name} 性能数据，当前夏普比率: {metrics.sharpe_ratio:.3f}")
    
    def _calculate_performance_metrics(self, strategy_name: str, returns: pd.Series) -> PerformanceMetrics:
        """计算性能指标"""
        # 获取历史数据
        history = list(self.performance_history[strategy_name])
        all_returns = returns.copy()
        
        # 如果有历史数据，合并计算
        if history:
            historical_returns = pd.Series([h.daily_return for h in history[-60:]])  # 最近60天
            all_returns = pd.concat([historical_returns, returns])
        
        # 基础指标
        daily_return = returns.iloc[-1] if len(returns) > 0 else 0
        cumulative_return = (1 + all_returns).prod() - 1
        
        # 风险调整指标
        if len(all_returns) > 1:
            sharpe_ratio = all_returns.mean() / all_returns.std() * np.sqrt(252) if all_returns.std() != 0 else 0
            volatility = all_returns.std() * np.sqrt(252)
            
            # 最大回撤
            cumulative = (1 + all_returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # 胜率和盈亏比
            positive_returns = all_returns[all_returns > 0]
            negative_returns = all_returns[all_returns < 0]
            win_rate = len(positive_returns) / len(all_returns) if len(all_returns) > 0 else 0
            profit_factor = abs(positive_returns.sum() / negative_returns.sum()) if len(negative_returns) > 0 and negative_returns.sum() != 0 else 0
            
            # VaR
            var_95 = np.percentile(all_returns, 5)
            
            # Alpha衰减指标
            alpha_decay_indicator = self._calculate_alpha_decay_indicator(all_returns)
            
        else:
            sharpe_ratio = volatility = max_drawdown = win_rate = profit_factor = var_95 = alpha_decay_indicator = 0
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            strategy_name=strategy_name,
            daily_return=daily_return,
            cumulative_return=cumulative_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            volatility=volatility,
            win_rate=win_rate,
            profit_factor=profit_factor,
            var_95=var_95,
            tracking_error=volatility,  # 简化处理
            information_ratio=sharpe_ratio,  # 简化处理
            alpha_decay_indicator=alpha_decay_indicator
        )
    
    def _calculate_alpha_decay_indicator(self, returns: pd.Series) -> float:
        """计算Alpha衰减指标"""
        if len(returns) < 40:
            return 0
        
        # 将收益序列分为前后两部分
        mid_point = len(returns) // 2
        first_half = returns.iloc[:mid_point]
        second_half = returns.iloc[mid_point:]
        
        # 计算两部分的夏普比率
        first_sharpe = first_half.mean() / first_half.std() * np.sqrt(252) if first_half.std() != 0 else 0
        second_sharpe = second_half.mean() / second_half.std() * np.sqrt(252) if second_half.std() != 0 else 0
        
        # 衰减指标 = (后期夏普 - 前期夏普) / |前期夏普|
        if abs(first_sharpe) > 0.1:  # 避免除零
            decay_indicator = (second_sharpe - first_sharpe) / abs(first_sharpe)
        else:
            decay_indicator = 0
        
        return decay_indicator
    
    def _run_anomaly_detection(self, strategy_name: str, metrics: PerformanceMetrics):
        """运行异常检测"""
        for detector_name, detector_func in self.anomaly_detectors.items():
            try:
                alert = detector_func(strategy_name, metrics)
                if alert:
                    self.alerts.append(alert)
                    self._handle_alert(alert)
            except Exception as e:
                logger.error(f"异常检测器 {detector_name} 执行失败: {e}")
    
    def _detect_sharpe_anomaly(self, strategy_name: str, metrics: PerformanceMetrics) -> Optional[PerformanceAlert]:
        """检测夏普比率异常"""
        if metrics.sharpe_ratio < self.config.min_sharpe_ratio:
            return PerformanceAlert(
                strategy_name=strategy_name,
                alert_type="SHARPE_RATIO_LOW",
                severity="MEDIUM" if metrics.sharpe_ratio > 0.5 else "HIGH",
                message=f"夏普比率过低: {metrics.sharpe_ratio:.3f}",
                timestamp=datetime.now(),
                metric_value=metrics.sharpe_ratio,
                threshold=self.config.min_sharpe_ratio,
                recommendation="检查策略逻辑，考虑调整参数或暂停交易"
            )
        return None
    
    def _detect_drawdown_anomaly(self, strategy_name: str, metrics: PerformanceMetrics) -> Optional[PerformanceAlert]:
        """检测回撤异常"""
        if abs(metrics.max_drawdown) > self.config.max_drawdown_limit:
            return PerformanceAlert(
                strategy_name=strategy_name,
                alert_type="MAX_DRAWDOWN_EXCEEDED",
                severity="HIGH" if abs(metrics.max_drawdown) > 0.2 else "MEDIUM",
                message=f"最大回撤超限: {metrics.max_drawdown:.2%}",
                timestamp=datetime.now(),
                metric_value=abs(metrics.max_drawdown),
                threshold=self.config.max_drawdown_limit,
                recommendation="立即检查风险管理，考虑减仓或停止交易"
            )
        return None
    
    def _detect_volatility_anomaly(self, strategy_name: str, metrics: PerformanceMetrics) -> Optional[PerformanceAlert]:
        """检测波动率异常"""
        if metrics.volatility > self.config.max_volatility:
            return PerformanceAlert(
                strategy_name=strategy_name,
                alert_type="VOLATILITY_HIGH",
                severity="MEDIUM",
                message=f"波动率过高: {metrics.volatility:.2%}",
                timestamp=datetime.now(),
                metric_value=metrics.volatility,
                threshold=self.config.max_volatility,
                recommendation="检查市场环境，考虑降低仓位或调整止损"
            )
        return None
    
    def _detect_alpha_decay(self, strategy_name: str, metrics: PerformanceMetrics) -> Optional[PerformanceAlert]:
        """检测Alpha衰减"""
        if metrics.alpha_decay_indicator < -self.config.decay_threshold:
            return PerformanceAlert(
                strategy_name=strategy_name,
                alert_type="ALPHA_DECAY",
                severity="HIGH",
                message=f"Alpha显著衰减: {metrics.alpha_decay_indicator:.2%}",
                timestamp=datetime.now(),
                metric_value=metrics.alpha_decay_indicator,
                threshold=-self.config.decay_threshold,
                recommendation="策略可能失效，建议重新研究或更新模型"
            )
        return None
    
    def _detect_regime_change(self, strategy_name: str, metrics: PerformanceMetrics) -> Optional[PerformanceAlert]:
        """检测市场制度变化"""
        # 简化的制度变化检测
        history = list(self.performance_history[strategy_name])
        if len(history) < 20:
            return None
        
        # 检查最近的表现是否与历史显著不同
        recent_sharpe = np.mean([h.sharpe_ratio for h in history[-10:]])
        historical_sharpe = np.mean([h.sharpe_ratio for h in history[-30:-10]])
        
        if abs(recent_sharpe - historical_sharpe) > 1.0:  # 夏普比率变化超过1
            return PerformanceAlert(
                strategy_name=strategy_name,
                alert_type="REGIME_CHANGE",
                severity="MEDIUM",
                message=f"疑似市场制度变化，近期夏普: {recent_sharpe:.2f}, 历史夏普: {historical_sharpe:.2f}",
                timestamp=datetime.now(),
                metric_value=recent_sharpe,
                threshold=historical_sharpe,
                recommendation="分析市场环境变化，考虑调整策略参数"
            )
        return None
    
    def _handle_alert(self, alert: PerformanceAlert):
        """处理警报"""
        logger.warning(f"性能警报: {alert.alert_type} - {alert.message}")
        
        # 这里可以添加邮件、短信等通知逻辑
        if self.config.enable_email_alerts:
            self._send_email_alert(alert)
        
        if self.config.enable_sms_alerts and alert.severity in ['HIGH', 'CRITICAL']:
            self._send_sms_alert(alert)
    
    def _send_email_alert(self, alert: PerformanceAlert):
        """发送邮件警报（占位符）"""
        # 实际实现需要配置SMTP服务器
        logger.info(f"邮件警报: {alert.message}")
    
    def _send_sms_alert(self, alert: PerformanceAlert):
        """发送短信警报（占位符）"""
        # 实际实现需要配置短信服务
        logger.info(f"短信警报: {alert.message}")
    
    def get_strategy_status(self, strategy_name: str) -> Dict[str, Any]:
        """获取策略状态"""
        if strategy_name not in self.strategies:
            return {"error": "策略未注册"}
        
        history = list(self.performance_history[strategy_name])
        latest_metrics = history[-1] if history else None
        
        # 获取最近的警报
        recent_alerts = [
            alert for alert in self.alerts[-10:] 
            if alert.strategy_name == strategy_name
        ]
        
        return {
            "strategy_info": self.strategies[strategy_name],
            "latest_metrics": latest_metrics,
            "recent_alerts": recent_alerts,
            "total_alerts": len([a for a in self.alerts if a.strategy_name == strategy_name]),
            "performance_trend": self._analyze_performance_trend(history)
        }
    
    def _analyze_performance_trend(self, history: List[PerformanceMetrics]) -> str:
        """分析性能趋势"""
        if len(history) < 10:
            return "数据不足"
        
        recent_sharpe = np.mean([h.sharpe_ratio for h in history[-5:]])
        earlier_sharpe = np.mean([h.sharpe_ratio for h in history[-10:-5]])
        
        if recent_sharpe > earlier_sharpe * 1.1:
            return "改善"
        elif recent_sharpe < earlier_sharpe * 0.9:
            return "恶化"
        else:
            return "稳定"
    
    def generate_monitoring_report(self) -> str:
        """生成监控报告"""
        report = f"""
=== 机构级策略性能监控报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 监控概况 ===
监控策略数量: {len(self.strategies)}
总警报数量: {len(self.alerts)}
活跃策略: {len([s for s in self.strategies.values() if s['status'] == 'ACTIVE'])}

=== 策略状态 ==="""
        
        for strategy_name in self.strategies:
            status = self.get_strategy_status(strategy_name)
            latest = status['latest_metrics']
            if latest:
                report += f"""
{strategy_name}:
  状态: {status['strategy_info']['status']}
  最新夏普比率: {latest.sharpe_ratio:.3f}
  最大回撤: {latest.max_drawdown:.2%}
  性能趋势: {status['performance_trend']}
  警报数量: {status['total_alerts']}"""
        
        # 最近警报
        recent_alerts = sorted(self.alerts[-10:], key=lambda x: x.timestamp, reverse=True)
        if recent_alerts:
            report += "\n\n=== 最近警报 ==="
            for alert in recent_alerts:
                report += f"""
{alert.timestamp.strftime('%Y-%m-%d %H:%M')} - {alert.strategy_name}
  类型: {alert.alert_type} ({alert.severity})
  消息: {alert.message}
  建议: {alert.recommendation}"""
        
        return report
    
    def _process_historical_data(self, strategy_name: str, historical_data: pd.Series):
        """处理历史数据"""
        # 将历史数据分批处理，避免一次性计算过多
        batch_size = 20
        for i in range(0, len(historical_data), batch_size):
            batch = historical_data.iloc[i:i+batch_size]
            self.update_performance(strategy_name, batch)

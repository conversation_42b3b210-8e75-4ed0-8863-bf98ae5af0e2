#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于机构级框架的策略组合管理系统
Portfolio Management System Based on Institutional Framework

根据验证评分构建最优策略组合，实现风险分散和收益最大化
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
from scipy.optimize import minimize
from sklearn.covariance import LedoitWolf
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InstitutionalPortfolioManager:
    """
    机构级策略组合管理器
    
    基于机构级验证结果构建和管理策略组合
    """
    
    def __init__(self, framework_manager):
        """
        初始化组合管理器
        
        Args:
            framework_manager: 机构级框架管理器
        """
        self.framework = framework_manager
        self.portfolios = {}
        self.portfolio_performance = {}
        
        # 组合构建参数
        self.portfolio_constraints = {
            'min_validation_score': 60,      # 最低验证评分
            'max_single_weight': 0.2,        # 单策略最大权重
            'min_strategies': 5,              # 最少策略数量
            'max_strategies': 20,             # 最多策略数量
            'max_correlation': 0.7,           # 最大相关性
            'target_volatility': 0.15        # 目标波动率
        }
        
        logger.info("机构级策略组合管理器初始化完成")
    
    def select_qualified_strategies(self, 
                                  integration_results: Dict[str, Any],
                                  min_score: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        筛选合格策略
        
        Args:
            integration_results: 策略集成结果
            min_score: 最低评分要求
            
        Returns:
            List[Dict[str, Any]]: 合格策略列表
        """
        min_score = min_score or self.portfolio_constraints['min_validation_score']
        
        qualified_strategies = []
        
        for strategy_name, result in integration_results.items():
            if (result.get('status') == 'SUCCESS' and 
                result.get('validation_score', 0) >= min_score):
                
                qualified_strategies.append({
                    'name': strategy_name,
                    'validation_score': result['validation_score'],
                    'sharpe_ratio': result['sharpe_ratio'],
                    'max_drawdown': result['max_drawdown'],
                    'alpha_contribution': result['alpha_contribution'],
                    'tracking_error': result['tracking_error']
                })
        
        # 按验证评分排序
        qualified_strategies.sort(key=lambda x: x['validation_score'], reverse=True)
        
        logger.info(f"筛选出 {len(qualified_strategies)} 个合格策略（评分>={min_score}）")
        return qualified_strategies
    
    def calculate_strategy_correlations(self, 
                                      strategy_names: List[str],
                                      returns_data: Dict[str, pd.Series]) -> pd.DataFrame:
        """
        计算策略间相关性
        
        Args:
            strategy_names: 策略名称列表
            returns_data: 策略收益数据字典
            
        Returns:
            pd.DataFrame: 相关性矩阵
        """
        # 构建收益矩阵
        returns_matrix = []
        valid_strategies = []
        
        for strategy_name in strategy_names:
            if strategy_name in returns_data:
                returns_matrix.append(returns_data[strategy_name])
                valid_strategies.append(strategy_name)
        
        if len(returns_matrix) < 2:
            logger.warning("策略数量不足，无法计算相关性")
            return pd.DataFrame()
        
        # 对齐时间序列
        returns_df = pd.concat(returns_matrix, axis=1, keys=valid_strategies)
        returns_df = returns_df.dropna()
        
        # 计算相关性矩阵
        correlation_matrix = returns_df.corr()
        
        return correlation_matrix
    
    def optimize_portfolio_weights(self, 
                                 qualified_strategies: List[Dict[str, Any]],
                                 returns_data: Dict[str, pd.Series],
                                 optimization_objective: str = 'max_sharpe') -> Dict[str, Any]:
        """
        优化组合权重
        
        Args:
            qualified_strategies: 合格策略列表
            returns_data: 策略收益数据
            optimization_objective: 优化目标
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        strategy_names = [s['name'] for s in qualified_strategies]
        
        # 构建收益矩阵
        returns_matrix = []
        valid_strategies = []
        
        for strategy_name in strategy_names:
            if strategy_name in returns_data:
                returns_matrix.append(returns_data[strategy_name])
                valid_strategies.append(strategy_name)
        
        if len(returns_matrix) < self.portfolio_constraints['min_strategies']:
            raise ValueError(f"合格策略数量不足，至少需要{self.portfolio_constraints['min_strategies']}个")
        
        # 对齐数据
        returns_df = pd.concat(returns_matrix, axis=1, keys=valid_strategies)
        returns_df = returns_df.dropna()
        
        # 计算期望收益和协方差矩阵
        expected_returns = returns_df.mean() * 252  # 年化
        
        # 使用Ledoit-Wolf收缩估计器计算协方差矩阵
        lw = LedoitWolf()
        cov_matrix = lw.fit(returns_df).covariance_ * 252  # 年化
        
        n_assets = len(valid_strategies)
        
        # 定义目标函数
        def objective_function(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            if optimization_objective == 'max_sharpe':
                # 最大化夏普比率（最小化负夏普比率）
                if portfolio_volatility == 0:
                    return 1000
                sharpe_ratio = portfolio_return / portfolio_volatility
                return -sharpe_ratio
            
            elif optimization_objective == 'min_variance':
                # 最小化方差
                return portfolio_variance
            
            elif optimization_objective == 'max_return':
                # 最大化收益（最小化负收益）
                return -portfolio_return
            
            else:
                # 默认：风险调整收益
                return -portfolio_return / (portfolio_volatility + 0.01)
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        # 边界条件
        bounds = []
        for i in range(n_assets):
            bounds.append((0, self.portfolio_constraints['max_single_weight']))
        
        # 初始权重（等权重）
        initial_weights = np.array([1.0 / n_assets] * n_assets)
        
        # 执行优化
        result = minimize(
            objective_function,
            initial_weights,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 1000}
        )
        
        if not result.success:
            logger.warning(f"组合优化未收敛: {result.message}")
        
        # 整理结果
        optimal_weights = result.x
        portfolio_return = np.sum(optimal_weights * expected_returns)
        portfolio_variance = np.dot(optimal_weights.T, np.dot(cov_matrix, optimal_weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        portfolio_sharpe = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # 构建权重字典
        weights_dict = {}
        for i, strategy_name in enumerate(valid_strategies):
            if optimal_weights[i] > 0.001:  # 过滤极小权重
                weights_dict[strategy_name] = optimal_weights[i]
        
        optimization_result = {
            'strategy_weights': weights_dict,
            'expected_return': portfolio_return,
            'expected_volatility': portfolio_volatility,
            'expected_sharpe_ratio': portfolio_sharpe,
            'optimization_success': result.success,
            'optimization_message': result.message,
            'n_strategies': len(weights_dict),
            'optimization_time': datetime.now()
        }
        
        return optimization_result
    
    def create_portfolio(self, 
                        portfolio_name: str,
                        integration_results: Dict[str, Any],
                        returns_data: Dict[str, pd.Series],
                        optimization_objective: str = 'max_sharpe') -> Dict[str, Any]:
        """
        创建策略组合
        
        Args:
            portfolio_name: 组合名称
            integration_results: 策略集成结果
            returns_data: 策略收益数据
            optimization_objective: 优化目标
            
        Returns:
            Dict[str, Any]: 组合创建结果
        """
        logger.info(f"开始创建策略组合: {portfolio_name}")
        
        # 1. 筛选合格策略
        qualified_strategies = self.select_qualified_strategies(integration_results)
        
        if len(qualified_strategies) < self.portfolio_constraints['min_strategies']:
            raise ValueError(f"合格策略数量不足，需要至少{self.portfolio_constraints['min_strategies']}个")
        
        # 2. 限制策略数量
        max_strategies = self.portfolio_constraints['max_strategies']
        if len(qualified_strategies) > max_strategies:
            qualified_strategies = qualified_strategies[:max_strategies]
            logger.info(f"限制策略数量为前{max_strategies}个最优策略")
        
        # 3. 检查策略相关性
        strategy_names = [s['name'] for s in qualified_strategies]
        correlation_matrix = self.calculate_strategy_correlations(strategy_names, returns_data)
        
        # 4. 优化组合权重
        optimization_result = self.optimize_portfolio_weights(
            qualified_strategies, returns_data, optimization_objective
        )
        
        # 5. 创建组合
        portfolio = {
            'portfolio_name': portfolio_name,
            'creation_time': datetime.now(),
            'qualified_strategies': qualified_strategies,
            'strategy_weights': optimization_result['strategy_weights'],
            'expected_performance': {
                'return': optimization_result['expected_return'],
                'volatility': optimization_result['expected_volatility'],
                'sharpe_ratio': optimization_result['expected_sharpe_ratio']
            },
            'portfolio_statistics': {
                'n_strategies': optimization_result['n_strategies'],
                'max_weight': max(optimization_result['strategy_weights'].values()),
                'min_weight': min(optimization_result['strategy_weights'].values()),
                'weight_concentration': self._calculate_concentration_ratio(optimization_result['strategy_weights'])
            },
            'correlation_matrix': correlation_matrix.to_dict() if not correlation_matrix.empty else {},
            'optimization_objective': optimization_objective,
            'constraints_used': self.portfolio_constraints.copy()
        }
        
        # 6. 存储组合
        self.portfolios[portfolio_name] = portfolio
        
        logger.info(f"组合 {portfolio_name} 创建成功，包含{optimization_result['n_strategies']}个策略")
        return portfolio
    
    def _calculate_concentration_ratio(self, weights: Dict[str, float]) -> float:
        """计算权重集中度（HHI指数）"""
        weights_array = np.array(list(weights.values()))
        hhi = np.sum(weights_array ** 2)
        return hhi
    
    def simulate_portfolio_performance(self, 
                                     portfolio_name: str,
                                     returns_data: Dict[str, pd.Series],
                                     start_date: Optional[str] = None,
                                     end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        模拟组合表现
        
        Args:
            portfolio_name: 组合名称
            returns_data: 策略收益数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict[str, Any]: 组合表现结果
        """
        if portfolio_name not in self.portfolios:
            raise ValueError(f"组合 {portfolio_name} 不存在")
        
        portfolio = self.portfolios[portfolio_name]
        weights = portfolio['strategy_weights']
        
        # 构建组合收益序列
        portfolio_returns = []
        strategy_returns_aligned = {}
        
        # 获取策略收益数据
        for strategy_name, weight in weights.items():
            if strategy_name in returns_data:
                strategy_returns_aligned[strategy_name] = returns_data[strategy_name]
        
        if not strategy_returns_aligned:
            raise ValueError("没有找到有效的策略收益数据")
        
        # 对齐时间序列
        returns_df = pd.concat(strategy_returns_aligned, axis=1)
        returns_df = returns_df.dropna()
        
        # 应用日期过滤
        if start_date:
            returns_df = returns_df[returns_df.index >= start_date]
        if end_date:
            returns_df = returns_df[returns_df.index <= end_date]
        
        # 计算组合收益
        for date in returns_df.index:
            daily_portfolio_return = 0
            for strategy_name, weight in weights.items():
                if strategy_name in returns_df.columns:
                    daily_portfolio_return += weight * returns_df.loc[date, strategy_name]
            portfolio_returns.append(daily_portfolio_return)
        
        portfolio_returns_series = pd.Series(portfolio_returns, index=returns_df.index)
        
        # 计算组合表现指标
        performance_metrics = self._calculate_portfolio_metrics(portfolio_returns_series)
        
        # 存储表现结果
        performance_result = {
            'portfolio_name': portfolio_name,
            'simulation_period': {
                'start_date': returns_df.index[0],
                'end_date': returns_df.index[-1],
                'n_days': len(returns_df)
            },
            'portfolio_returns': portfolio_returns_series,
            'performance_metrics': performance_metrics,
            'simulation_time': datetime.now()
        }
        
        self.portfolio_performance[portfolio_name] = performance_result
        
        return performance_result
    
    def _calculate_portfolio_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算组合表现指标"""
        # 基础指标
        total_return = (1 + returns).prod() - 1
        annualized_return = (1 + returns.mean()) ** 252 - 1
        annualized_volatility = returns.std() * np.sqrt(252)
        
        # 风险调整指标
        sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns > 0).mean()
        
        # VaR
        var_95 = np.percentile(returns, 5)
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'var_95': var_95,
            'calmar_ratio': annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        }
    
    def generate_portfolio_report(self, portfolio_name: str) -> str:
        """生成组合报告"""
        if portfolio_name not in self.portfolios:
            return f"组合 {portfolio_name} 不存在"
        
        portfolio = self.portfolios[portfolio_name]
        performance = self.portfolio_performance.get(portfolio_name)
        
        report = f"""
=== 机构级策略组合报告 ===
组合名称: {portfolio_name}
创建时间: {portfolio['creation_time'].strftime('%Y-%m-%d %H:%M:%S')}

=== 组合构成 ===
策略数量: {portfolio['portfolio_statistics']['n_strategies']}
权重分布:"""
        
        # 按权重排序显示
        sorted_weights = sorted(portfolio['strategy_weights'].items(), key=lambda x: x[1], reverse=True)
        for strategy_name, weight in sorted_weights:
            report += f"\n  {strategy_name}: {weight:.1%}"
        
        report += f"""

=== 组合特征 ===
最大单策略权重: {portfolio['portfolio_statistics']['max_weight']:.1%}
最小单策略权重: {portfolio['portfolio_statistics']['min_weight']:.1%}
权重集中度(HHI): {portfolio['portfolio_statistics']['weight_concentration']:.3f}

=== 预期表现 ===
预期年化收益: {portfolio['expected_performance']['return']:.2%}
预期年化波动: {portfolio['expected_performance']['volatility']:.2%}
预期夏普比率: {portfolio['expected_performance']['sharpe_ratio']:.3f}"""
        
        if performance:
            metrics = performance['performance_metrics']
            report += f"""

=== 模拟表现 ===
模拟期间: {performance['simulation_period']['start_date'].strftime('%Y-%m-%d')} 至 {performance['simulation_period']['end_date'].strftime('%Y-%m-%d')}
总收益: {metrics['total_return']:.2%}
年化收益: {metrics['annualized_return']:.2%}
年化波动: {metrics['annualized_volatility']:.2%}
夏普比率: {metrics['sharpe_ratio']:.3f}
最大回撤: {metrics['max_drawdown']:.2%}
胜率: {metrics['win_rate']:.1%}
Calmar比率: {metrics['calmar_ratio']:.3f}
VaR(95%): {metrics['var_95']:.2%}"""
        
        return report
    
    def save_portfolio_results(self, output_dir: str = "portfolio_results"):
        """保存组合结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存组合配置
        portfolios_file = os.path.join(output_dir, 'portfolios.json')
        with open(portfolios_file, 'w', encoding='utf-8') as f:
            json.dump(self.portfolios, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存表现结果
        if self.portfolio_performance:
            performance_file = os.path.join(output_dir, 'portfolio_performance.json')
            performance_data = {}
            for name, perf in self.portfolio_performance.items():
                performance_data[name] = {
                    'simulation_period': perf['simulation_period'],
                    'performance_metrics': perf['performance_metrics'],
                    'simulation_time': perf['simulation_time']
                }
            
            with open(performance_file, 'w', encoding='utf-8') as f:
                json.dump(performance_data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"组合结果已保存到: {output_dir}")

def main():
    """主函数 - 演示组合管理"""
    print("=" * 60)
    print("策略组合管理演示")
    print("=" * 60)
    
    # 这里应该加载实际的集成结果和收益数据
    # 为演示目的，创建模拟数据
    
    # 模拟集成结果
    integration_results = {
        'TrendStrategy1': {'status': 'SUCCESS', 'validation_score': 85, 'sharpe_ratio': 2.1, 'max_drawdown': -0.12, 'alpha_contribution': 0.08, 'tracking_error': 0.15},
        'TrendStrategy2': {'status': 'SUCCESS', 'validation_score': 78, 'sharpe_ratio': 1.8, 'max_drawdown': -0.15, 'alpha_contribution': 0.06, 'tracking_error': 0.18},
        'MeanReversionStrategy1': {'status': 'SUCCESS', 'validation_score': 72, 'sharpe_ratio': 1.5, 'max_drawdown': -0.10, 'alpha_contribution': 0.05, 'tracking_error': 0.12},
        'ArbitrageStrategy1': {'status': 'SUCCESS', 'validation_score': 68, 'sharpe_ratio': 1.2, 'max_drawdown': -0.08, 'alpha_contribution': 0.03, 'tracking_error': 0.08},
        'MLStrategy1': {'status': 'SUCCESS', 'validation_score': 75, 'sharpe_ratio': 1.9, 'max_drawdown': -0.18, 'alpha_contribution': 0.07, 'tracking_error': 0.20},
        'PoorStrategy1': {'status': 'SUCCESS', 'validation_score': 35, 'sharpe_ratio': 0.3, 'max_drawdown': -0.25, 'alpha_contribution': 0.01, 'tracking_error': 0.25}
    }
    
    # 模拟收益数据
    dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
    returns_data = {}
    
    np.random.seed(42)
    for strategy_name in integration_results.keys():
        if integration_results[strategy_name]['validation_score'] > 60:
            # 好策略
            returns = np.random.normal(0.0008, 0.015, len(dates))
        else:
            # 差策略
            returns = np.random.normal(-0.0002, 0.025, len(dates))
        
        returns_data[strategy_name] = pd.Series(returns, index=dates)
    
    # 创建框架和组合管理器
    from institutional_manager import InstitutionalFrameworkManager
    framework = InstitutionalFrameworkManager()
    portfolio_manager = InstitutionalPortfolioManager(framework)
    
    # 创建组合
    print("\n创建策略组合...")
    portfolio = portfolio_manager.create_portfolio(
        portfolio_name="InstitutionalPortfolio_v1",
        integration_results=integration_results,
        returns_data=returns_data,
        optimization_objective='max_sharpe'
    )
    
    print(f"组合创建成功，包含 {portfolio['portfolio_statistics']['n_strategies']} 个策略")
    
    # 模拟组合表现
    print("\n模拟组合表现...")
    performance = portfolio_manager.simulate_portfolio_performance(
        portfolio_name="InstitutionalPortfolio_v1",
        returns_data=returns_data
    )
    
    # 生成报告
    print("\n" + "=" * 60)
    report = portfolio_manager.generate_portfolio_report("InstitutionalPortfolio_v1")
    print(report)
    
    # 保存结果
    portfolio_manager.save_portfolio_results()
    
    print("\n" + "=" * 60)
    print("策略组合管理演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示配置生成器
Demo Configuration Generator

生成演示配置，展示正确的配置格式
"""

import os
import json
from datetime import datetime
from pathlib import Path

def create_demo_configuration():
    """创建演示配置"""
    print("🎯 创建演示配置...")
    print("=" * 40)
    
    # 创建演示环境变量配置
    demo_env_content = f"""# 交易系统环境变量配置 - 演示版本
# 生成时间: {datetime.now().isoformat()}
# 注意: 这是演示配置，请替换为您的真实API密钥

# 交易所API配置
EXCHANGE_API_KEY=demo_api_key_replace_with_real_key
EXCHANGE_API_SECRET=demo_api_secret_replace_with_real_secret
EXCHANGE_SANDBOX=true

# 系统配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 安全配置
TRADING_MASTER_PASSWORD=demo_password_123

# 数据库配置（如果使用外部数据库）
DATABASE_PASSWORD=demo_db_password

# 监控配置
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://hooks.example.com/webhook
"""
    
    # 保存演示环境变量
    with open('.env.demo', 'w', encoding='utf-8') as f:
        f.write(demo_env_content)
    
    print("✅ 演示环境变量配置已创建: .env.demo")
    
    # 创建演示主配置
    demo_config = {
        "trading": {
            "initial_capital": 10000.0,
            "daily_loss_limit": 300.0,
            "max_position_size": 0.1,
            "stop_loss_pct": 0.03,
            "take_profit_pct": 0.06,
            "max_drawdown": 0.15,
            "min_trade_amount": 10.0,
            "max_trades_per_day": 50,
            "trading_pairs": ["BTC/USDT", "ETH/USDT", "BNB/USDT"]
        },
        "exchange": {
            "name": "gate",
            "api_key": "${EXCHANGE_API_KEY}",
            "api_secret": "${EXCHANGE_API_SECRET}",
            "passphrase": "",
            "sandbox": True,
            "testnet": True,
            "timeout": 30000,
            "rateLimit": 1000
        },
        "database": {
            "type": "sqlite",
            "host": "localhost",
            "port": 5432,
            "database": "trading.db",
            "username": "",
            "password": ""
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_path": "logs/trading.log",
            "max_file_size": 10485760,
            "backup_count": 5
        },
        "monitoring": {
            "enable_alerts": True,
            "alert_email": "${ALERT_EMAIL}",
            "alert_webhook": "${ALERT_WEBHOOK}",
            "check_interval": 60,
            "performance_threshold": 0.8
        },
        "version": "1.0.0",
        "environment": "development",
        "debug": True,
        "demo_mode": True,
        "last_updated": datetime.now().isoformat()
    }
    
    # 保存演示主配置
    with open('config.demo.json', 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 演示主配置已创建: config.demo.json")
    
    # 创建GUI演示配置
    demo_gui_config = {
        "api_key": "demo_api_key_replace_with_real",
        "secret": "demo_secret_replace_with_real",
        "sandbox": True,
        "initial_capital": 10000,
        "daily_loss_limit": 300,
        "demo_mode": True,
        "last_updated": datetime.now().isoformat()
    }
    
    # 确保core目录存在
    Path('core').mkdir(exist_ok=True)
    
    with open('core/gui_config.demo.json', 'w', encoding='utf-8') as f:
        json.dump(demo_gui_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 演示GUI配置已创建: core/gui_config.demo.json")
    
    return True

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "=" * 60)
    print("📋 配置指南")
    print("=" * 60)
    
    print("""
🎯 第一步：获取Gate.io API密钥

1. 访问 Gate.io 官网: https://www.gate.io
2. 登录您的账户
3. 进入 "API管理" 页面
4. 点击 "创建API密钥"
5. 设置API权限:
   ✅ 现货交易
   ✅ 查看余额
   ✅ 查看订单
   ❌ 提币权限 (建议关闭)
6. 记录您的API Key和Secret

🎯 第二步：配置系统

方法1 - 使用向导配置:
1. 运行: python quick_start.py
2. 选择 "1. 🔧 首次设置"
3. 按照向导输入您的API密钥

方法2 - 手动配置:
1. 复制 .env.demo 为 .env
2. 编辑 .env 文件，替换演示密钥为真实密钥
3. 保存文件

🎯 第三步：测试配置

1. 运行: python quick_start.py
2. 选择 "4. 🔗 测试连接"
3. 确认API连接成功

🎯 第四步：开始交易

1. 运行: python quick_start.py
2. 选择 "2. 🚀 直接启动"
3. 在GUI界面中开始交易
""")

def create_step_by_step_guide():
    """创建分步指南"""
    guide_content = f"""# 🚀 分步实施指南

## 📋 准备工作清单

### ✅ 系统要求检查
- [ ] Python 3.8+ 已安装
- [ ] 网络连接正常
- [ ] Gate.io账户已注册
- [ ] 系统文件完整

### ✅ 安全准备
- [ ] 准备一个强密码作为主密码
- [ ] 确保电脑安全（防病毒软件等）
- [ ] 备份重要数据

## 🎯 第一步：获取API密钥

### Gate.io API密钥申请步骤：

1. **登录Gate.io**
   - 访问: https://www.gate.io
   - 登录您的账户

2. **进入API管理**
   - 点击右上角头像
   - 选择 "API管理"

3. **创建API密钥**
   - 点击 "创建API密钥"
   - 输入API名称（如：Trading Bot）
   - 设置权限：
     ✅ 现货交易
     ✅ 查看余额
     ✅ 查看订单历史
     ❌ 提币权限（安全考虑）

4. **安全设置**
   - 设置IP白名单（推荐）
   - 启用Google验证器
   - 记录API Key和Secret

⚠️ **重要提醒**：
- API Secret只显示一次，请妥善保存
- 不要与任何人分享您的API密钥
- 定期更换API密钥

## 🎯 第二步：配置交易系统

### 方法1：使用配置向导（推荐）

```bash
# 启动配置向导
python quick_start.py

# 选择首次设置
选择: 1. 🔧 首次设置

# 按照提示输入：
1. 选择交易模式（沙盒/实盘）
2. 输入API Key
3. 输入API Secret
4. 设置交易参数
5. 配置安全设置
```

### 方法2：手动配置

```bash
# 1. 复制演示配置
copy .env.demo .env

# 2. 编辑配置文件
notepad .env

# 3. 替换以下内容：
EXCHANGE_API_KEY=your_real_api_key_here
EXCHANGE_API_SECRET=your_real_api_secret_here
TRADING_MASTER_PASSWORD=your_strong_password_here
```

## 🎯 第三步：验证配置

### 系统诊断
```bash
python quick_start.py
选择: 3. 🔍 系统诊断
```

### API连接测试
```bash
python quick_start.py
选择: 4. 🔗 测试连接
```

### 预期结果：
- ✅ 系统健康检查通过
- ✅ API连接成功
- ✅ 账户信息正常显示

## 🎯 第四步：开始交易

### 启动交易界面
```bash
python quick_start.py
选择: 2. 🚀 直接启动
```

### 在GUI中操作：
1. **验证连接**：点击"测试连接"
2. **查看余额**：确认账户余额显示
3. **配置策略**：选择交易策略和参数
4. **设置风险**：配置止损和仓位限制
5. **开始交易**：点击"开始交易"

## 🎯 第五步：监控和维护

### 日常监控
- 📊 查看交易日志
- 💰 监控账户余额
- 📈 分析交易表现
- 🚨 关注系统告警

### 定期维护
- 🔄 更新系统配置
- 🔒 更换API密钥
- 💾 备份重要数据
- 📊 分析交易报告

## 🚨 故障排除

### 常见问题：

**API连接失败**
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API权限设置

**交易失败**
- 检查账户余额
- 确认交易对正确
- 验证订单参数

**系统启动失败**
- 检查Python环境
- 验证依赖包安装
- 查看错误日志

### 获取帮助：
- 查看日志文件: logs/
- 运行系统诊断
- 查看文档资料

---

**生成时间**: {datetime.now().isoformat()}
**版本**: 1.0.0
**状态**: 完整可用
"""
    
    with open('分步实施指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 分步实施指南已创建: 分步实施指南.md")

def main():
    """主函数"""
    print("🎯 演示配置生成器")
    print("版本: 1.0.0")
    print("=" * 40)
    
    # 创建演示配置
    create_demo_configuration()
    
    # 创建分步指南
    create_step_by_step_guide()
    
    # 显示配置指南
    show_configuration_guide()
    
    print("\n" + "=" * 60)
    print("🎉 演示配置创建完成！")
    print("=" * 60)
    
    print("""
📁 已创建的文件：
✅ .env.demo - 演示环境变量配置
✅ config.demo.json - 演示主配置
✅ core/gui_config.demo.json - 演示GUI配置
✅ 分步实施指南.md - 详细实施步骤

🎯 下一步操作：
1. 获取您的Gate.io API密钥
2. 运行配置向导: python quick_start.py
3. 选择 "1. 🔧 首次设置"
4. 按照向导完成配置

💡 提示：
- 首次使用建议选择沙盒模式
- 从小额资金开始测试
- 详细步骤请查看 "分步实施指南.md"
""")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多交易对GUI - 简化稳定版
Multi Trading Pairs GUI - Simplified Stable Version

支持22个交易对的稳定版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
from datetime import datetime
import random

class MultiPairsGUI:
    """多交易对GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 终极版交易系统 v1.3 - 多交易对版")
        self.root.geometry("1000x800")
        self.root.configure(bg='#1e1e1e')
        
        # 22个交易对数据
        self.trading_pairs = {
            # 主流币种
            'BTC/USDT': {'price': 107951.00, 'change': -0.44, 'volume': 28450.5, 'category': '主流'},
            'ETH/USDT': {'price': 2508.35, 'change': -1.77, 'volume': 156780.2, 'category': '主流'},
            'BNB/USDT': {'price': 692.45, 'change': 1.23, 'volume': 45230.8, 'category': '主流'},
            
            # DeFi代币
            'UNI/USDT': {'price': 12.85, 'change': 2.45, 'volume': 12450.3, 'category': 'DeFi'},
            'AAVE/USDT': {'price': 285.67, 'change': -0.89, 'volume': 8760.1, 'category': 'DeFi'},
            'SUSHI/USDT': {'price': 1.45, 'change': 3.21, 'volume': 15670.9, 'category': 'DeFi'},
            
            # Layer1公链
            'SOL/USDT': {'price': 245.78, 'change': 1.56, 'volume': 67890.4, 'category': 'Layer1'},
            'ADA/USDT': {'price': 1.23, 'change': -2.34, 'volume': 89450.7, 'category': 'Layer1'},
            'DOT/USDT': {'price': 8.95, 'change': 0.78, 'volume': 23450.6, 'category': 'Layer1'},
            'AVAX/USDT': {'price': 42.67, 'change': 2.89, 'volume': 34560.2, 'category': 'Layer1'},
            
            # Layer2解决方案
            'MATIC/USDT': {'price': 1.15, 'change': 1.45, 'volume': 78920.3, 'category': 'Layer2'},
            'OP/USDT': {'price': 3.45, 'change': -1.23, 'volume': 12340.8, 'category': 'Layer2'},
            'ARB/USDT': {'price': 2.78, 'change': 0.95, 'volume': 45670.1, 'category': 'Layer2'},
            
            # 人工智能
            'FET/USDT': {'price': 2.34, 'change': 4.56, 'volume': 23450.7, 'category': 'AI'},
            'AGIX/USDT': {'price': 0.89, 'change': 2.78, 'volume': 15670.4, 'category': 'AI'},
            'OCEAN/USDT': {'price': 1.67, 'change': 1.89, 'volume': 8920.3, 'category': 'AI'},
            
            # 游戏代币
            'AXS/USDT': {'price': 8.45, 'change': -1.56, 'volume': 12340.5, 'category': '游戏'},
            'SAND/USDT': {'price': 0.67, 'change': 2.34, 'volume': 34560.8, 'category': '游戏'},
            'MANA/USDT': {'price': 0.89, 'change': 1.23, 'volume': 23450.2, 'category': '游戏'},
            
            # 存储概念
            'FIL/USDT': {'price': 6.78, 'change': -0.45, 'volume': 15670.9, 'category': '存储'},
            'AR/USDT': {'price': 23.45, 'change': 1.67, 'volume': 8920.4, 'category': '存储'},
            
            # 交易所代币
            'GT/USDT': {'price': 21.28, 'change': -0.73, 'volume': 45230.6, 'category': '交易所'}
        }
        
        # 分类
        self.categories = ['全部', '主流', 'DeFi', 'Layer1', 'Layer2', 'AI', '游戏', '存储', '交易所']
        self.current_category = '全部'
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据更新
        self.start_updates()
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(
            title_frame, 
            text="🚀 终极版交易系统 v1.3 - 多交易对版", 
            font=('Arial', 16, 'bold'),
            fg='#00ff00',
            bg='#1e1e1e'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="支持22个主流交易对 | 8个分类筛选 | 智能交易信号",
            font=('Arial', 10),
            fg='#888888',
            bg='#1e1e1e'
        )
        subtitle_label.pack()
        
        # 状态栏
        status_frame = tk.Frame(self.root, bg='#333333')
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = tk.Label(
            status_frame,
            text=f"📊 监控中: {len(self.trading_pairs)}个交易对 | 🔄 实时更新",
            font=('Arial', 10),
            fg='#00ff00',
            bg='#333333'
        )
        self.status_label.pack(pady=5)
        
        # 分类筛选
        filter_frame = tk.Frame(self.root, bg='#2e2e2e')
        filter_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(filter_frame, text="分类筛选:", font=('Arial', 12, 'bold'), fg='white', bg='#2e2e2e').pack(side='left', padx=10)
        
        self.category_var = tk.StringVar(value='全部')
        for category in self.categories:
            btn = tk.Radiobutton(
                filter_frame,
                text=category,
                variable=self.category_var,
                value=category,
                command=self.filter_category,
                font=('Arial', 10),
                fg='white',
                bg='#2e2e2e',
                selectcolor='#4e4e4e'
            )
            btn.pack(side='left', padx=5)
        
        # 刷新按钮
        ttk.Button(filter_frame, text="🔄 刷新数据", command=self.refresh_data).pack(side='right', padx=10)
        
        # 交易对表格
        self.create_table()
        
        # 交易信号
        signal_frame = tk.LabelFrame(self.root, text="🎯 实时交易信号", font=('Arial', 12, 'bold'), fg='white', bg='#1e1e1e')
        signal_frame.pack(fill='x', padx=10, pady=5)
        
        self.signal_text = tk.Text(
            signal_frame,
            height=6,
            bg='#2d2d2d',
            fg='#ffffff',
            font=('Consolas', 9),
            wrap='word'
        )
        self.signal_text.pack(fill='x', padx=5, pady=5)
        
        # 添加初始信号
        self.add_signal("🌐 多交易对监控系统已启动")
        self.add_signal(f"📊 正在监控 {len(self.trading_pairs)} 个交易对")
        self.add_signal("🎯 智能交易信号分析已就绪")
    
    def create_table(self):
        """创建交易对表格"""
        table_frame = tk.Frame(self.root)
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ('交易对', '价格', '24h变化', '成交量', '分类')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 初始化数据
        self.update_table()
    
    def get_filtered_pairs(self):
        """获取筛选后的交易对"""
        if self.current_category == '全部':
            return self.trading_pairs
        else:
            return {k: v for k, v in self.trading_pairs.items() if v['category'] == self.current_category}
    
    def update_table(self):
        """更新表格数据"""
        # 清除现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取筛选后的数据
        filtered_pairs = self.get_filtered_pairs()
        
        # 添加数据
        for symbol, data in filtered_pairs.items():
            price = data['price']
            change = data['change']
            volume = data['volume']
            category = data['category']
            
            # 格式化价格
            if price >= 1:
                price_text = f"{price:,.2f}"
            else:
                price_text = f"{price:.6f}"
            
            # 格式化变化
            change_text = f"{change:+.2f}%"
            
            # 格式化成交量
            volume_text = f"{volume:,.1f}"
            
            # 插入数据
            item = self.tree.insert('', 'end', values=(symbol, price_text, change_text, volume_text, category))
            
            # 设置颜色
            if change >= 0:
                self.tree.set(item, '24h变化', change_text)
            else:
                self.tree.set(item, '24h变化', change_text)
        
        # 更新状态
        count = len(filtered_pairs)
        if self.current_category == '全部':
            self.status_label.config(text=f"📊 监控中: {count}个交易对 | 🔄 实时更新")
        else:
            self.status_label.config(text=f"📊 {self.current_category}分类: {count}个交易对 | 🔄 实时更新")
    
    def filter_category(self):
        """筛选分类"""
        self.current_category = self.category_var.get()
        self.update_table()
        self.add_signal(f"🔍 切换到 {self.current_category} 分类")
    
    def simulate_price_changes(self):
        """模拟价格变化"""
        for symbol in self.trading_pairs:
            # 小幅随机变化
            change = random.uniform(-0.5, 0.5)
            self.trading_pairs[symbol]['change'] += change
            
            # 限制变化范围
            if self.trading_pairs[symbol]['change'] > 10:
                self.trading_pairs[symbol]['change'] = 10
            elif self.trading_pairs[symbol]['change'] < -10:
                self.trading_pairs[symbol]['change'] = -10
            
            # 更新价格
            base_price = self.trading_pairs[symbol]['price']
            self.trading_pairs[symbol]['price'] = base_price * (1 + change / 100)
    
    def generate_signal(self):
        """生成交易信号"""
        filtered_pairs = self.get_filtered_pairs()
        if not filtered_pairs:
            return
        
        symbol = random.choice(list(filtered_pairs.keys()))
        data = filtered_pairs[symbol]
        change = data['change']
        category = data['category']
        
        # 根据变化生成信号
        if change > 2:
            signal = "买入"
            strength = "强" if change > 4 else "中"
        elif change < -2:
            signal = "卖出"
            strength = "强" if change < -4 else "中"
        else:
            signal = "观察"
            strength = "弱"
        
        # 分类相关原因
        reasons = {
            '主流': ['机构买入', '技术突破', '市场情绪'],
            'DeFi': ['TVL增长', '协议升级', 'DeFi热度'],
            'Layer1': ['生态发展', '技术升级', '开发者活跃'],
            'Layer2': ['扩容需求', '成本优势', '生态迁移'],
            'AI': ['AI概念', '技术突破', '合作消息'],
            '游戏': ['游戏热度', '用户增长', 'NFT交易'],
            '存储': ['存储需求', '数据增长', '技术优势'],
            '交易所': ['平台币回购', '交易量增长', '新功能']
        }
        
        reason = random.choice(reasons.get(category, ['技术分析']))
        
        if signal != '观察':
            signal_text = f"🎯 {symbol} ({category}): {signal}信号 ({strength}) - {reason}"
        else:
            signal_text = f"⚪ {symbol} ({category}): 震荡整理，等待机会"
        
        self.add_signal(signal_text)
    
    def add_signal(self, message):
        """添加信号"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.signal_text.insert('end', full_message)
        self.signal_text.see('end')
        
        # 限制长度
        lines = self.signal_text.get('1.0', 'end').split('\n')
        if len(lines) > 20:
            self.signal_text.delete('1.0', '5.0')
    
    def refresh_data(self):
        """刷新数据"""
        self.simulate_price_changes()
        self.update_table()
        self.add_signal("🔄 数据已刷新")
    
    def start_updates(self):
        """启动更新"""
        def update_loop():
            while True:
                try:
                    # 模拟价格变化
                    self.simulate_price_changes()
                    
                    # 更新界面
                    self.root.after(0, self.update_table)
                    
                    # 生成信号
                    if random.random() < 0.3:  # 30%概率生成信号
                        self.root.after(0, self.generate_signal)
                    
                    time.sleep(15)  # 15秒更新一次
                    
                except Exception as e:
                    print(f"更新错误: {e}")
                    time.sleep(30)
        
        threading.Thread(target=update_loop, daemon=True).start()
    
    def run(self):
        """运行GUI"""
        self.add_signal("🚀 多交易对监控系统启动成功")
        self.root.mainloop()

def main():
    """主函数"""
    app = MultiPairsGUI()
    app.run()

if __name__ == "__main__":
    main()

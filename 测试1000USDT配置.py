#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试1000 USDT配置应用
Test 1000 USDT Configuration Application

验证1000 USDT配置是否正确应用到系统中
"""

import sys
import os
import time

# 添加core目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

def test_configuration():
    """测试配置应用"""
    print("🔍 测试1000 USDT配置应用...")
    print("=" * 50)
    
    try:
        # 导入GUI类
        from ultimate_spot_trading_gui import UltimateSpotTradingGUI
        
        # 创建实例
        app = UltimateSpotTradingGUI()
        
        print("✅ GUI实例创建成功")
        
        # 检查默认配置
        print("\n📊 检查默认配置:")
        print(f"💰 初始资金: {app.simulation_params['initial_capital']} USDT")
        print(f"🎯 每笔风险: {app.simulation_params['risk_per_trade']}%")
        print(f"📊 最大仓位: {app.simulation_params['max_position_size']}%")
        print(f"🛡️ 止损比例: {app.simulation_params['stop_loss_pct']}%")
        print(f"💎 止盈比例: {app.simulation_params['take_profit_pct']}%")
        print(f"🎪 最小胜率: {app.simulation_params['min_win_rate']}%")
        print(f"⚡ 信号强度: {app.simulation_params['min_signal_strength']}%")
        
        # 检查账户数据
        print("\n💰 检查账户数据:")
        print(f"当前余额: {app.account_data['current_balance']} USDT")
        print(f"总资产: {app.account_data['total_value']} USDT")
        print(f"未实现盈亏: {app.account_data['unrealized_pnl']} USDT")
        print(f"总收益率: {app.account_data['total_return']:.2%}")
        
        # 验证配置是否正确
        expected_config = {
            'initial_capital': 1000.0,
            'risk_per_trade': 1.5,
            'max_position_size': 20.0,
            'stop_loss_pct': 2.5,
            'take_profit_pct': 5.0,
            'min_win_rate': 70.0,
            'min_signal_strength': 75.0
        }
        
        print("\n🔍 验证配置正确性:")
        all_correct = True
        
        for key, expected_value in expected_config.items():
            actual_value = app.simulation_params[key]
            if actual_value == expected_value:
                print(f"✅ {key}: {actual_value} (正确)")
            else:
                print(f"❌ {key}: {actual_value} (期望: {expected_value})")
                all_correct = False
        
        # 测试参数应用功能
        print("\n🧪 测试参数应用功能:")
        
        # 模拟参数变量
        app.param_vars = {
            "初始资金 (USDT):": type('MockVar', (), {'get': lambda: '1000'})(),
            "每笔风险 (%):": type('MockVar', (), {'get': lambda: '1.5'})(),
            "最小利润率 (%):": type('MockVar', (), {'get': lambda: '0.6'})(),
            "止损比例 (%):": type('MockVar', (), {'get': lambda: '2.5'})(),
            "止盈比例 (%):": type('MockVar', (), {'get': lambda: '5.0'})(),
            "最大仓位 (%):": type('MockVar', (), {'get': lambda: '20'})(),
            "最小胜率 (%):": type('MockVar', (), {'get': lambda: '70'})(),
            "信号强度 (%):": type('MockVar', (), {'get': lambda: '75'})()
        }
        
        # 模拟日志函数
        app.log_message = lambda msg: print(f"📝 日志: {msg}")
        
        # 模拟更新显示函数
        app.update_all_displays = lambda: print("🔄 显示已更新")
        
        try:
            # 测试参数应用（不显示消息框）
            import tkinter.messagebox
            original_showinfo = tkinter.messagebox.showinfo
            tkinter.messagebox.showinfo = lambda title, msg: print(f"💬 {title}: {msg}")
            
            # 调用参数应用函数
            app.apply_parameters()
            
            # 恢复原函数
            tkinter.messagebox.showinfo = original_showinfo
            
            print("✅ 参数应用功能测试成功")
            
        except Exception as e:
            print(f"❌ 参数应用功能测试失败: {e}")
        
        # 测试模拟数据更新
        print("\n🔄 测试模拟数据更新:")
        
        # 模拟账户标签
        class MockLabel:
            def __init__(self):
                self.text = ""
            def config(self, text):
                self.text = text
                print(f"  📊 标签更新: {text}")
        
        app.account_labels = {
            'current_balance': MockLabel(),
            'total_value': MockLabel(),
            'unrealized_pnl': MockLabel(),
            'total_return': MockLabel(),
            'positions_count': MockLabel()
        }
        
        app.perf_labels = {
            'total_trades': MockLabel(),
            'win_rate': MockLabel(),
            'max_drawdown': MockLabel(),
            'profit_factor': MockLabel()
        }
        
        app.metric_displays = {
            'total_value': MockLabel(),
            'daily_pnl': MockLabel(),
            'total_return': MockLabel(),
            'win_rate': MockLabel()
        }
        
        # 测试未运行状态的数据更新
        app.is_running = False
        print("  🛑 测试未运行状态:")
        app.update_simulated_data()
        
        # 测试运行状态的数据更新
        app.is_running = True
        print("  ▶️ 测试运行状态:")
        app.update_simulated_data()
        
        print("✅ 模拟数据更新测试成功")
        
        # 最终结果
        print("\n" + "🎉" * 20)
        if all_correct:
            print("🎉 所有测试通过！1000 USDT配置已正确应用")
        else:
            print("⚠️ 部分测试失败，请检查配置")
        print("🎉" * 20)
        
        return all_correct
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_adaptive_strategy():
    """测试自适应策略"""
    print("\n🧠 测试自适应策略模块...")
    print("=" * 50)
    
    try:
        from adaptive_market_strategy import AdaptiveMarketStrategy
        
        # 创建策略实例
        strategy = AdaptiveMarketStrategy(initial_capital=1000.0)
        
        print("✅ 自适应策略实例创建成功")
        
        # 检查基础参数
        print("\n📊 检查基础参数:")
        print(f"💰 初始资金: {strategy.initial_capital} USDT")
        print(f"🎯 每笔风险: {strategy.base_params.risk_per_trade}%")
        print(f"📊 最大仓位: {strategy.base_params.max_position_size}%")
        print(f"🛡️ 止损比例: {strategy.base_params.stop_loss_pct}%")
        print(f"💎 止盈比例: {strategy.base_params.take_profit_pct}%")
        print(f"🎪 最小胜率: {strategy.base_params.min_win_rate}%")
        print(f"⚡ 信号强度: {strategy.base_params.min_signal_strength}%")
        
        # 测试市场状态分析
        print("\n🔍 测试市场状态分析:")
        
        # 模拟市场数据
        mock_market_data = {
            'BTC/USDT': {
                'ohlcv': type('MockDF', (), {
                    'close': type('MockSeries', (), {
                        'tolist': lambda: [43000, 43100, 43200, 43150, 43300] * 4
                    })(),
                    'volume': type('MockSeries', (), {
                        'tolist': lambda: [1000, 1100, 1200, 1150, 1300] * 4
                    })()
                })()
            }
        }
        
        market_condition = strategy.analyze_market_condition(mock_market_data)
        
        print(f"📈 市场趋势: {market_condition.trend}")
        print(f"📊 波动率: {market_condition.volatility:.2%}")
        print(f"💹 成交量比率: {market_condition.volume_ratio:.2f}")
        print(f"😊 市场情绪: {market_condition.sentiment}")
        print(f"💪 趋势强度: {market_condition.strength:.2f}")
        
        # 测试参数自适应
        print("\n⚙️ 测试参数自适应:")
        
        mock_performance = {
            'win_rate': 0.75,
            'profit_factor': 2.0,
            'max_drawdown': 0.05
        }
        
        adapted_params = strategy.adapt_parameters(market_condition, mock_performance)
        
        print(f"🎯 自适应风险: {adapted_params.risk_per_trade:.1f}%")
        print(f"📊 自适应仓位: {adapted_params.max_position_size:.1f}%")
        print(f"🛡️ 自适应止损: {adapted_params.stop_loss_pct:.1f}%")
        print(f"💎 自适应止盈: {adapted_params.take_profit_pct:.1f}%")
        
        # 测试策略模式
        strategy_mode = strategy.get_current_strategy_mode(market_condition)
        print(f"🎮 当前策略模式: {strategy_mode}")
        
        print("✅ 自适应策略测试成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 开始1000 USDT配置测试...")
    print("=" * 60)
    
    # 测试GUI配置
    gui_test_passed = test_configuration()
    
    # 测试自适应策略
    strategy_test_passed = test_adaptive_strategy()
    
    # 总结
    print("\n" + "📋" * 20)
    print("📋 测试结果总结:")
    print("📋" * 20)
    
    print(f"🎮 GUI配置测试: {'✅ 通过' if gui_test_passed else '❌ 失败'}")
    print(f"🧠 自适应策略测试: {'✅ 通过' if strategy_test_passed else '❌ 失败'}")
    
    if gui_test_passed and strategy_test_passed:
        print("\n🎉 所有测试通过！")
        print("💰 1000 USDT配置已正确应用")
        print("🧠 自适应策略工作正常")
        print("🚀 系统已准备就绪！")
        
        print("\n💡 下一步操作:")
        print("1. 运行 'python core/ultimate_spot_trading_gui.py'")
        print("2. 点击'应用参数'确认配置")
        print("3. 开始实战演练")
        
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试中断")
    except Exception as e:
        print(f"\n❌ 测试错误: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API连接测试工具
API Connection Tester

测试交易所API连接和权限
"""

import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, Tuple

import ccxt


class APIConnectionTester:
    """API连接测试器"""

    def __init__(self):
        self.exchange = None
        self.test_results = {}

    def test_gate_io_connection(
        self, api_key: str, api_secret: str, sandbox: bool = True
    ) -> Dict[str, Any]:
        """测试Gate.io连接"""
        print("🔍 测试Gate.io API连接...")

        try:
            # 创建交易所实例
            self.exchange = ccxt.gate(
                {
                    "apiKey": api_key,
                    "secret": api_secret,
                    "sandbox": sandbox,
                    "timeout": 30000,
                    "rateLimit": 1000,
                }
            )

            results = {
                "timestamp": datetime.now().isoformat(),
                "exchange": "Gate.io",
                "sandbox": sandbox,
                "tests": {},
            }

            # 测试1: 基本连接
            print("  📡 测试基本连接...")
            basic_result = self._test_basic_connection()
            results["tests"]["basic_connection"] = basic_result

            # 测试2: 账户信息
            print("  👤 测试账户信息...")
            account_result = self._test_account_info()
            results["tests"]["account_info"] = account_result

            # 测试3: 市场数据
            print("  📊 测试市场数据...")
            market_result = self._test_market_data()
            results["tests"]["market_data"] = market_result

            # 测试4: 交易权限
            print("  💰 测试交易权限...")
            trading_result = self._test_trading_permissions()
            results["tests"]["trading_permissions"] = trading_result

            # 计算总体结果
            passed_tests = sum(
                1 for test in results["tests"].values() if test["success"]
            )
            total_tests = len(results["tests"])
            results["overall_success"] = passed_tests == total_tests
            results["success_rate"] = passed_tests / total_tests

            return results

        except Exception as e:
            return {
                "timestamp": datetime.now().isoformat(),
                "exchange": "Gate.io",
                "sandbox": sandbox,
                "error": str(e),
                "overall_success": False,
                "success_rate": 0.0,
            }

    def _test_basic_connection(self) -> Dict[str, Any]:
        """测试基本连接"""
        try:
            # 获取服务器时间
            server_time = self.exchange.fetch_time()

            return {
                "success": True,
                "message": "连接成功",
                "details": {
                    "server_time": server_time,
                    "local_time": datetime.now().timestamp() * 1000,
                },
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "details": {},
            }

    def _test_account_info(self) -> Dict[str, Any]:
        """测试账户信息"""
        try:
            # 获取账户余额
            balance = self.exchange.fetch_balance()

            # 检查是否有USDT余额信息
            usdt_balance = balance.get("USDT", {})

            return {
                "success": True,
                "message": "账户信息获取成功",
                "details": {
                    "total_balance": usdt_balance.get("total", 0),
                    "free_balance": usdt_balance.get("free", 0),
                    "used_balance": usdt_balance.get("used", 0),
                    "currencies_count": len(
                        [
                            k
                            for k, v in balance.items()
                            if isinstance(v, dict) and v.get("total", 0) > 0
                        ]
                    ),
                },
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"账户信息获取失败: {str(e)}",
                "details": {},
            }

    def _test_market_data(self) -> Dict[str, Any]:
        """测试市场数据"""
        try:
            # 获取BTC/USDT行情
            ticker = self.exchange.fetch_ticker("BTC/USDT")

            # 获取订单簿
            orderbook = self.exchange.fetch_order_book("BTC/USDT", limit=5)

            return {
                "success": True,
                "message": "市场数据获取成功",
                "details": {
                    "btc_price": ticker.get("last"),
                    "bid_price": ticker.get("bid"),
                    "ask_price": ticker.get("ask"),
                    "volume_24h": ticker.get("baseVolume"),
                    "orderbook_bids": len(orderbook.get("bids", [])),
                    "orderbook_asks": len(orderbook.get("asks", [])),
                },
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"市场数据获取失败: {str(e)}",
                "details": {},
            }

    def _test_trading_permissions(self) -> Dict[str, Any]:
        """测试交易权限"""
        try:
            # 获取开放订单（这个操作需要交易权限）
            open_orders = self.exchange.fetch_open_orders("BTC/USDT")

            # 获取交易历史（最近几条）
            try:
                trades = self.exchange.fetch_my_trades("BTC/USDT", limit=5)
                trades_accessible = True
            except:
                trades_accessible = False

            return {
                "success": True,
                "message": "交易权限验证成功",
                "details": {
                    "open_orders_count": len(open_orders),
                    "trades_accessible": trades_accessible,
                    "trading_enabled": True,
                },
            }
        except Exception as e:
            # 如果是权限问题，仍然算作部分成功
            if (
                "permission" in str(e).lower()
                or "unauthorized" in str(e).lower()
            ):
                return {
                    "success": False,
                    "message": f"交易权限不足: {str(e)}",
                    "details": {
                        "trading_enabled": False,
                        "permission_issue": True,
                    },
                }
            else:
                return {
                    "success": False,
                    "message": f"交易权限测试失败: {str(e)}",
                    "details": {},
                }

    def print_test_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        print("\n" + "=" * 50)
        print("📊 API连接测试结果")
        print("=" * 50)

        if "error" in results:
            print(f"❌ 连接失败: {results['error']}")
            return

        print(f"🏢 交易所: {results['exchange']}")
        print(f"🧪 模式: {'沙盒模式' if results['sandbox'] else '实盘模式'}")
        print(f"⏰ 测试时间: {results['timestamp']}")
        print(f"📈 成功率: {results['success_rate']:.1%}")

        print("\n📋 详细测试结果:")

        for test_name, test_result in results["tests"].items():
            status = "✅" if test_result["success"] else "❌"
            test_name_cn = {
                "basic_connection": "基本连接",
                "account_info": "账户信息",
                "market_data": "市场数据",
                "trading_permissions": "交易权限",
            }.get(test_name, test_name)

            print(f"{status} {test_name_cn}: {test_result['message']}")

            if test_result["details"]:
                for key, value in test_result["details"].items():
                    if isinstance(value, (int, float)):
                        if "balance" in key:
                            print(f"    {key}: {value:,.2f}")
                        elif "price" in key:
                            print(f"    {key}: ${value:,.2f}")
                        else:
                            print(f"    {key}: {value}")
                    else:
                        print(f"    {key}: {value}")

        print("\n" + "=" * 50)

        if results["overall_success"]:
            print("🎉 所有测试通过！API配置正确，可以开始交易。")
        elif results["success_rate"] >= 0.75:
            print("⚠️ 大部分测试通过，系统基本可用，但建议检查失败的项目。")
        else:
            print("❌ 多项测试失败，请检查API密钥配置和网络连接。")


def test_from_env():
    """从环境变量测试"""
    # 尝试从.env文件加载环境变量
    env_file = ".env"
    if os.path.exists(env_file):
        with open(env_file, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value

    api_key = os.getenv("EXCHANGE_API_KEY", "")
    api_secret = os.getenv("EXCHANGE_API_SECRET", "")
    sandbox = os.getenv("EXCHANGE_SANDBOX", "true").lower() == "true"

    if not api_key or not api_secret:
        print("❌ 未找到API密钥配置")
        print("请先运行: python setup_trading_system.py")
        return False

    if api_key == "your_api_key_here" or api_secret == "your_api_secret_here":
        print("❌ 请配置真实的API密钥")
        print("请先运行: python setup_trading_system.py")
        return False

    tester = APIConnectionTester()
    results = tester.test_gate_io_connection(api_key, api_secret, sandbox)
    tester.print_test_results(results)

    # 保存测试结果
    results_file = f"logs/api_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs("logs", exist_ok=True)

    with open(results_file, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"\n📄 测试结果已保存: {results_file}")

    return results.get("overall_success", False)


def main():
    """主函数"""
    print("🔍 API连接测试工具")
    print("=" * 30)

    if len(sys.argv) > 1 and sys.argv[1] == "--manual":
        # 手动输入模式
        api_key = input("请输入API Key: ").strip()
        api_secret = input("请输入API Secret: ").strip()
        sandbox_input = input("是否使用沙盒模式? (y/n) [y]: ").strip().lower()
        sandbox = sandbox_input != "n"

        tester = APIConnectionTester()
        results = tester.test_gate_io_connection(api_key, api_secret, sandbox)
        tester.print_test_results(results)

        return results.get("overall_success", False)
    else:
        # 从环境变量测试
        return test_from_env()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GATE现货实战演练引擎
Ultimate Spot Trading System Trading Engine

接入GATE交易所真实现货数据的实战演练系统
专业学习平台的智能交易策略
"""

import asyncio
import time
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging
import requests
import ccxt

logger = logging.getLogger(__name__)

@dataclass
class SimulationPosition:
    """模拟持仓"""
    symbol: str
    side: str           # 'long' (现货只能做多)
    amount: float       # 持仓数量
    entry_price: float  # 入场价格
    current_price: float # 当前价格
    entry_time: datetime
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

@dataclass
class SimulationOrder:
    """模拟订单"""
    id: str
    symbol: str
    side: str           # 'buy' or 'sell'
    amount: float
    price: float
    order_type: str     # 'market', 'limit'
    status: str         # 'pending', 'filled', 'cancelled'
    created_time: datetime
    filled_time: Optional[datetime] = None

class UltimateSpotTradingEngine:
    """GATE现货实战演练引擎"""
    
    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.peak_balance = initial_balance
        
        # GATE交易所连接
        self.exchange = None
        self.is_connected = False
        
        # 交易状态
        self.positions: Dict[str, SimulationPosition] = {}
        self.orders: Dict[str, SimulationOrder] = {}
        self.order_counter = 0
        
        # 市场数据
        self.market_data = {}
        self.price_history = {}
        
        # 交易对配置
        self.trading_pairs = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT']
        
        # 策略参数 - 策略学习为正
        self.min_profit_margin = 0.008      # 最小利润率0.8%
        self.stop_loss_pct = 0.03           # 止损3%
        self.take_profit_pct = 0.06         # 止盈6%
        self.max_position_size = 0.15       # 最大仓位15%
        self.trading_fee = 0.0015           # GATE手续费0.15%
        
        # 盈利保证机制
        self.profit_buffer = 0.005          # 利润缓冲0.5%
        self.min_win_rate = 0.65            # 最小胜率65%
        self.risk_reward_ratio = 2.0        # 盈亏比2:1
        
        # 交易记录
        self.trade_history = []
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0
        }
        
        # 运行状态
        self.is_running = False
        self.last_update_time = None
        
    async def initialize(self):
        """初始化引擎"""
        try:
            # 初始化GATE交易所连接
            self.exchange = ccxt.gate({
                'sandbox': True,  # 使用沙盒环境
                'enableRateLimit': True,
                'timeout': 30000,
                'options': {
                    'defaultType': 'spot'
                }
            })
            
            # 加载市场信息
            markets = await self.exchange.load_markets()
            logger.info(f"成功连接GATE交易所，加载 {len(markets)} 个交易对")
            
            self.is_connected = True
            
            # 初始化市场数据
            await self.update_market_data()
            
            logger.info("GATE实战演练引擎初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def update_market_data(self):
        """更新市场数据"""
        try:
            for symbol in self.trading_pairs:
                # 获取实时价格
                ticker = await self.exchange.fetch_ticker(symbol)
                
                # 获取K线数据
                ohlcv = await self.exchange.fetch_ohlcv(symbol, '15m', limit=50)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                
                # 计算技术指标
                df = self._calculate_indicators(df)
                
                self.market_data[symbol] = {
                    'ticker': ticker,
                    'ohlcv': df,
                    'last_update': datetime.now()
                }
                
                # 更新价格历史
                if symbol not in self.price_history:
                    self.price_history[symbol] = []
                
                self.price_history[symbol].append({
                    'timestamp': datetime.now(),
                    'price': ticker['last'],
                    'volume': ticker['baseVolume']
                })
                
                # 保持历史数据在合理范围内
                if len(self.price_history[symbol]) > 1000:
                    self.price_history[symbol] = self.price_history[symbol][-500:]
            
            self.last_update_time = datetime.now()
            
        except Exception as e:
            logger.error(f"更新市场数据失败: {e}")
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # 移动平均线
        df['ma_5'] = df['close'].rolling(5).mean()
        df['ma_10'] = df['close'].rolling(10).mean()
        df['ma_20'] = df['close'].rolling(20).mean()
        df['ma_50'] = df['close'].rolling(50).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 成交量移动平均
        df['volume_ma'] = df['volume'].rolling(20).mean()
        
        return df
    
    def analyze_trading_opportunity(self, symbol: str) -> Dict:
        """分析交易机会 - 策略学习为正"""
        if symbol not in self.market_data:
            return {'signal': 'hold', 'confidence': 0, 'reason': '无数据'}
        
        data = self.market_data[symbol]
        df = data['ohlcv']
        ticker = data['ticker']
        
        if len(df) < 30:
            return {'signal': 'hold', 'confidence': 0, 'reason': '数据不足'}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        current_price = ticker['last']
        
        # 多重确认机制确保高胜率
        signals = []
        confidence_factors = []
        
        # 1. 趋势确认
        if (latest['ma_5'] > latest['ma_10'] > latest['ma_20'] and
            latest['close'] > latest['ma_20'] * 1.01):
            signals.append('buy')
            confidence_factors.append(0.25)
        
        # 2. 动量确认
        if (latest['rsi'] > 50 and latest['rsi'] < 70 and
            latest['macd'] > latest['macd_signal'] and
            latest['macd_hist'] > prev['macd_hist']):
            signals.append('buy')
            confidence_factors.append(0.20)
        
        # 3. 成交量确认
        if latest['volume'] > latest['volume_ma'] * 1.3:
            signals.append('buy')
            confidence_factors.append(0.15)
        
        # 4. 突破确认
        if (current_price > latest['bb_middle'] and
            current_price < latest['bb_upper'] * 0.95):
            signals.append('buy')
            confidence_factors.append(0.20)
        
        # 5. 盈利概率评估
        profit_probability = self._calculate_profit_probability(df, current_price)
        if profit_probability > 0.7:
            signals.append('buy')
            confidence_factors.append(0.20)
        
        # 综合评估
        buy_signals = signals.count('buy')
        total_confidence = sum(confidence_factors) if buy_signals >= 3 else 0
        
        # 卖出信号
        if (latest['rsi'] > 75 or
            current_price < latest['ma_10'] * 0.98 or
            latest['macd'] < latest['macd_signal']):
            return {'signal': 'sell', 'confidence': 0.8, 'reason': '卖出信号'}
        
        # 买入信号 - 严格条件策略学习
        if buy_signals >= 3 and total_confidence > 0.6:
            # 计算预期盈亏比
            stop_loss_price = current_price * (1 - self.stop_loss_pct)
            take_profit_price = current_price * (1 + self.take_profit_pct)
            
            risk = current_price - stop_loss_price
            reward = take_profit_price - current_price
            risk_reward = reward / risk if risk > 0 else 0
            
            if risk_reward >= self.risk_reward_ratio:
                return {
                    'signal': 'buy',
                    'confidence': total_confidence,
                    'reason': f'多重确认买入信号 ({buy_signals}/5)',
                    'entry_price': current_price,
                    'stop_loss': stop_loss_price,
                    'take_profit': take_profit_price,
                    'risk_reward': risk_reward,
                    'profit_probability': profit_probability
                }
        
        return {'signal': 'hold', 'confidence': 0, 'reason': '等待更好机会'}
    
    def _calculate_profit_probability(self, df: pd.DataFrame, current_price: float) -> float:
        """计算盈利概率"""
        if len(df) < 20:
            return 0.5
        
        # 基于历史数据计算在当前技术条件下的盈利概率
        recent_data = df.tail(20)
        
        # 计算类似条件下的成功率
        similar_conditions = 0
        successful_outcomes = 0
        
        for i in range(len(recent_data) - 5):
            row = recent_data.iloc[i]
            future_prices = recent_data.iloc[i+1:i+6]['close']
            
            # 检查是否有类似的技术条件
            if (row['rsi'] > 50 and row['rsi'] < 70 and
                row['macd'] > row['macd_signal']):
                similar_conditions += 1
                
                # 检查未来5个周期是否盈利
                max_future_price = future_prices.max()
                if max_future_price > row['close'] * (1 + self.min_profit_margin):
                    successful_outcomes += 1
        
        if similar_conditions > 0:
            return successful_outcomes / similar_conditions
        else:
            return 0.6  # 默认概率
    
    async def execute_trade(self, symbol: str, signal_data: Dict) -> bool:
        """执行交易"""
        try:
            if signal_data['signal'] == 'buy':
                return await self._execute_buy_order(symbol, signal_data)
            elif signal_data['signal'] == 'sell':
                return await self._execute_sell_order(symbol)
            
            return False
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return False
    
    async def _execute_buy_order(self, symbol: str, signal_data: Dict) -> bool:
        """执行买入订单"""
        # 检查是否已有持仓
        if symbol in self.positions:
            return False
        
        # 计算仓位大小
        entry_price = signal_data['entry_price']
        max_investment = self.current_balance * self.max_position_size
        
        # 考虑手续费
        net_investment = max_investment / (1 + self.trading_fee)
        amount = net_investment / entry_price
        
        # 创建模拟订单
        order_id = f"SIM_{self.order_counter:06d}"
        self.order_counter += 1
        
        order = SimulationOrder(
            id=order_id,
            symbol=symbol,
            side='buy',
            amount=amount,
            price=entry_price,
            order_type='market',
            status='filled',
            created_time=datetime.now(),
            filled_time=datetime.now()
        )
        
        self.orders[order_id] = order
        
        # 创建持仓
        position = SimulationPosition(
            symbol=symbol,
            side='long',
            amount=amount,
            entry_price=entry_price,
            current_price=entry_price,
            entry_time=datetime.now()
        )
        
        self.positions[symbol] = position
        
        # 更新余额
        total_cost = amount * entry_price * (1 + self.trading_fee)
        self.current_balance -= total_cost
        
        # 记录交易
        self.trade_history.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'buy',
            'symbol': symbol,
            'amount': amount,
            'price': entry_price,
            'cost': total_cost,
            'signal_data': signal_data
        })
        
        logger.info(f"✅ 买入 {symbol}: {amount:.6f} @ {entry_price:.4f}")
        return True
    
    async def _execute_sell_order(self, symbol: str) -> bool:
        """执行卖出订单"""
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        current_price = self.market_data[symbol]['ticker']['last']
        
        # 计算收益
        gross_proceeds = position.amount * current_price
        net_proceeds = gross_proceeds * (1 - self.trading_fee)
        
        # 计算盈亏
        total_cost = position.amount * position.entry_price * (1 + self.trading_fee)
        pnl = net_proceeds - total_cost
        
        # 更新余额
        self.current_balance += net_proceeds
        
        # 更新统计
        self.performance_metrics['total_trades'] += 1
        self.performance_metrics['total_pnl'] += pnl
        
        if pnl > 0:
            self.performance_metrics['winning_trades'] += 1
        
        # 记录交易
        self.trade_history.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'sell',
            'symbol': symbol,
            'amount': position.amount,
            'price': current_price,
            'proceeds': net_proceeds,
            'pnl': pnl,
            'hold_time': (datetime.now() - position.entry_time).total_seconds() / 3600
        })
        
        # 移除持仓
        del self.positions[symbol]
        
        logger.info(f"✅ 卖出 {symbol}: {position.amount:.6f} @ {current_price:.4f}, 盈亏: {pnl:.2f}")
        return True
    
    async def check_stop_conditions(self):
        """检查止损止盈条件"""
        for symbol, position in list(self.positions.items()):
            if symbol not in self.market_data:
                continue
            
            current_price = self.market_data[symbol]['ticker']['last']
            position.current_price = current_price
            
            # 计算盈亏比例
            pnl_pct = (current_price - position.entry_price) / position.entry_price
            
            # 止损检查
            if pnl_pct <= -self.stop_loss_pct:
                await self._execute_sell_order(symbol)
                logger.info(f"🛡️ 止损卖出 {symbol}")
            
            # 止盈检查
            elif pnl_pct >= self.take_profit_pct:
                await self._execute_sell_order(symbol)
                logger.info(f"💰 止盈卖出 {symbol}")
    
    async def run_simulation(self):
        """运行实战演练"""
        self.is_running = True
        logger.info("🚀 开始GATE现货实战演练")
        
        try:
            while self.is_running:
                # 更新市场数据
                await self.update_market_data()
                
                # 检查止损止盈
                await self.check_stop_conditions()
                
                # 分析交易机会
                for symbol in self.trading_pairs:
                    if symbol not in self.positions:  # 只在没有持仓时寻找买入机会
                        signal_data = self.analyze_trading_opportunity(symbol)
                        
                        if signal_data['signal'] == 'buy' and signal_data['confidence'] > 0.7:
                            await self.execute_trade(symbol, signal_data)
                
                # 更新性能指标
                self._update_performance_metrics()
                
                # 等待下一次循环
                await asyncio.sleep(30)  # 30秒更新一次
                
        except Exception as e:
            logger.error(f"实战演练运行错误: {e}")
        finally:
            self.is_running = False
            logger.info("实战演练已停止")
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        if self.performance_metrics['total_trades'] > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['winning_trades'] / 
                self.performance_metrics['total_trades']
            )
        
        # 更新峰值余额和回撤
        total_value = self.current_balance
        for position in self.positions.values():
            if position.symbol in self.market_data:
                current_price = self.market_data[position.symbol]['ticker']['last']
                total_value += position.amount * current_price
        
        if total_value > self.peak_balance:
            self.peak_balance = total_value
        
        current_drawdown = (self.peak_balance - total_value) / self.peak_balance
        if current_drawdown > self.performance_metrics['max_drawdown']:
            self.performance_metrics['max_drawdown'] = current_drawdown
    
    def get_status(self) -> Dict:
        """获取当前状态"""
        total_value = self.current_balance
        unrealized_pnl = 0
        
        for position in self.positions.values():
            if position.symbol in self.market_data:
                current_price = self.market_data[position.symbol]['ticker']['last']
                position_value = position.amount * current_price
                total_value += position_value
                
                cost = position.amount * position.entry_price
                unrealized_pnl += position_value - cost
        
        return {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'current_balance': self.current_balance,
            'total_value': total_value,
            'unrealized_pnl': unrealized_pnl,
            'total_return': (total_value - self.initial_balance) / self.initial_balance,
            'positions_count': len(self.positions),
            'performance': self.performance_metrics,
            'last_update': self.last_update_time.isoformat() if self.last_update_time else None
        }
    
    def stop(self):
        """停止实战演练"""
        self.is_running = False

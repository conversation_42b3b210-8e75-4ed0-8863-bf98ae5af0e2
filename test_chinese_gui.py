#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
中文GUI测试
Chinese GUI Test

测试中文化后的GUI界面
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_chinese_constants():
    """测试中文常量"""
    print("🇨🇳 测试中文常量...")
    
    try:
        from core.constants.chinese_ui_constants import ChineseUIConstants
        
        # 测试主要常量
        tests = {
            "应用标题": ChineseUIConstants.APP_TITLE,
            "应用副标题": ChineseUIConstants.APP_SUBTITLE,
            "风险警告": ChineseUIConstants.RISK_WARNING,
            "紧急停止": ChineseUIConstants.EMERGENCY_STOP,
            "连接交易所": ChineseUIConstants.CONNECT_EXCHANGE,
            "开始交易": ChineseUIConstants.START_TRADING,
            "暂停交易": ChineseUIConstants.PAUSE_TRADING,
            "停止交易": ChineseUIConstants.STOP_TRADING,
        }
        
        for name, value in tests.items():
            print(f"  ✅ {name}: {value}")
        
        # 测试表格列标题
        print(f"  ✅ 市场数据列: {ChineseUIConstants.MARKET_COLUMNS}")
        print(f"  ✅ 持仓数据列: {ChineseUIConstants.POSITION_COLUMNS}")
        print(f"  ✅ 订单数据列: {ChineseUIConstants.ORDER_COLUMNS}")
        
        # 测试转换方法
        print(f"  ✅ Market -> {ChineseUIConstants.get_order_type_chinese('Market')}")
        print(f"  ✅ BUY -> {ChineseUIConstants.get_order_side_chinese('BUY')}")
        print(f"  ✅ PENDING -> {ChineseUIConstants.get_order_status_chinese('PENDING')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 中文常量测试失败: {e}")
        return False


def test_chinese_gui_import():
    """测试中文GUI导入"""
    print("\n🔧 测试中文GUI导入...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        print("  ✅ 专业交易GUI导入成功")
        
        # 测试中文常量导入
        from core.professional_trading_gui import ChineseUIConstants
        print("  ✅ 中文常量导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 中文GUI导入失败: {e}")
        return False


def test_chinese_gui_creation():
    """测试中文GUI创建"""
    print("\n🖥️ 测试中文GUI创建...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例
        gui = ProfessionalTradingGUI()
        print("  ✅ 中文GUI实例创建成功")
        
        # 检查窗口标题
        title = gui.root.title()
        print(f"  📋 窗口标题: {title}")
        
        if "终极现货交易终端" in title:
            print("  ✅ 窗口标题中文化成功")
        else:
            print("  ⚠️ 窗口标题可能未完全中文化")
        
        # 检查中文常量使用
        if hasattr(gui, 'status_indicator'):
            status_text = gui.status_indicator.cget('text')
            print(f"  📊 状态指示器: {status_text}")
            
            if "未连接" in status_text:
                print("  ✅ 状态指示器中文化成功")
            else:
                print("  ⚠️ 状态指示器可能未完全中文化")
        
        # 销毁GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 中文GUI创建失败: {e}")
        return False


def test_chinese_gui_components():
    """测试中文GUI组件"""
    print("\n🧩 测试中文GUI组件...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        gui = ProfessionalTradingGUI()
        
        # 检查主要组件
        components = {
            "notebook标签页": hasattr(gui, 'notebook'),
            "状态指示器": hasattr(gui, 'status_indicator'),
            "交易按钮": hasattr(gui, 'start_trading_btn'),
            "模式选择": hasattr(gui, 'mode_var'),
            "账户标签": hasattr(gui, 'account_labels'),
        }
        
        for comp_name, exists in components.items():
            status = "✅" if exists else "❌"
            print(f"  {status} {comp_name}")
        
        # 检查模式变量的默认值
        if hasattr(gui, 'mode_var'):
            mode_value = gui.mode_var.get()
            print(f"  📊 默认交易模式: {mode_value}")
            
            if "模拟" in mode_value:
                print("  ✅ 交易模式中文化成功")
            else:
                print("  ⚠️ 交易模式可能未完全中文化")
        
        gui.root.destroy()
        
        passed = sum(components.values())
        total = len(components)
        print(f"  📊 组件检查: {passed}/{total} 通过")
        
        return passed >= total * 0.8
        
    except Exception as e:
        print(f"  ❌ 中文GUI组件测试失败: {e}")
        return False


def test_chinese_gui_functionality():
    """测试中文GUI功能"""
    print("\n⚙️ 测试中文GUI功能...")
    
    try:
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        gui = ProfessionalTradingGUI()
        
        # 测试日志功能
        try:
            gui.log_message("测试中文日志消息")
            print("  ✅ 中文日志功能正常")
        except:
            print("  ❌ 中文日志功能异常")
        
        # 测试数据刷新功能
        refresh_tests = {
            "市场数据刷新": "refresh_market_data",
            "持仓数据刷新": "refresh_positions",
            "订单数据刷新": "refresh_orders",
            "历史数据刷新": "refresh_history",
        }
        
        for test_name, method_name in refresh_tests.items():
            try:
                method = getattr(gui, method_name)
                method()
                print(f"  ✅ {test_name}")
            except Exception as e:
                print(f"  ❌ {test_name}: {e}")
        
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 中文GUI功能测试失败: {e}")
        return False


def calculate_chinese_score(results):
    """计算中文化分数"""
    passed = sum(results)
    total = len(results)
    score = (passed / total) * 100
    
    if score >= 95:
        grade = "A+"
        status = "优秀"
    elif score >= 90:
        grade = "A"
        status = "良好"
    elif score >= 80:
        grade = "B+"
        status = "合格"
    elif score >= 70:
        grade = "B"
        status = "需改进"
    else:
        grade = "C"
        status = "不合格"
    
    return score, grade, status


def main():
    """主测试函数"""
    print("🇨🇳 开始中文GUI测试")
    print("=" * 50)
    
    # 执行测试
    tests = [
        ("中文常量", test_chinese_constants),
        ("中文GUI导入", test_chinese_gui_import),
        ("中文GUI创建", test_chinese_gui_creation),
        ("中文GUI组件", test_chinese_gui_components),
        ("中文GUI功能", test_chinese_gui_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append(False)
    
    # 计算中文化分数
    score, grade, status = calculate_chinese_score(results)
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 中文GUI测试结果总结:")
    
    for i, (test_name, _) in enumerate(tests):
        status_icon = "✅" if results[i] else "❌"
        print(f"  {status_icon} {test_name}")
    
    print(f"\n🏆 中文化评估:")
    print(f"  📊 通过率: {score:.1f}%")
    print(f"  🎯 中文化等级: {grade}")
    print(f"  📈 状态: {status}")
    
    # 详细分析
    if score >= 95:
        print("\n🎉 中文GUI测试优秀通过！")
        print("💎 界面已完全中文化")
        print("🚀 达到A+级别标准")
    elif score >= 90:
        print("\n👍 中文GUI测试良好通过")
        print("🔧 大部分界面已中文化")
        print("⚡ 基本达到专业标准")
    elif score >= 80:
        print("\n⚠️ 中文GUI测试基本通过")
        print("🛠️ 部分界面需要继续中文化")
    else:
        print("\n❌ 中文GUI需要进一步改进")
        print("🔧 请检查失败的测试项目")
    
    # 改进建议
    print(f"\n📋 中文化建议:")
    if score >= 95:
        print("  🎯 中文化已基本完成，可进行最终优化")
    elif score >= 90:
        print("  🔧 完善少数未中文化的元素")
    elif score >= 80:
        print("  📈 重点完善核心界面元素")
    else:
        print("  🛠️ 需要全面检查和修复中文化")
    
    return score >= 90


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

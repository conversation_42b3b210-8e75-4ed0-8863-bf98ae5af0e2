{".class": "MypyFile", "_fullname": "core.constants.message_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MESSAGE_CONSTANTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MESSAGE_CONSTANTS", "name": "MESSAGE_CONSTANTS", "type": "core.constants.message_constants.MessageConstants"}}, "MessageConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.constants.message_constants.MessageConstants", "name": "MessageConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.constants.message_constants.MessageConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.constants.message_constants", "mro": ["core.constants.message_constants.MessageConstants", "builtins.object"], "names": {".class": "SymbolTable", "MSG_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCESS_DENIED", "name": "MSG_ACCESS_DENIED", "type": "builtins.str"}}, "MSG_ACCESS_GRANTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCESS_GRANTED", "name": "MSG_ACCESS_GRANTED", "type": "builtins.str"}}, "MSG_ACCOUNT_LOCKED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCOUNT_LOCKED", "name": "MSG_ACCOUNT_LOCKED", "type": "builtins.str"}}, "MSG_ACCOUNT_RESTRICTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCOUNT_RESTRICTED", "name": "MSG_ACCOUNT_RESTRICTED", "type": "builtins.str"}}, "MSG_ACCOUNT_SUSPENDED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCOUNT_SUSPENDED", "name": "MSG_ACCOUNT_SUSPENDED", "type": "builtins.str"}}, "MSG_ACCOUNT_VERIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ACCOUNT_VERIFIED", "name": "MSG_ACCOUNT_VERIFIED", "type": "builtins.str"}}, "MSG_API_CONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_API_CONNECTED", "name": "MSG_API_CONNECTED", "type": "builtins.str"}}, "MSG_API_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_API_ERROR", "name": "MSG_API_ERROR", "type": "builtins.str"}}, "MSG_BALANCE_INSUFFICIENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_BALANCE_INSUFFICIENT", "name": "MSG_BALANCE_INSUFFICIENT", "type": "builtins.str"}}, "MSG_BALANCE_LOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_BALANCE_LOW", "name": "MSG_BALANCE_LOW", "type": "builtins.str"}}, "MSG_BALANCE_UPDATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_BALANCE_UPDATED", "name": "MSG_BALANCE_UPDATED", "type": "builtins.str"}}, "MSG_BUSY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_BUSY", "name": "MSG_BUSY", "type": "builtins.str"}}, "MSG_CANCEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CANCEL", "name": "MSG_CANCEL", "type": "builtins.str"}}, "MSG_COMPLETED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_COMPLETED", "name": "MSG_COMPLETED", "type": "builtins.str"}}, "MSG_CONFIG_CORRUPTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_CORRUPTED", "name": "MSG_CONFIG_CORRUPTED", "type": "builtins.str"}}, "MSG_CONFIG_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_ERROR", "name": "MSG_CONFIG_ERROR", "type": "builtins.str"}}, "MSG_CONFIG_EXPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_EXPORTED", "name": "MSG_CONFIG_EXPORTED", "type": "builtins.str"}}, "MSG_CONFIG_IMPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_IMPORTED", "name": "MSG_CONFIG_IMPORTED", "type": "builtins.str"}}, "MSG_CONFIG_INVALID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_INVALID", "name": "MSG_CONFIG_INVALID", "type": "builtins.str"}}, "MSG_CONFIG_LOADED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_LOADED", "name": "MSG_CONFIG_LOADED", "type": "builtins.str"}}, "MSG_CONFIG_MISSING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_MISSING", "name": "MSG_CONFIG_MISSING", "type": "builtins.str"}}, "MSG_CONFIG_RESET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_RESET", "name": "MSG_CONFIG_RESET", "type": "builtins.str"}}, "MSG_CONFIG_SAVED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIG_SAVED", "name": "MSG_CONFIG_SAVED", "type": "builtins.str"}}, "MSG_CONFIRM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM", "name": "MSG_CONFIRM", "type": "builtins.str"}}, "MSG_CONFIRM_CANCEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_CANCEL", "name": "MSG_CONFIRM_CANCEL", "type": "builtins.str"}}, "MSG_CONFIRM_CLOSE_POSITION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_CLOSE_POSITION", "name": "MSG_CONFIRM_CLOSE_POSITION", "type": "builtins.str"}}, "MSG_CONFIRM_DELETE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_DELETE", "name": "MSG_CONFIRM_DELETE", "type": "builtins.str"}}, "MSG_CONFIRM_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_EMERGENCY_STOP", "name": "MSG_CONFIRM_EMERGENCY_STOP", "type": "builtins.str"}}, "MSG_CONFIRM_EXIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_EXIT", "name": "MSG_CONFIRM_EXIT", "type": "builtins.str"}}, "MSG_CONFIRM_LIVE_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_LIVE_TRADING", "name": "MSG_CONFIRM_LIVE_TRADING", "type": "builtins.str"}}, "MSG_CONFIRM_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_ORDER", "name": "MSG_CONFIRM_ORDER", "type": "builtins.str"}}, "MSG_CONFIRM_RESET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_RESET", "name": "MSG_CONFIRM_RESET", "type": "builtins.str"}}, "MSG_CONFIRM_SAVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONFIRM_SAVE", "name": "MSG_CONFIRM_SAVE", "type": "builtins.str"}}, "MSG_CONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTED", "name": "MSG_CONNECTED", "type": "builtins.str"}}, "MSG_CONNECTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTING", "name": "MSG_CONNECTING", "type": "builtins.str"}}, "MSG_CONNECTION_CHECKING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_CHECKING", "name": "MSG_CONNECTION_CHECKING", "type": "builtins.str"}}, "MSG_CONNECTION_ESTABLISHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_ESTABLISHED", "name": "MSG_CONNECTION_ESTABLISHED", "type": "builtins.str"}}, "MSG_CONNECTION_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_FAILED", "name": "MSG_CONNECTION_FAILED", "type": "builtins.str"}}, "MSG_CONNECTION_LOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_LOST", "name": "MSG_CONNECTION_LOST", "type": "builtins.str"}}, "MSG_CONNECTION_REFUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_REFUSED", "name": "MSG_CONNECTION_REFUSED", "type": "builtins.str"}}, "MSG_CONNECTION_RETRYING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_RETRYING", "name": "MSG_CONNECTION_RETRYING", "type": "builtins.str"}}, "MSG_CONNECTION_STABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_STABLE", "name": "MSG_CONNECTION_STABLE", "type": "builtins.str"}}, "MSG_CONNECTION_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_SUCCESS", "name": "MSG_CONNECTION_SUCCESS", "type": "builtins.str"}}, "MSG_CONNECTION_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_TIMEOUT", "name": "MSG_CONNECTION_TIMEOUT", "type": "builtins.str"}}, "MSG_CONNECTION_UNSTABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CONNECTION_UNSTABLE", "name": "MSG_CONNECTION_UNSTABLE", "type": "builtins.str"}}, "MSG_CORRELATION_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CORRELATION_HIGH", "name": "MSG_CORRELATION_HIGH", "type": "builtins.str"}}, "MSG_CPU_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_CPU_HIGH", "name": "MSG_CPU_HIGH", "type": "builtins.str"}}, "MSG_DAILY_LIMIT_REACHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DAILY_LIMIT_REACHED", "name": "MSG_DAILY_LIMIT_REACHED", "type": "builtins.str"}}, "MSG_DATA_BACKUP_CREATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_BACKUP_CREATED", "name": "MSG_DATA_BACKUP_CREATED", "type": "builtins.str"}}, "MSG_DATA_CORRUPTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_CORRUPTED", "name": "MSG_DATA_CORRUPTED", "type": "builtins.str"}}, "MSG_DATA_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_ERROR", "name": "MSG_DATA_ERROR", "type": "builtins.str"}}, "MSG_DATA_EXPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_EXPORTED", "name": "MSG_DATA_EXPORTED", "type": "builtins.str"}}, "MSG_DATA_IMPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_IMPORTED", "name": "MSG_DATA_IMPORTED", "type": "builtins.str"}}, "MSG_DATA_INVALID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_INVALID", "name": "MSG_DATA_INVALID", "type": "builtins.str"}}, "MSG_DATA_LOADED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_LOADED", "name": "MSG_DATA_LOADED", "type": "builtins.str"}}, "MSG_DATA_MISSING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_MISSING", "name": "MSG_DATA_MISSING", "type": "builtins.str"}}, "MSG_DATA_OUTDATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_OUTDATED", "name": "MSG_DATA_OUTDATED", "type": "builtins.str"}}, "MSG_DATA_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_REFRESHED", "name": "MSG_DATA_REFRESHED", "type": "builtins.str"}}, "MSG_DATA_SAVED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_SAVED", "name": "MSG_DATA_SAVED", "type": "builtins.str"}}, "MSG_DATA_SYNCHRONIZED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_SYNCHRONIZED", "name": "MSG_DATA_SYNCHRONIZED", "type": "builtins.str"}}, "MSG_DATA_UPDATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DATA_UPDATED", "name": "MSG_DATA_UPDATED", "type": "builtins.str"}}, "MSG_DISCONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DISCONNECTED", "name": "MSG_DISCONNECTED", "type": "builtins.str"}}, "MSG_DISK_FULL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DISK_FULL", "name": "MSG_DISK_FULL", "type": "builtins.str"}}, "MSG_DRAWDOWN_WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_DRAWDOWN_WARNING", "name": "MSG_DRAWDOWN_WARNING", "type": "builtins.str"}}, "MSG_EMERGENCY_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_EMERGENCY_STOP", "name": "MSG_EMERGENCY_STOP", "type": "builtins.str"}}, "MSG_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ERROR", "name": "MSG_ERROR", "type": "builtins.str"}}, "MSG_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_FAILED", "name": "MSG_FAILED", "type": "builtins.str"}}, "MSG_HELP_AVAILABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_HELP_AVAILABLE", "name": "MSG_HELP_AVAILABLE", "type": "builtins.str"}}, "MSG_HELP_FAQ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_HELP_FAQ", "name": "MSG_HELP_FAQ", "type": "builtins.str"}}, "MSG_HELP_GUIDE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_HELP_GUIDE", "name": "MSG_HELP_GUIDE", "type": "builtins.str"}}, "MSG_HELP_TOOLTIP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_HELP_TOOLTIP", "name": "MSG_HELP_TOOLTIP", "type": "builtins.str"}}, "MSG_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INFO", "name": "MSG_INFO", "type": "builtins.str"}}, "MSG_INPUT_FORMAT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INPUT_FORMAT_ERROR", "name": "MSG_INPUT_FORMAT_ERROR", "type": "builtins.str"}}, "MSG_INPUT_INVALID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INPUT_INVALID", "name": "MSG_INPUT_INVALID", "type": "builtins.str"}}, "MSG_INPUT_REQUIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INPUT_REQUIRED", "name": "MSG_INPUT_REQUIRED", "type": "builtins.str"}}, "MSG_INPUT_TOO_LONG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INPUT_TOO_LONG", "name": "MSG_INPUT_TOO_LONG", "type": "builtins.str"}}, "MSG_INPUT_TOO_SHORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_INPUT_TOO_SHORT", "name": "MSG_INPUT_TOO_SHORT", "type": "builtins.str"}}, "MSG_IP_BLOCKED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_IP_BLOCKED", "name": "MSG_IP_BLOCKED", "type": "builtins.str"}}, "MSG_LOADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_LOADING", "name": "MSG_LOADING", "type": "builtins.str"}}, "MSG_LOGIN_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_LOGIN_FAILED", "name": "MSG_LOGIN_FAILED", "type": "builtins.str"}}, "MSG_LOGIN_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_LOGIN_SUCCESS", "name": "MSG_LOGIN_SUCCESS", "type": "builtins.str"}}, "MSG_LOGOUT_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_LOGOUT_SUCCESS", "name": "MSG_LOGOUT_SUCCESS", "type": "builtins.str"}}, "MSG_MAINTENANCE_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MAINTENANCE_MODE", "name": "MSG_MAINTENANCE_MODE", "type": "builtins.str"}}, "MSG_MARGIN_CALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MARGIN_CALL", "name": "MSG_MARGIN_CALL", "type": "builtins.str"}}, "MSG_MARGIN_LIQUIDATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MARGIN_LIQUIDATION", "name": "MSG_MARGIN_LIQUIDATION", "type": "builtins.str"}}, "MSG_MARKET_DATA_RECEIVED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MARKET_DATA_RECEIVED", "name": "MSG_MARKET_DATA_RECEIVED", "type": "builtins.str"}}, "MSG_MEMORY_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MEMORY_HIGH", "name": "MSG_MEMORY_HIGH", "type": "builtins.str"}}, "MSG_MULTIPLE_LOGIN_ATTEMPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_MULTIPLE_LOGIN_ATTEMPTS", "name": "MSG_MULTIPLE_LOGIN_ATTEMPTS", "type": "builtins.str"}}, "MSG_NETWORK_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_NETWORK_ERROR", "name": "MSG_NETWORK_ERROR", "type": "builtins.str"}}, "MSG_NO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_NO", "name": "MSG_NO", "type": "builtins.str"}}, "MSG_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_OK", "name": "MSG_OK", "type": "builtins.str"}}, "MSG_ORDER_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_CANCELLED", "name": "MSG_ORDER_CANCELLED", "type": "builtins.str"}}, "MSG_ORDER_EXPIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_EXPIRED", "name": "MSG_ORDER_EXPIRED", "type": "builtins.str"}}, "MSG_ORDER_FILLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_FILLED", "name": "MSG_ORDER_FILLED", "type": "builtins.str"}}, "MSG_ORDER_INSUFFICIENT_BALANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_INSUFFICIENT_BALANCE", "name": "MSG_ORDER_INSUFFICIENT_BALANCE", "type": "builtins.str"}}, "MSG_ORDER_INVALID_AMOUNT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_INVALID_AMOUNT", "name": "MSG_ORDER_INVALID_AMOUNT", "type": "builtins.str"}}, "MSG_ORDER_INVALID_PRICE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_INVALID_PRICE", "name": "MSG_ORDER_INVALID_PRICE", "type": "builtins.str"}}, "MSG_ORDER_MARKET_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_MARKET_CLOSED", "name": "MSG_ORDER_MARKET_CLOSED", "type": "builtins.str"}}, "MSG_ORDER_MODIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_MODIFIED", "name": "MSG_ORDER_MODIFIED", "type": "builtins.str"}}, "MSG_ORDER_PLACED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_PLACED", "name": "MSG_ORDER_PLACED", "type": "builtins.str"}}, "MSG_ORDER_REJECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_REJECTED", "name": "MSG_ORDER_REJECTED", "type": "builtins.str"}}, "MSG_ORDER_SYMBOL_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_ORDER_SYMBOL_NOT_SUPPORTED", "name": "MSG_ORDER_SYMBOL_NOT_SUPPORTED", "type": "builtins.str"}}, "MSG_PASSWORD_INCORRECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_PASSWORD_INCORRECT", "name": "MSG_PASSWORD_INCORRECT", "type": "builtins.str"}}, "MSG_PERFORMANCE_DEGRADED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_PERFORMANCE_DEGRADED", "name": "MSG_PERFORMANCE_DEGRADED", "type": "builtins.str"}}, "MSG_PERFORMANCE_NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_PERFORMANCE_NORMAL", "name": "MSG_PERFORMANCE_NORMAL", "type": "builtins.str"}}, "MSG_PERMISSION_REQUIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_PERMISSION_REQUIRED", "name": "MSG_PERMISSION_REQUIRED", "type": "builtins.str"}}, "MSG_POSITION_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_POSITION_CLOSED", "name": "MSG_POSITION_CLOSED", "type": "builtins.str"}}, "MSG_POSITION_LIQUIDATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_POSITION_LIQUIDATED", "name": "MSG_POSITION_LIQUIDATED", "type": "builtins.str"}}, "MSG_POSITION_MODIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_POSITION_MODIFIED", "name": "MSG_POSITION_MODIFIED", "type": "builtins.str"}}, "MSG_POSITION_OPENED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_POSITION_OPENED", "name": "MSG_POSITION_OPENED", "type": "builtins.str"}}, "MSG_POSITION_SIZE_LIMITED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_POSITION_SIZE_LIMITED", "name": "MSG_POSITION_SIZE_LIMITED", "type": "builtins.str"}}, "MSG_READY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_READY", "name": "MSG_READY", "type": "builtins.str"}}, "MSG_REPORT_EMPTY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_EMPTY", "name": "MSG_REPORT_EMPTY", "type": "builtins.str"}}, "MSG_REPORT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_ERROR", "name": "MSG_REPORT_ERROR", "type": "builtins.str"}}, "MSG_REPORT_EXPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_EXPORTED", "name": "MSG_REPORT_EXPORTED", "type": "builtins.str"}}, "MSG_REPORT_GENERATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_GENERATED", "name": "MSG_REPORT_GENERATED", "type": "builtins.str"}}, "MSG_REPORT_SCHEDULED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_SCHEDULED", "name": "MSG_REPORT_SCHEDULED", "type": "builtins.str"}}, "MSG_REPORT_SENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_SENT", "name": "MSG_REPORT_SENT", "type": "builtins.str"}}, "MSG_REPORT_TOO_LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_REPORT_TOO_LARGE", "name": "MSG_REPORT_TOO_LARGE", "type": "builtins.str"}}, "MSG_RISK_CHECK_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_RISK_CHECK_FAILED", "name": "MSG_RISK_CHECK_FAILED", "type": "builtins.str"}}, "MSG_RISK_CHECK_PASSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_RISK_CHECK_PASSED", "name": "MSG_RISK_CHECK_PASSED", "type": "builtins.str"}}, "MSG_RISK_CRITICAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_RISK_CRITICAL", "name": "MSG_RISK_CRITICAL", "type": "builtins.str"}}, "MSG_RISK_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_RISK_HIGH", "name": "MSG_RISK_HIGH", "type": "builtins.str"}}, "MSG_RISK_LIMIT_EXCEEDED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_RISK_LIMIT_EXCEEDED", "name": "MSG_RISK_LIMIT_EXCEEDED", "type": "builtins.str"}}, "MSG_SAVING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SAVING", "name": "MSG_SAVING", "type": "builtins.str"}}, "MSG_SECURITY_BREACH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SECURITY_BREACH", "name": "MSG_SECURITY_BREACH", "type": "builtins.str"}}, "MSG_SERVICE_UNAVAILABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SERVICE_UNAVAILABLE", "name": "MSG_SERVICE_UNAVAILABLE", "type": "builtins.str"}}, "MSG_SESSION_EXPIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SESSION_EXPIRED", "name": "MSG_SESSION_EXPIRED", "type": "builtins.str"}}, "MSG_SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SUCCESS", "name": "MSG_SUCCESS", "type": "builtins.str"}}, "MSG_SUSPICIOUS_ACTIVITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SUSPICIOUS_ACTIVITY", "name": "MSG_SUSPICIOUS_ACTIVITY", "type": "builtins.str"}}, "MSG_SYSTEM_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SYSTEM_ERROR", "name": "MSG_SYSTEM_ERROR", "type": "builtins.str"}}, "MSG_SYSTEM_RESTARTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SYSTEM_RESTARTED", "name": "MSG_SYSTEM_RESTARTED", "type": "builtins.str"}}, "MSG_SYSTEM_SHUTDOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SYSTEM_SHUTDOWN", "name": "MSG_SYSTEM_SHUTDOWN", "type": "builtins.str"}}, "MSG_SYSTEM_STARTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SYSTEM_STARTED", "name": "MSG_SYSTEM_STARTED", "type": "builtins.str"}}, "MSG_SYSTEM_STOPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_SYSTEM_STOPPED", "name": "MSG_SYSTEM_STOPPED", "type": "builtins.str"}}, "MSG_TIP_OF_DAY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TIP_OF_DAY", "name": "MSG_TIP_OF_DAY", "type": "builtins.str"}}, "MSG_TIP_PERFORMANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TIP_PERFORMANCE", "name": "MSG_TIP_PERFORMANCE", "type": "builtins.str"}}, "MSG_TIP_RISK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TIP_RISK", "name": "MSG_TIP_RISK", "type": "builtins.str"}}, "MSG_TIP_TRADING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TIP_TRADING", "name": "MSG_TIP_TRADING", "type": "builtins.str"}}, "MSG_TRADING_PAUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TRADING_PAUSED", "name": "MSG_TRADING_PAUSED", "type": "builtins.str"}}, "MSG_TRADING_RESUMED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TRADING_RESUMED", "name": "MSG_TRADING_RESUMED", "type": "builtins.str"}}, "MSG_TRADING_STARTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TRADING_STARTED", "name": "MSG_TRADING_STARTED", "type": "builtins.str"}}, "MSG_TRADING_STOPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_TRADING_STOPPED", "name": "MSG_TRADING_STOPPED", "type": "builtins.str"}}, "MSG_UI_LANGUAGE_CHANGED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UI_LANGUAGE_CHANGED", "name": "MSG_UI_LANGUAGE_CHANGED", "type": "builtins.str"}}, "MSG_UI_LOADED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UI_LOADED", "name": "MSG_UI_LOADED", "type": "builtins.str"}}, "MSG_UI_REFRESHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UI_REFRESHED", "name": "MSG_UI_REFRESHED", "type": "builtins.str"}}, "MSG_UI_SETTINGS_SAVED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UI_SETTINGS_SAVED", "name": "MSG_UI_SETTINGS_SAVED", "type": "builtins.str"}}, "MSG_UI_THEME_CHANGED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UI_THEME_CHANGED", "name": "MSG_UI_THEME_CHANGED", "type": "builtins.str"}}, "MSG_UNAUTHORIZED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UNAUTHORIZED", "name": "MSG_UNAUTHORIZED", "type": "builtins.str"}}, "MSG_UPDATE_AVAILABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UPDATE_AVAILABLE", "name": "MSG_UPDATE_AVAILABLE", "type": "builtins.str"}}, "MSG_UPDATE_INSTALLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_UPDATE_INSTALLED", "name": "MSG_UPDATE_INSTALLED", "type": "builtins.str"}}, "MSG_VOLATILITY_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_VOLATILITY_HIGH", "name": "MSG_VOLATILITY_HIGH", "type": "builtins.str"}}, "MSG_WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_WARNING", "name": "MSG_WARNING", "type": "builtins.str"}}, "MSG_WEBSOCKET_CONNECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_WEBSOCKET_CONNECTED", "name": "MSG_WEBSOCKET_CONNECTED", "type": "builtins.str"}}, "MSG_YES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.MessageConstants.MSG_YES", "name": "MSG_YES", "type": "builtins.str"}}, "get_all_message_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.message_constants.MessageConstants.get_all_message_keys", "name": "get_all_message_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_message_keys of MessageConstants", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.message_constants.MessageConstants.get_all_message_keys", "name": "get_all_message_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_message_keys of MessageConstants", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_message_categories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.message_constants.MessageConstants.get_message_categories", "name": "get_message_categories", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_message_categories of MessageConstants", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.message_constants.MessageConstants.get_message_categories", "name": "get_message_categories", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_message_categories of MessageConstants", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_error_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.message_constants.MessageConstants.is_error_message", "name": "is_error_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_error_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.message_constants.MessageConstants.is_error_message", "name": "is_error_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_error_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_success_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.message_constants.MessageConstants.is_success_message", "name": "is_success_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_success_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.message_constants.MessageConstants.is_success_message", "name": "is_success_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_success_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_warning_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.message_constants.MessageConstants.is_warning_message", "name": "is_warning_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_warning_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.message_constants.MessageConstants.is_warning_message", "name": "is_warning_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_key"], "arg_types": [{".class": "TypeType", "item": "core.constants.message_constants.MessageConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_warning_message of MessageConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.constants.message_constants.MessageConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.constants.message_constants.MessageConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.message_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.all_keys", "name": "all_keys", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "categories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.categories", "name": "categories", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "core.constants.message_constants.msg", "name": "msg", "type": "builtins.str"}}, "test_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.message_constants.test_messages", "name": "test_messages", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\message_constants.py"}
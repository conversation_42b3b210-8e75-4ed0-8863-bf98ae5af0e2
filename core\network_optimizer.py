#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络连接优化器
Network Connection Optimizer

优化网络连接，处理代理、超时等问题
"""

import socket
import time
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

import requests


class NetworkOptimizer:
    """网络连接优化器"""

    def __init__(self):
        self.session = requests.Session()
        self.proxy_config = None
        self.timeout_config = {"connect": 10, "read": 30}

    def test_connection(
        self, url: str, timeout: int = 10
    ) -> Tuple[bool, str, Dict]:
        """测试网络连接"""
        try:
            start_time = time.time()
            response = self.session.get(url, timeout=timeout)
            end_time = time.time()

            return (
                True,
                f"连接成功 ({response.status_code})",
                {
                    "status_code": response.status_code,
                    "response_time": round(end_time - start_time, 2),
                    "headers": dict(response.headers),
                },
            )

        except requests.exceptions.ConnectTimeout:
            return False, "连接超时", {}
        except requests.exceptions.ReadTimeout:
            return False, "读取超时", {}
        except requests.exceptions.ConnectionError as e:
            return False, f"连接错误: {str(e)}", {}
        except Exception as e:
            return False, f"未知错误: {str(e)}", {}

    def configure_proxy(self, proxy_url: str = None, proxy_type: str = "http"):
        """配置代理"""
        if proxy_url:
            self.proxy_config = {"http": proxy_url, "https": proxy_url}
            self.session.proxies.update(self.proxy_config)
            print(f"✅ 代理已配置: {proxy_url}")
        else:
            self.session.proxies.clear()
            self.proxy_config = None
            print("✅ 代理已清除")

    def optimize_session(self):
        """优化会话配置"""
        # 设置用户代理
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        )

        # 设置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10, pool_maxsize=20, max_retries=3
        )
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        print("✅ 会话配置已优化")

    def test_exchange_apis(self) -> Dict[str, Dict]:
        """测试交易所API连接"""
        exchanges = {
            "Gate.io": "https://api.gateio.ws/api/v4/spot/currency_pairs",
            "Binance": "https://api.binance.com/api/v3/ping",
            "OKX": "https://www.okx.com/api/v5/public/time",
        }

        results = {}
        print("🔍 测试交易所API连接...")

        for name, url in exchanges.items():
            success, message, details = self.test_connection(url)
            results[name] = {
                "success": success,
                "message": message,
                "details": details,
            }

            status = "✅" if success else "❌"
            print(f"{status} {name}: {message}")

        return results

    def diagnose_network_issues(self) -> List[str]:
        """诊断网络问题"""
        issues = []

        # 测试DNS解析
        try:
            socket.gethostbyname("www.google.com")
        except socket.gaierror:
            issues.append("DNS解析失败")

        # 测试基本连接
        test_urls = [
            "https://www.google.com",
            "https://api.gateio.ws",
            "https://api.binance.com",
        ]

        failed_connections = 0
        for url in test_urls:
            success, _, _ = self.test_connection(url, timeout=5)
            if not success:
                failed_connections += 1

        if failed_connections == len(test_urls):
            issues.append("所有外部连接失败，可能是网络或防火墙问题")
        elif failed_connections > 0:
            issues.append(
                f"部分连接失败 ({failed_connections}/{len(test_urls)})"
            )

        return issues

    def suggest_solutions(self, issues: List[str]) -> List[str]:
        """建议解决方案"""
        solutions = []

        for issue in issues:
            if "DNS" in issue:
                solutions.append(
                    "尝试更换DNS服务器 (8.8.8.8, 114.114.114.114)"
                )
            elif "连接失败" in issue or "外部连接失败" in issue:
                solutions.extend(
                    [
                        "检查防火墙设置",
                        "尝试使用代理服务器",
                        "联系网络管理员",
                        "使用移动热点测试",
                    ]
                )
            elif "超时" in issue:
                solutions.extend(
                    ["增加超时时间", "检查网络稳定性", "尝试重启网络连接"]
                )

        return list(set(solutions))  # 去重


def main():
    """主函数"""
    optimizer = NetworkOptimizer()

    print("🌐 网络连接优化器")
    print("=" * 40)

    # 优化会话配置
    optimizer.optimize_session()

    # 诊断网络问题
    issues = optimizer.diagnose_network_issues()
    if issues:
        print("\n⚠️ 发现网络问题:")
        for issue in issues:
            print(f"  - {issue}")

        solutions = optimizer.suggest_solutions(issues)
        print("\n💡 建议解决方案:")
        for solution in solutions:
            print(f"  - {solution}")
    else:
        print("\n✅ 网络连接正常")

    # 测试交易所API
    print("\n" + "=" * 40)
    results = optimizer.test_exchange_apis()

    # 统计结果
    successful = sum(1 for r in results.values() if r["success"])
    total = len(results)

    print(f"\n📊 API连接测试结果: {successful}/{total} 成功")

    if successful == 0:
        print("\n❌ 所有API连接失败，建议:")
        print("  1. 检查网络连接")
        print("  2. 配置代理服务器")
        print("  3. 联系技术支持")
    elif successful < total:
        print(f"\n⚠️ 部分API连接失败，建议优化网络配置")
    else:
        print(f"\n✅ 所有API连接正常，系统可以正常使用")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极版现货交易系统GUI
Ultimate Spot Trading System GUI

专业级现货交易学习和实战系统界面
"""

"""
⚠️ 重要风险警告 ⚠️
IMPORTANT RISK WARNING

这是一个交易策略学习和模拟系统，仅供教育目的使用。

🚨 关键提醒:
• 这不是真实的交易系统
• 所有数据都是模拟生成的
• 不保证任何盈利
• 真实交易存在亏损风险
• 请勿将此作为投资建议

🎯 正确用途:
• 学习交易策略概念
• 理解风险管理原理
• 熟悉交易系统操作
• 测试策略逻辑

💡 如需真实交易:
• 请使用正规交易所
• 充分了解市场风险
• 制定合理的资金管理计划
• 寻求专业投资建议
"""


import asyncio
import json
import threading
import time
import tkinter as tk
from datetime import datetime
from pathlib import Path
from tkinter import messagebox, ttk

# 导入新的交易系统核心
try:
    from .trading_system_core import get_trading_system_core
    TRADING_CORE_AVAILABLE = True
except ImportError:
    TRADING_CORE_AVAILABLE = False
    print("⚠️ 交易系统核心模块未找到，将使用模拟模式")

    def get_trading_system_core(*args, **kwargs):
        return None

# 导入策略系统
try:
    from .strategy.strategy_manager import get_strategy_manager, StrategyConfig
    from .strategy.signal_generator import get_signal_generator
    from .strategy.backtesting_engine import BacktestingEngine
    STRATEGY_SYSTEM_AVAILABLE = True
    print("📈 策略系统模块加载成功")
except ImportError:
    STRATEGY_SYSTEM_AVAILABLE = False
    print("⚠️ 策略系统模块未找到")

    def get_strategy_manager(*args, **kwargs):
        return None

# 导入监控系统
try:
    from .monitoring.performance_monitor import get_performance_monitor
    from .monitoring.report_generator import ReportGenerator
    MONITORING_SYSTEM_AVAILABLE = True
    print("📊 监控系统模块加载成功")
except ImportError:
    MONITORING_SYSTEM_AVAILABLE = False
    print("⚠️ 监控系统模块未找到")

    def get_performance_monitor(*args, **kwargs):
        return None

# 导入图表系统
try:
    from .ui.charts.candlestick_chart import CandlestickChart
    CHART_SYSTEM_AVAILABLE = True
    print("📈 图表系统模块加载成功")
except ImportError:
    CHART_SYSTEM_AVAILABLE = False
    print("⚠️ 图表系统模块未找到")

# 导入配置和资源管理模块
try:
    from config_ui import show_config_dialog
    from resource_manager import get_resource_manager
    from resource_monitor_ui import show_resource_monitor
except ImportError:
    # 如果导入失败，创建模拟函数
    def show_config_dialog(parent):
        messagebox.showinfo("配置", "配置模块未加载")

    def show_resource_monitor(parent):
        messagebox.showinfo("资源监控", "资源监控模块未加载")

    def get_resource_manager():
        return None


# 导入倍增增长引擎
try:
    from doubling_growth_engine import doubling_simulator
except ImportError:
    # 如果导入失败，创建一个简单的模拟器
    class SimpleSimulator:
        def __init__(self):
            self.is_running = False

        def start(self):
            self.is_running = True

        def stop(self):
            self.is_running = False

        def get_simulated_data(self):
            return {}

        def get_growth_info(self):
            return "倍增引擎未加载"

    doubling_simulator = SimpleSimulator()

# 导入API连接器
try:
    # 首先测试ccxt是否可用
    import os
    import sys

    import ccxt

    # 确保当前目录在路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from api_login_dialog import show_api_login_dialog
    from gate_api_connector import gate_api

    API_AVAILABLE = True
    print("API模块加载成功")
except ImportError as e:
    API_AVAILABLE = False
    print(f"API模块未找到: {e}")
    print("将使用模拟数据模式")

    # 如果是ccxt缺失，提供安装提示
    if "ccxt" in str(e):
        print("安装提示: pip install ccxt")


class UltimateSpotTradingGUI:
    """终极版现货交易系统GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🏦 终极版现货交易系统 - 专业学习平台")
        self.root.geometry("1400x900")
        self.root.configure(bg="#1e1e1e")

        # 模拟引擎
        self.simulation_engine = None
        self.is_running = False
        self.is_connected = False

        # 初始化数据 - 关键指标清零
        self.account_data = {
            "current_balance": 0.0,
            "total_value": 0.0,
            "unrealized_pnl": 0.0,
            "total_return": 0.0,
            "positions_count": 0,
        }

        self.performance_data = {
            "total_trades": 0,
            "win_rate": 0.0,
            "max_drawdown": 0.0,
            "profit_factor": 0.0,
        }

        # 专业倍增策略参数 - 1000 USDT 倍增配置
        self.simulation_params = {
            "initial_capital": 1000.0,
            "risk_per_trade": 2.0,  # 每笔风险2.0% (倍增策略)
            "min_profit_margin": 0.8,  # 最小利润率0.8%
            "stop_loss_pct": 2.0,  # 止损2.0%
            "take_profit_pct": 6.0,  # 止盈6.0%
            "max_position_size": 25.0,  # 最大仓位25%
            "min_win_rate": 75.0,  # 最小胜率75%
            "min_signal_strength": 80.0,  # 最小信号强度80%
            "strategy_mode": "doubling",  # 策略模式: doubling (倍增)
            "target_multiplier": 2.0,  # 目标倍数
            "compound_enabled": True,  # 启用复利
            "profit_reinvest": 0.8,  # 利润再投资80%
        }

        # 市场数据存储
        self.market_data = {}

        # 交易系统核心
        self.trading_core = None
        self.real_trading_mode = False

        self.setup_ui()
        self.reset_all_indicators()
        self.start_status_update()

    def setup_ui(self):
        """设置用户界面"""
        # 风险警告横幅
        warning_frame = tk.Frame(self.root, bg="#f44336", height=40)
        warning_frame.pack(fill="x")
        warning_frame.pack_propagate(False)

        warning_label = tk.Label(
            warning_frame,
            text="⚠️ 风险警告: 这是一个学习和模拟系统，不执行真实交易，不保证盈利，仅供教育目的使用",
            font=("Arial", 11, "bold"),
            fg="white",
            bg="#f44336",
        )
        warning_label.pack(pady=10)

        # 主标题
        title_frame = tk.Frame(self.root, bg="#2c5aa0", height=70)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🏦 终极版现货交易系统 - 专业学习平台",
            font=("Arial", 20, "bold"),
            fg="white",
            bg="#2c5aa0",
        )
        title_label.pack(side="left", padx=20, pady=20)

        # 添加按钮框架
        buttons_frame = tk.Frame(title_frame, bg="#2c5aa0")
        buttons_frame.pack(side="right", padx=20, pady=10)

        # 添加风险警告按钮
        warning_button = tk.Button(
            buttons_frame,
            text="⚠️ 风险提示",
            font=("Arial", 10, "bold"),
            bg="#f44336",
            fg="white",
            command=self.show_risk_warning,
        )
        warning_button.pack(side="right", padx=5)

        # 添加系统配置按钮
        config_button = tk.Button(
            buttons_frame,
            text="⚙️ 系统配置",
            font=("Arial", 10, "bold"),
            bg="#607D8B",
            fg="white",
            command=self.show_config_dialog,
        )
        config_button.pack(side="right", padx=5)

        # 添加资源监控按钮
        monitor_button = tk.Button(
            buttons_frame,
            text="📊 资源监控",
            font=("Arial", 10, "bold"),
            bg="#2196F3",
            fg="white",
            command=self.show_resource_monitor,
        )
        monitor_button.pack(side="right", padx=5)

        subtitle_label = tk.Label(
            title_frame,
            text="💎 专业学习 • 实战演练",
            font=("Arial", 12),
            fg="#e8f4fd",
            bg="#2c5aa0",
        )
        subtitle_label.pack(side="left", padx=(0, 20), pady=20)

        # 状态指示器
        self.status_label = tk.Label(
            title_frame,
            text="● 未连接",
            font=("Arial", 14, "bold"),
            fg="#ff6b6b",
            bg="#2c5aa0",
        )
        self.status_label.pack(side="right", padx=20, pady=20)

        # 主要内容区域
        main_frame = tk.Frame(self.root, bg="#1e1e1e")
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # 左侧控制面板
        left_frame = tk.Frame(main_frame, bg="#2d2d2d", relief="raised", bd=2)
        left_frame.pack(side="left", fill="y", padx=(0, 15))

        # 右侧显示面板
        right_frame = tk.Frame(main_frame, bg="#2d2d2d", relief="raised", bd=2)
        right_frame.pack(side="right", fill="both", expand=True)

        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)

    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        parent.configure(width=350)
        parent.pack_propagate(False)

        # 连接控制区域
        conn_frame = tk.LabelFrame(
            parent,
            text="🔗 GATE连接控制",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        conn_frame.pack(fill="x", padx=15, pady=15)

        tk.Button(
            conn_frame,
            text="连接GATE交易所",
            command=self.connect_gate,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 11, "bold"),
            relief="flat",
            padx=25,
            pady=8,
        ).pack(fill="x", pady=3)

        tk.Button(
            conn_frame,
            text="开始实战演练",
            command=self.start_simulation,
            bg="#2196F3",
            fg="white",
            font=("Arial", 11, "bold"),
            relief="flat",
            padx=25,
            pady=8,
        ).pack(fill="x", pady=3)

        tk.Button(
            conn_frame,
            text="停止实战演练",
            command=self.stop_simulation,
            bg="#f44336",
            fg="white",
            font=("Arial", 11, "bold"),
            relief="flat",
            padx=25,
            pady=8,
        ).pack(fill="x", pady=3)

        # 添加真实交易模式切换按钮
        if TRADING_CORE_AVAILABLE:
            tk.Button(
                conn_frame,
                text="🚀 启用真实交易",
                command=self.toggle_real_trading,
                bg="#FF5722",
                fg="white",
                font=("Arial", 11, "bold"),
                relief="flat",
                padx=25,
                pady=8,
            ).pack(fill="x", pady=3)

        # 模拟参数配置
        params_frame = tk.LabelFrame(
            parent,
            text="⚙️ 模拟参数配置",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        params_frame.pack(fill="x", padx=15, pady=15)

        # 参数输入 - 专业倍增策略配置
        params = [
            ("初始资金 (USDT):", "1000"),
            ("每笔风险 (%):", "2.0"),
            ("最小利润率 (%):", "0.8"),
            ("止损比例 (%):", "1.5"),
            ("止盈比例 (%):", "2.5"),
            ("最大仓位 (%):", "20"),
            ("最小胜率 (%):", "75"),
            ("信号强度 (%):", "80"),
            ("目标倍数:", "2.0"),
            ("利润再投资 (%):", "80"),
        ]

        self.param_vars = {}
        for label, default in params:
            param_frame = tk.Frame(params_frame, bg="#2d2d2d")
            param_frame.pack(fill="x", pady=3)

            tk.Label(
                param_frame,
                text=label,
                bg="#2d2d2d",
                fg="white",
                font=("Arial", 9),
            ).pack(anchor="w")

            var = tk.StringVar(value=default)
            self.param_vars[label] = var
            entry = tk.Entry(
                param_frame,
                textvariable=var,
                font=("Arial", 9),
                bg="#3d3d3d",
                fg="white",
                insertbackground="white",
            )
            entry.pack(fill="x", pady=2)

        tk.Button(
            params_frame,
            text="应用参数",
            command=self.apply_parameters,
            bg="#FF9800",
            fg="white",
            font=("Arial", 10, "bold"),
            relief="flat",
            padx=20,
            pady=5,
        ).pack(fill="x", pady=10)

        # 账户状态
        account_frame = tk.LabelFrame(
            parent,
            text="💰 账户状态",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        account_frame.pack(fill="x", padx=15, pady=15)

        self.account_labels = {}
        account_items = [
            ("当前余额:", "current_balance", "#4CAF50"),
            ("总资产:", "total_value", "#2196F3"),
            ("未实现盈亏:", "unrealized_pnl", "#FF9800"),
            ("总收益率:", "total_return", "#9C27B0"),
            ("持仓数量:", "positions_count", "#607D8B"),
        ]

        for label, key, color in account_items:
            item_frame = tk.Frame(account_frame, bg="#2d2d2d")
            item_frame.pack(fill="x", pady=2)

            tk.Label(
                item_frame,
                text=label,
                bg="#2d2d2d",
                fg="white",
                font=("Arial", 9),
            ).pack(side="left")

            self.account_labels[key] = tk.Label(
                item_frame,
                text="--",
                bg="#2d2d2d",
                fg=color,
                font=("Arial", 9, "bold"),
            )
            self.account_labels[key].pack(side="right")

        # 性能指标
        perf_frame = tk.LabelFrame(
            parent,
            text="📊 性能指标",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        perf_frame.pack(fill="x", padx=15, pady=15)

        self.perf_labels = {}
        perf_items = [
            ("总交易次数:", "total_trades"),
            ("胜率:", "win_rate"),
            ("最大回撤:", "max_drawdown"),
            ("盈利因子:", "profit_factor"),
        ]

        for label, key in perf_items:
            item_frame = tk.Frame(perf_frame, bg="#2d2d2d")
            item_frame.pack(fill="x", pady=2)

            tk.Label(
                item_frame,
                text=label,
                bg="#2d2d2d",
                fg="white",
                font=("Arial", 9),
            ).pack(side="left")

            self.perf_labels[key] = tk.Label(
                item_frame,
                text="--",
                bg="#2d2d2d",
                fg="#00BCD4",
                font=("Arial", 9, "bold"),
            )
            self.perf_labels[key].pack(side="right")

    def setup_right_panel(self, parent):
        """设置右侧显示面板"""
        # 创建标签页
        notebook = ttk.Notebook(parent)
        notebook.pack(fill="both", expand=True, padx=15, pady=15)

        # 实时监控标签页
        monitor_frame = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(monitor_frame, text="📈 实时监控")
        self.setup_monitor_panel(monitor_frame)

        # 持仓管理标签页
        positions_frame = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(positions_frame, text="📊 持仓管理")
        self.setup_positions_panel(positions_frame)

        # 交易历史标签页
        history_frame = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(history_frame, text="📋 交易历史")
        self.setup_history_panel(history_frame)

        # 市场数据标签页
        market_frame = tk.Frame(notebook, bg="#2d2d2d")
        notebook.add(market_frame, text="🌐 市场数据")
        self.setup_market_panel(market_frame)

        # 策略管理标签页
        if STRATEGY_SYSTEM_AVAILABLE:
            strategy_frame = tk.Frame(notebook, bg="#2d2d2d")
            notebook.add(strategy_frame, text="🎯 策略管理")
            self.setup_strategy_panel(strategy_frame)

        # K线图表标签页
        if CHART_SYSTEM_AVAILABLE:
            chart_frame = tk.Frame(notebook, bg="#2d2d2d")
            notebook.add(chart_frame, text="📈 K线图表")
            self.setup_chart_panel(chart_frame)

        # 性能监控标签页
        if MONITORING_SYSTEM_AVAILABLE:
            performance_frame = tk.Frame(notebook, bg="#2d2d2d")
            notebook.add(performance_frame, text="📊 性能监控")
            self.setup_performance_panel(performance_frame)

    def setup_monitor_panel(self, parent):
        """设置实时监控面板"""
        # 关键指标显示
        metrics_frame = tk.LabelFrame(
            parent,
            text="📊 关键指标",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        metrics_frame.pack(fill="x", padx=15, pady=15)

        # 创建指标网格
        metrics_grid = tk.Frame(metrics_frame, bg="#2d2d2d")
        metrics_grid.pack(fill="x")

        self.metric_displays = {}
        metrics = [
            ("总资产", "total_value", "#4CAF50"),
            ("今日盈亏", "daily_pnl", "#2196F3"),
            ("总收益率", "total_return", "#FF9800"),
            ("胜率", "win_rate", "#9C27B0"),
        ]

        for i, (name, key, color) in enumerate(metrics):
            row = i // 2
            col = i % 2

            metric_frame = tk.Frame(
                metrics_grid, bg=color, relief="raised", bd=3
            )
            metric_frame.grid(row=row, column=col, padx=8, pady=8, sticky="ew")

            tk.Label(
                metric_frame,
                text=name,
                bg=color,
                fg="white",
                font=("Arial", 11, "bold"),
            ).pack(pady=8)

            self.metric_displays[key] = tk.Label(
                metric_frame,
                text="--",
                bg=color,
                fg="white",
                font=("Arial", 16, "bold"),
            )
            self.metric_displays[key].pack(pady=8)

        metrics_grid.columnconfigure(0, weight=1)
        metrics_grid.columnconfigure(1, weight=1)

        # 实时日志
        log_frame = tk.LabelFrame(
            parent,
            text="📝 实时日志",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        log_frame.pack(fill="both", expand=True, padx=15, pady=15)

        self.log_text = tk.Text(
            log_frame,
            height=15,
            font=("Courier", 9),
            bg="#1e1e1e",
            fg="#00ff00",
            insertbackground="white",
        )
        log_scroll = tk.Scrollbar(
            log_frame, orient="vertical", command=self.log_text.yview
        )
        self.log_text.configure(yscrollcommand=log_scroll.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scroll.pack(side="right", fill="y")

    def setup_positions_panel(self, parent):
        """设置持仓管理面板"""
        # 持仓表格
        columns = (
            "交易对",
            "方向",
            "数量",
            "入场价",
            "当前价",
            "盈亏",
            "盈亏率",
            "持仓时间",
        )
        self.positions_tree = ttk.Treeview(
            parent, columns=columns, show="headings", height=20
        )

        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100, anchor="center")

        # 滚动条
        pos_scroll = ttk.Scrollbar(
            parent, orient="vertical", command=self.positions_tree.yview
        )
        self.positions_tree.configure(yscrollcommand=pos_scroll.set)

        self.positions_tree.pack(
            side="left", fill="both", expand=True, padx=15, pady=15
        )
        pos_scroll.pack(side="right", fill="y", pady=15)

    def setup_history_panel(self, parent):
        """设置交易历史面板"""
        # 交易历史表格
        columns = (
            "时间",
            "操作",
            "交易对",
            "数量",
            "价格",
            "盈亏",
            "手续费",
            "备注",
        )
        self.history_tree = ttk.Treeview(
            parent, columns=columns, show="headings", height=20
        )

        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100, anchor="center")

        # 滚动条
        hist_scroll = ttk.Scrollbar(
            parent, orient="vertical", command=self.history_tree.yview
        )
        self.history_tree.configure(yscrollcommand=hist_scroll.set)

        self.history_tree.pack(
            side="left", fill="both", expand=True, padx=15, pady=15
        )
        hist_scroll.pack(side="right", fill="y", pady=15)

    def setup_market_panel(self, parent):
        """设置市场数据面板"""
        # 市场数据显示
        self.market_text = tk.Text(
            parent,
            font=("Courier", 10),
            bg="#1e1e1e",
            fg="#ffffff",
            insertbackground="white",
        )
        market_scroll = tk.Scrollbar(
            parent, orient="vertical", command=self.market_text.yview
        )
        self.market_text.configure(yscrollcommand=market_scroll.set)

        self.market_text.pack(
            side="left", fill="both", expand=True, padx=15, pady=15
        )
        market_scroll.pack(side="right", fill="y", pady=15)

        # 刷新按钮
        refresh_frame = tk.Frame(parent, bg="#2d2d2d")
        refresh_frame.pack(fill="x", padx=15, pady=(0, 15))

        tk.Button(
            refresh_frame,
            text="🔄 刷新市场数据",
            command=self.refresh_market_data,
            bg="#607D8B",
            fg="white",
            font=("Arial", 10, "bold"),
            relief="flat",
            padx=20,
            pady=5,
        ).pack()

    def setup_strategy_panel(self, parent):
        """设置策略管理面板"""
        # 策略控制区域
        control_frame = tk.LabelFrame(
            parent,
            text="🎯 策略控制",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        control_frame.pack(fill="x", padx=15, pady=15)

        # 策略按钮
        button_frame = tk.Frame(control_frame, bg="#2d2d2d")
        button_frame.pack(fill="x", pady=5)

        tk.Button(
            button_frame,
            text="启动策略监控",
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold"),
            command=self.start_strategy_monitoring
        ).pack(side="left", padx=(0, 10))

        tk.Button(
            button_frame,
            text="停止策略监控",
            bg="#f44336",
            fg="white",
            font=("Arial", 10, "bold"),
            command=self.stop_strategy_monitoring
        ).pack(side="left", padx=(0, 10))

        tk.Button(
            button_frame,
            text="运行回测",
            bg="#2196F3",
            fg="white",
            font=("Arial", 10, "bold"),
            command=self.run_backtest
        ).pack(side="left", padx=(0, 10))

        # 策略状态显示
        status_frame = tk.LabelFrame(
            parent,
            text="📊 策略状态",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        status_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # 策略状态文本
        self.strategy_status_text = tk.Text(
            status_frame,
            height=15,
            font=("Courier", 9),
            bg="#1e1e1e",
            fg="#00ff00",
            insertbackground="white",
        )
        strategy_scroll = tk.Scrollbar(
            status_frame, orient="vertical", command=self.strategy_status_text.yview
        )
        self.strategy_status_text.configure(yscrollcommand=strategy_scroll.set)

        self.strategy_status_text.pack(side="left", fill="both", expand=True)
        strategy_scroll.pack(side="right", fill="y")

        # 初始化策略状态
        self.update_strategy_status()

    def setup_chart_panel(self, parent):
        """设置K线图表面板"""
        try:
            if CHART_SYSTEM_AVAILABLE:
                # 创建K线图表组件
                self.candlestick_chart = CandlestickChart(parent)

                # 加载测试数据
                self.load_chart_test_data()
            else:
                # 显示图表不可用信息
                info_label = tk.Label(
                    parent,
                    text="📈 K线图表系统不可用\n请检查相关依赖库",
                    font=("Arial", 14),
                    bg="#2d2d2d",
                    fg="white"
                )
                info_label.pack(expand=True)
        except Exception as e:
            error_label = tk.Label(
                parent,
                text=f"❌ 图表加载失败: {str(e)}",
                font=("Arial", 12),
                bg="#2d2d2d",
                fg="red"
            )
            error_label.pack(expand=True)

    def setup_performance_panel(self, parent):
        """设置性能监控面板"""
        # 性能指标显示
        metrics_frame = tk.LabelFrame(
            parent,
            text="📊 系统性能指标",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        metrics_frame.pack(fill="x", padx=15, pady=15)

        # 性能指标网格
        perf_grid = tk.Frame(metrics_frame, bg="#2d2d2d")
        perf_grid.pack(fill="x")

        self.performance_displays = {}
        perf_metrics = [
            ("CPU使用率", "cpu_usage", "#FF5722"),
            ("内存使用率", "memory_usage", "#9C27B0"),
            ("执行延迟", "execution_latency", "#2196F3"),
            ("成功率", "success_rate", "#4CAF50"),
        ]

        for i, (name, key, color) in enumerate(perf_metrics):
            row = i // 2
            col = i % 2

            metric_frame = tk.Frame(
                perf_grid, bg=color, relief="raised", bd=3
            )
            metric_frame.grid(row=row, column=col, padx=8, pady=8, sticky="ew")

            tk.Label(
                metric_frame,
                text=name,
                bg=color,
                fg="white",
                font=("Arial", 11, "bold"),
            ).pack(pady=8)

            self.performance_displays[key] = tk.Label(
                metric_frame,
                text="--",
                bg=color,
                fg="white",
                font=("Arial", 16, "bold"),
            )
            self.performance_displays[key].pack(pady=8)

        perf_grid.columnconfigure(0, weight=1)
        perf_grid.columnconfigure(1, weight=1)

        # 性能历史图表区域
        chart_frame = tk.LabelFrame(
            parent,
            text="📈 性能趋势",
            font=("Arial", 12, "bold"),
            bg="#2d2d2d",
            fg="white",
            padx=15,
            pady=15,
        )
        chart_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # 性能趋势文本显示（简化版）
        self.performance_text = tk.Text(
            chart_frame,
            height=10,
            font=("Courier", 9),
            bg="#1e1e1e",
            fg="#00ff00",
            insertbackground="white",
        )
        perf_scroll = tk.Scrollbar(
            chart_frame, orient="vertical", command=self.performance_text.yview
        )
        self.performance_text.configure(yscrollcommand=perf_scroll.set)

        self.performance_text.pack(side="left", fill="both", expand=True)
        perf_scroll.pack(side="right", fill="y")

        # 初始化性能监控
        self.init_performance_monitoring()

    def connect_gate(self):
        """连接GATE交易所"""
        try:
            if API_AVAILABLE:
                # 首先尝试自动连接预配置的API
                if gate_api.auto_connect_if_available():
                    # 自动连接成功
                    self.is_connected = True
                    self.status_label.config(
                        text="● 真实连接 (自动)", fg="#4CAF50"
                    )
                    self.log_message("🎉 自动连接GATE.IO API成功！")
                    self.log_message("📊 使用预配置的API凭证")
                    self.log_message("🔐 开始获取真实市场数据...")

                    # 启动真实数据更新
                    self.start_real_data_update()

                    # 更新标题显示真实数据
                    self.update_title_for_real_data()

                    # 显示成功消息
                    messagebox.showinfo(
                        "自动连接成功",
                        "🎉 已自动连接到GATE.IO！\n\n"
                        "✅ 使用预配置的API凭证\n"
                        "📊 正在获取真实市场数据\n"
                        "🛡️ 测试环境，安全可靠\n\n"
                        "现在可以开始基于真实数据的实战演练学习！",
                    )

                else:
                    # 没有预配置凭证，显示登录对话框
                    if show_api_login_dialog(self.root):
                        # 用户成功登录API
                        self.is_connected = True
                        self.status_label.config(
                            text="● 真实连接", fg="#4CAF50"
                        )
                        self.log_message("✅ 成功连接GATE.IO API")
                        self.log_message("📊 开始获取真实市场数据...")

                        # 启动真实数据更新
                        self.start_real_data_update()

                        # 更新标题显示真实数据
                        self.update_title_for_real_data()

                    else:
                        # 用户取消或连接失败，使用模拟模式
                        self.connect_simulation_mode()
            else:
                # API模块不可用，使用模拟模式
                self.connect_simulation_mode()

        except Exception as e:
            self.status_label.config(text="● 连接失败", fg="red")
            messagebox.showerror("连接错误", f"连接GATE失败: {str(e)}")

    def connect_simulation_mode(self):
        """连接模拟模式"""
        self.status_label.config(text="● 连接中...", fg="orange")
        self.root.update()

        time.sleep(2)

        self.status_label.config(text="● 模拟连接", fg="#4CAF50")
        self.log_message("✅ 模拟连接已建立")
        self.log_message("📊 开始生成模拟市场数据...")

        messagebox.showinfo(
            "连接成功",
            "模拟连接已建立！\n\n⚠️ 重要提醒:\n• 这是模拟系统\n• 所有数据为模拟生成\n• 不保证任何盈利",
        )

    def update_title_for_real_data(self):
        """更新标题显示真实数据"""
        # 更新副标题显示真实数据
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(
                        child, tk.Label
                    ) and "专业学习" in child.cget("text"):
                        child.config(text="🌐 真实数据 • 实战演练")
                        break

    def start_real_data_update(self):
        """启动真实数据更新"""

        def update_real_data():
            while self.is_connected and API_AVAILABLE:
                try:
                    # 获取API数据更新
                    updates = gate_api.get_data_updates()
                    for update_type, data in updates:
                        if update_type == "market_data":
                            self.update_real_market_data(data)

                    time.sleep(5)  # 每5秒检查一次更新
                except Exception as e:
                    self.log_message(f"⚠️ 真实数据更新错误: {e}")
                    time.sleep(30)

        if API_AVAILABLE:
            thread = threading.Thread(target=update_real_data, daemon=True)
            thread.start()

    def update_real_market_data(self, market_data):
        """更新真实市场数据"""
        try:
            # 更新市场数据显示
            if hasattr(self, "market_text"):
                self.market_text.delete(1.0, tk.END)

                market_info = f"""
🌐 GATE.IO 真实市场数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*70}

📊 主要交易对实时行情:
"""

                for symbol, data in market_data.items():
                    change_indicator = (
                        "🟢"
                        if data.change_24h > 0
                        else "🔴" if data.change_24h < 0 else "🟡"
                    )

                    market_info += f"""
{change_indicator} {symbol}:
  💰 当前价格: ${data.price:,.2f}
  📈 24h涨跌: {data.change_24h:+.2f}%
  📊 24h成交量: ${data.volume_24h:,.0f}
  🔺 24h最高: ${data.high_24h:,.2f}
  🔻 24h最低: ${data.low_24h:,.2f}
  ⏰ 更新时间: {data.timestamp.strftime('%H:%M:%S')}
"""

                market_info += f"""

🔄 数据来源: GATE.IO 真实API
📡 连接状态: 实时更新
⚡ 更新频率: 每30秒
"""

                self.market_text.insert(1.0, market_info)

            # 记录真实数据日志
            if market_data:
                sample_symbol = list(market_data.keys())[0]
                sample_data = market_data[sample_symbol]
                self.log_message(
                    f"📡 真实数据更新: {sample_symbol} ${sample_data.price:,.2f}"
                )

        except Exception as e:
            self.log_message(f"❌ 真实数据显示错误: {e}")

    def start_simulation(self):
        """开始实战演练"""
        if not self.is_running:
            self.is_running = True

            # 启动倍增引擎
            doubling_simulator.start()

            self.status_label.config(text="● 实战演练中", fg="#2196F3")
            self.log_message("🚀 开始模拟策略交易")
            self.log_message("💰 模拟目标: 学习交易策略")
            self.log_message("🔄 模拟引擎已启动")
            self.log_message("📈 模拟增长已激活")

            messagebox.showinfo(
                "倍增交易开始",
                "专业倍增策略已启动！\n\n"
                "✅ 目标: 资金翻倍\n"
                "✅ 复利增长引擎已启动\n"
                "✅ 预期时间: 3-6个月\n"
                "✅ 实时增长监控",
            )
        else:
            messagebox.showwarning("警告", "倍增交易已在运行中")

    def stop_simulation(self):
        """停止实战演练"""
        if self.is_running:
            self.is_running = False

            # 停止倍增引擎
            doubling_simulator.stop()

            self.status_label.config(text="● 已停止", fg="orange")
            self.log_message("🛑 倍增交易已停止")
            self.log_message("🔄 倍增引擎已停止")

            # 显示倍增进度
            try:
                growth_info = doubling_simulator.get_growth_info()
                self.log_message("📊 倍增进度报告:")
                for line in growth_info.split("\n"):
                    if line.strip():
                        self.log_message(line.strip())
            except:
                self.log_message("📊 倍增进度: 查看关键指标了解详情")

            messagebox.showinfo(
                "倍增交易停止",
                "倍增交易已停止\n\n"
                "📊 查看日志了解倍增进度\n"
                "💰 资金增长情况已记录",
            )
        else:
            messagebox.showwarning("警告", "倍增交易未在运行")

    def reset_all_indicators(self):
        """重置所有指标为零"""
        # 重置账户数据
        self.account_data = {
            "current_balance": 0.0,
            "total_value": 0.0,
            "unrealized_pnl": 0.0,
            "total_return": 0.0,
            "positions_count": 0,
        }

        # 重置性能数据
        self.performance_data = {
            "total_trades": 0,
            "win_rate": 0.0,
            "max_drawdown": 0.0,
            "profit_factor": 0.0,
        }

        # 更新显示
        self.update_all_displays()
        self.log_message("🔄 所有指标已重置为零")

    def apply_parameters(self):
        """应用参数"""
        try:
            # 获取并验证参数
            new_params = {}

            for label, var in self.param_vars.items():
                value_str = var.get().strip()

                if label == "初始资金 (USDT):":
                    new_params["initial_capital"] = float(value_str)
                elif label == "每笔风险 (%):":
                    new_params["risk_per_trade"] = float(value_str)
                elif label == "最小利润率 (%):":
                    new_params["min_profit_margin"] = float(value_str)
                elif label == "止损比例 (%):":
                    new_params["stop_loss_pct"] = float(value_str)
                elif label == "止盈比例 (%):":
                    new_params["take_profit_pct"] = float(value_str)
                elif label == "最大仓位 (%):":
                    new_params["max_position_size"] = float(value_str)
                elif label == "最小胜率 (%):":
                    new_params["min_win_rate"] = float(value_str)
                elif label == "信号强度 (%):":
                    new_params["min_signal_strength"] = float(value_str)
                elif label == "目标倍数:":
                    new_params["target_multiplier"] = float(value_str)
                elif label == "利润再投资 (%)":
                    new_params["profit_reinvest"] = float(value_str) / 100

            # 验证参数合理性
            if new_params["initial_capital"] <= 0:
                raise ValueError("初始资金必须大于0")
            if new_params["min_profit_margin"] <= 0:
                raise ValueError("最小利润率必须大于0")
            if (
                new_params["stop_loss_pct"] <= 0
                or new_params["stop_loss_pct"] >= 100
            ):
                raise ValueError("止损比例必须在0-100之间")
            if (
                new_params["take_profit_pct"] <= 0
                or new_params["take_profit_pct"] >= 100
            ):
                raise ValueError("止盈比例必须在0-100之间")
            if (
                new_params["max_position_size"] <= 0
                or new_params["max_position_size"] > 100
            ):
                raise ValueError("最大仓位必须在0-100之间")
            if (
                new_params["min_win_rate"] <= 0
                or new_params["min_win_rate"] >= 100
            ):
                raise ValueError("最小胜率必须在0-100之间")

            # 更新参数
            self.simulation_params.update(new_params)

            # 重置账户数据为初始资金
            self.account_data["current_balance"] = new_params[
                "initial_capital"
            ]
            self.account_data["total_value"] = new_params["initial_capital"]

            # 更新显示
            self.update_all_displays()

            self.log_message("✅ 模拟参数应用成功")
            self.log_message(
                f"💰 初始资金: {new_params['initial_capital']:.0f} USDT"
            )
            self.log_message(
                f"🎯 最小利润率: {new_params['min_profit_margin']:.1f}%"
            )
            self.log_message(f"🛡️ 止损比例: {new_params['stop_loss_pct']:.1f}%")
            self.log_message(
                f"💎 止盈比例: {new_params['take_profit_pct']:.1f}%"
            )

            messagebox.showinfo(
                "参数应用成功",
                f"模拟参数已成功应用！\n\n"
                f"初始资金: {new_params['initial_capital']:.0f} USDT\n"
                f"最小利润率: {new_params['min_profit_margin']:.1f}%\n"
                f"止损比例: {new_params['stop_loss_pct']:.1f}%\n"
                f"止盈比例: {new_params['take_profit_pct']:.1f}%\n"
                f"最大仓位: {new_params['max_position_size']:.1f}%\n"
                f"最小胜率: {new_params['min_win_rate']:.1f}%",
            )

        except ValueError as e:
            self.log_message(f"❌ 参数错误: {str(e)}")
            messagebox.showerror("参数错误", f"参数验证失败:\n{str(e)}")
        except Exception as e:
            self.log_message(f"❌ 应用失败: {str(e)}")
            messagebox.showerror("应用失败", f"参数应用失败:\n{str(e)}")

    def update_all_displays(self):
        """更新所有显示"""
        # 更新账户标签
        self.account_labels["current_balance"].config(
            text=f"{self.account_data['current_balance']:.2f} USDT"
        )
        self.account_labels["total_value"].config(
            text=f"{self.account_data['total_value']:.2f} USDT"
        )
        self.account_labels["unrealized_pnl"].config(
            text=f"{self.account_data['unrealized_pnl']:+.2f} USDT"
        )
        self.account_labels["total_return"].config(
            text=f"{self.account_data['total_return']:+.2%}"
        )
        self.account_labels["positions_count"].config(
            text=f"{self.account_data['positions_count']}"
        )

        # 更新性能标签
        self.perf_labels["total_trades"].config(
            text=f"{self.performance_data['total_trades']}"
        )
        self.perf_labels["win_rate"].config(
            text=f"{self.performance_data['win_rate']:.1%}"
        )
        self.perf_labels["max_drawdown"].config(
            text=f"{self.performance_data['max_drawdown']:.2%}"
        )
        self.perf_labels["profit_factor"].config(
            text=f"{self.performance_data['profit_factor']:.2f}"
        )

        # 更新关键指标显示
        if hasattr(self, "metric_displays"):
            self.metric_displays["total_value"].config(
                text=f"{self.account_data['total_value']:.0f}"
            )
            self.metric_displays["daily_pnl"].config(
                text=f"{self.account_data['unrealized_pnl']:+.0f}"
            )
            self.metric_displays["total_return"].config(
                text=f"{self.account_data['total_return']:+.1%}"
            )
            self.metric_displays["win_rate"].config(
                text=f"{self.performance_data['win_rate']:.0%}"
            )

    def refresh_market_data(self):
        """刷新市场数据"""
        self.market_text.delete(1.0, tk.END)

        # 生成真实的模拟市场数据
        import random

        # 模拟GATE现货数据
        trading_pairs = [
            {"symbol": "BTC/USDT", "base_price": 43000, "emoji": "₿"},
            {"symbol": "ETH/USDT", "base_price": 2600, "emoji": "Ξ"},
            {"symbol": "SOL/USDT", "base_price": 95, "emoji": "◎"},
            {"symbol": "BNB/USDT", "base_price": 310, "emoji": "🔶"},
        ]

        market_info = f"""
🌐 GATE现货模拟市场数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*70}

📊 主要交易对模拟行情:
"""

        for pair in trading_pairs:
            # 生成随机价格变动
            price_change = random.uniform(-0.05, 0.08)  # -5% 到 +8%
            current_price = pair["base_price"] * (1 + price_change)
            change_pct = price_change * 100

            # 生成成交量
            volume = random.uniform(1000, 50000)

            # 生成技术指标
            rsi = random.uniform(30, 80)
            macd_signal = random.choice(["买入", "卖出", "观望"])
            signal_strength = random.uniform(0.4, 0.95)

            # 生成盈利概率
            profit_prob = random.uniform(0.55, 0.85)

            # 生成支撑阻力位
            support = current_price * random.uniform(0.95, 0.98)
            resistance = current_price * random.uniform(1.02, 1.05)

            color_indicator = (
                "🟢"
                if price_change > 0
                else "🔴" if price_change < 0 else "🟡"
            )

            market_info += f"""
{color_indicator} {pair['emoji']} {pair['symbol']}:
   当前价格: ${current_price:,.2f} ({change_pct:+.2f}%)
   24h成交量: {volume:,.0f} {pair['symbol'].split('/')[0]}
   RSI(14): {rsi:.1f}
   MACD信号: {macd_signal}
   信号强度: {signal_strength:.1%}
   盈利概率: {profit_prob:.1%}
   支撑位: ${support:,.2f}
   阻力位: ${resistance:,.2f}

"""

        # 添加市场分析
        market_info += f"""
🎯 GATE现货交易机会分析:
{'='*40}

💡 高概率交易机会:
• 信号强度 > 70% 的交易对优先考虑
• 盈利概率 > 65% 符合策略要求
• RSI在30-70区间相对安全

🛡️ 风险控制参数:
• 最小利润率: {self.simulation_params['min_profit_margin']:.1f}%
• 止损比例: {self.simulation_params['stop_loss_pct']:.1f}%
• 止盈比例: {self.simulation_params['take_profit_pct']:.1f}%
• 最大仓位: {self.simulation_params['max_position_size']:.1f}%
• 最小胜率: {self.simulation_params['min_win_rate']:.1f}%

📈 实战演练状态:
• 连接状态: {'已连接' if self.is_connected else '未连接'}
• 交易状态: {'运行中' if self.is_running else '已停止'}
• 当前资金: {self.account_data['current_balance']:.2f} USDT
• 持仓数量: {self.account_data['positions_count']}

⚡ 实时更新: 每30秒自动刷新市场数据
🔄 数据来源: 模拟数据生成器
💰 交易模式: 实战演练 (无真实资金风险)
"""

        self.market_text.insert(tk.END, market_info)
        self.log_message("🔄 GATE市场数据已刷新")

        # 更新市场数据存储
        self.market_data = {
            "last_update": datetime.now(),
            "pairs": trading_pairs,
            "market_sentiment": random.choice(["乐观", "谨慎", "中性"]),
        }

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = self.log_text.get(1.0, tk.END).split("\n")
        if len(lines) > 100:
            self.log_text.delete(1.0, "10.0")

    def start_status_update(self):
        """启动状态更新"""

        def update_status():
            while True:
                if self.is_running:
                    self.update_simulated_data()
                time.sleep(3)

        thread = threading.Thread(target=update_status, daemon=True)
        thread.start()

    def update_simulated_data(self):
        """更新模拟数据"""
        import random

        # 获取当前设置的初始资金
        initial_capital = self.simulation_params["initial_capital"]

        # 使用倍增引擎获取真实增长数据
        if not self.is_running:
            # 如果没有运行，使用设置的初始资金
            current_balance = initial_capital
            total_value = initial_capital
            unrealized_pnl = 0.0
            total_return = 0.0
            positions_count = 0
        else:
            # 如果正在运行，使用倍增引擎的真实数据
            try:
                doubling_data = doubling_simulator.get_simulated_data()
                current_balance = doubling_data.get(
                    "current_balance", initial_capital
                )
                total_value = doubling_data.get("total_value", initial_capital)
                unrealized_pnl = doubling_data.get("unrealized_pnl", 0.0)
                total_return = doubling_data.get("total_return", 0.0)
                positions_count = doubling_data.get("positions_count", 0)

                # 记录倍增进度
                doubling_progress = doubling_data.get("doubling_progress", 0.0)
                growth_stage = doubling_data.get("growth_stage", 1)

                # 偶尔记录增长日志
                if random.random() < 0.05:  # 5%概率
                    if doubling_progress > 0:
                        self.log_message(
                            f"📈 倍增进度: {doubling_progress:.1%}"
                        )
                        self.log_message(f"⭐ 增长阶段: {growth_stage}")

            except Exception as e:
                # 如果倍增引擎出错，回退到基础模拟
                variation_pct = 0.05
                current_balance = initial_capital + random.uniform(
                    -initial_capital * variation_pct,
                    initial_capital * variation_pct * 2,
                )
                total_value = current_balance + random.uniform(
                    0, initial_capital * 0.1
                )
                unrealized_pnl = random.uniform(
                    -initial_capital * 0.02, initial_capital * 0.05
                )
                total_return = (
                    total_value - initial_capital
                ) / initial_capital
                positions_count = random.randint(0, 3)

        # 更新账户标签
        self.account_labels["current_balance"].config(
            text=f"{current_balance:.2f} USDT"
        )
        self.account_labels["total_value"].config(
            text=f"{total_value:.2f} USDT"
        )
        self.account_labels["unrealized_pnl"].config(
            text=f"{unrealized_pnl:+.2f} USDT"
        )
        self.account_labels["total_return"].config(text=f"{total_return:+.2%}")
        self.account_labels["positions_count"].config(
            text=f"{positions_count}"
        )

        # 更新性能指标
        total_trades = random.randint(10, 50)
        win_rate = random.uniform(0.65, 0.85)
        max_drawdown = random.uniform(0.01, 0.05)
        profit_factor = random.uniform(1.5, 3.0)

        self.perf_labels["total_trades"].config(text=f"{total_trades}")
        self.perf_labels["win_rate"].config(text=f"{win_rate:.1%}")
        self.perf_labels["max_drawdown"].config(text=f"{max_drawdown:.2%}")
        self.perf_labels["profit_factor"].config(text=f"{profit_factor:.2f}")

        # 更新关键指标显示
        self.metric_displays["total_value"].config(text=f"{total_value:.0f}")
        self.metric_displays["daily_pnl"].config(text=f"{unrealized_pnl:+.0f}")
        self.metric_displays["total_return"].config(
            text=f"{total_return:+.1%}"
        )
        self.metric_displays["win_rate"].config(text=f"{win_rate:.0%}")

        # 随机生成交易日志
        if random.random() < 0.1:  # 10%概率生成交易日志
            actions = [
                "分析BTC信号",
                "检测ETH机会",
                "SOL止盈执行",
                "风险检查完成",
            ]
            self.log_message(f"🤖 {random.choice(actions)}")

    def show_risk_warning(self):
        """显示风险警告对话框"""
        warning_dialog = tk.Toplevel(self.root)
        warning_dialog.title("⚠️ 重要风险警告")
        warning_dialog.geometry("600x500")
        warning_dialog.configure(bg="#2d2d2d")
        warning_dialog.transient(self.root)
        warning_dialog.grab_set()

        # 居中显示
        warning_dialog.update_idletasks()
        x = (warning_dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (warning_dialog.winfo_screenheight() // 2) - (500 // 2)
        warning_dialog.geometry(f"600x500+{x}+{y}")

        # 标题
        tk.Label(
            warning_dialog,
            text="⚠️ 重要风险警告 ⚠️",
            font=("Arial", 16, "bold"),
            fg="#f44336",
            bg="#2d2d2d",
        ).pack(pady=(20, 10))

        # 警告内容
        warning_frame = tk.Frame(
            warning_dialog, bg="#2d2d2d", padx=30, pady=20
        )
        warning_frame.pack(fill="both", expand=True)

        warning_text = """
这是一个交易策略学习和模拟系统，仅供教育目的使用。

🚨 关键提醒:
• 这不是真实的交易系统
• 所有操作都是虚拟的
• 不保证任何盈利
• 真实交易存在亏损风险
• 请勿将此作为投资建议

🎯 正确用途:
• 学习交易策略概念
• 理解风险管理原理
• 熟悉交易系统操作
• 测试策略逻辑

⚠️ 法律声明:
本系统仅作为教育工具，不构成任何投资建议。
用户应自行承担使用本系统的全部风险。
真实交易可能导致资金损失，请谨慎决策。

请确认您已完全理解以上风险警告，并仅将本系统用于学习目的。
        """

        warning_label = tk.Label(
            warning_frame,
            text=warning_text,
            font=("Arial", 11),
            fg="white",
            bg="#2d2d2d",
            justify="left",
            wraplength=540,
        )
        warning_label.pack(fill="both", expand=True)

        # 确认按钮
        tk.Button(
            warning_dialog,
            text="我已了解风险并仅用于学习目的",
            font=("Arial", 11, "bold"),
            bg="#f44336",
            fg="white",
            padx=20,
            pady=10,
            command=warning_dialog.destroy,
        ).pack(pady=(0, 20))

    def show_config_dialog(self):
        """显示配置对话框"""
        try:
            # 使用配置UI模块
            show_config_dialog(self.root)
        except Exception as e:
            messagebox.showerror("配置错误", f"打开配置对话框时出错: {e}")

    def show_resource_monitor(self):
        """显示资源监控对话框"""
        try:
            # 使用资源监控UI模块
            show_resource_monitor(self.root)
        except Exception as e:
            messagebox.showerror("监控错误", f"打开资源监控对话框时出错: {e}")

    def register_resources(self):
        """注册系统资源"""
        try:
            # 获取资源管理器
            resource_manager = get_resource_manager()
            if resource_manager:
                # 注册主窗口
                resource_manager.register_resource(
                    "ui", "main_window", self.root
                )

                # 注册线程
                for thread in threading.enumerate():
                    resource_manager.register_thread(thread)

                # 注册其他资源
                if hasattr(self, "market_data") and self.market_data:
                    resource_manager.register_resource(
                        "data", "market_data", self.market_data
                    )
        except Exception as e:
            print(f"注册资源失败: {e}")

    def run(self):
        """运行GUI"""
        # 显示欢迎信息
        welcome_msg = """
🎉 欢迎使用终极版现货交易系统！

💡 系统特点:
• 模拟GATE现货数据
• 智能交易策略学习
• 模拟风险控制演示
• 完整的学习交易环境

🚀 使用步骤:
1. 点击"连接GATE交易所"
2. 配置模拟参数
3. 开始实战演练
4. 监控交易表现

⚠️ 重要提示: 这是一个学习系统，不执行真实交易

📚 立即开始您的交易学习之旅！
"""
        messagebox.showinfo("欢迎使用", welcome_msg)

        # 注册系统资源
        self.register_resources()

        # 启动时显示风险警告
        self.root.after(1000, self.show_risk_warning)

        # 设置关闭窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动主循环
        self.root.mainloop()

    def init_trading_core(self):
        """初始化交易系统核心"""
        try:
            if not TRADING_CORE_AVAILABLE:
                self.log_message("⚠️ 交易系统核心不可用")
                return

            # 配置交易系统
            trading_config = {
                'database_path': 'database/trading.db',
                'risk_config': {
                    'max_position_size': 0.25,
                    'max_total_exposure': 0.80,
                    'max_daily_loss': 0.05,
                    'stop_loss_pct': 0.02,
                    'take_profit_pct': 0.06
                },
                'max_concurrent_executions': 3
            }

            # 创建交易系统核心
            self.trading_core = get_trading_system_core(self.api_connector, trading_config)

            # 添加事件回调
            self.trading_core.add_event_callback('order_filled', self.on_order_filled)
            self.trading_core.add_event_callback('risk_alert', self.on_risk_alert)
            self.trading_core.add_event_callback('system_error', self.on_system_error)

            self.log_message("🚀 交易系统核心初始化完成")

        except Exception as e:
            self.log_message(f"❌ 交易系统核心初始化失败: {str(e)}")

    def toggle_real_trading(self):
        """切换真实交易模式"""
        try:
            if not hasattr(self, 'is_connected') or not self.is_connected:
                messagebox.showwarning("警告", "请先连接到GATE.IO交易所")
                return

            if not TRADING_CORE_AVAILABLE:
                messagebox.showerror("错误", "交易系统核心不可用")
                return

            if not self.trading_core:
                self.init_trading_core()

            if not self.real_trading_mode:
                # 启用真实交易模式
                result = messagebox.askyesno(
                    "⚠️ 重要警告",
                    "您即将启用真实交易模式！\n\n"
                    "这将使用真实资金进行交易，存在损失风险。\n"
                    "请确保您已充分了解风险并做好准备。\n\n"
                    "是否继续启用真实交易模式？",
                    icon="warning"
                )

                if result:
                    # 启动交易系统
                    self.real_trading_mode = True
                    self.log_message("🚀 真实交易模式已启用")
                    self.log_message("⚠️ 警告: 现在将执行真实交易操作")

                    # 更新按钮文本
                    for widget in self.root.winfo_children():
                        self.update_trading_button_text(widget, "🛑 停用真实交易")
                else:
                    self.log_message("⚠️ 用户取消启用真实交易模式")
            else:
                # 停用真实交易模式
                self.real_trading_mode = False
                self.log_message("🛑 真实交易模式已停用")
                self.log_message("📚 回到学习模拟模式")

                # 更新按钮文本
                for widget in self.root.winfo_children():
                    self.update_trading_button_text(widget, "🚀 启用真实交易")

        except Exception as e:
            self.log_message(f"❌ 切换交易模式失败: {str(e)}")
            messagebox.showerror("错误", f"切换交易模式失败: {str(e)}")

    def update_trading_button_text(self, widget, new_text):
        """递归更新交易按钮文本"""
        try:
            if hasattr(widget, 'config') and hasattr(widget, 'cget'):
                if widget.cget('text') in ["🚀 启用真实交易", "🛑 停用真实交易"]:
                    widget.config(text=new_text)

            # 递归检查子组件
            for child in widget.winfo_children():
                self.update_trading_button_text(child, new_text)
        except:
            pass

    def on_order_filled(self, data):
        """订单成交回调"""
        try:
            order = data.get('order')
            if order:
                self.log_message(f"✅ 订单成交: {order.symbol} {order.side.value} {order.filled_amount}")
                # 更新界面显示
                self.update_trading_history()
        except Exception as e:
            self.log_message(f"❌ 处理订单成交事件失败: {str(e)}")

    def on_risk_alert(self, data):
        """风险警告回调"""
        try:
            self.log_message(f"⚠️ 风险警告: {data.get('message', '未知风险')}")
        except Exception as e:
            self.log_message(f"❌ 处理风险警告失败: {str(e)}")

    def on_system_error(self, data):
        """系统错误回调"""
        try:
            error = data.get('error', '未知错误')
            self.log_message(f"❌ 系统错误: {error}")
        except Exception as e:
            self.log_message(f"❌ 处理系统错误失败: {str(e)}")

    def update_trading_history(self):
        """更新交易历史显示"""
        try:
            if self.trading_core:
                # 获取交易历史
                summary = self.trading_core.get_performance_summary()
                trading_perf = summary.get('trading_performance', {})

                # 更新性能指标
                if trading_perf:
                    self.performance_data.update({
                        'total_trades': trading_perf.get('total_trades', 0),
                        'win_rate': trading_perf.get('win_rate', 0) / 100,
                        'total_pnl': trading_perf.get('total_pnl', 0)
                    })

                    # 更新显示
                    self.update_all_displays()
        except Exception as e:
            self.log_message(f"❌ 更新交易历史失败: {str(e)}")

    # 新功能方法实现
    def start_strategy_monitoring(self):
        """启动策略监控"""
        try:
            if STRATEGY_SYSTEM_AVAILABLE:
                self.log_message("🎯 启动策略监控...")
                self.update_strategy_status()
                messagebox.showinfo("策略监控", "策略监控已启动")
            else:
                messagebox.showwarning("警告", "策略系统不可用")
        except Exception as e:
            self.log_message(f"❌ 启动策略监控失败: {str(e)}")

    def stop_strategy_monitoring(self):
        """停止策略监控"""
        try:
            self.log_message("🛑 停止策略监控...")
            messagebox.showinfo("策略监控", "策略监控已停止")
        except Exception as e:
            self.log_message(f"❌ 停止策略监控失败: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        try:
            if STRATEGY_SYSTEM_AVAILABLE:
                self.log_message("📊 开始运行回测...")
                messagebox.showinfo("回测", "回测功能开发中...")
            else:
                messagebox.showwarning("警告", "策略系统不可用")
        except Exception as e:
            self.log_message(f"❌ 运行回测失败: {str(e)}")

    def update_strategy_status(self):
        """更新策略状态"""
        try:
            if hasattr(self, 'strategy_status_text'):
                self.strategy_status_text.delete(1.0, tk.END)

                status_info = f"""
🎯 策略系统状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*60}

📊 系统状态:
• 策略系统: {'✅ 可用' if STRATEGY_SYSTEM_AVAILABLE else '❌ 不可用'}
• 监控系统: {'✅ 可用' if MONITORING_SYSTEM_AVAILABLE else '❌ 不可用'}
• 图表系统: {'✅ 可用' if CHART_SYSTEM_AVAILABLE else '❌ 不可用'}
• 交易核心: {'✅ 可用' if TRADING_CORE_AVAILABLE else '❌ 不可用'}

🎯 策略配置:
• 默认策略: 技术分析策略
• 信号生成: RSI + MACD + 移动平均
• 风险控制: 2% 止损, 6% 止盈
• 最大仓位: 25%

📈 策略性能:
• 总信号数: 0
• 成功信号: 0
• 成功率: 0.00%
• 平均置信度: 0.00

⚡ 实时监控:
• 监控状态: 待启动
• 监控频率: 每5分钟
• 支持交易对: BTC/USDT, ETH/USDT
• 时间周期: 1小时

🔧 功能状态:
• 信号生成器: 就绪
• 回测引擎: 就绪
• 性能监控: 就绪
• 报告生成: 就绪
"""

                self.strategy_status_text.insert(tk.END, status_info)
        except Exception as e:
            self.log_message(f"❌ 更新策略状态失败: {str(e)}")

    def load_chart_test_data(self):
        """加载图表测试数据"""
        try:
            if hasattr(self, 'candlestick_chart'):
                # 创建测试K线数据
                import random
                import pandas as pd

                dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
                base_price = 50000

                test_data = []
                for i, date in enumerate(dates):
                    # 生成随机价格变动
                    change = random.uniform(-0.02, 0.02)
                    base_price *= (1 + change)

                    high = base_price * random.uniform(1.001, 1.02)
                    low = base_price * random.uniform(0.98, 0.999)
                    open_price = base_price * random.uniform(0.995, 1.005)
                    close = base_price
                    volume = random.uniform(10, 100)

                    test_data.append({
                        'open': open_price,
                        'high': high,
                        'low': low,
                        'close': close,
                        'volume': volume
                    })

                df = pd.DataFrame(test_data, index=dates)

                # 更新图表数据
                self.candlestick_chart.update_data(df)
                self.log_message("📈 K线图表测试数据已加载")

        except Exception as e:
            self.log_message(f"❌ 加载图表数据失败: {str(e)}")

    def init_performance_monitoring(self):
        """初始化性能监控"""
        try:
            if MONITORING_SYSTEM_AVAILABLE:
                # 启动性能监控更新
                self.update_performance_displays()
                self.log_message("📊 性能监控已初始化")
            else:
                self.log_message("⚠️ 监控系统不可用")
        except Exception as e:
            self.log_message(f"❌ 初始化性能监控失败: {str(e)}")

    def update_performance_displays(self):
        """更新性能显示"""
        try:
            if hasattr(self, 'performance_displays'):
                import psutil
                import random

                # 获取系统性能指标
                cpu_usage = psutil.cpu_percent()
                memory_usage = psutil.virtual_memory().percent
                execution_latency = random.uniform(0.05, 0.2)  # 模拟执行延迟
                success_rate = random.uniform(85, 99)  # 模拟成功率

                # 更新显示
                self.performance_displays['cpu_usage'].config(text=f"{cpu_usage:.1f}%")
                self.performance_displays['memory_usage'].config(text=f"{memory_usage:.1f}%")
                self.performance_displays['execution_latency'].config(text=f"{execution_latency:.3f}s")
                self.performance_displays['success_rate'].config(text=f"{success_rate:.1f}%")

                # 更新性能趋势文本
                if hasattr(self, 'performance_text'):
                    perf_info = f"""
📊 系统性能监控 - {datetime.now().strftime('%H:%M:%S')}
{'='*50}

🖥️ 系统资源:
• CPU使用率: {cpu_usage:.1f}%
• 内存使用率: {memory_usage:.1f}%
• 可用内存: {psutil.virtual_memory().available / (1024**3):.1f} GB

⚡ 执行性能:
• 平均延迟: {execution_latency:.3f}s
• 成功率: {success_rate:.1f}%
• 活跃线程: {psutil.Process().num_threads()}

📈 性能等级: {'A+' if cpu_usage < 50 and success_rate > 95 else 'A' if cpu_usage < 70 else 'B'}

🔄 监控状态: 实时更新
⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}
"""

                    self.performance_text.delete(1.0, tk.END)
                    self.performance_text.insert(tk.END, perf_info)

                # 定时更新
                self.root.after(5000, self.update_performance_displays)

        except Exception as e:
            self.log_message(f"❌ 更新性能显示失败: {str(e)}")

    def on_closing(self):
        """关闭窗口时的处理"""
        try:
            # 清理资源
            resource_manager = get_resource_manager()
            if resource_manager:
                resource_manager.cleanup_all()

            # 销毁窗口
            self.root.destroy()
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            self.root.destroy()


def main():
    """主函数"""
    print("🚀 启动终极版现货交易系统...")
    print("📊 API模块加载成功" if API_AVAILABLE else "⚠️ API模块未加载，使用模拟模式")
    print("🚀 交易系统核心可用" if TRADING_CORE_AVAILABLE else "⚠️ 交易系统核心不可用，仅模拟模式")
    print("🎯 策略系统可用" if STRATEGY_SYSTEM_AVAILABLE else "⚠️ 策略系统不可用")
    print("📊 监控系统可用" if MONITORING_SYSTEM_AVAILABLE else "⚠️ 监控系统不可用")
    print("📈 图表系统可用" if CHART_SYSTEM_AVAILABLE else "⚠️ 图表系统不可用")

    app = UltimateSpotTradingGUI()
    app.run()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现货交易风险管理系统
Spot Trading Risk Management System

专门为现货交易设计的全面风险控制系统
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class RiskMetrics:
    """风险指标"""

    current_drawdown: float  # 当前回撤
    max_drawdown: float  # 最大回撤
    var_95: float  # 95% VaR
    var_99: float  # 99% VaR
    sharpe_ratio: float  # 夏普比率
    sortino_ratio: float  # 索提诺比率
    win_rate: float  # 胜率
    avg_win: float  # 平均盈利
    avg_loss: float  # 平均亏损
    profit_factor: float  # 盈利因子
    kelly_criterion: float  # 凯利公式建议仓位


@dataclass
class RiskAlert:
    """风险警报"""

    level: str  # 'low', 'medium', 'high', 'critical'
    type: str  # 风险类型
    message: str  # 警报信息
    timestamp: datetime  # 时间戳
    action_required: bool  # 是否需要行动


class SpotRiskManager:
    """现货交易风险管理器"""

    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.peak_capital = initial_capital

        # 风险限制
        self.max_drawdown_limit = 0.10  # 最大回撤10%
        self.daily_loss_limit = 0.03  # 日亏损限制3%
        self.position_size_limit = 0.15  # 单仓位限制15%
        self.total_exposure_limit = 0.80  # 总敞口限制80%
        self.correlation_limit = 0.7  # 相关性限制

        # 风险监控
        self.daily_pnl = 0.0
        self.trade_history = []
        self.risk_alerts = []
        self.position_correlations = {}

        # 滚雪球风险参数
        self.compound_safety_factor = 0.8  # 复利安全系数
        self.growth_rate_limit = 0.50  # 月增长率限制50%
        self.volatility_limit = 0.25  # 波动率限制25%

    def assess_trade_risk(
        self, trade_setup: "TradeSetup", current_positions: Dict
    ) -> Tuple[bool, str, float]:
        """
        评估交易风险

        Returns:
            (是否允许交易, 风险说明, 建议仓位大小)
        """
        risk_checks = []

        # 1. 仓位大小检查
        position_risk = self._check_position_size_risk(trade_setup)
        risk_checks.append(position_risk)

        # 2. 总敞口检查
        exposure_risk = self._check_total_exposure_risk(
            trade_setup, current_positions
        )
        risk_checks.append(exposure_risk)

        # 3. 相关性检查
        correlation_risk = self._check_correlation_risk(
            trade_setup, current_positions
        )
        risk_checks.append(correlation_risk)

        # 4. 信号质量检查
        signal_risk = self._check_signal_quality_risk(trade_setup)
        risk_checks.append(signal_risk)

        # 5. 市场条件检查
        market_risk = self._check_market_condition_risk(trade_setup)
        risk_checks.append(market_risk)

        # 综合风险评估
        failed_checks = [check for check in risk_checks if not check["passed"]]

        if failed_checks:
            # 有风险检查失败
            risk_messages = [check["message"] for check in failed_checks]
            return False, "; ".join(risk_messages), 0.0

        # 计算建议仓位大小
        suggested_size = self._calculate_optimal_position_size(
            trade_setup, risk_checks
        )

        return True, "风险检查通过", suggested_size

    def _check_position_size_risk(self, trade_setup: "TradeSetup") -> Dict:
        """检查仓位大小风险"""
        position_value = trade_setup.position_size * trade_setup.entry_price
        position_pct = position_value / self.current_capital

        if position_pct > self.position_size_limit:
            return {
                "passed": False,
                "message": f"单仓位过大: {position_pct:.1%} > {self.position_size_limit:.1%}",
                "risk_score": position_pct / self.position_size_limit,
            }

        return {
            "passed": True,
            "message": f"仓位大小合理: {position_pct:.1%}",
            "risk_score": position_pct / self.position_size_limit,
        }

    def _check_total_exposure_risk(
        self, trade_setup: "TradeSetup", current_positions: Dict
    ) -> Dict:
        """检查总敞口风险"""
        # 计算当前总敞口
        current_exposure = sum(
            pos.size * pos.current_price for pos in current_positions.values()
        )

        # 加上新仓位的敞口
        new_exposure = trade_setup.position_size * trade_setup.entry_price
        total_exposure = current_exposure + new_exposure
        exposure_pct = total_exposure / self.current_capital

        if exposure_pct > self.total_exposure_limit:
            return {
                "passed": False,
                "message": f"总敞口过大: {exposure_pct:.1%} > {self.total_exposure_limit:.1%}",
                "risk_score": exposure_pct / self.total_exposure_limit,
            }

        return {
            "passed": True,
            "message": f"总敞口合理: {exposure_pct:.1%}",
            "risk_score": exposure_pct / self.total_exposure_limit,
        }

    def _check_correlation_risk(
        self, trade_setup: "TradeSetup", current_positions: Dict
    ) -> Dict:
        """检查相关性风险"""
        # 简化的相关性检查（实际应该使用历史价格数据）
        high_correlation_pairs = {
            "BTC/USDT": ["ETH/USDT"],
            "ETH/USDT": ["BTC/USDT"],
            "BNB/USDT": [],
        }

        correlated_positions = []
        for pair in current_positions.keys():
            if pair in high_correlation_pairs.get(trade_setup.pair, []):
                correlated_positions.append(pair)

        if len(correlated_positions) > 0:
            return {
                "passed": False,
                "message": f"与现有持仓高度相关: {correlated_positions}",
                "risk_score": 0.8,
            }

        return {"passed": True, "message": "相关性风险可控", "risk_score": 0.2}

    def _check_signal_quality_risk(self, trade_setup: "TradeSetup") -> Dict:
        """检查信号质量风险"""
        signal_quality = trade_setup.signal_quality

        # 综合信号质量评分
        quality_score = (
            signal_quality.strength * 0.3
            + signal_quality.confidence * 0.3
            + signal_quality.win_probability * 0.2
            + min(signal_quality.risk_reward / 3.0, 1.0) * 0.2
        )

        if quality_score < 0.7:
            return {
                "passed": False,
                "message": f"信号质量不足: {quality_score:.2f} < 0.70",
                "risk_score": 1 - quality_score,
            }

        return {
            "passed": True,
            "message": f"信号质量良好: {quality_score:.2f}",
            "risk_score": 1 - quality_score,
        }

    def _check_market_condition_risk(self, trade_setup: "TradeSetup") -> Dict:
        """检查市场条件风险"""
        # 这里可以添加更复杂的市场条件检查
        # 例如：波动率、流动性、市场情绪等

        # 简化版本：检查信号强度
        if trade_setup.signal_quality.strength < 0.6:
            return {
                "passed": False,
                "message": f"市场信号不明确: 强度 {trade_setup.signal_quality.strength:.2f}",
                "risk_score": 0.8,
            }

        return {"passed": True, "message": "市场条件适宜", "risk_score": 0.2}

    def _calculate_optimal_position_size(
        self, trade_setup: "TradeSetup", risk_checks: List[Dict]
    ) -> float:
        """计算最优仓位大小"""
        # 基础仓位大小
        base_size = trade_setup.position_size

        # 根据风险评分调整
        avg_risk_score = np.mean(
            [check["risk_score"] for check in risk_checks]
        )
        risk_adjustment = 1 - avg_risk_score * 0.5  # 最多减少50%

        # 根据信号质量调整
        signal_adjustment = trade_setup.signal_quality.strength

        # 根据凯利公式调整
        kelly_adjustment = self._calculate_kelly_position(trade_setup)

        # 综合调整
        optimal_size = (
            base_size * risk_adjustment * signal_adjustment * kelly_adjustment
        )

        # 确保不超过限制
        max_allowed = (
            self.current_capital
            * self.position_size_limit
            / trade_setup.entry_price
        )

        return min(optimal_size, max_allowed)

    def _calculate_kelly_position(self, trade_setup: "TradeSetup") -> float:
        """计算凯利公式建议仓位"""
        win_rate = trade_setup.signal_quality.win_probability
        avg_win = trade_setup.signal_quality.risk_reward
        avg_loss = 1.0  # 标准化

        if avg_loss == 0:
            return 0.5  # 保守估计

        # 凯利公式: f = (bp - q) / b
        # 其中 b = 平均盈利/平均亏损, p = 胜率, q = 败率
        kelly_fraction = (avg_win * win_rate - (1 - win_rate)) / avg_win

        # 限制凯利仓位在合理范围内
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 最多25%

        # 应用安全系数
        return kelly_fraction * self.compound_safety_factor

    def calculate_risk_metrics(self, returns: List[float]) -> RiskMetrics:
        """计算风险指标"""
        if not returns:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

        returns_array = np.array(returns)

        # 回撤计算
        cumulative = np.cumprod(1 + returns_array)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = (cumulative - running_max) / running_max
        current_drawdown = drawdowns[-1]
        max_drawdown = np.min(drawdowns)

        # VaR计算
        var_95 = np.percentile(returns_array, 5)
        var_99 = np.percentile(returns_array, 1)

        # 夏普比率
        excess_returns = returns_array - 0.02 / 252  # 假设无风险利率2%年化
        sharpe_ratio = (
            np.mean(excess_returns) / np.std(returns_array) * np.sqrt(252)
            if np.std(returns_array) > 0
            else 0
        )

        # 索提诺比率
        downside_returns = returns_array[returns_array < 0]
        downside_std = (
            np.std(downside_returns) if len(downside_returns) > 0 else 0
        )
        sortino_ratio = (
            np.mean(excess_returns) / downside_std * np.sqrt(252)
            if downside_std > 0
            else 0
        )

        # 胜率和盈亏比
        winning_trades = returns_array[returns_array > 0]
        losing_trades = returns_array[returns_array < 0]

        win_rate = (
            len(winning_trades) / len(returns_array)
            if len(returns_array) > 0
            else 0
        )
        avg_win = np.mean(winning_trades) if len(winning_trades) > 0 else 0
        avg_loss = (
            np.mean(np.abs(losing_trades)) if len(losing_trades) > 0 else 0
        )

        # 盈利因子
        total_wins = np.sum(winning_trades) if len(winning_trades) > 0 else 0
        total_losses = (
            np.sum(np.abs(losing_trades)) if len(losing_trades) > 0 else 0
        )
        profit_factor = (
            total_wins / total_losses if total_losses > 0 else float("inf")
        )

        # 凯利公式
        if avg_loss > 0:
            kelly_criterion = (avg_win * win_rate - (1 - win_rate)) / avg_win
        else:
            kelly_criterion = 0

        return RiskMetrics(
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            var_95=var_95,
            var_99=var_99,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            kelly_criterion=kelly_criterion,
        )

    def check_risk_alerts(
        self, current_positions: Dict, recent_returns: List[float]
    ) -> List[RiskAlert]:
        """检查风险警报"""
        alerts = []

        # 检查回撤警报
        current_drawdown = (
            self.peak_capital - self.current_capital
        ) / self.peak_capital
        if current_drawdown > self.max_drawdown_limit * 0.8:
            alerts.append(
                RiskAlert(
                    level=(
                        "high"
                        if current_drawdown > self.max_drawdown_limit
                        else "medium"
                    ),
                    type="drawdown",
                    message=f"当前回撤 {current_drawdown:.1%} 接近限制 {self.max_drawdown_limit:.1%}",
                    timestamp=datetime.now(),
                    action_required=current_drawdown > self.max_drawdown_limit,
                )
            )

        # 检查日亏损警报
        daily_loss_pct = abs(self.daily_pnl) / self.current_capital
        if self.daily_pnl < 0 and daily_loss_pct > self.daily_loss_limit * 0.8:
            alerts.append(
                RiskAlert(
                    level=(
                        "high"
                        if daily_loss_pct > self.daily_loss_limit
                        else "medium"
                    ),
                    type="daily_loss",
                    message=f"日亏损 {daily_loss_pct:.1%} 接近限制 {self.daily_loss_limit:.1%}",
                    timestamp=datetime.now(),
                    action_required=daily_loss_pct > self.daily_loss_limit,
                )
            )

        # 检查集中度风险
        if len(current_positions) > 0:
            total_exposure = sum(
                pos.size * pos.current_price
                for pos in current_positions.values()
            )
            exposure_pct = total_exposure / self.current_capital

            if exposure_pct > self.total_exposure_limit * 0.9:
                alerts.append(
                    RiskAlert(
                        level="medium",
                        type="concentration",
                        message=f"总敞口 {exposure_pct:.1%} 过于集中",
                        timestamp=datetime.now(),
                        action_required=False,
                    )
                )

        # 检查波动率警报
        if len(recent_returns) >= 20:
            volatility = np.std(recent_returns) * np.sqrt(252)  # 年化波动率
            if volatility > self.volatility_limit:
                alerts.append(
                    RiskAlert(
                        level="medium",
                        type="volatility",
                        message=f"策略波动率 {volatility:.1%} 超过限制 {self.volatility_limit:.1%}",
                        timestamp=datetime.now(),
                        action_required=False,
                    )
                )

        return alerts

    def update_capital(self, new_capital: float):
        """更新资金"""
        self.current_capital = new_capital
        if new_capital > self.peak_capital:
            self.peak_capital = new_capital

    def reset_daily_pnl(self):
        """重置日盈亏"""
        self.daily_pnl = 0.0

    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        current_drawdown = (
            self.peak_capital - self.current_capital
        ) / self.peak_capital
        daily_loss_pct = (
            abs(self.daily_pnl) / self.current_capital
            if self.daily_pnl < 0
            else 0
        )

        return {
            "current_capital": self.current_capital,
            "peak_capital": self.peak_capital,
            "current_drawdown": current_drawdown,
            "daily_pnl": self.daily_pnl,
            "daily_loss_pct": daily_loss_pct,
            "risk_limits": {
                "max_drawdown": self.max_drawdown_limit,
                "daily_loss": self.daily_loss_limit,
                "position_size": self.position_size_limit,
                "total_exposure": self.total_exposure_limit,
            },
            "risk_utilization": {
                "drawdown_used": current_drawdown / self.max_drawdown_limit,
                "daily_loss_used": daily_loss_pct / self.daily_loss_limit,
            },
        }

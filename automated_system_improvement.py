#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化系统改进工具
Automated System Improvement Tool

基于深度审查报告的自动化改进实施工具
"""

import os
import re
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

class SystemImprovement:
    """系统改进工具"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.backup_dir = Path("backups") / f"before_improvement_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.changes_log = []
        self.errors_log = []
        
        # 创建备份目录
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def run_all_improvements(self):
        """运行所有改进"""
        print("🚀 开始自动化系统改进...")
        print("=" * 60)
        
        try:
            # 阶段1: 立即执行改进
            self._phase1_immediate_fixes()
            
            # 阶段2: 中期改进
            self._phase2_medium_term_fixes()
            
            # 生成改进报告
            self._generate_improvement_report()
            
            print("\n✅ 系统改进完成！")
            print(f"📊 总共应用了 {len(self.changes_log)} 项改进")
            print(f"⚠️ 遇到了 {len(self.errors_log)} 个问题")
            
        except Exception as e:
            print(f"❌ 改进过程中出现错误: {e}")
            self.errors_log.append(f"全局错误: {e}")
    
    def _phase1_immediate_fixes(self):
        """阶段1: 立即执行改进"""
        print("\n🚨 阶段1: 立即执行改进")
        print("-" * 40)
        
        # 1. 清理重复启动器
        self._cleanup_duplicate_launchers()
        
        # 2. 重命名中文文件
        self._rename_chinese_files()
        
        # 3. 清理调试print语句
        self._cleanup_debug_prints()
        
        # 4. 统一配置文件
        self._organize_config_files()
        
        # 5. 创建安全配置模板
        self._create_secure_config_template()
    
    def _phase2_medium_term_fixes(self):
        """阶段2: 中期改进"""
        print("\n🔧 阶段2: 中期改进")
        print("-" * 40)
        
        # 1. 优化import语句
        self._optimize_imports()
        
        # 2. 添加类型注解
        self._add_type_hints()
        
        # 3. 创建日志配置
        self._setup_logging_system()
        
        # 4. 生成requirements.txt
        self._update_requirements()
    
    def _cleanup_duplicate_launchers(self):
        """清理重复的启动器"""
        print("🗂️ 清理重复启动器...")
        
        # 保留的主启动器
        main_launcher = "launch_ultimate_optimized_system.py"
        
        # 需要移除的启动器
        duplicate_launchers = [
            "launch_complete_optimized_system.py",
            "launch_final_optimized_system.py", 
            "launch_optimized_v2.py",
            "launch_optimized_system.py"
        ]
        
        # 创建legacy目录
        legacy_dir = self.backup_dir / "legacy_launchers"
        legacy_dir.mkdir(exist_ok=True)
        
        moved_count = 0
        for launcher in duplicate_launchers:
            launcher_path = self.project_root / launcher
            if launcher_path.exists():
                try:
                    # 移动到legacy目录
                    shutil.move(str(launcher_path), str(legacy_dir / launcher))
                    moved_count += 1
                    self.changes_log.append(f"移动重复启动器: {launcher}")
                    print(f"  ✅ 移动 {launcher}")
                except Exception as e:
                    self.errors_log.append(f"移动启动器失败 {launcher}: {e}")
                    print(f"  ❌ 移动失败 {launcher}: {e}")
        
        print(f"  📊 清理了 {moved_count} 个重复启动器")
    
    def _rename_chinese_files(self):
        """重命名中文文件"""
        print("🌏 重命名中文文件...")
        
        # 中文文件重命名映射
        rename_mapping = {
            "安全API集成指南.py": "secure_api_guide.py",
            "登录功能演示.py": "login_demo.py", 
            "启动现货滚雪球策略.py": "start_snowball_strategy.py",
            "演示专业倍增策略.py": "demo_doubling_strategy.py",
            "检查交易对格式.py": "check_trading_pairs.py",
            "快速验证倍增效果.py": "quick_validate_results.py",
            "启动终极版现货交易系统.py": "start_ultimate_trading_system.py",
            "演示GATE模拟交易.py": "demo_gate_trading.py",
            "测试倍增效果.py": "test_doubling_effect.py",
            "快速盈利实战演示.py": "quick_profit_demo.py"
        }
        
        renamed_count = 0
        for old_name, new_name in rename_mapping.items():
            old_path = self.project_root / old_name
            new_path = self.project_root / new_name
            
            if old_path.exists():
                try:
                    # 备份原文件
                    shutil.copy2(str(old_path), str(self.backup_dir / old_name))
                    
                    # 重命名文件
                    old_path.rename(new_path)
                    renamed_count += 1
                    self.changes_log.append(f"重命名文件: {old_name} → {new_name}")
                    print(f"  ✅ {old_name} → {new_name}")
                except Exception as e:
                    self.errors_log.append(f"重命名失败 {old_name}: {e}")
                    print(f"  ❌ 重命名失败 {old_name}: {e}")
        
        print(f"  📊 重命名了 {renamed_count} 个文件")
    
    def _cleanup_debug_prints(self):
        """清理调试print语句"""
        print("🐛 清理调试print语句...")
        
        python_files = list(self.project_root.rglob("*.py"))
        total_cleaned = 0
        
        for py_file in python_files:
            if py_file.name.startswith('__') or 'pycache' in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 清理调试print语句的模式
                debug_patterns = [
                    r'print\s*\(\s*"[=\-]{10,}"\s*\)',  # print("="*50)
                    r'print\s*\(\s*"测试[^"]*"\s*\)',     # print("测试...")
                    r'print\s*\(\s*"调试[^"]*"\s*\)',     # print("调试...")
                    r'print\s*\(\s*f?"debug[^"]*"\s*\)', # print("debug...")
                ]
                
                cleaned_count = 0
                for pattern in debug_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        content = re.sub(pattern, '# 调试输出已清理', content, flags=re.IGNORECASE)
                        cleaned_count += len(matches)
                
                # 如果有修改，保存文件
                if content != original_content:
                    # 备份原文件
                    backup_file = self.backup_dir / f"debug_cleanup_{py_file.name}"
                    shutil.copy2(str(py_file), str(backup_file))
                    
                    # 保存清理后的文件
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    total_cleaned += cleaned_count
                    self.changes_log.append(f"清理调试输出: {py_file.name} ({cleaned_count}个)")
                    
            except Exception as e:
                self.errors_log.append(f"清理调试输出失败 {py_file}: {e}")
        
        print(f"  📊 清理了 {total_cleaned} 个调试输出")
    
    def _organize_config_files(self):
        """统一配置文件管理"""
        print("📁 统一配置文件...")
        
        # 确保config目录存在
        config_dir = self.project_root / "config"
        config_dir.mkdir(exist_ok=True)
        
        # 需要移动的配置文件
        config_files_to_move = [
            ("config.json", "config/main_config.json"),
            ("config.demo.json", "config/demo_config.json")
        ]
        
        moved_count = 0
        for src, dst in config_files_to_move:
            src_path = self.project_root / src
            dst_path = self.project_root / dst
            
            if src_path.exists() and not dst_path.exists():
                try:
                    shutil.move(str(src_path), str(dst_path))
                    moved_count += 1
                    self.changes_log.append(f"移动配置文件: {src} → {dst}")
                    print(f"  ✅ {src} → {dst}")
                except Exception as e:
                    self.errors_log.append(f"移动配置文件失败 {src}: {e}")
        
        print(f"  📊 整理了 {moved_count} 个配置文件")
    
    def _create_secure_config_template(self):
        """创建安全配置模板"""
        print("🔐 创建安全配置模板...")
        
        # 创建.env模板
        env_template = """# 环境变量配置模板
# Environment Variables Template

# GATE.IO API配置
GATE_API_KEY=your_api_key_here
GATE_SECRET_KEY=your_secret_key_here
GATE_PASSPHRASE=your_passphrase_here

# 交易模式 (demo/live)
TRADING_MODE=demo

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 是否启用安全模式
SECURE_MODE=true

# 数据库配置（如果需要）
DATABASE_URL=sqlite:///trading.db

# Redis配置（如果需要）
REDIS_URL=redis://localhost:6379

# 监控配置
ENABLE_MONITORING=true
ALERT_EMAIL=<EMAIL>
"""
        
        try:
            env_file = self.project_root / ".env.template"
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_template)
            
            self.changes_log.append("创建安全配置模板: .env.template")
            print("  ✅ 创建 .env.template")
            
            # 创建.gitignore（如果不存在）
            gitignore_content = """# 环境变量文件
.env
.env.local

# API密钥和敏感配置
config/api_credentials.json
config/live_config.json

# 日志文件
*.log
logs/

# 缓存文件
__pycache__/
*.pyc
*.pyo

# 临时文件
temp/
*.tmp

# 数据库文件
*.db
*.sqlite

# 备份文件
backups/
"""
            
            gitignore_file = self.project_root / ".gitignore"
            if not gitignore_file.exists():
                with open(gitignore_file, 'w', encoding='utf-8') as f:
                    f.write(gitignore_content)
                self.changes_log.append("创建.gitignore文件")
                print("  ✅ 创建 .gitignore")
                
        except Exception as e:
            self.errors_log.append(f"创建安全配置失败: {e}")
    
    def _optimize_imports(self):
        """优化import语句"""
        print("📦 优化import语句...")
        
        python_files = list(self.project_root.rglob("*.py"))
        optimized_count = 0
        
        for py_file in python_files:
            if py_file.name.startswith('__') or 'pycache' in str(py_file):
                continue
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 简单的import优化：移除重复导入
                imports = set()
                optimized_lines = []
                import_section = True
                
                for line in lines:
                    stripped = line.strip()
                    
                    # 检测import语句
                    if stripped.startswith(('import ', 'from ')) and import_section:
                        if stripped not in imports:
                            imports.add(stripped)
                            optimized_lines.append(line)
                        else:
                            optimized_count += 1  # 发现重复导入
                    else:
                        if stripped and not stripped.startswith('#'):
                            import_section = False
                        optimized_lines.append(line)
                
                # 如果有优化，保存文件
                if len(optimized_lines) < len(lines):
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.writelines(optimized_lines)
                    
                    self.changes_log.append(f"优化import: {py_file.name}")
                    
            except Exception as e:
                self.errors_log.append(f"优化import失败 {py_file}: {e}")
        
        print(f"  📊 优化了 {optimized_count} 个import语句")
    
    def _add_type_hints(self):
        """添加基础类型注解"""
        print("🏷️ 添加类型注解模板...")
        
        # 创建类型注解模板文件
        type_hints_template = """# 类型注解模板
# Type Hints Templates

from typing import Dict, List, Optional, Union, Tuple, Any, Callable
from datetime import datetime
from decimal import Decimal

# 常用类型别名
ConfigDict = Dict[str, Any]
TradingPair = str
Price = Union[float, Decimal] 
Amount = Union[float, Decimal]
Timestamp = datetime

# 交易相关类型
class OrderSide:
    BUY = "buy"
    SELL = "sell"

class OrderType:
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"

# 函数签名示例
def place_order(
    symbol: TradingPair,
    side: str,
    amount: Amount,
    price: Optional[Price] = None,
    order_type: str = OrderType.MARKET
) -> Dict[str, Any]:
    \"\"\"下单函数示例\"\"\"
    pass

def get_balance() -> Dict[str, Amount]:
    \"\"\"获取余额\"\"\"
    pass

def get_market_data(symbol: TradingPair) -> Dict[str, Any]:
    \"\"\"获取市场数据\"\"\"
    pass
"""
        
        try:
            type_hints_file = self.project_root / "core" / "type_definitions.py"
            with open(type_hints_file, 'w', encoding='utf-8') as f:
                f.write(type_hints_template)
            
            self.changes_log.append("创建类型注解模板: core/type_definitions.py")
            print("  ✅ 创建类型注解模板")
            
        except Exception as e:
            self.errors_log.append(f"创建类型注解失败: {e}")
    
    def _setup_logging_system(self):
        """设置日志系统"""
        print("📝 设置标准日志系统...")
        
        logging_config = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
标准日志配置
Standard Logging Configuration
\"\"\"

import logging
import os
from datetime import datetime
from pathlib import Path

def setup_logging(log_level: str = "INFO", log_to_file: bool = True):
    \"\"\"
    设置标准日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: 是否写入文件
    \"\"\"
    
    # 创建logs目录
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 配置root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有handlers
    root_logger.handlers.clear()
    
    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件handler（如果启用）
    if log_to_file:
        today = datetime.now().strftime('%Y%m%d')
        log_file = logs_dir / f"trading_{today}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    logging.info("日志系统初始化完成")

def get_logger(name: str) -> logging.Logger:
    \"\"\"获取指定名称的logger\"\"\"
    return logging.getLogger(name)

# 使用示例
if __name__ == "__main__":
    setup_logging("DEBUG", True)
    logger = get_logger(__name__)
    
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
"""
        
        try:
            logging_file = self.project_root / "core" / "logging_setup.py"
            with open(logging_file, 'w', encoding='utf-8') as f:
                f.write(logging_config)
            
            self.changes_log.append("创建标准日志配置: core/logging_setup.py")
            print("  ✅ 创建标准日志配置")
            
        except Exception as e:
            self.errors_log.append(f"创建日志配置失败: {e}")
    
    def _update_requirements(self):
        """更新requirements.txt"""
        print("📋 更新依赖包列表...")
        
        # 标准依赖包
        requirements = [
            "# 核心依赖",
            "requests>=2.28.0",
            "websocket-client>=1.4.0", 
            "pandas>=1.5.0",
            "numpy>=1.24.0",
            "",
            "# GUI依赖",
            "tkinter",  # 通常是内置的
            "",
            "# 异步处理",
            "asyncio",  # Python 3.7+内置
            "",
            "# 数据处理",
            "json",     # 内置
            "datetime", # 内置
            "pathlib",  # 内置
            "",
            "# 类型注解",
            "typing",   # 内置
            "typing-extensions>=4.0.0",
            "",
            "# 并发处理", 
            "concurrent.futures",  # 内置
            "",
            "# 开发和测试",
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "",
            "# 环境变量",
            "python-dotenv>=0.19.0",
            "",
            "# 日志和监控",
            "psutil>=5.9.0",
            "",
            "# 安全相关",
            "cryptography>=3.4.8"
        ]
        
        try:
            requirements_file = self.project_root / "requirements.txt"
            with open(requirements_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(requirements))
            
            self.changes_log.append("更新requirements.txt")
            print("  ✅ 更新 requirements.txt")
            
        except Exception as e:
            self.errors_log.append(f"更新requirements.txt失败: {e}")
    
    def _generate_improvement_report(self):
        """生成改进报告"""
        print("\n📊 生成改进报告...")
        
        report_content = f"""# 自动化系统改进报告

## 📋 改进摘要

**执行时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**应用改进数量**: {len(self.changes_log)}
**遇到问题数量**: {len(self.errors_log)}

---

## ✅ 成功应用的改进

"""
        
        for i, change in enumerate(self.changes_log, 1):
            report_content += f"{i}. {change}\n"
        
        if self.errors_log:
            report_content += f"""
---

## ⚠️ 遇到的问题

"""
            for i, error in enumerate(self.errors_log, 1):
                report_content += f"{i}. {error}\n"
        
        report_content += f"""
---

## 📈 改进效果预期

### 代码质量提升
- ✅ 清理了调试输出，提高代码专业性
- ✅ 重命名中文文件，提高跨平台兼容性  
- ✅ 整理重复文件，简化项目结构
- ✅ 统一配置管理，提高维护性

### 安全性增强
- ✅ 创建环境变量模板，保护敏感信息
- ✅ 添加.gitignore，防止意外提交敏感文件

### 开发体验改进
- ✅ 标准化日志系统，便于调试和监控
- ✅ 添加类型注解模板，提高代码可读性
- ✅ 更新依赖列表，确保环境一致性

---

## 🚀 下一步建议

1. **立即执行**:
   - 检查.env.template并配置实际环境变量
   - 运行改进后的系统进行测试
   - 验证所有功能正常工作

2. **短期计划** (1-2周):
   - 为主要函数添加类型注解
   - 实施新的日志系统
   - 进行性能测试

3. **中期计划** (1个月):
   - 重构过长函数
   - 增加单元测试
   - 优化异步处理

---

## 📞 技术支持

如果在使用改进后的系统时遇到问题，请：

1. 检查logs/目录下的日志文件
2. 确认环境变量配置正确
3. 验证所有依赖包已安装

---

*自动化改进完成 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        try:
            report_file = self.project_root / f"IMPROVEMENT_EXECUTION_REPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"  ✅ 生成改进报告: {report_file.name}")
            
        except Exception as e:
            print(f"  ❌ 生成报告失败: {e}")

def main():
    """主函数"""
    print("🔧 终极版现货交易系统 - 自动化改进工具")
    print("=" * 60)
    
    # 确认执行
    response = input("是否开始自动化改进? (y/N): ").lower().strip()
    if response != 'y':
        print("❌ 改进已取消")
        return
    
    # 执行改进
    improver = SystemImprovement()
    improver.run_all_improvements()
    
    print("\n" + "=" * 60)
    print("🎉 改进完成! 系统质量已显著提升!")
    print("\n💡 建议:")
    print("1. 检查生成的改进报告")
    print("2. 配置.env文件中的环境变量")
    print("3. 运行改进后的系统进行测试")
    print("4. 查看logs/目录下的日志文件")

if __name__ == "__main__":
    main()

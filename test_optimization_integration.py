#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统集成测试
System Integration Test
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

def test_optimization_modules():
    """测试优化模块"""
    print("🧪 测试优化模块导入...")
    
    try:
        # 测试策略优化器
        from strategy_optimizer import StrategyOptimizer
        print("✅ 策略优化器模块导入成功")
        
        # 测试UX增强
        from ux_enhancement import SmartHintSystem
        print("✅ UX增强模块导入成功")
        
        # 测试测试框架
        from testing_framework import TestRunner
        print("✅ 测试框架模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🔧 测试系统集成...")
    
    try:
        # 创建优化组件实例
        from strategy_optimizer import StrategyOptimizer
        from ux_enhancement import SmartHintSystem
        from testing_framework import TestRunner
        
        # 初始化组件
        strategy_optimizer = StrategyOptimizer()
        smart_hints = SmartHintSystem()
        test_runner = TestRunner()
        
        print("✅ 优化组件初始化成功")
        
        # 测试基本功能
        if hasattr(strategy_optimizer, 'optimize_strategy'):
            print("✅ 策略优化功能可用")
        
        if hasattr(smart_hints, 'add_hint'):
            print("✅ 智能提示功能可用")
            
        if hasattr(test_runner, 'run_tests'):
            print("✅ 测试运行功能可用")
            
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成 (无界面)"""
    print("\n🖥️ 测试GUI集成准备...")
    
    try:
        # 导入GUI模块
        from ultimate_trading_gui import UltimateTradingGUI
        print("✅ 主GUI模块可导入")
        
        # 检查系统集成模块
        import system_integration
        print("✅ 系统集成模块可导入")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成准备失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 企业级现货交易系统 - 优化集成测试")
    print("=" * 60)
    
    # 测试模块导入
    modules_ok = test_optimization_modules()
    
    # 测试系统集成
    integration_ok = test_system_integration()
    
    # 测试GUI集成准备
    gui_ok = test_gui_integration()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   模块导入: {'✅ 通过' if modules_ok else '❌ 失败'}")
    print(f"   系统集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
    print(f"   GUI准备: {'✅ 通过' if gui_ok else '❌ 失败'}")
    
    if modules_ok and integration_ok and gui_ok:
        print("\n🎉 所有测试通过！系统优化集成成功！")
        print("\n🚀 可以启动优化版交易系统:")
        print("   python core/ultimate_trading_gui.py")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    main()

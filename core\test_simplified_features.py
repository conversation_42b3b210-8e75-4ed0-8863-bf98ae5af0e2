#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化功能测试脚本
Simplified Features Test: Core Functions without External Dependencies
"""

import os
import sys
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from institutional_framework.multi_exchange_interface import (BinanceInterface,
                                                              ExchangeManager,
                                                              OKXInterface)
from institutional_framework.strategy_backtest_system import (BacktestEngine,
                                                              StrategyLibrary)


def test_multi_exchange_basic():
    """测试多交易所基础功能"""
    print("🔍 测试多交易所基础功能...")

    try:
        # 创建交易所管理器
        manager = ExchangeManager()

        # 添加Binance接口（演示模式）
        binance_interface = BinanceInterface(testnet=True)
        manager.add_exchange("binance", binance_interface)

        # 添加OKX接口（演示模式）
        okx_interface = OKXInterface(testnet=True)
        manager.add_exchange("okx", okx_interface)

        print(f"✅ 已添加 {len(manager.exchanges)} 个交易所")

        # 测试交易所切换
        manager.set_active_exchange("binance")
        active = manager.get_active_exchange()
        print(f"🎯 当前活跃交易所: {active.name if active else 'None'}")

        # 测试支持的交易所列表
        supported = manager.get_supported_exchanges()
        print(f"📋 支持的交易所: {list(supported.values())}")

        print("✅ 多交易所基础功能测试完成")
        return True

    except Exception as e:
        print(f"❌ 多交易所测试失败: {e}")
        return False


def test_strategy_backtesting_basic():
    """测试策略回测基础功能"""
    print("\n🔍 测试策略回测基础功能...")

    try:
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=10000.0, commission=0.001)

        # 生成示例数据
        print("📊 生成历史数据...")
        engine.generate_sample_data("BTC_USDT", days=30, start_price=50000.0)

        # 检查数据
        if "BTC_USDT" in engine.price_data:
            data_length = len(engine.price_data["BTC_USDT"])
            print(f"✅ 生成了 {data_length} 条历史数据")

        # 测试简单交易
        print("💰 测试交易功能...")
        engine.current_time = datetime.now()
        engine.current_prices = {"BTC_USDT": 50000.0}

        # 买入测试
        buy_result = engine.place_order(
            "BTC_USDT", "buy", 1000.0
        )  # 买入1000 USDT的BTC
        if buy_result:
            print(
                f"✅ 买入成功，持仓: {engine.positions.get('BTC_USDT', 0):.6f} BTC"
            )

        # 卖出测试
        if "BTC_USDT" in engine.positions:
            sell_amount = engine.positions["BTC_USDT"]
            sell_result = engine.place_order("BTC_USDT", "sell", sell_amount)
            if sell_result:
                print(
                    f"✅ 卖出成功，剩余资金: {engine.current_capital:.2f} USDT"
                )

        # 测试性能指标计算
        engine.equity_curve = [
            {"timestamp": datetime.now(), "portfolio_value": 10000.0},
            {"timestamp": datetime.now(), "portfolio_value": 10500.0},
        ]
        engine.calculate_metrics()

        if engine.metrics:
            print(
                f"📊 性能指标计算成功: 收益率 {engine.metrics.get('total_return', 0):.2%}"
            )

        print("✅ 策略回测基础功能测试完成")
        return True

    except Exception as e:
        print(f"❌ 策略回测测试失败: {e}")
        return False


def test_monitoring_basic():
    """测试监控基础功能"""
    print("\n🔍 测试监控基础功能...")

    try:
        # 简化的监控系统测试
        from institutional_framework.monitoring_alert_system import (
            Alert, AlertLevel, AlertType, MonitoringSystem)

        # 创建监控系统
        monitor = MonitoringSystem()

        # 测试告警创建
        test_alert = Alert(
            alert_id="test_001",
            alert_type=AlertType.SYSTEM_ALERT,
            level=AlertLevel.INFO,
            title="测试告警",
            message="这是一个测试告警",
        )

        monitor.alerts.append(test_alert)
        print(f"✅ 创建测试告警: {test_alert.title}")

        # 测试告警查询
        active_alerts = monitor.get_active_alerts()
        print(f"📢 活跃告警数量: {len(active_alerts)}")

        # 测试告警确认
        if active_alerts:
            monitor.acknowledge_alert(active_alerts[0].alert_id)
            print("✅ 告警确认功能正常")

        # 测试监控状态
        status = monitor.get_monitoring_status()
        print(f"📊 监控状态: {status}")

        print("✅ 监控基础功能测试完成")
        return True

    except Exception as e:
        print(f"❌ 监控测试失败: {e}")
        return False


def test_key_management_basic():
    """测试密钥管理基础功能"""
    print("\n🔍 测试密钥管理基础功能...")

    try:
        from institutional_framework.secure_key_management import KeyManager

        # 创建密钥管理器（使用简单密码）
        km = KeyManager(master_password="test_password")

        # 测试添加密钥
        add_result = km.add_exchange_keys(
            exchange_id="test_exchange",
            exchange_name="测试交易所",
            api_key="test_api_key_123456",
            api_secret="test_api_secret_789012",
            testnet=True,
            description="测试用密钥",
        )

        if add_result:
            print("✅ 密钥添加成功")

        # 测试密钥列表
        exchanges = km.list_exchanges()
        print(f"📋 管理的交易所数量: {len(exchanges)}")

        # 测试密钥获取
        if exchanges:
            test_keys = km.get_exchange_keys("test_exchange")
            if test_keys:
                print(f"🔑 密钥获取成功: {test_keys['api_key'][:8]}...")

        # 测试安全状态
        security_status = km.get_security_status()
        print(f"🛡️ 安全状态: {security_status}")

        print("✅ 密钥管理基础功能测试完成")
        return True

    except Exception as e:
        print(f"❌ 密钥管理测试失败: {e}")
        return False


def test_integration_workflow():
    """测试集成工作流"""
    print("\n🔍 测试集成工作流...")

    try:
        # 1. 创建交易所管理器
        manager = ExchangeManager()
        binance = BinanceInterface(testnet=True)
        manager.add_exchange("binance", binance)

        # 2. 创建回测引擎
        engine = BacktestEngine(initial_capital=5000.0)
        engine.generate_sample_data("BTC_USDT", days=10)

        # 3. 简单策略测试
        def simple_strategy(engine, timestamp, prices):
            """简单的买入持有策略"""
            if "BTC_USDT" in prices and "BTC_USDT" not in engine.positions:
                # 第一次买入
                buy_amount = engine.current_capital * 0.5 / prices["BTC_USDT"]
                engine.place_order("BTC_USDT", "buy", buy_amount)

        # 运行简单回测
        result = engine.run_backtest(
            simple_strategy,
            ["BTC_USDT"],
            start_date=datetime.now() - timedelta(days=8),
            end_date=datetime.now() - timedelta(days=2),
        )

        # 4. 生成报告
        print("\n📊 集成工作流报告:")
        print(f"   交易所管理: {len(manager.exchanges)} 个交易所")
        print(f"   回测结果: {'成功' if result['success'] else '失败'}")
        if result["success"]:
            print(f"   交易次数: {result['trades']}")
            print(f"   最终价值: {result['final_value']:.2f}")

        print("✅ 集成工作流测试完成")
        return True

    except Exception as e:
        print(f"❌ 集成工作流测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 简化功能测试开始")
    print("=" * 60)

    # 测试各个模块
    multi_exchange_success = test_multi_exchange_basic()
    backtesting_success = test_strategy_backtesting_basic()
    monitoring_success = test_monitoring_basic()
    key_management_success = test_key_management_basic()
    integration_success = test_integration_workflow()

    # 总结
    print("\n" + "=" * 60)
    print("📋 简化测试结果总结:")
    print(
        f"   多交易所基础: {'✅ 通过' if multi_exchange_success else '❌ 失败'}"
    )
    print(
        f"   策略回测基础: {'✅ 通过' if backtesting_success else '❌ 失败'}"
    )
    print(f"   监控基础功能: {'✅ 通过' if monitoring_success else '❌ 失败'}")
    print(
        f"   密钥管理基础: {'✅ 通过' if key_management_success else '❌ 失败'}"
    )
    print(f"   集成工作流: {'✅ 通过' if integration_success else '❌ 失败'}")

    # 计算总体成功率
    tests = [
        multi_exchange_success,
        backtesting_success,
        monitoring_success,
        key_management_success,
        integration_success,
    ]

    success_count = sum(tests)
    total_tests = len(tests)
    success_rate = (success_count / total_tests) * 100

    print(
        f"\n📊 总体测试结果: {success_count}/{total_tests} 通过 ({success_rate:.1f}%)"
    )

    if success_rate >= 80:
        print("\n🎊 简化功能测试大部分通过!")
        print("💡 核心功能模块基本正常")
        print("🔗 系统架构和基础功能完善")
        print("🛡️ 多交易所、回测、监控、安全管理框架完整")
    else:
        print("\n⚠️ 部分功能测试失败")
        print("💡 建议检查系统配置")

    print("\n🎯 已实现的核心功能:")
    print("   📊 多交易所统一接口")
    print("   📈 策略回测引擎")
    print("   🔔 监控告警系统")
    print("   🔐 安全密钥管理")
    print("   🔄 高级订单类型")
    print("   📡 WebSocket实时数据")

    print("\n🔮 下一步可以实现:")
    print("   📈 高级图表分析")
    print("   🤖 机器学习策略")
    print("   🌐 Web管理界面")
    print("   📱 移动端应用")

    print("\n按回车键退出...")
    input()


if __name__ == "__main__":
    main()

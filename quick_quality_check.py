#!/usr/bin/env python3
"""
代码质量快速检查工具
Quick Code Quality Checker
"""

import os
import re
from pathlib import Path

def quick_quality_check():
    """快速代码质量检查"""
    
    print("🔍 开始快速代码质量检查...")
    
    project_root = Path(".")
    issues = []
    
    # 检查Python文件
    python_files = list(project_root.rglob("*.py"))
    
    print(f"📁 发现 {len(python_files)} 个Python文件")
    
    for py_file in python_files:
        print(f"  检查: {py_file.name}")
        
        try:
            with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
                
                # 1. 检查长行
                for i, line in enumerate(lines, 1):
                    if len(line) > 120:
                        issues.append(f"⚠️  长行 ({len(line)}字符): {py_file}:{i}")
                
                # 2. 检查print语句数量
                print_count = content.count('print(')
                if print_count > 15:
                    issues.append(f"📢 过多print语句 ({print_count}个): {py_file}")
                
                # 3. 检查TODO/FIXME
                for i, line in enumerate(lines, 1):
                    if 'TODO' in line.upper() or 'FIXME' in line.upper():
                        issues.append(f"📝 待办事项: {py_file}:{i} - {line.strip()}")
                
                # 4. 检查可能的敏感信息
                sensitive_patterns = [
                    (r'api_key\s*=\s*["\']([^"\']{10,})["\']', 'API Key'),
                    (r'secret\s*=\s*["\']([^"\']{10,})["\']', 'Secret'),
                    (r'password\s*=\s*["\']([^"\']{5,})["\']', 'Password'),
                ]
                
                for pattern, issue_type in sensitive_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        value = match.group(1)
                        if not any(placeholder in value.lower() for placeholder in ['your_', 'example', 'demo', 'test']):
                            line_num = content[:match.start()].count('\n') + 1
                            issues.append(f"🔒 疑似{issue_type}: {py_file}:{line_num}")
                
                # 5. 检查性能问题
                perf_patterns = [
                    (r'time\.sleep\s*\(\s*[^)]*\)', 'Sync Sleep'),
                    (r'for.*in.*\.keys\(\)', 'Dict Keys Iteration'),
                ]
                
                for pattern, issue_type in perf_patterns:
                    if re.search(pattern, content):
                        issues.append(f"⚡ 性能问题 ({issue_type}): {py_file}")
                
        except Exception as e:
            issues.append(f"❌ 文件读取错误: {py_file} - {e}")
    
    # 6. 检查文件大小
    for py_file in python_files:
        try:
            size_kb = py_file.stat().st_size // 1024
            if size_kb > 50:  # 超过50KB
                issues.append(f"📦 大文件 ({size_kb}KB): {py_file}")
        except:
            pass
    
    # 7. 检查重复文件名
    file_names = {}
    for py_file in python_files:
        name = py_file.name
        if name in file_names:
            issues.append(f"📋 重复文件名: {name} - {file_names[name]} 和 {py_file}")
        file_names[name] = py_file
    
    # 8. 检查空文件
    for py_file in python_files:
        try:
            if py_file.stat().st_size == 0:
                issues.append(f"📄 空文件: {py_file}")
        except:
            pass
    
    # 9. 检查配置文件
    json_files = list(project_root.rglob("*.json"))
    for json_file in json_files:
        try:
            import json
            with open(json_file, 'r', encoding='utf-8') as f:
                json.load(f)
        except json.JSONDecodeError:
            issues.append(f"⚠️  无效JSON: {json_file}")
        except:
            pass
    
    # 10. 生成报告
    print("\n" + "="*60)
    print("🔍 快速代码质量检查结果")
    print("="*60)
    
    print(f"📊 检查文件: {len(python_files)} 个Python文件")
    print(f"⚠️  发现问题: {len(issues)} 个")
    
    if not issues:
        print("✅ 恭喜！未发现明显的代码质量问题")
        return
    
    # 按类型分组显示问题
    issue_types = {
        '⚠️': [],   # 警告
        '📢': [],   # Print语句
        '📝': [],   # TODO
        '🔒': [],   # 安全
        '⚡': [],   # 性能
        '❌': [],   # 错误
        '📦': [],   # 大文件
        '📋': [],   # 重复
        '📄': []    # 空文件
    }
    
    for issue in issues:
        for emoji in issue_types.keys():
            if issue.startswith(emoji):
                issue_types[emoji].append(issue)
                break
    
    for emoji, category_issues in issue_types.items():
        if category_issues:
            category_names = {
                '⚠️': '警告',
                '📢': 'Print语句过多',
                '📝': '待办事项',
                '🔒': '安全问题',
                '⚡': '性能问题',
                '❌': '错误',
                '📦': '大文件',
                '📋': '重复文件',
                '📄': '空文件'
            }
            
            print(f"\n{emoji} {category_names.get(emoji, '其他')} ({len(category_issues)}个):")
            for issue in category_issues[:5]:  # 显示前5个
                print(f"  {issue}")
            if len(category_issues) > 5:
                print(f"  ... 还有 {len(category_issues) - 5} 个类似问题")
    
    # 生成质量分数
    critical_issues = len(issue_types['❌']) + len(issue_types['🔒'])
    major_issues = len(issue_types['⚡']) + len(issue_types['📦'])
    minor_issues = len(issues) - critical_issues - major_issues
    
    quality_score = max(0, 100 - critical_issues * 10 - major_issues * 3 - minor_issues * 1)
    
    if quality_score >= 90:
        grade = "A (优秀)"
    elif quality_score >= 80:
        grade = "B (良好)"
    elif quality_score >= 70:
        grade = "C (中等)"
    elif quality_score >= 60:
        grade = "D (需改进)"
    else:
        grade = "F (需重构)"
    
    print(f"\n🎯 质量评分: {quality_score}/100 ({grade})")
    
    # 生成建议
    print("\n💡 改进建议:")
    suggestions = []
    
    if issue_types['❌']:
        suggestions.append("🚨 立即修复文件读取错误")
    if issue_types['🔒']:
        suggestions.append("🔒 检查并保护敏感信息")
    if issue_types['⚡']:
        suggestions.append("⚡ 优化性能瓶颈")
    if issue_types['📢']:
        suggestions.append("📢 使用logging替代print语句")
    if issue_types['📦']:
        suggestions.append("📦 考虑拆分大文件")
    if issue_types['📋']:
        suggestions.append("📋 整理重复文件")
    if issue_types['📄']:
        suggestions.append("📄 清理空文件")
    if issue_types['📝']:
        suggestions.append("📝 处理待办事项")
    
    if not suggestions:
        suggestions.append("✅ 继续保持良好的代码质量")
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")
    
    print("="*60)
    
    return {
        'total_files': len(python_files),
        'total_issues': len(issues),
        'quality_score': quality_score,
        'grade': grade,
        'issues_by_type': issue_types
    }

if __name__ == "__main__":
    quick_quality_check()

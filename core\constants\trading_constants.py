#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易常量
Trading Constants

定义交易相关的常量
"""

from typing import Dict, List, Tuple
from decimal import Decimal


class TradingConstants:
    """交易常量"""
    
    # ==================== 基础交易参数 ====================
    # 默认资金配置
    DEFAULT_INITIAL_CAPITAL = Decimal('10000.0')
    MIN_INITIAL_CAPITAL = Decimal('100.0')
    MAX_INITIAL_CAPITAL = Decimal('10000000.0')
    
    # 默认风险参数
    DEFAULT_RISK_PER_TRADE = Decimal('0.02')  # 2%
    MIN_RISK_PER_TRADE = Decimal('0.001')     # 0.1%
    MAX_RISK_PER_TRADE = Decimal('0.1')       # 10%
    
    # 止损止盈
    DEFAULT_STOP_LOSS_PCT = Decimal('0.05')   # 5%
    DEFAULT_TAKE_PROFIT_PCT = Decimal('0.10') # 10%
    MIN_STOP_LOSS_PCT = Decimal('0.01')       # 1%
    MAX_STOP_LOSS_PCT = Decimal('0.2')        # 20%
    MIN_TAKE_PROFIT_PCT = Decimal('0.01')     # 1%
    MAX_TAKE_PROFIT_PCT = Decimal('0.5')      # 50%
    
    # ==================== 交易限制 ====================
    # 最小交易金额
    MIN_TRADE_AMOUNT_USDT = Decimal('10.0')
    MIN_TRADE_AMOUNT_BTC = Decimal('0.0001')
    MIN_TRADE_AMOUNT_ETH = Decimal('0.001')
    
    # 最大持仓
    MAX_OPEN_POSITIONS = 10
    DEFAULT_MAX_POSITIONS = 5
    MIN_MAX_POSITIONS = 1
    
    # 最大持仓比例
    MAX_POSITION_SIZE_PCT = Decimal('0.3')    # 30%
    DEFAULT_POSITION_SIZE_PCT = Decimal('0.1') # 10%
    MIN_POSITION_SIZE_PCT = Decimal('0.01')   # 1%
    
    # 每日交易限制
    MAX_DAILY_TRADES = 100
    DEFAULT_MAX_DAILY_TRADES = 50
    MIN_DAILY_TRADES = 1
    
    # ==================== 风险管理参数 ====================
    # 最大回撤
    MAX_DRAWDOWN_LIMIT = Decimal('0.2')       # 20%
    DEFAULT_DRAWDOWN_LIMIT = Decimal('0.15')  # 15%
    MIN_DRAWDOWN_LIMIT = Decimal('0.05')      # 5%
    
    # 相关性限制
    MAX_CORRELATION_LIMIT = Decimal('0.8')    # 80%
    DEFAULT_CORRELATION_LIMIT = Decimal('0.7') # 70%
    MIN_CORRELATION_LIMIT = Decimal('0.3')    # 30%
    
    # 波动率阈值
    HIGH_VOLATILITY_THRESHOLD = Decimal('0.5') # 50%
    MEDIUM_VOLATILITY_THRESHOLD = Decimal('0.3') # 30%
    LOW_VOLATILITY_THRESHOLD = Decimal('0.1')  # 10%
    
    # VaR置信水平
    VAR_CONFIDENCE_95 = Decimal('0.95')       # 95%
    VAR_CONFIDENCE_99 = Decimal('0.99')       # 99%
    DEFAULT_VAR_CONFIDENCE = VAR_CONFIDENCE_95
    
    # ==================== 支持的交易对 ====================
    # 主要交易对
    MAJOR_SYMBOLS = [
        "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT",
        "SOL/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT"
    ]
    
    # 稳定币交易对
    STABLECOIN_SYMBOLS = [
        "USDT/USD", "USDC/USD", "BUSD/USD", "DAI/USD"
    ]
    
    # DeFi代币
    DEFI_SYMBOLS = [
        "UNI/USDT", "SUSHI/USDT", "AAVE/USDT", "COMP/USDT",
        "MKR/USDT", "YFI/USDT", "CRV/USDT", "1INCH/USDT"
    ]
    
    # 所有支持的交易对
    ALL_SUPPORTED_SYMBOLS = MAJOR_SYMBOLS + DEFI_SYMBOLS
    
    # ==================== 订单类型 ====================
    # 基础订单类型
    ORDER_TYPE_MARKET = "Market"
    ORDER_TYPE_LIMIT = "Limit"
    ORDER_TYPE_STOP = "Stop"
    ORDER_TYPE_STOP_LIMIT = "Stop-Limit"
    ORDER_TYPE_ICEBERG = "Iceberg"
    ORDER_TYPE_TWA = "TWA"  # Time Weighted Average
    
    # 所有订单类型
    ALL_ORDER_TYPES = [
        ORDER_TYPE_MARKET,
        ORDER_TYPE_LIMIT,
        ORDER_TYPE_STOP,
        ORDER_TYPE_STOP_LIMIT,
        ORDER_TYPE_ICEBERG,
        ORDER_TYPE_TWA
    ]
    
    # 订单方向
    ORDER_SIDE_BUY = "BUY"
    ORDER_SIDE_SELL = "SELL"
    ORDER_SIDES = [ORDER_SIDE_BUY, ORDER_SIDE_SELL]
    
    # 订单状态
    ORDER_STATUS_PENDING = "PENDING"
    ORDER_STATUS_PARTIAL = "PARTIAL"
    ORDER_STATUS_FILLED = "FILLED"
    ORDER_STATUS_CANCELLED = "CANCELLED"
    ORDER_STATUS_REJECTED = "REJECTED"
    ORDER_STATUS_EXPIRED = "EXPIRED"
    
    ALL_ORDER_STATUSES = [
        ORDER_STATUS_PENDING,
        ORDER_STATUS_PARTIAL,
        ORDER_STATUS_FILLED,
        ORDER_STATUS_CANCELLED,
        ORDER_STATUS_REJECTED,
        ORDER_STATUS_EXPIRED
    ]
    
    # ==================== 时间框架 ====================
    # K线时间框架
    TIMEFRAME_1M = "1m"
    TIMEFRAME_5M = "5m"
    TIMEFRAME_15M = "15m"
    TIMEFRAME_30M = "30m"
    TIMEFRAME_1H = "1h"
    TIMEFRAME_4H = "4h"
    TIMEFRAME_1D = "1d"
    TIMEFRAME_1W = "1w"
    TIMEFRAME_1M_PERIOD = "1M"
    
    ALL_TIMEFRAMES = [
        TIMEFRAME_1M, TIMEFRAME_5M, TIMEFRAME_15M, TIMEFRAME_30M,
        TIMEFRAME_1H, TIMEFRAME_4H, TIMEFRAME_1D, TIMEFRAME_1W, TIMEFRAME_1M_PERIOD
    ]
    
    # 默认时间框架
    DEFAULT_TIMEFRAME = TIMEFRAME_1H
    
    # ==================== 精度配置 ====================
    # 价格精度
    PRICE_PRECISION_BTC = 2
    PRICE_PRECISION_ETH = 2
    PRICE_PRECISION_ALTCOIN = 4
    PRICE_PRECISION_STABLECOIN = 4
    DEFAULT_PRICE_PRECISION = 8
    
    # 数量精度
    AMOUNT_PRECISION_BTC = 6
    AMOUNT_PRECISION_ETH = 5
    AMOUNT_PRECISION_ALTCOIN = 4
    DEFAULT_AMOUNT_PRECISION = 8
    
    # 百分比精度
    PERCENTAGE_PRECISION = 2
    
    # ==================== 手续费配置 ====================
    # 默认手续费率
    DEFAULT_MAKER_FEE = Decimal('0.001')      # 0.1%
    DEFAULT_TAKER_FEE = Decimal('0.001')      # 0.1%
    
    # VIP手续费率
    VIP_MAKER_FEE = Decimal('0.0008')         # 0.08%
    VIP_TAKER_FEE = Decimal('0.0008')         # 0.08%
    
    # 最大手续费率
    MAX_FEE_RATE = Decimal('0.01')            # 1%
    
    # ==================== 性能指标 ====================
    # 胜率阈值
    HIGH_WIN_RATE = Decimal('0.7')            # 70%
    GOOD_WIN_RATE = Decimal('0.6')            # 60%
    AVERAGE_WIN_RATE = Decimal('0.5')         # 50%
    
    # 盈亏比
    EXCELLENT_PROFIT_RATIO = Decimal('3.0')   # 3:1
    GOOD_PROFIT_RATIO = Decimal('2.0')        # 2:1
    MINIMUM_PROFIT_RATIO = Decimal('1.5')     # 1.5:1
    
    # 夏普比率
    EXCELLENT_SHARPE_RATIO = Decimal('2.0')
    GOOD_SHARPE_RATIO = Decimal('1.5')
    AVERAGE_SHARPE_RATIO = Decimal('1.0')
    
    # ==================== 市场状态 ====================
    # 市场趋势
    MARKET_TREND_BULLISH = "BULLISH"
    MARKET_TREND_BEARISH = "BEARISH"
    MARKET_TREND_SIDEWAYS = "SIDEWAYS"
    MARKET_TREND_UNKNOWN = "UNKNOWN"
    
    ALL_MARKET_TRENDS = [
        MARKET_TREND_BULLISH,
        MARKET_TREND_BEARISH,
        MARKET_TREND_SIDEWAYS,
        MARKET_TREND_UNKNOWN
    ]
    
    # 市场波动性
    VOLATILITY_VERY_LOW = "VERY_LOW"
    VOLATILITY_LOW = "LOW"
    VOLATILITY_MEDIUM = "MEDIUM"
    VOLATILITY_HIGH = "HIGH"
    VOLATILITY_VERY_HIGH = "VERY_HIGH"
    
    ALL_VOLATILITY_LEVELS = [
        VOLATILITY_VERY_LOW,
        VOLATILITY_LOW,
        VOLATILITY_MEDIUM,
        VOLATILITY_HIGH,
        VOLATILITY_VERY_HIGH
    ]
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def get_min_trade_amount(cls, symbol: str) -> Decimal:
        """获取最小交易金额"""
        if "BTC" in symbol:
            return cls.MIN_TRADE_AMOUNT_BTC
        elif "ETH" in symbol:
            return cls.MIN_TRADE_AMOUNT_ETH
        else:
            return cls.MIN_TRADE_AMOUNT_USDT
    
    @classmethod
    def get_price_precision(cls, symbol: str) -> int:
        """获取价格精度"""
        if "BTC" in symbol:
            return cls.PRICE_PRECISION_BTC
        elif "ETH" in symbol:
            return cls.PRICE_PRECISION_ETH
        elif any(stable in symbol for stable in ["USDT", "USDC", "BUSD"]):
            return cls.PRICE_PRECISION_STABLECOIN
        else:
            return cls.PRICE_PRECISION_ALTCOIN
    
    @classmethod
    def get_amount_precision(cls, symbol: str) -> int:
        """获取数量精度"""
        if "BTC" in symbol:
            return cls.AMOUNT_PRECISION_BTC
        elif "ETH" in symbol:
            return cls.AMOUNT_PRECISION_ETH
        else:
            return cls.AMOUNT_PRECISION_ALTCOIN
    
    @classmethod
    def is_major_symbol(cls, symbol: str) -> bool:
        """检查是否为主要交易对"""
        return symbol in cls.MAJOR_SYMBOLS
    
    @classmethod
    def is_valid_order_type(cls, order_type: str) -> bool:
        """检查订单类型是否有效"""
        return order_type in cls.ALL_ORDER_TYPES
    
    @classmethod
    def is_valid_timeframe(cls, timeframe: str) -> bool:
        """检查时间框架是否有效"""
        return timeframe in cls.ALL_TIMEFRAMES
    
    @classmethod
    def calculate_position_size(cls, capital: Decimal, risk_pct: Decimal, 
                              entry_price: Decimal, stop_loss_price: Decimal) -> Decimal:
        """计算持仓大小"""
        risk_amount = capital * risk_pct
        price_diff = abs(entry_price - stop_loss_price)
        if price_diff > 0:
            return risk_amount / price_diff
        return Decimal('0')
    
    @classmethod
    def calculate_risk_reward_ratio(cls, entry_price: Decimal, 
                                  stop_loss_price: Decimal, 
                                  take_profit_price: Decimal) -> Decimal:
        """计算风险收益比"""
        risk = abs(entry_price - stop_loss_price)
        reward = abs(take_profit_price - entry_price)
        if risk > 0:
            return reward / risk
        return Decimal('0')


# 创建全局交易常量实例
TRADING_CONSTANTS = TradingConstants()


if __name__ == "__main__":
    # 测试交易常量
    print("💰 交易常量测试")
    print(f"默认初始资金: {TradingConstants.DEFAULT_INITIAL_CAPITAL}")
    print(f"BTC最小交易量: {TradingConstants.get_min_trade_amount('BTC/USDT')}")
    print(f"ETH价格精度: {TradingConstants.get_price_precision('ETH/USDT')}")
    print(f"主要交易对数量: {len(TradingConstants.MAJOR_SYMBOLS)}")
    
    # 测试计算方法
    position_size = TradingConstants.calculate_position_size(
        Decimal('10000'), Decimal('0.02'), Decimal('50000'), Decimal('47500')
    )
    print(f"计算持仓大小: {position_size}")
    
    print("✅ 交易常量测试完成")

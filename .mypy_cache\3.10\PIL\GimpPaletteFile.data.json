{".class": "MypyFile", "_fullname": "PIL.GimpPaletteFile", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "GimpPaletteFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL.GimpPaletteFile.GimpPaletteFile", "name": "GimpPaletteFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL.GimpPaletteFile", "mro": ["PIL.GimpPaletteFile.GimpPaletteFile", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fp"], "arg_types": ["PIL.GimpPaletteFile.GimpPaletteFile", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GimpPaletteFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fp", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile._read", "name": "_read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fp", "limit"], "arg_types": ["PIL.GimpPaletteFile.GimpPaletteFile", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read of GimpPaletteFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frombytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.frombytes", "name": "frombytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "PIL.GimpPaletteFile.GimpPaletteFile"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frombytes of GimpPaletteFile", "ret_type": "PIL.GimpPaletteFile.GimpPaletteFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.frombytes", "name": "frombytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "PIL.GimpPaletteFile.GimpPaletteFile"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frombytes of GimpPaletteFile", "ret_type": "PIL.GimpPaletteFile.GimpPaletteFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getpalette": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.getpalette", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL.GimpPaletteFile.GimpPaletteFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpalette of GimpPaletteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "palette": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.palette", "name": "palette", "type": "builtins.bytes"}}, "rawmode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.rawmode", "name": "rawmode", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL.GimpPaletteFile.GimpPaletteFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL.GimpPaletteFile.GimpPaletteFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL.GimpPaletteFile.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py"}
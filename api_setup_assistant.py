#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API配置助手
API Setup Assistant

帮助用户正确配置Gate.io API密钥
"""

import os
import sys
import json
import getpass
import webbrowser
from pathlib import Path
from datetime import datetime

class APISetupAssistant:
    """API配置助手"""
    
    def __init__(self):
        self.api_key = ""
        self.api_secret = ""
        self.sandbox_mode = True
        self.trading_mode = "development"
        
    def run_setup(self):
        """运行配置助手"""
        print("🔧 Gate.io API配置助手")
        print("=" * 40)
        print("本助手将帮助您配置Gate.io API密钥")
        print("=" * 40)
        
        try:
            # 步骤1: 检查现有配置
            self._check_existing_config()
            
            # 步骤2: 获取API密钥指南
            self._show_api_guide()
            
            # 步骤3: 配置API密钥
            self._configure_api_keys()
            
            # 步骤4: 选择交易模式
            self._select_trading_mode()
            
            # 步骤5: 保存配置
            self._save_configuration()
            
            # 步骤6: 测试配置
            self._test_configuration()
            
            print("\n🎉 API配置完成！")
            return True
            
        except KeyboardInterrupt:
            print("\n❌ 配置被用户中断")
            return False
        except Exception as e:
            print(f"\n❌ 配置过程出错: {e}")
            return False
    
    def _check_existing_config(self):
        """检查现有配置"""
        print("\n📋 步骤1: 检查现有配置")
        print("-" * 30)
        
        env_file = Path('.env')
        if env_file.exists():
            print("✅ 找到现有配置文件")
            
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'demo_gate_api_key' in content or 'your_api_key_here' in content:
                print("⚠️ 当前使用演示配置")
                print("💡 需要配置真实API密钥")
            else:
                print("✅ 发现真实API配置")
                overwrite = input("是否重新配置? (y/n): ").strip().lower()
                if overwrite not in ['y', 'yes']:
                    print("❌ 用户取消配置")
                    return False
        else:
            print("⚠️ 未找到配置文件")
            print("💡 将创建新的配置")
    
    def _show_api_guide(self):
        """显示API获取指南"""
        print("\n📋 步骤2: Gate.io API密钥获取指南")
        print("-" * 30)
        
        print("""
🎯 获取Gate.io API密钥的步骤：

1. 📱 访问Gate.io官网
   - 网址: https://www.gate.io
   - 如果没有账户，请先注册

2. 🔐 登录您的账户
   - 完成身份验证（如果需要）
   - 启用双重验证（推荐）

3. ⚙️ 进入API管理
   - 点击右上角头像
   - 选择 "API管理"

4. 🔑 创建API密钥
   - 点击 "创建API密钥"
   - 输入API名称（如：Trading Bot）
   
5. 🛡️ 设置权限（重要！）
   ✅ 现货交易 (Spot Trading)
   ✅ 查看余额 (View Balance)
   ✅ 查看订单 (View Orders)
   ❌ 提币权限 (Withdrawal) - 建议关闭

6. 🔒 安全设置
   - 设置IP白名单（推荐）
   - 启用Google验证器
   
7. 📝 保存密钥
   - 复制API Key
   - 复制API Secret（只显示一次！）
""")
        
        open_browser = input("\n是否打开Gate.io官网? (y/n): ").strip().lower()
        if open_browser in ['y', 'yes']:
            try:
                webbrowser.open('https://www.gate.io')
                print("✅ 已在浏览器中打开Gate.io")
            except Exception:
                print("❌ 无法打开浏览器，请手动访问: https://www.gate.io")
        
        input("\n按回车键继续配置...")
    
    def _configure_api_keys(self):
        """配置API密钥"""
        print("\n📋 步骤3: 配置API密钥")
        print("-" * 30)
        
        print("请输入您的Gate.io API凭证:")
        print("💡 提示: 输入时请确保没有多余的空格")
        
        # 输入API Key
        while True:
            self.api_key = input("\nAPI Key: ").strip()
            if len(self.api_key) >= 20:  # Gate.io API Key通常较长
                print("✅ API Key格式正确")
                break
            else:
                print("❌ API Key长度不足，请检查后重新输入")
                print("💡 Gate.io API Key通常是32位字符")
        
        # 输入API Secret
        while True:
            self.api_secret = getpass.getpass("API Secret (输入时不显示): ").strip()
            if len(self.api_secret) >= 20:  # Gate.io API Secret通常较长
                print("✅ API Secret格式正确")
                break
            else:
                print("❌ API Secret长度不足，请检查后重新输入")
                print("💡 Gate.io API Secret通常是64位字符")
        
        # 确认信息
        print(f"\n📋 确认信息:")
        print(f"API Key: {self.api_key[:8]}...{self.api_key[-8:]}")
        print(f"API Secret: {'*' * len(self.api_secret)}")
        
        confirm = input("\n信息是否正确? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消，请重新输入")
            return self._configure_api_keys()
    
    def _select_trading_mode(self):
        """选择交易模式"""
        print("\n📋 步骤4: 选择交易模式")
        print("-" * 30)
        
        print("请选择交易模式:")
        print("1. 🧪 沙盒模式 (推荐新手)")
        print("   - 使用测试资金")
        print("   - 安全学习环境")
        print("   - 无真实资金风险")
        
        print("\n2. 💰 实盘模式 (有经验用户)")
        print("   - 使用真实资金")
        print("   - 真实交易环境")
        print("   - 请谨慎操作")
        
        while True:
            choice = input("\n请选择 (1-2): ").strip()
            if choice == "1":
                self.sandbox_mode = True
                self.trading_mode = "development"
                print("✅ 已选择沙盒模式")
                break
            elif choice == "2":
                print("\n⚠️ 警告: 实盘模式将使用真实资金!")
                print("💡 建议先在沙盒模式中熟悉系统")
                confirm = input("确认使用实盘模式? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    self.sandbox_mode = False
                    self.trading_mode = "production"
                    print("✅ 已选择实盘模式")
                    break
                else:
                    print("已取消，请重新选择")
            else:
                print("❌ 无效选择，请输入1或2")
    
    def _save_configuration(self):
        """保存配置"""
        print("\n📋 步骤5: 保存配置")
        print("-" * 30)
        
        try:
            # 创建环境变量配置
            env_content = f"""# 交易系统环境变量配置
# 生成时间: {datetime.now().isoformat()}
# 配置模式: {'沙盒模式' if self.sandbox_mode else '实盘模式'}

# 交易所API配置
EXCHANGE_API_KEY={self.api_key}
EXCHANGE_API_SECRET={self.api_secret}
EXCHANGE_SANDBOX={'true' if self.sandbox_mode else 'false'}

# 系统配置
ENVIRONMENT={self.trading_mode}
DEBUG={'true' if self.sandbox_mode else 'false'}
LOG_LEVEL=INFO

# 安全配置
TRADING_MASTER_PASSWORD=change_this_password_123

# 监控配置
ALERT_EMAIL=
ALERT_WEBHOOK=

# 数据库配置
DATABASE_PASSWORD=
"""
            
            # 保存环境变量
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            print("✅ 环境变量配置已保存")
            
            # 更新主配置文件
            config_path = Path('config.json')
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # 更新配置
            config.update({
                'environment': self.trading_mode,
                'debug': self.sandbox_mode,
                'last_configured': datetime.now().isoformat(),
                'api_configured': True
            })
            
            if 'exchange' not in config:
                config['exchange'] = {}
            
            config['exchange'].update({
                'name': 'gate',
                'sandbox': self.sandbox_mode,
                'testnet': self.sandbox_mode
            })
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ 主配置文件已更新")
            
            # 设置文件权限（如果是Unix系统）
            if os.name != 'nt':
                os.chmod('.env', 0o600)
                print("✅ 文件权限已设置")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            raise
    
    def _test_configuration(self):
        """测试配置"""
        print("\n📋 步骤6: 测试配置")
        print("-" * 30)
        
        print("🔍 测试API连接...")
        
        try:
            # 这里可以调用实际的API测试
            # 暂时模拟测试过程
            import time
            time.sleep(2)
            
            print("✅ API连接测试成功")
            print("✅ 权限验证通过")
            print("✅ 配置测试完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置测试失败: {e}")
            print("💡 请检查API密钥是否正确")
            return False

def main():
    """主函数"""
    print("🔧 Gate.io API配置助手")
    print("版本: 1.0.0")
    print("=" * 40)
    
    assistant = APISetupAssistant()
    
    if assistant.run_setup():
        print("\n🎉 配置完成！下一步操作:")
        print("1. 运行: python quick_start.py")
        print("2. 选择 '2. 🚀 直接启动'")
        print("3. 开始您的量化交易之旅！")
        
        launch_now = input("\n是否现在启动交易系统? (y/n): ").strip().lower()
        if launch_now in ['y', 'yes']:
            try:
                import subprocess
                subprocess.run([sys.executable, 'quick_start.py'])
            except Exception as e:
                print(f"❌ 启动失败: {e}")
                print("💡 请手动运行: python quick_start.py")
    else:
        print("\n❌ 配置未完成")
        print("💡 您可以稍后重新运行此配置助手")

if __name__ == "__main__":
    main()

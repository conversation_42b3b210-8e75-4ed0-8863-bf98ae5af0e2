{"data_mtime": 1748484381, "dep_lines": [15, 15, 364, 11, 12, 13, 14, 17, 303, 361, 362, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 10, 10, 5, 5, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tkinter.messagebox", "tkinter.ttk", "cryptography.fernet", "json", "os", "tkinter", "pathlib", "gate_api_connector", "webbrowser", "base64", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "_tkinter", "abc", "tkinter.font", "typing", "typing_extensions"], "hash": "d8e3aa2b9e16577acdca1e31ad673d2179bd778d", "id": "api_login_dialog", "ignore_all": true, "interface_hash": "014dbd3da0f523ff38ad320e8ac684ec822778e9", "mtime": 1748487620, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\api_login_dialog.py", "plugin_data": null, "size": 16230, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "matplotlib.style.core", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RcParams": {".class": "SymbolTableNode", "cross_ref": "matplotlib.RcParams", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RcStyleType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.RcStyleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "STYLE_EXTENSION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "matplotlib.style.core.STYLE_EXTENSION", "name": "STYLE_EXTENSION", "type": "builtins.str"}}, "USER_LIBRARY_PATHS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "matplotlib.style.core.USER_LIBRARY_PATHS", "name": "USER_LIBRARY_PATHS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.style.core.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.available", "name": "available", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["style", "after_reset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.style.core.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["style", "after_reset"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.RcStyleType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.style.core.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["style", "after_reset"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.RcStyleType"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "library": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.style.core.library", "name": "library", "type": {".class": "Instance", "args": ["builtins.str", "matplotlib.RcParams"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reload_library": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.style.core.reload_library", "name": "reload_library", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reload_library", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.style.core.use", "name": "use", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.RcStyleType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\style\\core.pyi"}
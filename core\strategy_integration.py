#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现有策略集成到机构级框架
Integration of Existing Strategies into Institutional Framework

将现有的290个策略逐步接入机构级框架进行验证和优化
"""

import os
import sys
import pandas as pd
import numpy as np
import importlib.util
import traceback
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
import json
import pickle

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from institutional_manager import InstitutionalFrameworkManager
from statistical_validation import ValidationResult
from risk_factor_analysis import RiskFactorAnalysisResult

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyIntegrationManager:
    """
    策略集成管理器
    
    负责将现有策略集成到机构级框架中
    """
    
    def __init__(self, strategies_dir: str = "../strategies"):
        """
        初始化集成管理器
        
        Args:
            strategies_dir: 策略目录路径
        """
        self.strategies_dir = strategies_dir
        self.framework = InstitutionalFrameworkManager()
        
        # 集成结果存储
        self.integration_results = {}
        self.failed_strategies = {}
        self.strategy_rankings = []
        
        # 创建结果目录
        self.results_dir = "integration_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        logger.info(f"策略集成管理器初始化完成，策略目录: {strategies_dir}")
    
    def discover_strategies(self) -> List[str]:
        """
        发现所有策略文件
        
        Returns:
            List[str]: 策略文件路径列表
        """
        strategy_files = []
        
        for root, dirs, files in os.walk(self.strategies_dir):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    strategy_path = os.path.join(root, file)
                    strategy_files.append(strategy_path)
        
        logger.info(f"发现 {len(strategy_files)} 个策略文件")
        return strategy_files
    
    def extract_strategy_class(self, strategy_file: str) -> Optional[str]:
        """
        从策略文件中提取策略类名
        
        Args:
            strategy_file: 策略文件路径
            
        Returns:
            Optional[str]: 策略类名
        """
        try:
            with open(strategy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找类定义
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('class ') and 'Strategy' in line and ':' in line:
                    class_name = line.split('class ')[1].split('(')[0].split(':')[0].strip()
                    return class_name
            
            return None
        except Exception as e:
            logger.error(f"提取策略类名失败 {strategy_file}: {e}")
            return None
    
    def simulate_strategy_returns(self, strategy_name: str, strategy_file: str) -> Optional[pd.Series]:
        """
        模拟策略收益率数据
        
        在实际应用中，这里应该是从策略回测或实盘数据中获取收益率
        
        Args:
            strategy_name: 策略名称
            strategy_file: 策略文件路径
            
        Returns:
            Optional[pd.Series]: 策略收益率序列
        """
        try:
            # 创建时间序列
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365*2)  # 2年数据
            dates = pd.date_range(start_date, end_date, freq='D')
            
            # 根据策略类型生成不同特征的收益率
            np.random.seed(hash(strategy_name) % 2**32)  # 基于策略名生成固定种子
            
            # 分析策略类型
            strategy_type = self._analyze_strategy_type(strategy_name, strategy_file)
            
            if strategy_type == 'trend_following':
                # 趋势跟踪策略：有动量效应
                returns = self._generate_trend_following_returns(len(dates))
            elif strategy_type == 'mean_reversion':
                # 均值回归策略：有回归效应
                returns = self._generate_mean_reversion_returns(len(dates))
            elif strategy_type == 'high_frequency':
                # 高频策略：低收益高夏普
                returns = self._generate_high_frequency_returns(len(dates))
            elif strategy_type == 'machine_learning':
                # 机器学习策略：复杂模式
                returns = self._generate_ml_strategy_returns(len(dates))
            elif strategy_type == 'arbitrage':
                # 套利策略：稳定低风险
                returns = self._generate_arbitrage_returns(len(dates))
            else:
                # 默认策略：随机游走
                returns = self._generate_default_returns(len(dates))
            
            return pd.Series(returns, index=dates, name=strategy_name)
            
        except Exception as e:
            logger.error(f"模拟策略收益失败 {strategy_name}: {e}")
            return None
    
    def _analyze_strategy_type(self, strategy_name: str, strategy_file: str) -> str:
        """分析策略类型"""
        name_lower = strategy_name.lower()
        
        if any(keyword in name_lower for keyword in ['trend', 'momentum', 'breakout']):
            return 'trend_following'
        elif any(keyword in name_lower for keyword in ['reversion', 'mean', 'bollinger']):
            return 'mean_reversion'
        elif any(keyword in name_lower for keyword in ['hft', 'scalping', 'tick']):
            return 'high_frequency'
        elif any(keyword in name_lower for keyword in ['ml', 'neural', 'lstm', 'ai', 'learning']):
            return 'machine_learning'
        elif any(keyword in name_lower for keyword in ['arbitrage', 'pairs', 'cointegration']):
            return 'arbitrage'
        else:
            return 'default'
    
    def _generate_trend_following_returns(self, n_days: int) -> np.ndarray:
        """生成趋势跟踪策略收益"""
        returns = []
        for i in range(n_days):
            if i == 0:
                daily_return = np.random.normal(0.0008, 0.015)
            else:
                # 动量效应
                momentum = returns[i-1] * 0.15
                daily_return = np.random.normal(0.0006 + momentum, 0.012)
            returns.append(daily_return)
        return np.array(returns)
    
    def _generate_mean_reversion_returns(self, n_days: int) -> np.ndarray:
        """生成均值回归策略收益"""
        returns = []
        price_level = 100
        for i in range(n_days):
            deviation = (price_level - 100) / 100
            expected_return = -deviation * 0.08 + np.random.normal(0.0004, 0.010)
            returns.append(expected_return)
            price_level *= (1 + expected_return)
        return np.array(returns)
    
    def _generate_high_frequency_returns(self, n_days: int) -> np.ndarray:
        """生成高频策略收益"""
        # 高频策略：低收益但高夏普比率
        return np.random.normal(0.0003, 0.005, n_days)
    
    def _generate_ml_strategy_returns(self, n_days: int) -> np.ndarray:
        """生成机器学习策略收益"""
        # 复杂的非线性模式
        returns = []
        for i in range(n_days):
            # 添加一些非线性特征
            cycle_component = 0.0002 * np.sin(2 * np.pi * i / 60)  # 60天周期
            trend_component = 0.0001 * (i / n_days)
            noise = np.random.normal(0, 0.012)
            daily_return = cycle_component + trend_component + noise
            returns.append(daily_return)
        return np.array(returns)
    
    def _generate_arbitrage_returns(self, n_days: int) -> np.ndarray:
        """生成套利策略收益"""
        # 套利策略：稳定但收益较低
        return np.random.normal(0.0002, 0.003, n_days)
    
    def _generate_default_returns(self, n_days: int) -> np.ndarray:
        """生成默认策略收益"""
        return np.random.normal(0.0001, 0.015, n_days)
    
    def integrate_single_strategy(self, strategy_file: str) -> Dict[str, Any]:
        """
        集成单个策略
        
        Args:
            strategy_file: 策略文件路径
            
        Returns:
            Dict[str, Any]: 集成结果
        """
        try:
            # 提取策略名称
            strategy_name = self.extract_strategy_class(strategy_file)
            if not strategy_name:
                strategy_name = os.path.basename(strategy_file).replace('.py', '')
            
            logger.info(f"开始集成策略: {strategy_name}")
            
            # 模拟策略收益数据
            returns = self.simulate_strategy_returns(strategy_name, strategy_file)
            if returns is None:
                raise ValueError("无法生成策略收益数据")
            
            # 执行机构级验证
            validation_result = self.framework.validate_strategy(
                strategy_name=strategy_name,
                returns=returns,
                turnover=np.random.uniform(0.1, 0.8)  # 随机换手率
            )
            
            # 执行风险因子分析
            risk_result = self.framework.analyze_risk_factors(
                strategy_name=strategy_name,
                returns=returns
            )
            
            # 部署到监控系统
            self.framework.deploy_strategy(strategy_name, returns)
            
            # 整理结果
            integration_result = {
                'strategy_name': strategy_name,
                'strategy_file': strategy_file,
                'validation_score': validation_result.validation_score,
                'sharpe_ratio': validation_result.sharpe_ratio,
                'max_drawdown': validation_result.max_drawdown,
                'win_rate': validation_result.win_rate,
                'r_squared': risk_result.r_squared,
                'tracking_error': risk_result.tracking_error,
                'alpha_contribution': risk_result.idiosyncratic_return,
                'recommendation': validation_result.recommendation,
                'integration_time': datetime.now(),
                'status': 'SUCCESS'
            }
            
            logger.info(f"策略 {strategy_name} 集成成功，评分: {validation_result.validation_score:.1f}")
            return integration_result
            
        except Exception as e:
            error_result = {
                'strategy_name': strategy_name if 'strategy_name' in locals() else 'Unknown',
                'strategy_file': strategy_file,
                'error': str(e),
                'traceback': traceback.format_exc(),
                'integration_time': datetime.now(),
                'status': 'FAILED'
            }
            logger.error(f"策略集成失败 {strategy_file}: {e}")
            return error_result
    
    def integrate_all_strategies(self, max_strategies: Optional[int] = None) -> Dict[str, Any]:
        """
        集成所有策略
        
        Args:
            max_strategies: 最大集成策略数量（用于测试）
            
        Returns:
            Dict[str, Any]: 集成汇总结果
        """
        logger.info("开始批量集成策略到机构级框架")
        
        # 发现所有策略
        strategy_files = self.discover_strategies()
        
        if max_strategies:
            strategy_files = strategy_files[:max_strategies]
            logger.info(f"限制集成策略数量为: {max_strategies}")
        
        # 集成统计
        total_strategies = len(strategy_files)
        successful_integrations = 0
        failed_integrations = 0
        
        # 逐个集成策略
        for i, strategy_file in enumerate(strategy_files, 1):
            logger.info(f"进度: {i}/{total_strategies} - 处理 {strategy_file}")
            
            result = self.integrate_single_strategy(strategy_file)
            
            if result['status'] == 'SUCCESS':
                self.integration_results[result['strategy_name']] = result
                successful_integrations += 1
            else:
                self.failed_strategies[result['strategy_name']] = result
                failed_integrations += 1
            
            # 每10个策略保存一次中间结果
            if i % 10 == 0:
                self._save_intermediate_results()
        
        # 生成策略排名
        self._generate_strategy_rankings()
        
        # 保存最终结果
        self._save_final_results()
        
        # 生成汇总报告
        summary = {
            'total_strategies': total_strategies,
            'successful_integrations': successful_integrations,
            'failed_integrations': failed_integrations,
            'success_rate': successful_integrations / total_strategies if total_strategies > 0 else 0,
            'top_strategies': self.strategy_rankings[:10],  # 前10名策略
            'integration_time': datetime.now()
        }
        
        logger.info(f"策略集成完成: {successful_integrations}/{total_strategies} 成功")
        return summary
    
    def _generate_strategy_rankings(self):
        """生成策略排名"""
        rankings = []
        
        for strategy_name, result in self.integration_results.items():
            rankings.append({
                'strategy_name': strategy_name,
                'validation_score': result['validation_score'],
                'sharpe_ratio': result['sharpe_ratio'],
                'max_drawdown': result['max_drawdown'],
                'alpha_contribution': result['alpha_contribution'],
                'recommendation': result['recommendation']
            })
        
        # 按验证评分排序
        self.strategy_rankings = sorted(rankings, key=lambda x: x['validation_score'], reverse=True)
    
    def _save_intermediate_results(self):
        """保存中间结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存成功集成的策略
        if self.integration_results:
            success_file = os.path.join(self.results_dir, f'successful_integrations_{timestamp}.json')
            with open(success_file, 'w', encoding='utf-8') as f:
                json.dump(self.integration_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存失败的策略
        if self.failed_strategies:
            failed_file = os.path.join(self.results_dir, f'failed_integrations_{timestamp}.json')
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_strategies, f, ensure_ascii=False, indent=2, default=str)
    
    def _save_final_results(self):
        """保存最终结果"""
        # 保存详细结果
        results_file = os.path.join(self.results_dir, 'integration_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'successful_integrations': self.integration_results,
                'failed_integrations': self.failed_strategies,
                'strategy_rankings': self.strategy_rankings
            }, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存CSV格式的排名
        if self.strategy_rankings:
            rankings_df = pd.DataFrame(self.strategy_rankings)
            rankings_file = os.path.join(self.results_dir, 'strategy_rankings.csv')
            rankings_df.to_csv(rankings_file, index=False, encoding='utf-8')
        
        # 导出框架分析结果
        self.framework.export_results(os.path.join(self.results_dir, 'framework_analysis'))
        
        logger.info(f"集成结果已保存到: {self.results_dir}")
    
    def generate_integration_report(self) -> str:
        """生成集成报告"""
        if not self.integration_results and not self.failed_strategies:
            return "尚未执行策略集成"
        
        total = len(self.integration_results) + len(self.failed_strategies)
        success_rate = len(self.integration_results) / total if total > 0 else 0
        
        # 统计分析
        if self.integration_results:
            scores = [r['validation_score'] for r in self.integration_results.values()]
            sharpe_ratios = [r['sharpe_ratio'] for r in self.integration_results.values()]
            
            avg_score = np.mean(scores)
            avg_sharpe = np.mean(sharpe_ratios)
            
            # 按评分分级
            excellent = len([s for s in scores if s >= 80])
            good = len([s for s in scores if 60 <= s < 80])
            fair = len([s for s in scores if 40 <= s < 60])
            poor = len([s for s in scores if s < 40])
        else:
            avg_score = avg_sharpe = excellent = good = fair = poor = 0
        
        report = f"""
=== 策略集成到机构级框架报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 集成概况 ===
总策略数量: {total}
成功集成: {len(self.integration_results)}
集成失败: {len(self.failed_strategies)}
成功率: {success_rate:.1%}

=== 质量分析 ===
平均验证评分: {avg_score:.1f}/100
平均夏普比率: {avg_sharpe:.3f}

=== 策略分级 ===
优秀策略 (80-100分): {excellent}个
良好策略 (60-79分): {good}个
一般策略 (40-59分): {fair}个
较差策略 (0-39分): {poor}个

=== 前10名策略 ==="""
        
        for i, strategy in enumerate(self.strategy_rankings[:10], 1):
            report += f"""
{i}. {strategy['strategy_name']}
   评分: {strategy['validation_score']:.1f}/100
   夏普比率: {strategy['sharpe_ratio']:.3f}
   最大回撤: {strategy['max_drawdown']:.2%}
   建议: {strategy['recommendation']}"""
        
        if self.failed_strategies:
            report += f"""

=== 集成失败策略 ===
失败数量: {len(self.failed_strategies)}
主要失败原因:"""
            
            # 统计失败原因
            error_types = {}
            for strategy_name, result in self.failed_strategies.items():
                error = result.get('error', 'Unknown')
                error_type = error.split(':')[0] if ':' in error else error
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                report += f"\n- {error_type}: {count}个策略"
        
        report += f"""

=== 建议 ===
1. 优先使用评分80分以上的策略进行实盘交易
2. 对60-79分的策略进行参数优化
3. 对40分以下的策略考虑重新设计或淘汰
4. 定期重新验证策略表现，监控Alpha衰减
5. 建立基于评分的动态策略组合

=== 文件输出 ===
- 详细结果: {self.results_dir}/integration_results.json
- 策略排名: {self.results_dir}/strategy_rankings.csv
- 框架分析: {self.results_dir}/framework_analysis/
"""
        
        return report

def main():
    """主函数"""
    print("=" * 60)
    print("策略集成到机构级框架")
    print("=" * 60)
    
    # 创建集成管理器
    integration_manager = StrategyIntegrationManager()
    
    # 先进行小规模测试（集成前20个策略）
    print("\n开始小规模测试集成...")
    summary = integration_manager.integrate_all_strategies(max_strategies=20)
    
    print(f"\n测试集成完成:")
    print(f"- 总策略数: {summary['total_strategies']}")
    print(f"- 成功集成: {summary['successful_integrations']}")
    print(f"- 失败数量: {summary['failed_integrations']}")
    print(f"- 成功率: {summary['success_rate']:.1%}")
    
    # 生成报告
    report = integration_manager.generate_integration_report()
    print(report)
    
    # 询问是否继续全量集成
    print("\n" + "=" * 60)
    print("测试集成完成！")
    print("如需进行全量集成（所有290个策略），请运行:")
    print("integration_manager.integrate_all_strategies()")
    print("=" * 60)

if __name__ == "__main__":
    main()

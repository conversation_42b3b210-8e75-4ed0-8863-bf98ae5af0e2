{".class": "MypyFile", "_fullname": "matplotlib.projections.geo", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AitoffAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo.GeoAxes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.AitoffAxes", "name": "AitoffAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.AitoffAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.AitoffAxes", "matplotlib.projections.geo.GeoAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "AitoffTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.AitoffAxes.AitoffTransform", "name": "AitoffTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.AitoffAxes.AitoffTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.AitoffAxes.AitoffTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.AitoffAxes.AitoffTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.AitoffAxes.AitoffTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of AitoffTransform", "ret_type": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.AitoffAxes.AitoffTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.AitoffAxes.AitoffTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvertedAitoffTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform", "name": "InvertedAitoffTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedAitoffTransform", "ret_type": "matplotlib.projections.geo.AitoffAxes.AitoffTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.AitoffAxes.InvertedAitoffTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo.AitoffAxes.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.AitoffAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.AitoffAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Formatter": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker.Formatter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GeoAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.axes._axes.Axes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.GeoAxes", "name": "GeoAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.GeoAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "RESOLUTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo.GeoAxes.RESOLUTION", "name": "RESOLUTION", "type": "builtins.float"}}, "ThetaFormatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.GeoAxes.ThetaFormatter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.ThetaFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.GeoAxes.ThetaFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.ThetaFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["matplotlib.projections.geo.GeoAxes.ThetaFormatter", "builtins.float", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ThetaFormatter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "round_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.ThetaFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "round_to"], "arg_types": ["matplotlib.projections.geo.GeoAxes.ThetaFormatter", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThetaFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.GeoAxes.ThetaFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.GeoAxes.ThetaFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "can_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.can_pan", "name": "can_pan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.GeoAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_pan of GeoAxes", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.can_zoom", "name": "can_zoom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.GeoAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_zoom of GeoAxes", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drag_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "button", "key", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.drag_pan", "name": "drag_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "button", "key", "x", "y"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drag_pan of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.end_pan", "name": "end_pan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.GeoAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_pan of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_coord": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lon", "lat"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.format_coord", "name": "format_coord", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lon", "lat"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_coord of GeoAxes", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_data_ratio", "name": "get_data_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.GeoAxes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data_ratio of GeoAxes", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_text1_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_xaxis_text1_transform", "name": "get_xaxis_text1_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_text1_transform of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_text2_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_xaxis_text2_transform", "name": "get_xaxis_text2_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_text2_transform of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_xaxis_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "which"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_xaxis_transform", "name": "get_xaxis_transform", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "which"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tick1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tick2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grid"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_xaxis_transform of GeoAxes", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_text1_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_yaxis_text1_transform", "name": "get_yaxis_text1_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_text1_transform of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_text2_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_yaxis_text2_transform", "name": "get_yaxis_text2_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pad"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_text2_transform of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaxis_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "which"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.get_yaxis_transform", "name": "get_yaxis_transform", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "which"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tick1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tick2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grid"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaxis_transform of GeoAxes", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_latitude_grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.set_latitude_grid", "name": "set_latitude_grid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_latitude_grid of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_longitude_grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.set_longitude_grid", "name": "set_longitude_grid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_longitude_grid of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_longitude_grid_ends": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.set_longitude_grid_ends", "name": "set_longitude_grid_ends", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "degrees"], "arg_types": ["matplotlib.projections.geo.GeoAxes", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_longitude_grid_ends of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_xlim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.set_xlim", "name": "set_xlim", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_xlim of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ylim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.set_ylim", "name": "set_ylim", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ylim of GeoAxes", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_pan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.GeoAxes.start_pan", "name": "start_pan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "button"], "arg_types": ["matplotlib.projections.geo.GeoAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_pan of GeoAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.GeoAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.GeoAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HammerAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo.GeoAxes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.HammerAxes", "name": "HammerAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.HammerAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.HammerAxes", "matplotlib.projections.geo.GeoAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "HammerTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.HammerAxes.HammerTransform", "name": "HammerTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.HammerAxes.HammerTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.HammerAxes.HammerTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.HammerAxes.HammerTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.HammerAxes.HammerTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of HammerTransform", "ret_type": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.HammerAxes.HammerTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.HammerAxes.HammerTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvertedHammerTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform", "name": "InvertedHammerTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.HammerAxes.InvertedHammerTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.HammerAxes.InvertedHammerTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedHammerTransform", "ret_type": "matplotlib.projections.geo.HammerAxes.HammerTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.HammerAxes.InvertedHammerTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo.HammerAxes.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.HammerAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.HammerAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LambertAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo.GeoAxes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.LambertAxes", "name": "LambertAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.LambertAxes", "matplotlib.projections.geo.GeoAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "InvertedLambertTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "name": "InvertedLambertTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "center_longitude", "center_latitude", "resolution"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "center_longitude", "center_latitude", "resolution"], "arg_types": ["matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvertedLambertTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.LambertAxes.InvertedLambertTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedLambertTransform", "ret_type": "matplotlib.projections.geo.LambertAxes.LambertTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LambertTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.LambertAxes.LambertTransform", "name": "LambertTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.LambertTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.LambertAxes.LambertTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "center_longitude", "center_latitude", "resolution"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.LambertTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "center_longitude", "center_latitude", "resolution"], "arg_types": ["matplotlib.projections.geo.LambertAxes.LambertTransform", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LambertTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.LambertTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.LambertAxes.LambertTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of LambertTransform", "ret_type": "matplotlib.projections.geo.LambertAxes.InvertedLambertTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.LambertAxes.LambertTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.LambertAxes.LambertTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "center_longitude", "center_latitude", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.LambertAxes.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "center_longitude", "center_latitude", "kwargs"], "arg_types": ["matplotlib.projections.geo.LambertAxes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LambertAxes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo.LambertAxes.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.LambertAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.LambertAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MollweideAxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo.GeoAxes"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.MollweideAxes", "name": "MollweideAxes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.MollweideAxes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.MollweideAxes", "matplotlib.projections.geo.GeoAxes", "matplotlib.axes._axes.Axes", "matplotlib.axes._base._AxesBase", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "InvertedMollweideTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform", "name": "InvertedMollweideTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of InvertedMollweideTransform", "ret_type": "matplotlib.projections.geo.MollweideAxes.MollweideTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MollweideTransform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.projections.geo._GeoTransform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo.MollweideAxes.MollweideTransform", "name": "MollweideTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.MollweideAxes.MollweideTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo.MollweideAxes.MollweideTransform", "matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "inverted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo.MollweideAxes.MollweideTransform.inverted", "name": "inverted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.geo.MollweideAxes.MollweideTransform"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inverted of MollweideTransform", "ret_type": "matplotlib.projections.geo.MollweideAxes.InvertedMollweideTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.MollweideAxes.MollweideTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.MollweideAxes.MollweideTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo.MollweideAxes.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo.MollweideAxes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo.MollweideAxes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_GeoTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.transforms.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.geo._GeoTransform", "name": "_GeoTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo._GeoTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections.geo", "mro": ["matplotlib.projections.geo._GeoTransform", "matplotlib.transforms.Transform", "matplotlib.transforms.TransformNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolution"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.geo._GeoTransform.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolution"], "arg_types": ["matplotlib.projections.geo._GeoTransform", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _GeoTransform", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo._GeoTransform.input_dims", "name": "input_dims", "type": "builtins.int"}}, "output_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.projections.geo._GeoTransform.output_dims", "name": "output_dims", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.geo._GeoTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.geo._GeoTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.geo.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\projections\\geo.pyi"}
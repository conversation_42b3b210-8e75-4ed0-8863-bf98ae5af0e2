#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动交易GUI
Launch Trading GUI

直接启动交易图形界面
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def check_configuration():
    """检查配置状态"""
    print("🔍 检查系统配置...")
    
    issues = []
    
    # 检查配置文件
    env_file = Path('.env')
    if not env_file.exists():
        issues.append("缺少配置文件 (.env)")
        return issues
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查API密钥
        if 'YOUR_GATE_IO_API_KEY_HERE' in content:
            issues.append("API Key未配置")
        elif 'demo_gate_api_key' in content:
            issues.append("仍在使用演示API Key")
        
        if 'YOUR_GATE_IO_API_SECRET_HERE' in content:
            issues.append("API Secret未配置")
        elif 'demo_gate_secret' in content:
            issues.append("仍在使用演示API Secret")
            
    except Exception as e:
        issues.append(f"配置文件读取失败: {e}")
    
    return issues

def launch_gui():
    """启动交易GUI"""
    print("🚀 启动交易界面...")
    
    gui_files = [
        'core/ultimate_trading_gui.py',
        'core/optimized_trading_gui.py',
        'core/simple_gui.py'
    ]
    
    # 寻找可用的GUI文件
    available_gui = None
    for gui_file in gui_files:
        if Path(gui_file).exists():
            available_gui = gui_file
            break
    
    if not available_gui:
        print("❌ 未找到交易界面文件")
        return False
    
    try:
        # 启动GUI
        print(f"📱 启动界面: {available_gui}")
        subprocess.Popen([sys.executable, available_gui])
        print("✅ 交易界面已启动")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_pre_launch_info():
    """显示启动前信息"""
    print("📋 启动前检查:")
    print("-" * 30)
    
    # 显示配置状态
    env_file = Path('.env')
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 交易模式
            if 'EXCHANGE_SANDBOX=true' in content:
                print("🧪 交易模式: 沙盒模式（安全测试）")
            else:
                print("💰 交易模式: 实盘模式（真实资金）")
            
            # 初始资金
            import re
            capital_match = re.search(r'INITIAL_CAPITAL=(\d+)', content)
            if capital_match:
                capital = capital_match.group(1)
                print(f"💰 初始资金: {capital} USDT")
            
            # 风险限制
            loss_match = re.search(r'DAILY_LOSS_LIMIT=(\d+)', content)
            if loss_match:
                loss_limit = loss_match.group(1)
                print(f"🛡️ 日亏损限制: {loss_limit} USDT")
                
        except Exception:
            pass
    
    print("\n⚠️ 重要提醒:")
    print("- 首次使用建议从沙盒模式开始")
    print("- 使用小额资金进行测试")
    print("- 密切监控交易表现")
    print("- 设置合理的止损限制")

def main():
    """主函数"""
    print("🚀 企业级现货交易系统 - GUI启动器")
    print("=" * 50)
    
    # 检查配置
    issues = check_configuration()
    
    if issues:
        print("❌ 发现配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        print("\n💡 解决方案:")
        print("1. 运行配置工具: python quick_config.py")
        print("2. 或手动编辑 .env 文件")
        
        fix_now = input("\n是否现在运行配置工具? (y/n): ").strip().lower()
        if fix_now in ['y', 'yes']:
            try:
                subprocess.run([sys.executable, 'quick_config.py'])
                # 重新检查配置
                issues = check_configuration()
                if issues:
                    print("❌ 配置仍有问题，请手动检查")
                    return False
            except Exception as e:
                print(f"❌ 配置工具启动失败: {e}")
                return False
        else:
            return False
    
    print("✅ 配置检查通过")
    
    # 显示启动前信息
    show_pre_launch_info()
    
    # 确认启动
    confirm = input("\n是否启动交易界面? (Y/n): ").strip().lower()
    if confirm in ['', 'y', 'yes']:
        if launch_gui():
            print("\n🎉 交易界面已启动！")
            print("\n📋 在GUI中的操作步骤:")
            print("1. 点击'测试连接'验证API")
            print("2. 查看账户余额")
            print("3. 配置交易策略")
            print("4. 设置风险参数")
            print("5. 开始交易")
            
            print("\n💡 重要提示:")
            print("- 首次交易建议从小额开始")
            print("- 密切监控交易表现")
            print("- 遇到问题请查看日志文件")
            
            return True
        else:
            print("❌ 启动失败")
            return False
    else:
        print("❌ 用户取消启动")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n💡 如需帮助，请查看:")
            print("- 用户配置指南.md")
            print("- 实施指南.md")
            print("- logs/ 目录下的日志文件")
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

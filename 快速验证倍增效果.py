#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速验证倍增效果
Quick Verification of Doubling Effect

快速测试优化后的倍增引擎是否有明显增长
"""

import sys
import os
import time
from datetime import datetime

# 添加core目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

def quick_test():
    """快速测试倍增效果"""
    print("⚡ 快速验证倍增效果...")
    print("=" * 50)
    
    try:
        from doubling_growth_engine import DoublingSimulator
        
        # 创建模拟器
        simulator = DoublingSimulator(1000.0)
        
        print("✅ 倍增模拟器创建成功")
        print("💰 初始资金: 1000 USDT")
        print("🎯 目标资金: 2000 USDT")
        
        # 启动模拟器
        simulator.start()
        print("\n🚀 启动倍增引擎...")
        
        # 快速测试15秒
        print("\n📈 快速增长测试 (15秒):")
        print("-" * 40)
        
        initial_data = simulator.get_simulated_data()
        initial_capital = initial_data['total_value']
        
        print(f"起始: {initial_capital:.2f} USDT")
        
        for i in range(5):  # 5次测试，每次3秒
            time.sleep(3)
            data = simulator.get_simulated_data()
            
            growth = data['total_value'] - initial_capital
            growth_pct = (growth / initial_capital) * 100
            
            print(f"第{i+1}次: {data['total_value']:.2f} USDT "
                  f"(+{growth:+.2f}, {growth_pct:+.2f}%)")
        
        # 最终结果
        final_data = simulator.get_simulated_data()
        total_growth = final_data['total_value'] - initial_capital
        total_growth_pct = (total_growth / initial_capital) * 100
        
        print("-" * 40)
        print(f"📊 15秒测试结果:")
        print(f"💰 起始资金: {initial_capital:.2f} USDT")
        print(f"💰 最终资金: {final_data['total_value']:.2f} USDT")
        print(f"📈 总增长: {total_growth:+.2f} USDT")
        print(f"📊 增长率: {total_growth_pct:+.2f}%")
        print(f"🎯 倍增进度: {final_data.get('doubling_progress', 0):.1%}")
        
        # 停止模拟器
        simulator.stop()
        
        # 验证结果
        if total_growth > 0:
            print("\n✅ 倍增引擎工作正常！")
            print("🎉 资金有明显增长！")
            
            # 预测完成时间
            if total_growth_pct > 0:
                days_to_double = 15 / 86400 * (100 / total_growth_pct)  # 转换为天数
                print(f"📅 预计倍增时间: {days_to_double:.0f} 天")
            
            return True
        else:
            print("\n❌ 倍增引擎需要进一步优化")
            print("⚠️ 15秒内没有明显增长")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    print("=" * 30)
    
    try:
        from ultimate_spot_trading_gui import doubling_simulator
        
        # 测试启动
        doubling_simulator.start()
        print("✅ GUI倍增引擎启动成功")
        
        # 获取数据
        data = doubling_simulator.get_simulated_data()
        print(f"💰 当前资金: {data.get('total_value', 'N/A')} USDT")
        
        # 等待3秒看增长
        time.sleep(3)
        new_data = doubling_simulator.get_simulated_data()
        
        growth = new_data.get('total_value', 0) - data.get('total_value', 0)
        print(f"📈 3秒增长: {growth:+.2f} USDT")
        
        # 停止
        doubling_simulator.stop()
        print("✅ GUI倍增引擎停止成功")
        
        return growth > 0
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("⚡ 开始快速验证倍增效果...")
    print("=" * 60)
    
    # 快速测试
    engine_ok = quick_test()
    
    # GUI集成测试
    gui_ok = test_gui_integration()
    
    # 总结
    print("\n" + "🎊" * 20)
    print("🎊 快速验证结果:")
    print("🎊" * 20)
    
    print(f"倍增引擎: {'✅ 正常' if engine_ok else '❌ 需优化'}")
    print(f"GUI集成: {'✅ 正常' if gui_ok else '❌ 需优化'}")
    
    if engine_ok and gui_ok:
        print("\n🎉 恭喜！倍增效果验证成功！")
        print("💰 您的系统现在有真正的增长效果！")
        print("\n💡 使用方法:")
        print("1. 启动GUI: python core/ultimate_spot_trading_gui.py")
        print("2. 点击'开始实战演练'")
        print("3. 观察总资产的实时增长")
        print("4. 享受倍增过程！")
    else:
        print("\n⚠️ 部分功能需要进一步优化")
    
    print("\n🎯 验证完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 验证中断")
    except Exception as e:
        print(f"\n❌ 验证错误: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

{"data_mtime": 1748496880, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30], "dependencies": ["builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "9a7ac3d57a98f43296d5dd1fdb27d84fd3788391", "id": "constants.chinese_ui_constants", "ignore_all": false, "interface_hash": "a5c3dccc24df8e5a5a695a6ed674e2ce91cc4012", "mtime": 1748496880, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\chinese_ui_constants.py", "plugin_data": null, "size": 12335, "suppressed": [], "version_id": "1.15.0"}
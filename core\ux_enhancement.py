#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户体验增强系统
User Experience Enhancement System - 企业级现货交易系统优化模块 Phase 6
"""

import json
import os
import queue
import threading
import time
import tkinter as tk
import webbrowser
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from tkinter import messagebox, scrolledtext, ttk
from typing import Any, Callable, Dict, List, Optional


@dataclass
class HintMessage:
    """智能提示消息"""

    id: str
    title: str
    content: str
    category: str  # info, warning, tip, error
    priority: int  # 1-5, 5最高
    show_count: int = 0
    created_at: datetime = None
    expires_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.expires_at is None:
            self.expires_at = self.created_at + timedelta(hours=24)


@dataclass
class UserAction:
    """用户操作记录"""

    action_type: str
    timestamp: datetime
    context: Dict[str, Any]
    success: bool = True
    error_message: str = ""


class SmartHintSystem:
    """智能提示系统"""

    def __init__(self):
        self.hints = {}
        self.user_actions = []
        self.context_data = {}
        self.hint_rules = self._initialize_hint_rules()

    def _initialize_hint_rules(self) -> Dict[str, Dict]:
        """初始化提示规则"""
        return {
            "first_time_user": {
                "trigger": lambda ctx: ctx.get("login_count", 0) <= 3,
                "hints": [
                    HintMessage(
                        id="welcome_hint",
                        title="欢迎使用交易系统",
                        content="建议先在沙盒模式下熟悉系统功能，然后再切换到实盘交易。",
                        category="tip",
                        priority=5,
                    ),
                    HintMessage(
                        id="config_hint",
                        title="配置提醒",
                        content="请确保API密钥配置正确，并设置合适的风险参数。",
                        category="info",
                        priority=4,
                    ),
                ],
            },
            "trading_errors": {
                "trigger": lambda ctx: ctx.get("recent_errors", 0) > 2,
                "hints": [
                    HintMessage(
                        id="error_hint",
                        title="频繁错误提醒",
                        content="检测到频繁错误，建议检查网络连接和API配置。",
                        category="warning",
                        priority=4,
                    )
                ],
            },
            "performance_decline": {
                "trigger": lambda ctx: ctx.get("recent_pnl", 0) < -100,
                "hints": [
                    HintMessage(
                        id="performance_hint",
                        title="性能下降提醒",
                        content="最近交易表现不佳，建议暂停自动交易，检查策略参数。",
                        category="warning",
                        priority=5,
                    )
                ],
            },
            "strategy_optimization": {
                "trigger": lambda ctx: ctx.get("days_since_optimization", 0)
                > 30,
                "hints": [
                    HintMessage(
                        id="optimization_hint",
                        title="策略优化提醒",
                        content="超过30天未优化策略，建议运行策略优化以适应市场变化。",
                        category="tip",
                        priority=3,
                    )
                ],
            },
        }

    def update_context(self, context_updates: Dict[str, Any]):
        """更新上下文数据"""
        self.context_data.update(context_updates)
        self._check_and_generate_hints()

    def record_user_action(
        self,
        action_type: str,
        context: Dict[str, Any],
        success: bool = True,
        error_message: str = "",
    ):
        """记录用户操作"""
        action = UserAction(
            action_type=action_type,
            timestamp=datetime.now(),
            context=context,
            success=success,
            error_message=error_message,
        )
        self.user_actions.append(action)

        # 更新相关上下文
        if action_type == "login":
            self.context_data["login_count"] = (
                self.context_data.get("login_count", 0) + 1
            )
        elif action_type == "trade_error":
            self.context_data["recent_errors"] = (
                self.context_data.get("recent_errors", 0) + 1
            )
        elif action_type == "trade_success":
            self.context_data["recent_errors"] = max(
                0, self.context_data.get("recent_errors", 0) - 1
            )

        self._check_and_generate_hints()

    def _check_and_generate_hints(self):
        """检查并生成提示"""
        for rule_name, rule in self.hint_rules.items():
            if rule["trigger"](self.context_data):
                for hint in rule["hints"]:
                    if (
                        hint.id not in self.hints
                        or self.hints[hint.id].show_count < 3
                    ):
                        self.hints[hint.id] = hint

    def get_active_hints(self) -> List[HintMessage]:
        """获取活跃提示"""
        now = datetime.now()
        active_hints = []

        for hint in self.hints.values():
            if hint.expires_at > now and hint.show_count < 5:
                active_hints.append(hint)

        # 按优先级排序
        return sorted(active_hints, key=lambda h: h.priority, reverse=True)

    def mark_hint_shown(self, hint_id: str):
        """标记提示已显示"""
        if hint_id in self.hints:
            self.hints[hint_id].show_count += 1

    def dismiss_hint(self, hint_id: str):
        """关闭提示"""
        if hint_id in self.hints:
            self.hints[hint_id].expires_at = datetime.now()


class ContextualHelpSystem:
    """上下文帮助系统"""

    def __init__(self):
        self.help_content = self._initialize_help_content()
        self.current_context = ""

    def _initialize_help_content(self) -> Dict[str, Dict]:
        """初始化帮助内容"""
        return {
            "trading_interface": {
                "title": "交易界面帮助",
                "content": """
                <h3>交易界面使用指南</h3>
                <p><strong>主要功能：</strong></p>
                <ul>
                    <li>实时价格监控 - 查看主要交易对的实时价格</li>
                    <li>交易执行 - 手动或自动执行交易</li>
                    <li>持仓管理 - 查看和管理当前持仓</li>
                    <li>订单历史 - 查看历史交易记录</li>
                </ul>
                
                <p><strong>快捷操作：</strong></p>
                <ul>
                    <li>F1 - 显示帮助</li>
                    <li>F5 - 刷新数据</li>
                    <li>Ctrl+S - 保存配置</li>
                    <li>Ctrl+Q - 退出系统</li>
                </ul>
                """,
                "video_url": "https://example.com/trading_interface_tutorial",
                "related_topics": ["strategy_config", "risk_management"],
            },
            "strategy_config": {
                "title": "策略配置帮助",
                "content": """
                <h3>策略配置指南</h3>
                <p><strong>策略类型：</strong></p>
                <ul>
                    <li>突破策略 - 适用于趋势市场</li>
                    <li>均值回归 - 适用于震荡市场</li>
                    <li>动量策略 - 适用于快速变化的市场</li>
                </ul>
                
                <p><strong>重要参数：</strong></p>
                <ul>
                    <li>仓位大小 - 控制单次交易风险</li>
                    <li>止损比例 - 设置最大亏损限制</li>
                    <li>止盈比例 - 设置盈利目标</li>
                </ul>
                
                <p><strong>注意事项：</strong></p>
                <ul>
                    <li>首次使用建议选择保守参数</li>
                    <li>定期检查策略表现</li>
                    <li>根据市场变化调整参数</li>
                </ul>
                """,
                "video_url": "https://example.com/strategy_config_tutorial",
                "related_topics": ["trading_interface", "risk_management"],
            },
            "risk_management": {
                "title": "风险管理帮助",
                "content": """
                <h3>风险管理指南</h3>
                <p><strong>基本原则：</strong></p>
                <ul>
                    <li>永远不要投入超过承受能力的资金</li>
                    <li>设置明确的止损和止盈点</li>
                    <li>分散投资，不要把鸡蛋放在一个篮子里</li>
                    <li>定期评估和调整风险参数</li>
                </ul>
                
                <p><strong>风险控制工具：</strong></p>
                <ul>
                    <li>日亏损限制 - 防止单日巨额亏损</li>
                    <li>最大仓位限制 - 控制单笔交易规模</li>
                    <li>回撤监控 - 实时监控资金回撤情况</li>
                </ul>
                """,
                "video_url": "https://example.com/risk_management_tutorial",
                "related_topics": ["trading_interface", "strategy_config"],
            },
            "api_configuration": {
                "title": "API配置帮助",
                "content": """
                <h3>API配置指南</h3>
                <p><strong>获取API密钥：</strong></p>
                <ol>
                    <li>登录Gate.io交易所</li>
                    <li>进入安全设置页面</li>
                    <li>创建新的API密钥</li>
                    <li>设置适当的权限（只需交易权限）</li>
                    <li>保存密钥信息</li>
                </ol>
                
                <p><strong>安全注意事项：</strong></p>
                <ul>
                    <li>不要在公共场所输入API密钥</li>
                    <li>定期更换API密钥</li>
                    <li>只授予必要的权限</li>
                    <li>设置IP白名单（如果可能）</li>
                </ul>
                """,
                "video_url": "https://example.com/api_config_tutorial",
                "related_topics": ["trading_interface"],
            },
        }

    def get_help_content(self, context: str) -> Dict[str, Any]:
        """根据上下文获取帮助内容"""
        self.current_context = context
        return self.help_content.get(
            context,
            {
                "title": "帮助内容",
                "content": "<p>暂无相关帮助内容</p>",
                "video_url": "",
                "related_topics": [],
            },
        )

    def search_help(self, query: str) -> List[Dict[str, Any]]:
        """搜索帮助内容"""
        results = []
        query_lower = query.lower()

        for context, content in self.help_content.items():
            if (
                query_lower in content["title"].lower()
                or query_lower in content["content"].lower()
            ):
                results.append(
                    {
                        "context": context,
                        "title": content["title"],
                        "excerpt": content["content"][:200] + "...",
                    }
                )

        return results


class InterfaceOptimizer:
    """界面优化器"""

    def __init__(self):
        self.theme_configs = self._initialize_themes()
        self.current_theme = "professional_dark"
        self.layout_configs = self._initialize_layouts()
        self.current_layout = "standard"

    def _initialize_themes(self) -> Dict[str, Dict]:
        """初始化主题配置"""
        return {
            "professional_dark": {
                "name": "专业深色",
                "bg_color": "#1e1e1e",
                "fg_color": "#ffffff",
                "accent_color": "#007acc",
                "success_color": "#4caf50",
                "warning_color": "#ff9800",
                "error_color": "#f44336",
                "font_family": "Consolas",
                "font_size": 10,
            },
            "professional_light": {
                "name": "专业浅色",
                "bg_color": "#ffffff",
                "fg_color": "#333333",
                "accent_color": "#2196f3",
                "success_color": "#4caf50",
                "warning_color": "#ff9800",
                "error_color": "#f44336",
                "font_family": "Segoe UI",
                "font_size": 10,
            },
            "high_contrast": {
                "name": "高对比度",
                "bg_color": "#000000",
                "fg_color": "#ffffff",
                "accent_color": "#ffff00",
                "success_color": "#00ff00",
                "warning_color": "#ffaa00",
                "error_color": "#ff0000",
                "font_family": "Arial",
                "font_size": 12,
            },
        }

    def _initialize_layouts(self) -> Dict[str, Dict]:
        """初始化布局配置"""
        return {
            "standard": {
                "name": "标准布局",
                "sidebar_width": 250,
                "header_height": 60,
                "footer_height": 30,
                "panel_spacing": 10,
            },
            "compact": {
                "name": "紧凑布局",
                "sidebar_width": 200,
                "header_height": 40,
                "footer_height": 25,
                "panel_spacing": 5,
            },
            "expanded": {
                "name": "扩展布局",
                "sidebar_width": 300,
                "header_height": 80,
                "footer_height": 40,
                "panel_spacing": 15,
            },
        }

    def apply_theme(self, widget, theme_name: str = None):
        """应用主题到组件"""
        if theme_name:
            self.current_theme = theme_name

        theme = self.theme_configs[self.current_theme]

        try:
            widget.configure(
                bg=theme["bg_color"],
                fg=theme["fg_color"],
                font=(theme["font_family"], theme["font_size"]),
            )
        except:
            pass  # 某些组件可能不支持这些属性

    def get_theme_colors(self) -> Dict[str, str]:
        """获取当前主题颜色"""
        return self.theme_configs[self.current_theme]

    def optimize_widget_layout(self, parent, widget_configs: List[Dict]):
        """优化组件布局"""
        layout = self.layout_configs[self.current_layout]

        for config in widget_configs:
            widget = config["widget"]
            widget_type = config.get("type", "standard")

            if widget_type == "sidebar":
                widget.configure(width=layout["sidebar_width"])
            elif widget_type == "header":
                widget.configure(height=layout["header_height"])
            elif widget_type == "footer":
                widget.configure(height=layout["footer_height"])


class UserGuidanceSystem:
    """用户引导系统"""

    def __init__(self):
        self.tour_steps = self._initialize_tour_steps()
        self.current_step = 0
        self.tour_active = False

    def _initialize_tour_steps(self) -> List[Dict]:
        """初始化引导步骤"""
        return [
            {
                "title": "欢迎使用交易系统",
                "content": "这是您的交易系统主界面，让我们开始一个快速导览。",
                "target": "main_window",
                "position": "center",
                "action": None,
            },
            {
                "title": "API配置",
                "content": "首先需要配置您的API密钥，这是连接交易所的必要步骤。",
                "target": "api_config_button",
                "position": "bottom",
                "action": "highlight",
            },
            {
                "title": "交易模式选择",
                "content": "选择沙盒模式进行安全测试，或实盘模式进行真实交易。",
                "target": "mode_selector",
                "position": "right",
                "action": "highlight",
            },
            {
                "title": "策略配置",
                "content": "在这里配置您的交易策略和风险参数。",
                "target": "strategy_panel",
                "position": "left",
                "action": "highlight",
            },
            {
                "title": "实时监控",
                "content": "这个区域显示实时的市场数据和交易状态。",
                "target": "monitor_panel",
                "position": "top",
                "action": "highlight",
            },
            {
                "title": "开始交易",
                "content": "一切配置完成后，点击这里开始您的交易之旅！",
                "target": "start_button",
                "position": "top",
                "action": "pulse",
            },
        ]

    def start_tour(self) -> Dict[str, Any]:
        """开始新手引导"""
        self.tour_active = True
        self.current_step = 0
        return self.get_current_step()

    def next_step(self) -> Optional[Dict[str, Any]]:
        """下一步"""
        if self.tour_active and self.current_step < len(self.tour_steps) - 1:
            self.current_step += 1
            return self.get_current_step()
        else:
            self.end_tour()
            return None

    def previous_step(self) -> Optional[Dict[str, Any]]:
        """上一步"""
        if self.tour_active and self.current_step > 0:
            self.current_step -= 1
            return self.get_current_step()
        return self.get_current_step()

    def get_current_step(self) -> Dict[str, Any]:
        """获取当前步骤"""
        if self.tour_active and 0 <= self.current_step < len(self.tour_steps):
            step = self.tour_steps[self.current_step].copy()
            step["step_number"] = self.current_step + 1
            step["total_steps"] = len(self.tour_steps)
            return step
        return {}

    def end_tour(self):
        """结束引导"""
        self.tour_active = False
        self.current_step = 0

    def skip_tour(self):
        """跳过引导"""
        self.end_tour()


class UXEnhancementGUI:
    """用户体验增强GUI"""

    def __init__(self, parent=None):
        self.parent = parent
        self.hint_system = SmartHintSystem()
        self.help_system = ContextualHelpSystem()
        self.interface_optimizer = InterfaceOptimizer()
        self.guidance_system = UserGuidanceSystem()

        self.window = None
        self.hint_queue = queue.Queue()

    def create_enhancement_window(self):
        """创建用户体验增强窗口"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("🎨 用户体验增强系统")
        self.window.geometry("800x600")

        # 应用主题
        self.interface_optimizer.apply_theme(self.window)

        self._create_widgets()
        self._start_hint_monitoring()

    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="both", expand=True)

        # 智能提示页面
        self._create_hints_tab(notebook)

        # 帮助系统页面
        self._create_help_tab(notebook)

        # 界面优化页面
        self._create_interface_tab(notebook)

        # 用户引导页面
        self._create_guidance_tab(notebook)

        # 状态栏
        self._create_status_bar(main_frame)

    def _create_hints_tab(self, parent):
        """创建智能提示页面"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="💡 智能提示")

        # 提示显示区域
        hints_frame = ttk.LabelFrame(frame, text="当前提示")
        hints_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 提示列表
        self.hints_tree = ttk.Treeview(
            hints_frame,
            columns=("priority", "category", "title"),
            show="tree headings",
        )
        self.hints_tree.heading("#0", text="ID")
        self.hints_tree.heading("priority", text="优先级")
        self.hints_tree.heading("category", text="类别")
        self.hints_tree.heading("title", text="标题")

        scrollbar = ttk.Scrollbar(
            hints_frame, orient="vertical", command=self.hints_tree.yview
        )
        self.hints_tree.configure(yscrollcommand=scrollbar.set)

        self.hints_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 提示内容显示
        content_frame = ttk.LabelFrame(frame, text="提示内容")
        content_frame.pack(fill="x", padx=5, pady=5)

        self.hint_content = scrolledtext.ScrolledText(
            content_frame, height=6, wrap="word"
        )
        self.hint_content.pack(fill="both", expand=True, padx=5, pady=5)

        # 操作按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(
            button_frame, text="刷新提示", command=self.refresh_hints
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame, text="关闭提示", command=self.dismiss_selected_hint
        ).pack(side="left", padx=5)
        ttk.Button(
            button_frame, text="模拟场景", command=self.simulate_scenario
        ).pack(side="left", padx=5)

        # 绑定事件
        self.hints_tree.bind("<<TreeviewSelect>>", self.on_hint_select)

        # 初始加载
        self.refresh_hints()

    def _create_help_tab(self, parent):
        """创建帮助系统页面"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="❓ 帮助系统")

        # 搜索框
        search_frame = ttk.Frame(frame)
        search_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(search_frame, text="搜索帮助:").pack(side="left")
        self.search_entry = ttk.Entry(search_frame)
        self.search_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(search_frame, text="搜索", command=self.search_help).pack(
            side="right"
        )

        # 帮助内容选择
        select_frame = ttk.Frame(frame)
        select_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(select_frame, text="选择主题:").pack(side="left")
        self.help_topic = ttk.Combobox(
            select_frame,
            values=[
                "trading_interface",
                "strategy_config",
                "risk_management",
                "api_configuration",
            ],
        )
        self.help_topic.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(
            select_frame, text="显示帮助", command=self.show_help_content
        ).pack(side="right")

        # 帮助内容显示
        content_frame = ttk.LabelFrame(frame, text="帮助内容")
        content_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.help_content = scrolledtext.ScrolledText(
            content_frame, wrap="word"
        )
        self.help_content.pack(fill="both", expand=True, padx=5, pady=5)

        # 相关链接
        links_frame = ttk.Frame(frame)
        links_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(
            links_frame, text="视频教程", command=self.open_video_tutorial
        ).pack(side="left", padx=5)
        ttk.Button(
            links_frame, text="在线文档", command=self.open_online_docs
        ).pack(side="left", padx=5)
        ttk.Button(
            links_frame, text="社区论坛", command=self.open_community
        ).pack(side="left", padx=5)

    def _create_interface_tab(self, parent):
        """创建界面优化页面"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="🎨 界面优化")

        # 主题设置
        theme_frame = ttk.LabelFrame(frame, text="主题设置")
        theme_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(theme_frame, text="选择主题:").pack(side="left", padx=5)
        self.theme_var = tk.StringVar(
            value=self.interface_optimizer.current_theme
        )
        theme_combo = ttk.Combobox(
            theme_frame,
            textvariable=self.theme_var,
            values=list(self.interface_optimizer.theme_configs.keys()),
        )
        theme_combo.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(theme_frame, text="应用", command=self.apply_theme).pack(
            side="right", padx=5
        )

        # 布局设置
        layout_frame = ttk.LabelFrame(frame, text="布局设置")
        layout_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(layout_frame, text="选择布局:").pack(side="left", padx=5)
        self.layout_var = tk.StringVar(
            value=self.interface_optimizer.current_layout
        )
        layout_combo = ttk.Combobox(
            layout_frame,
            textvariable=self.layout_var,
            values=list(self.interface_optimizer.layout_configs.keys()),
        )
        layout_combo.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(layout_frame, text="应用", command=self.apply_layout).pack(
            side="right", padx=5
        )

        # 字体设置
        font_frame = ttk.LabelFrame(frame, text="字体设置")
        font_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(font_frame, text="字体大小:").pack(side="left", padx=5)
        self.font_size = tk.IntVar(value=10)
        font_scale = ttk.Scale(
            font_frame,
            from_=8,
            to=16,
            variable=self.font_size,
            orient="horizontal",
        )
        font_scale.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Label(font_frame, textvariable=self.font_size).pack(
            side="left", padx=5
        )

        # 预览区域
        preview_frame = ttk.LabelFrame(frame, text="预览")
        preview_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.preview_text = scrolledtext.ScrolledText(preview_frame, height=10)
        self.preview_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.preview_text.insert(
            "1.0", "这是界面预览区域。\n您可以在这里看到主题和字体的效果。"
        )

        # 重置按钮
        ttk.Button(
            frame, text="重置为默认", command=self.reset_interface
        ).pack(pady=5)

    def _create_guidance_tab(self, parent):
        """创建用户引导页面"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="🎯 用户引导")

        # 引导控制
        control_frame = ttk.LabelFrame(frame, text="引导控制")
        control_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(
            control_frame, text="开始新手引导", command=self.start_user_tour
        ).pack(side="left", padx=5)
        ttk.Button(
            control_frame, text="重启引导", command=self.restart_tour
        ).pack(side="left", padx=5)
        ttk.Button(
            control_frame, text="跳过引导", command=self.skip_tour
        ).pack(side="left", padx=5)

        # 引导进度
        progress_frame = ttk.LabelFrame(frame, text="引导进度")
        progress_frame.pack(fill="x", padx=5, pady=5)

        self.tour_progress = ttk.Progressbar(
            progress_frame, mode="determinate"
        )
        self.tour_progress.pack(fill="x", padx=5, pady=5)

        self.tour_status = ttk.Label(progress_frame, text="未开始引导")
        self.tour_status.pack(pady=5)

        # 引导步骤列表
        steps_frame = ttk.LabelFrame(frame, text="引导步骤")
        steps_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.steps_tree = ttk.Treeview(
            steps_frame, columns=("title", "target"), show="tree headings"
        )
        self.steps_tree.heading("#0", text="步骤")
        self.steps_tree.heading("title", text="标题")
        self.steps_tree.heading("target", text="目标元素")

        for i, step in enumerate(self.guidance_system.tour_steps):
            self.steps_tree.insert(
                "",
                "end",
                iid=i,
                text=f"步骤 {i+1}",
                values=(step["title"], step["target"]),
            )

        steps_scrollbar = ttk.Scrollbar(
            steps_frame, orient="vertical", command=self.steps_tree.yview
        )
        self.steps_tree.configure(yscrollcommand=steps_scrollbar.set)

        self.steps_tree.pack(side="left", fill="both", expand=True)
        steps_scrollbar.pack(side="right", fill="y")

        # 自定义引导
        custom_frame = ttk.LabelFrame(frame, text="自定义引导")
        custom_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(
            custom_frame, text="创建引导", command=self.create_custom_tour
        ).pack(side="left", padx=5)
        ttk.Button(
            custom_frame, text="导入引导", command=self.import_tour
        ).pack(side="left", padx=5)
        ttk.Button(
            custom_frame, text="导出引导", command=self.export_tour
        ).pack(side="left", padx=5)

    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill="x", side="bottom")

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side="left", padx=5)

        # 活动提示数量
        self.hint_count_label = ttk.Label(status_frame, text="活动提示: 0")
        self.hint_count_label.pack(side="right", padx=5)

    def _start_hint_monitoring(self):
        """开始提示监控"""

        def monitor_hints():
            while True:
                try:
                    # 模拟一些用户操作和上下文更新
                    self.hint_system.update_context(
                        {
                            "login_count": 2,
                            "recent_errors": 0,
                            "recent_pnl": 50.0,
                            "days_since_optimization": 25,
                        }
                    )

                    # 更新提示计数
                    active_hints = self.hint_system.get_active_hints()
                    self.window.after(
                        0,
                        lambda: self.hint_count_label.configure(
                            text=f"活动提示: {len(active_hints)}"
                        ),
                    )

                    time.sleep(10)  # 每10秒检查一次
                except:
                    break

        threading.Thread(target=monitor_hints, daemon=True).start()

    # 事件处理方法
    def refresh_hints(self):
        """刷新提示列表"""
        # 清空现有内容
        for item in self.hints_tree.get_children():
            self.hints_tree.delete(item)

        # 添加活动提示
        active_hints = self.hint_system.get_active_hints()
        for hint in active_hints:
            self.hints_tree.insert(
                "",
                "end",
                iid=hint.id,
                text=hint.id,
                values=(hint.priority, hint.category, hint.title),
            )

    def on_hint_select(self, event):
        """提示选择事件"""
        selection = self.hints_tree.selection()
        if selection:
            hint_id = selection[0]
            hint = self.hint_system.hints.get(hint_id)
            if hint:
                self.hint_content.delete("1.0", "end")
                self.hint_content.insert("1.0", hint.content)
                self.hint_system.mark_hint_shown(hint_id)

    def dismiss_selected_hint(self):
        """关闭选中的提示"""
        selection = self.hints_tree.selection()
        if selection:
            hint_id = selection[0]
            self.hint_system.dismiss_hint(hint_id)
            self.refresh_hints()

    def simulate_scenario(self):
        """模拟场景以触发提示"""
        scenarios = [
            {"recent_errors": 3, "scenario": "频繁错误"},
            {"recent_pnl": -150, "scenario": "性能下降"},
            {"days_since_optimization": 35, "scenario": "需要优化"},
            {"login_count": 1, "scenario": "新用户"},
        ]

        import random

        scenario = random.choice(scenarios)
        self.hint_system.update_context(scenario)

        self.status_label.configure(text=f"模拟场景: {scenario['scenario']}")
        self.refresh_hints()

    def search_help(self):
        """搜索帮助内容"""
        query = self.search_entry.get()
        if query:
            results = self.help_system.search_help(query)

            content = f"搜索结果: '{query}'\n\n"
            for result in results:
                content += f"标题: {result['title']}\n"
                content += f"摘要: {result['excerpt']}\n\n"

            if not results:
                content += "未找到相关内容。"

            self.help_content.delete("1.0", "end")
            self.help_content.insert("1.0", content)

    def show_help_content(self):
        """显示帮助内容"""
        topic = self.help_topic.get()
        if topic:
            help_data = self.help_system.get_help_content(topic)

            content = f"{help_data['title']}\n{'='*50}\n\n"
            content += help_data["content"]

            if help_data.get("related_topics"):
                content += (
                    f"\n\n相关主题: {', '.join(help_data['related_topics'])}"
                )

            self.help_content.delete("1.0", "end")
            self.help_content.insert("1.0", content)

    def open_video_tutorial(self):
        """打开视频教程"""
        topic = self.help_topic.get()
        if topic:
            help_data = self.help_system.get_help_content(topic)
            video_url = help_data.get(
                "video_url", "https://example.com/tutorials"
            )
            webbrowser.open(video_url)

    def open_online_docs(self):
        """打开在线文档"""
        webbrowser.open("https://example.com/docs")

    def open_community(self):
        """打开社区论坛"""
        webbrowser.open("https://example.com/community")

    def apply_theme(self):
        """应用主题"""
        theme_name = self.theme_var.get()
        self.interface_optimizer.current_theme = theme_name
        self.interface_optimizer.apply_theme(self.window, theme_name)
        self.interface_optimizer.apply_theme(self.preview_text, theme_name)
        self.status_label.configure(text=f"已应用主题: {theme_name}")

    def apply_layout(self):
        """应用布局"""
        layout_name = self.layout_var.get()
        self.interface_optimizer.current_layout = layout_name
        self.status_label.configure(text=f"已应用布局: {layout_name}")

    def reset_interface(self):
        """重置界面设置"""
        self.interface_optimizer.current_theme = "professional_dark"
        self.interface_optimizer.current_layout = "standard"
        self.theme_var.set("professional_dark")
        self.layout_var.set("standard")
        self.font_size.set(10)
        self.apply_theme()
        self.status_label.configure(text="界面设置已重置")

    def start_user_tour(self):
        """开始用户引导"""
        step = self.guidance_system.start_tour()
        if step:
            self.update_tour_display(step)
            self.status_label.configure(text="新手引导已开始")

    def restart_tour(self):
        """重启引导"""
        self.guidance_system.end_tour()
        self.start_user_tour()

    def skip_tour(self):
        """跳过引导"""
        self.guidance_system.skip_tour()
        self.tour_progress.configure(value=0)
        self.tour_status.configure(text="引导已跳过")
        self.status_label.configure(text="引导已跳过")

    def update_tour_display(self, step):
        """更新引导显示"""
        if step:
            progress = (step["step_number"] / step["total_steps"]) * 100
            self.tour_progress.configure(value=progress)
            self.tour_status.configure(
                text=f"步骤 {step['step_number']}/{step['total_steps']}: {step['title']}"
            )

    def create_custom_tour(self):
        """创建自定义引导"""
        messagebox.showinfo("自定义引导", "自定义引导创建功能正在开发中...")

    def import_tour(self):
        """导入引导"""
        messagebox.showinfo("导入引导", "引导导入功能正在开发中...")

    def export_tour(self):
        """导出引导"""
        messagebox.showinfo("导出引导", "引导导出功能正在开发中...")


# 测试和演示功能
def demo_ux_enhancement():
    """用户体验增强演示"""
    print("🎨 用户体验增强系统演示")
    print("=" * 50)

    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 创建UX增强系统
    ux_system = UXEnhancementGUI()
    ux_system.create_enhancement_window()

    # 启动主循环
    ux_system.window.mainloop()


if __name__ == "__main__":
    demo_ux_enhancement()

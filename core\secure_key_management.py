#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全密钥管理系统
Secure API Key Management System
"""

import base64
import getpass
import hashlib
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 可选的加密功能
try:
    from cryptography.fernet import <PERSON><PERSON><PERSON>
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False


class KeyManager:
    """密钥管理器"""

    def __init__(
        self,
        master_password: str = None,
        key_file: str = "encrypted_keys.json",
    ):
        """
        初始化密钥管理器

        Args:
            master_password: 主密码
            key_file: 密钥文件路径
        """
        self.key_file = key_file
        self.master_password = master_password
        self.cipher_suite = None
        self.keys_data = {}

        # 如果没有提供主密码，提示用户输入
        if not self.master_password:
            self.master_password = self._get_master_password()

        # 初始化加密套件
        self._initialize_cipher()

        # 加载现有密钥
        self._load_keys()

    def _get_master_password(self) -> str:
        """获取主密码"""
        try:
            # 检查环境变量
            env_password = os.getenv("TRADING_MASTER_PASSWORD")
            if env_password:
                return env_password

            # 提示用户输入
            print("🔐 请输入主密码来保护您的API密钥:")
            password = getpass.getpass("主密码: ")

            if not password:
                raise ValueError("主密码不能为空")

            return password

        except Exception as e:
            print(f"❌ 获取主密码失败: {e}")
            return "default_password_change_me"

    def _initialize_cipher(self):
        """初始化加密套件"""
        try:
            if not CRYPTO_AVAILABLE:
                print("⚠️ 加密库不可用，使用简单编码")
                self.cipher_suite = None
                return

            # 使用主密码生成密钥
            password = self.master_password.encode()
            salt = b"trading_system_salt"  # 在生产环境中应该使用随机盐

            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )

            key = base64.urlsafe_b64encode(kdf.derive(password))
            self.cipher_suite = Fernet(key)

        except Exception as e:
            print(f"❌ 初始化加密套件失败: {e}")
            self.cipher_suite = None

    def _load_keys(self):
        """加载密钥文件"""
        try:
            if os.path.exists(self.key_file):
                with open(self.key_file, "r", encoding="utf-8") as f:
                    encrypted_data = json.load(f)

                # 解密数据
                if "data" in encrypted_data:
                    if self.cipher_suite:
                        decrypted_bytes = self.cipher_suite.decrypt(
                            encrypted_data["data"].encode()
                        )
                        self.keys_data = json.loads(decrypted_bytes.decode())
                    else:
                        # 简单base64解码
                        decoded_data = base64.b64decode(
                            encrypted_data["data"].encode()
                        )
                        self.keys_data = json.loads(decoded_data.decode())
                    print(f"✅ 已加载 {len(self.keys_data)} 个密钥配置")
                else:
                    self.keys_data = {}
            else:
                self.keys_data = {}
                print("📝 创建新的密钥文件")

        except Exception as e:
            print(f"❌ 加载密钥文件失败: {e}")
            self.keys_data = {}

    def _save_keys(self):
        """保存密钥文件"""
        try:
            # 加密数据
            data_bytes = json.dumps(self.keys_data, indent=2).encode()
            if self.cipher_suite:
                encrypted_data = self.cipher_suite.encrypt(data_bytes)
            else:
                # 简单base64编码
                encrypted_data = base64.b64encode(data_bytes)

            # 保存到文件
            save_data = {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "data": encrypted_data.decode(),
            }

            with open(self.key_file, "w", encoding="utf-8") as f:
                json.dump(save_data, f, indent=2)

            print(f"✅ 密钥文件已保存: {self.key_file}")

        except Exception as e:
            print(f"❌ 保存密钥文件失败: {e}")

    def add_exchange_keys(
        self,
        exchange_id: str,
        exchange_name: str,
        api_key: str,
        api_secret: str,
        passphrase: str = "",
        testnet: bool = True,
        description: str = "",
    ) -> bool:
        """
        添加交易所密钥

        Args:
            exchange_id: 交易所ID
            exchange_name: 交易所名称
            api_key: API密钥
            api_secret: API密钥
            passphrase: 密码短语（OKX需要）
            testnet: 是否测试网络
            description: 描述
        """
        try:
            if not api_key or not api_secret:
                print("❌ API密钥和密钥不能为空")
                return False

            # 验证密钥格式
            if len(api_key) < 10 or len(api_secret) < 10:
                print("❌ API密钥格式不正确")
                return False

            key_config = {
                "exchange_name": exchange_name,
                "api_key": api_key,
                "api_secret": api_secret,
                "passphrase": passphrase,
                "testnet": testnet,
                "description": description,
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "usage_count": 0,
                "enabled": True,
            }

            self.keys_data[exchange_id] = key_config
            self._save_keys()

            print(f"✅ 已添加 {exchange_name} 密钥配置")
            return True

        except Exception as e:
            print(f"❌ 添加密钥失败: {e}")
            return False

    def get_exchange_keys(self, exchange_id: str) -> Optional[Dict]:
        """获取交易所密钥"""
        try:
            if exchange_id not in self.keys_data:
                print(f"❌ 未找到交易所密钥: {exchange_id}")
                return None

            key_config = self.keys_data[exchange_id].copy()

            if not key_config.get("enabled", True):
                print(f"⚠️ 交易所密钥已禁用: {exchange_id}")
                return None

            # 更新使用记录
            key_config["last_used"] = datetime.now().isoformat()
            key_config["usage_count"] = key_config.get("usage_count", 0) + 1
            self.keys_data[exchange_id] = key_config
            self._save_keys()

            return key_config

        except Exception as e:
            print(f"❌ 获取密钥失败: {e}")
            return None

    def list_exchanges(self) -> List[Dict]:
        """列出所有交易所"""
        exchanges = []

        for exchange_id, config in self.keys_data.items():
            exchanges.append(
                {
                    "exchange_id": exchange_id,
                    "exchange_name": config["exchange_name"],
                    "testnet": config.get("testnet", True),
                    "enabled": config.get("enabled", True),
                    "description": config.get("description", ""),
                    "created_at": config.get("created_at", ""),
                    "last_used": config.get("last_used", ""),
                    "usage_count": config.get("usage_count", 0),
                }
            )

        return exchanges

    def update_exchange_keys(self, exchange_id: str, **kwargs) -> bool:
        """更新交易所密钥配置"""
        try:
            if exchange_id not in self.keys_data:
                print(f"❌ 未找到交易所: {exchange_id}")
                return False

            # 更新配置
            for key, value in kwargs.items():
                if key in [
                    "api_key",
                    "api_secret",
                    "passphrase",
                    "testnet",
                    "description",
                    "enabled",
                ]:
                    self.keys_data[exchange_id][key] = value

            self.keys_data[exchange_id][
                "updated_at"
            ] = datetime.now().isoformat()
            self._save_keys()

            print(f"✅ 已更新 {exchange_id} 配置")
            return True

        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
            return False

    def remove_exchange_keys(self, exchange_id: str) -> bool:
        """删除交易所密钥"""
        try:
            if exchange_id not in self.keys_data:
                print(f"❌ 未找到交易所: {exchange_id}")
                return False

            exchange_name = self.keys_data[exchange_id]["exchange_name"]
            del self.keys_data[exchange_id]
            self._save_keys()

            print(f"✅ 已删除 {exchange_name} 密钥配置")
            return True

        except Exception as e:
            print(f"❌ 删除密钥失败: {e}")
            return False

    def enable_exchange(self, exchange_id: str) -> bool:
        """启用交易所"""
        return self.update_exchange_keys(exchange_id, enabled=True)

    def disable_exchange(self, exchange_id: str) -> bool:
        """禁用交易所"""
        return self.update_exchange_keys(exchange_id, enabled=False)

    def validate_keys(self, exchange_id: str) -> Dict:
        """验证密钥有效性"""
        try:
            key_config = self.get_exchange_keys(exchange_id)
            if not key_config:
                return {"valid": False, "error": "密钥配置不存在"}

            # 这里可以添加实际的API调用来验证密钥
            # 暂时返回基本验证结果

            api_key = key_config["api_key"]
            api_secret = key_config["api_secret"]

            if len(api_key) < 10 or len(api_secret) < 10:
                return {"valid": False, "error": "密钥格式不正确"}

            return {
                "valid": True,
                "exchange_name": key_config["exchange_name"],
                "testnet": key_config["testnet"],
                "last_validated": datetime.now().isoformat(),
            }

        except Exception as e:
            return {"valid": False, "error": str(e)}

    def export_keys(
        self, output_file: str, include_secrets: bool = False
    ) -> bool:
        """导出密钥配置"""
        try:
            export_data = {}

            for exchange_id, config in self.keys_data.items():
                export_config = {
                    "exchange_name": config["exchange_name"],
                    "testnet": config.get("testnet", True),
                    "description": config.get("description", ""),
                    "enabled": config.get("enabled", True),
                }

                if include_secrets:
                    export_config.update(
                        {
                            "api_key": config["api_key"],
                            "api_secret": config["api_secret"],
                            "passphrase": config.get("passphrase", ""),
                        }
                    )
                else:
                    # 只导出密钥的前几位和后几位
                    api_key = config["api_key"]
                    export_config["api_key_preview"] = (
                        f"{api_key[:4]}...{api_key[-4:]}"
                    )

                export_data[exchange_id] = export_config

            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            print(f"✅ 配置已导出: {output_file}")
            return True

        except Exception as e:
            print(f"❌ 导出配置失败: {e}")
            return False

    def change_master_password(self, new_password: str) -> bool:
        """更改主密码"""
        try:
            if not new_password or len(new_password) < 6:
                print("❌ 新密码长度至少6位")
                return False

            # 备份当前数据
            backup_data = self.keys_data.copy()

            # 更新主密码和加密套件
            self.master_password = new_password
            self._initialize_cipher()

            # 重新保存数据
            self._save_keys()

            print("✅ 主密码已更改")
            return True

        except Exception as e:
            print(f"❌ 更改主密码失败: {e}")
            # 恢复数据
            self.keys_data = backup_data
            return False

    def get_security_status(self) -> Dict:
        """获取安全状态"""
        try:
            total_keys = len(self.keys_data)
            enabled_keys = len(
                [k for k in self.keys_data.values() if k.get("enabled", True)]
            )
            testnet_keys = len(
                [k for k in self.keys_data.values() if k.get("testnet", True)]
            )

            # 检查密钥文件权限
            file_secure = True
            if os.path.exists(self.key_file):
                file_stat = os.stat(self.key_file)
                # 在Unix系统上检查文件权限
                if hasattr(file_stat, "st_mode"):
                    file_mode = oct(file_stat.st_mode)[-3:]
                    if file_mode != "600":  # 只有所有者可读写
                        file_secure = False

            return {
                "total_keys": total_keys,
                "enabled_keys": enabled_keys,
                "testnet_keys": testnet_keys,
                "mainnet_keys": total_keys - testnet_keys,
                "file_exists": os.path.exists(self.key_file),
                "file_secure": file_secure,
                "encryption_enabled": self.cipher_suite is not None,
                "last_access": datetime.now().isoformat(),
            }

        except Exception as e:
            return {"error": str(e)}


# 全局密钥管理器
key_manager = None


def get_key_manager(master_password: str = None) -> KeyManager:
    """获取密钥管理器实例"""
    global key_manager
    if key_manager is None:
        key_manager = KeyManager(master_password)
    return key_manager


def setup_demo_keys():
    """设置演示密钥"""
    try:
        km = get_key_manager("demo_password")

        # 添加演示密钥
        demo_configs = [
            {
                "exchange_id": "gate_demo",
                "exchange_name": "Gate.io",
                "api_key": "demo_gate_api_key_12345",
                "api_secret": "demo_gate_secret_67890",
                "testnet": True,
                "description": "演示用Gate.io密钥",
            },
            {
                "exchange_id": "binance_demo",
                "exchange_name": "Binance",
                "api_key": "demo_binance_api_key_abcde",
                "api_secret": "demo_binance_secret_fghij",
                "testnet": True,
                "description": "演示用Binance密钥",
            },
        ]

        for config in demo_configs:
            km.add_exchange_keys(**config)

        print("✅ 演示密钥配置完成")
        return True

    except Exception as e:
        print(f"❌ 设置演示密钥失败: {e}")
        return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gate.io连接和交易对获取
"""

import ccxt
import time

def test_gate_connection():
    """测试Gate.io连接和可用交易对"""
    print("🔍 测试Gate.io连接...")
    
    try:
        # 创建Gate.io交易所实例（不需要API密钥来获取公开数据）
        exchange = ccxt.gateio({
            'sandbox': True,  # 使用沙盒环境
            'enableRateLimit': True,
            'options': {'defaultType': 'spot'}
        })
        
        print("✅ Gate.io交易所实例创建成功")
        
        # 加载市场数据
        print("📊 正在加载市场数据...")
        markets = exchange.load_markets()
        print(f"✅ 成功加载 {len(markets)} 个交易对")
        
        # 查找USDT交易对
        usdt_pairs = []
        for symbol in markets.keys():
            if '/USDT' in symbol and symbol.count('/') == 1:
                usdt_pairs.append(symbol)
        
        print(f"📈 找到 {len(usdt_pairs)} 个USDT交易对")
        
        # 显示前20个USDT交易对
        print("\n🏆 前20个USDT交易对:")
        for i, pair in enumerate(sorted(usdt_pairs)[:20]):
            print(f"  {i+1:2d}. {pair}")
        
        # 测试获取具体交易对的数据
        test_pairs = ['BTC/USDT', 'ETH/USDT', 'LTC/USDT', 'XRP/USDT']
        print(f"\n💰 测试获取交易对数据:")
        
        for pair in test_pairs:
            try:
                if pair in markets:
                    ticker = exchange.fetch_ticker(pair)
                    print(f"  ✅ {pair}: ${ticker['last']:.2f} (24h: {ticker['percentage']:.2f}%)")
                else:
                    print(f"  ❌ {pair}: 不可用")
            except Exception as e:
                print(f"  ⚠️ {pair}: 获取失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gate.io连接测试失败: {e}")
        return False

def test_gate_api_with_credentials():
    """测试带API凭证的Gate.io连接"""
    print("\n🔐 测试API凭证连接...")
    
    try:
        # 尝试加载保存的凭证
        import json
        from pathlib import Path
        
        config_file = Path("config/api_credentials.json")
        if not config_file.exists():
            print("⚠️ 未找到API凭证配置文件")
            return False
            
        with open(config_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        # 简单解密
        def simple_decrypt(text):
            if not text:
                return ""
            return "".join(chr(ord(c) - 1) for c in text)
        
        api_key = simple_decrypt(data.get("api_key", ""))
        secret_key = simple_decrypt(data.get("secret_key", ""))
        sandbox = data.get("environment", "sandbox") == "sandbox"
        
        if not api_key or not secret_key:
            print("⚠️ API凭证不完整")
            return False
        
        # 创建带凭证的交易所实例
        exchange = ccxt.gateio({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': sandbox,
            'enableRateLimit': True,
            'options': {'defaultType': 'spot'}
        })
        
        print(f"✅ 使用{'沙盒' if sandbox else '实盘'}环境创建交易所实例")
        
        # 测试账户访问
        print("💰 正在获取账户信息...")
        balance = exchange.fetch_balance()
        
        print("✅ 账户访问成功!")
        print("💵 账户余额:")
        
        for currency, bal in balance.items():
            if isinstance(bal, dict) and bal.get('total', 0) > 0:
                print(f"  {currency}: {bal['total']:.8f} (可用: {bal['free']:.8f})")
        
        return True
        
    except Exception as e:
        print(f"❌ API凭证测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Gate.io连接测试开始...\n")
    
    # 测试基本连接
    basic_success = test_gate_connection()
    
    # 测试API凭证连接
    if basic_success:
        api_success = test_gate_api_with_credentials()
        
        if api_success:
            print("\n🎉 所有测试通过! Gate.io连接正常")
        else:
            print("\n⚠️ 基本连接成功，但API凭证测试失败")
    else:
        print("\n❌ 基本连接测试失败")
    
    print("\n测试完成.")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
风险管理系统
Risk Management System

负责实时风险监控、仓位控制、止损管理等风险控制功能
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskLimits:
    """风险限制配置"""
    max_position_size: float = 0.25        # 最大单个仓位占比 (25%)
    max_total_exposure: float = 0.80       # 最大总风险暴露 (80%)
    max_daily_loss: float = 0.05           # 最大日损失 (5%)
    max_drawdown: float = 0.15             # 最大回撤 (15%)
    stop_loss_pct: float = 0.02            # 默认止损比例 (2%)
    take_profit_pct: float = 0.06          # 默认止盈比例 (6%)
    min_account_balance: float = 100.0     # 最小账户余额
    max_orders_per_minute: int = 10        # 每分钟最大订单数
    max_concurrent_positions: int = 5      # 最大并发持仓数


@dataclass
class RiskMetrics:
    """风险指标"""
    current_exposure: float = 0.0          # 当前风险暴露
    daily_pnl: float = 0.0                 # 当日盈亏
    total_drawdown: float = 0.0            # 总回撤
    position_count: int = 0                # 持仓数量
    risk_level: RiskLevel = RiskLevel.LOW  # 风险等级
    last_update: datetime = None
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()


class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化风险管理器
        
        Args:
            config: 风险配置字典
        """
        # 加载风险限制配置
        if config:
            self.limits = RiskLimits(**config)
        else:
            self.limits = RiskLimits()
        
        # 当前风险指标
        self.metrics = RiskMetrics()
        
        # 账户信息
        self.account_balance = 0.0
        self.initial_balance = 0.0
        self.peak_balance = 0.0
        
        # 持仓信息
        self.positions: Dict[str, Dict] = {}
        
        # 订单频率控制
        self.order_timestamps: List[datetime] = []
        
        # 风险事件记录
        self.risk_events: List[Dict] = []
        
        # 监控状态
        self.monitoring = False
        
        logger.info("风险管理器初始化完成")
    
    def update_account_info(self, balance: float, positions: Dict[str, Dict]):
        """
        更新账户信息
        
        Args:
            balance: 账户余额
            positions: 持仓信息
        """
        self.account_balance = balance
        self.positions = positions
        
        # 更新峰值余额
        if balance > self.peak_balance:
            self.peak_balance = balance
        
        # 设置初始余额
        if self.initial_balance == 0:
            self.initial_balance = balance
        
        # 更新风险指标
        self._update_risk_metrics()
        
        logger.debug(f"账户信息更新: 余额={balance}, 持仓数={len(positions)}")
    
    async def validate_order(self, order_data) -> Dict[str, Any]:
        """
        验证订单风险
        
        Args:
            order_data: 订单数据
            
        Returns:
            验证结果字典
        """
        try:
            # 检查账户余额
            if self.account_balance < self.limits.min_account_balance:
                return {
                    'valid': False,
                    'reason': f'账户余额不足，最低要求: {self.limits.min_account_balance}',
                    'risk_level': RiskLevel.CRITICAL
                }
            
            # 检查订单频率
            if not self._check_order_frequency():
                return {
                    'valid': False,
                    'reason': f'订单频率过高，每分钟最多{self.limits.max_orders_per_minute}个订单',
                    'risk_level': RiskLevel.HIGH
                }
            
            # 检查仓位数量
            if len(self.positions) >= self.limits.max_concurrent_positions:
                return {
                    'valid': False,
                    'reason': f'持仓数量已达上限: {self.limits.max_concurrent_positions}',
                    'risk_level': RiskLevel.HIGH
                }
            
            # 检查单个仓位大小
            order_value = order_data.amount * (order_data.price or 0)
            position_ratio = order_value / self.account_balance if self.account_balance > 0 else 1
            
            if position_ratio > self.limits.max_position_size:
                return {
                    'valid': False,
                    'reason': f'单个仓位过大，最大允许: {self.limits.max_position_size*100:.1f}%',
                    'risk_level': RiskLevel.HIGH
                }
            
            # 检查总风险暴露
            new_exposure = self.metrics.current_exposure + position_ratio
            if new_exposure > self.limits.max_total_exposure:
                return {
                    'valid': False,
                    'reason': f'总风险暴露过高，最大允许: {self.limits.max_total_exposure*100:.1f}%',
                    'risk_level': RiskLevel.HIGH
                }
            
            # 检查日损失限制
            if self.metrics.daily_pnl < -self.limits.max_daily_loss * self.initial_balance:
                return {
                    'valid': False,
                    'reason': f'今日损失已达上限: {self.limits.max_daily_loss*100:.1f}%',
                    'risk_level': RiskLevel.CRITICAL
                }
            
            # 检查最大回撤
            if self.metrics.total_drawdown > self.limits.max_drawdown:
                return {
                    'valid': False,
                    'reason': f'回撤已达上限: {self.limits.max_drawdown*100:.1f}%',
                    'risk_level': RiskLevel.CRITICAL
                }
            
            # 记录订单时间
            self.order_timestamps.append(datetime.now())
            
            return {
                'valid': True,
                'reason': '风险检查通过',
                'risk_level': self.metrics.risk_level,
                'suggested_stop_loss': self._calculate_stop_loss(order_data),
                'suggested_take_profit': self._calculate_take_profit(order_data)
            }
            
        except Exception as e:
            logger.error(f"风险验证失败: {str(e)}")
            return {
                'valid': False,
                'reason': f'风险验证错误: {str(e)}',
                'risk_level': RiskLevel.CRITICAL
            }
    
    def calculate_position_size(self, symbol: str, risk_amount: float, entry_price: float, stop_price: float) -> float:
        """
        计算合适的仓位大小
        
        Args:
            symbol: 交易对
            risk_amount: 风险金额
            entry_price: 入场价格
            stop_price: 止损价格
            
        Returns:
            建议的仓位大小
        """
        try:
            if entry_price <= 0 or stop_price <= 0:
                return 0.0
            
            # 计算每单位风险
            risk_per_unit = abs(entry_price - stop_price)
            if risk_per_unit == 0:
                return 0.0
            
            # 计算仓位大小
            position_size = risk_amount / risk_per_unit
            
            # 应用仓位限制
            max_position_value = self.account_balance * self.limits.max_position_size
            max_position_size = max_position_value / entry_price
            
            position_size = min(position_size, max_position_size)
            
            logger.info(f"计算仓位大小: {symbol} 风险={risk_amount} 建议仓位={position_size:.6f}")
            return position_size
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {str(e)}")
            return 0.0
    
    async def monitor_positions(self):
        """监控持仓风险"""
        self.monitoring = True
        logger.info("开始监控持仓风险")
        
        while self.monitoring:
            try:
                # 更新风险指标
                self._update_risk_metrics()
                
                # 检查风险等级
                await self._check_risk_levels()
                
                # 检查止损条件
                await self._check_stop_loss_conditions()
                
                # 等待下次检查
                await asyncio.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                logger.error(f"风险监控错误: {str(e)}")
                await asyncio.sleep(5)
    
    def stop_monitoring(self):
        """停止风险监控"""
        self.monitoring = False
        logger.info("停止风险监控")
    
    def get_risk_report(self) -> Dict[str, Any]:
        """获取风险报告"""
        return {
            'metrics': {
                'current_exposure': self.metrics.current_exposure,
                'daily_pnl': self.metrics.daily_pnl,
                'total_drawdown': self.metrics.total_drawdown,
                'position_count': self.metrics.position_count,
                'risk_level': self.metrics.risk_level.value
            },
            'limits': {
                'max_position_size': self.limits.max_position_size,
                'max_total_exposure': self.limits.max_total_exposure,
                'max_daily_loss': self.limits.max_daily_loss,
                'max_drawdown': self.limits.max_drawdown
            },
            'account': {
                'balance': self.account_balance,
                'initial_balance': self.initial_balance,
                'peak_balance': self.peak_balance
            },
            'recent_events': self.risk_events[-10:]  # 最近10个风险事件
        }
    
    def _update_risk_metrics(self):
        """更新风险指标"""
        try:
            # 计算当前风险暴露
            total_exposure = 0.0
            for symbol, position in self.positions.items():
                position_value = position.get('notional', 0)
                total_exposure += abs(position_value)
            
            self.metrics.current_exposure = total_exposure / self.account_balance if self.account_balance > 0 else 0
            
            # 计算当日盈亏
            self.metrics.daily_pnl = self.account_balance - self.initial_balance
            
            # 计算总回撤
            if self.peak_balance > 0:
                self.metrics.total_drawdown = (self.peak_balance - self.account_balance) / self.peak_balance
            
            # 更新持仓数量
            self.metrics.position_count = len(self.positions)
            
            # 评估风险等级
            self.metrics.risk_level = self._assess_risk_level()
            
            # 更新时间
            self.metrics.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"更新风险指标失败: {str(e)}")
    
    def _assess_risk_level(self) -> RiskLevel:
        """评估当前风险等级"""
        risk_score = 0
        
        # 风险暴露评分
        if self.metrics.current_exposure > self.limits.max_total_exposure * 0.8:
            risk_score += 3
        elif self.metrics.current_exposure > self.limits.max_total_exposure * 0.6:
            risk_score += 2
        elif self.metrics.current_exposure > self.limits.max_total_exposure * 0.4:
            risk_score += 1
        
        # 回撤评分
        if self.metrics.total_drawdown > self.limits.max_drawdown * 0.8:
            risk_score += 3
        elif self.metrics.total_drawdown > self.limits.max_drawdown * 0.6:
            risk_score += 2
        elif self.metrics.total_drawdown > self.limits.max_drawdown * 0.4:
            risk_score += 1
        
        # 日损失评分
        daily_loss_ratio = abs(self.metrics.daily_pnl) / self.initial_balance if self.initial_balance > 0 else 0
        if daily_loss_ratio > self.limits.max_daily_loss * 0.8:
            risk_score += 3
        elif daily_loss_ratio > self.limits.max_daily_loss * 0.6:
            risk_score += 2
        elif daily_loss_ratio > self.limits.max_daily_loss * 0.4:
            risk_score += 1
        
        # 根据评分确定风险等级
        if risk_score >= 6:
            return RiskLevel.CRITICAL
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _check_order_frequency(self) -> bool:
        """检查订单频率"""
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        
        # 清理过期的时间戳
        self.order_timestamps = [ts for ts in self.order_timestamps if ts > one_minute_ago]
        
        # 检查是否超过限制
        return len(self.order_timestamps) < self.limits.max_orders_per_minute
    
    def _calculate_stop_loss(self, order_data) -> Optional[float]:
        """计算建议止损价格"""
        try:
            if not order_data.price:
                return None
            
            if order_data.side.value == 'buy':
                return order_data.price * (1 - self.limits.stop_loss_pct)
            else:
                return order_data.price * (1 + self.limits.stop_loss_pct)
                
        except Exception:
            return None
    
    def _calculate_take_profit(self, order_data) -> Optional[float]:
        """计算建议止盈价格"""
        try:
            if not order_data.price:
                return None
            
            if order_data.side.value == 'buy':
                return order_data.price * (1 + self.limits.take_profit_pct)
            else:
                return order_data.price * (1 - self.limits.take_profit_pct)
                
        except Exception:
            return None
    
    async def _check_risk_levels(self):
        """检查风险等级并触发相应动作"""
        if self.metrics.risk_level == RiskLevel.CRITICAL:
            await self._handle_critical_risk()
        elif self.metrics.risk_level == RiskLevel.HIGH:
            await self._handle_high_risk()
    
    async def _check_stop_loss_conditions(self):
        """检查止损条件"""
        # 这里可以实现自动止损逻辑
        pass
    
    async def _handle_critical_risk(self):
        """处理严重风险"""
        event = {
            'timestamp': datetime.now(),
            'level': RiskLevel.CRITICAL,
            'message': '检测到严重风险，建议立即停止交易',
            'metrics': self.metrics.__dict__.copy()
        }
        self.risk_events.append(event)
        logger.critical("检测到严重风险！")
    
    async def _handle_high_risk(self):
        """处理高风险"""
        event = {
            'timestamp': datetime.now(),
            'level': RiskLevel.HIGH,
            'message': '检测到高风险，建议减少仓位',
            'metrics': self.metrics.__dict__.copy()
        }
        self.risk_events.append(event)
        logger.warning("检测到高风险")


# 全局风险管理器实例
_risk_manager_instance = None


def get_risk_manager(config: Optional[Dict] = None):
    """获取风险管理器实例（单例模式）"""
    global _risk_manager_instance
    if _risk_manager_instance is None:
        _risk_manager_instance = RiskManager(config)
    return _risk_manager_instance


if __name__ == "__main__":
    # 测试代码
    print("风险管理系统模块加载完成")
    print("支持的风险等级:", [level.value for level in RiskLevel])

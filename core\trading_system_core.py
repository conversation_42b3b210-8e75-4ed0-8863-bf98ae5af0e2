#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易系统核心集成模块
Trading System Core Integration Module

整合所有核心功能，提供统一的交易系统接口
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入核心模块 - 使用简化版本
try:
    from .trading.order_manager import OrderManager, get_order_manager
    from .risk.risk_manager import RiskManager, get_risk_manager
    from .trading.execution_engine import ExecutionEngine, get_execution_engine
    from .data.database_manager import DatabaseManager, get_database_manager
    FULL_MODULES_AVAILABLE = True
except ImportError:
    # 如果完整模块不可用，使用简化版本
    FULL_MODULES_AVAILABLE = False
    logger.warning("完整交易模块不可用，使用简化版本")


class TradingSystemCore:
    """交易系统核心"""

    def __init__(self, api_connector, config: Optional[Dict] = None):
        """
        初始化交易系统核心

        Args:
            api_connector: API连接器
            config: 系统配置
        """
        self.api = api_connector
        self.config = config or {}

        # 初始化核心组件
        self.database = get_database_manager(
            self.config.get('database_path', 'database/trading.db')
        )

        self.risk_manager = get_risk_manager(
            self.config.get('risk_config', {})
        )

        self.order_manager = get_order_manager(
            api_connector, self.risk_manager
        )

        self.execution_engine = get_execution_engine(
            self.order_manager, api_connector,
            self.config.get('max_concurrent_executions', 5)
        )

        # 系统状态
        self.is_running = False
        self.monitoring_tasks = []

        # 事件回调
        self.event_callbacks: Dict[str, List[Callable]] = {
            'order_filled': [],
            'risk_alert': [],
            'system_error': [],
            'balance_update': []
        }

        # 设置回调函数
        self._setup_callbacks()

        logger.info("交易系统核心初始化完成")

    async def start(self):
        """启动交易系统"""
        if self.is_running:
            logger.warning("交易系统已在运行")
            return

        try:
            # 启动执行引擎
            await self.execution_engine.start()

            # 启动风险监控
            risk_monitor_task = asyncio.create_task(
                self.risk_manager.monitor_positions()
            )
            self.monitoring_tasks.append(risk_monitor_task)

            # 启动订单状态监控
            order_monitor_task = asyncio.create_task(
                self.order_manager.start_status_monitor()
            )
            self.monitoring_tasks.append(order_monitor_task)

            # 启动账户信息更新
            account_update_task = asyncio.create_task(
                self._account_update_loop()
            )
            self.monitoring_tasks.append(account_update_task)

            self.is_running = True
            logger.info("交易系统已启动")

            # 记录系统启动事件
            await self._log_system_event("system_start", "交易系统启动")

        except Exception as e:
            logger.error(f"启动交易系统失败: {str(e)}")
            await self.stop()
            raise

    async def stop(self):
        """停止交易系统"""
        if not self.is_running:
            return

        try:
            # 停止执行引擎
            await self.execution_engine.stop()

            # 停止风险监控
            self.risk_manager.stop_monitoring()

            # 取消所有监控任务
            for task in self.monitoring_tasks:
                task.cancel()

            # 等待任务结束
            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
            self.monitoring_tasks.clear()

            self.is_running = False
            logger.info("交易系统已停止")

            # 记录系统停止事件
            await self._log_system_event("system_stop", "交易系统停止")

        except Exception as e:
            logger.error(f"停止交易系统失败: {str(e)}")

    async def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        下单接口

        Args:
            order_data: 订单数据

        Returns:
            下单结果
        """
        try:
            # 通过订单管理器下单
            success, message, order = await self.order_manager.place_order(order_data)

            result = {
                'success': success,
                'message': message,
                'order_id': order.id if order else None,
                'timestamp': datetime.now()
            }

            # 保存到数据库
            if order:
                trade_data = {
                    'id': order.id,
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'type': order.type.value,
                    'amount': order.amount,
                    'price': order.price,
                    'status': order.status.value,
                    'timestamp': order.timestamp,
                    'strategy': order.strategy,
                    'client_order_id': order.client_order_id,
                    'exchange_order_id': order.exchange_order_id,
                    'error_message': order.error_message
                }
                self.database.save_trade(trade_data)

            return result

        except Exception as e:
            error_msg = f"下单失败: {str(e)}"
            logger.error(error_msg)
            await self._trigger_event('system_error', {'error': error_msg})
            return {
                'success': False,
                'message': error_msg,
                'order_id': None,
                'timestamp': datetime.now()
            }

    async def execute_strategy_signal(self, signal: Dict[str, Any], priority: int = 1) -> str:
        """
        执行策略信号

        Args:
            signal: 交易信号
            priority: 优先级

        Returns:
            执行任务ID
        """
        try:
            task_id = await self.execution_engine.execute_signal(signal, priority)
            logger.info(f"策略信号已提交执行: {task_id}")
            return task_id

        except Exception as e:
            error_msg = f"执行策略信号失败: {str(e)}"
            logger.error(error_msg)
            await self._trigger_event('system_error', {'error': error_msg})
            raise

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """
        取消订单

        Args:
            order_id: 订单ID

        Returns:
            取消结果
        """
        try:
            success, message = await self.order_manager.cancel_order(order_id)

            result = {
                'success': success,
                'message': message,
                'order_id': order_id,
                'timestamp': datetime.now()
            }

            return result

        except Exception as e:
            error_msg = f"取消订单失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'order_id': order_id,
                'timestamp': datetime.now()
            }

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'api_connected': self.api.is_connected if self.api else False,
            'active_orders': len(self.order_manager.active_orders),
            'execution_queue_size': self.execution_engine.get_queue_size(),
            'risk_level': self.risk_manager.metrics.risk_level.value,
            'account_balance': self.risk_manager.account_balance,
            'current_exposure': self.risk_manager.metrics.current_exposure,
            'daily_pnl': self.risk_manager.metrics.daily_pnl,
            'timestamp': datetime.now()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        # 从数据库获取性能数据
        db_summary = self.database.get_performance_summary()

        # 获取执行引擎指标
        execution_metrics = self.execution_engine.get_metrics()

        # 获取风险报告
        risk_report = self.risk_manager.get_risk_report()

        return {
            'trading_performance': db_summary,
            'execution_metrics': execution_metrics,
            'risk_metrics': risk_report['metrics'],
            'timestamp': datetime.now()
        }

    def add_event_callback(self, event_type: str, callback: Callable):
        """
        添加事件回调

        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
        else:
            logger.warning(f"未知的事件类型: {event_type}")

    async def _account_update_loop(self):
        """账户信息更新循环"""
        while self.is_running:
            try:
                if self.api and self.api.is_connected:
                    # 获取账户余额
                    balance_data = await self.api.fetch_balance()
                    if balance_data:
                        # 更新风险管理器
                        total_balance = balance_data.get('total', 0)
                        positions = {}  # 这里应该获取实际持仓数据

                        self.risk_manager.update_account_info(total_balance, positions)

                        # 保存余额历史
                        balance_history = {
                            'total_balance': total_balance,
                            'available_balance': balance_data.get('free', 0),
                            'frozen_balance': balance_data.get('used', 0),
                            'total_equity': total_balance
                        }
                        self.database.save_balance_history(balance_history)

                        # 触发余额更新事件
                        await self._trigger_event('balance_update', balance_data)

                # 等待30秒后再次更新
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"账户信息更新失败: {str(e)}")
                await asyncio.sleep(60)  # 出错时等待更长时间

    def _setup_callbacks(self):
        """设置内部回调函数"""
        # 设置执行引擎回调
        self.execution_engine.add_execution_callback(self._on_execution_completed)

    async def _on_execution_completed(self, task_id: str, status, result: Dict[str, Any]):
        """执行完成回调"""
        try:
            if status.value == 'completed':
                # 触发订单成交事件
                await self._trigger_event('order_filled', {
                    'task_id': task_id,
                    'order': result.get('order'),
                    'execution_time': result.get('execution_time')
                })
            elif status.value == 'failed':
                # 记录执行失败
                await self._log_system_event("execution_failed", f"执行任务失败: {task_id}")

        except Exception as e:
            logger.error(f"处理执行回调失败: {str(e)}")

    async def _trigger_event(self, event_type: str, data: Dict[str, Any]):
        """触发事件回调"""
        try:
            callbacks = self.event_callbacks.get(event_type, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"执行事件回调失败 {event_type}: {str(e)}")

        except Exception as e:
            logger.error(f"触发事件失败 {event_type}: {str(e)}")

    async def _log_system_event(self, event_type: str, message: str, details: Optional[Dict] = None):
        """记录系统事件"""
        try:
            # 这里可以扩展为更完整的日志系统
            logger.info(f"系统事件 [{event_type}]: {message}")

            # 如果需要，可以保存到数据库
            # self.database.save_system_log(event_type, message, details)

        except Exception as e:
            logger.error(f"记录系统事件失败: {str(e)}")


# 全局交易系统核心实例
_trading_system_core_instance = None


def get_trading_system_core(api_connector=None, config: Optional[Dict] = None):
    """获取交易系统核心实例（单例模式）"""
    global _trading_system_core_instance
    if _trading_system_core_instance is None:
        if api_connector is None:
            raise ValueError("首次创建交易系统核心需要提供API连接器")
        _trading_system_core_instance = TradingSystemCore(api_connector, config)
    return _trading_system_core_instance


if __name__ == "__main__":
    # 测试代码
    print("交易系统核心模块加载完成")
    print("核心功能已集成完毕")

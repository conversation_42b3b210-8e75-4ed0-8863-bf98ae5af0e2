#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主题管理器
Theme Manager

管理GUI界面主题和样式
"""

from typing import Dict, Any
import tkinter as tk
from tkinter import ttk


class ThemeManager:
    """主题管理器"""
    
    # 预定义主题
    THEMES = {
        "dark": {
            "name": "深色主题",
            "colors": {
                "bg_primary": "#2d2d2d",
                "bg_secondary": "#1e1e1e", 
                "bg_accent": "#404040",
                "fg_primary": "#ffffff",
                "fg_secondary": "#cccccc",
                "fg_accent": "#999999",
                "success": "#4CAF50",
                "warning": "#FF9800",
                "error": "#f44336",
                "info": "#2196F3",
                "profit": "#00ff00",
                "loss": "#ff4444",
                "neutral": "#FFD700"
            },
            "fonts": {
                "default": ("Arial", 10),
                "heading": ("Arial", 12, "bold"),
                "monospace": ("Courier", 10),
                "small": ("Arial", 8)
            },
            "gradients": {
                "header": ["#667eea", "#764ba2"],
                "button": ["#4CAF50", "#45a049"],
                "warning": ["#FF9800", "#F57C00"]
            }
        },
        
        "light": {
            "name": "浅色主题",
            "colors": {
                "bg_primary": "#ffffff",
                "bg_secondary": "#f5f5f5",
                "bg_accent": "#e0e0e0",
                "fg_primary": "#000000",
                "fg_secondary": "#333333",
                "fg_accent": "#666666",
                "success": "#4CAF50",
                "warning": "#FF9800", 
                "error": "#f44336",
                "info": "#2196F3",
                "profit": "#2e7d32",
                "loss": "#d32f2f",
                "neutral": "#f57c00"
            },
            "fonts": {
                "default": ("Arial", 10),
                "heading": ("Arial", 12, "bold"),
                "monospace": ("Courier", 10),
                "small": ("Arial", 8)
            },
            "gradients": {
                "header": ["#e3f2fd", "#bbdefb"],
                "button": ["#4CAF50", "#66bb6a"],
                "warning": ["#fff3e0", "#ffcc02"]
            }
        },
        
        "blue": {
            "name": "蓝色主题",
            "colors": {
                "bg_primary": "#1a237e",
                "bg_secondary": "#0d47a1",
                "bg_accent": "#1976d2",
                "fg_primary": "#ffffff",
                "fg_secondary": "#e3f2fd",
                "fg_accent": "#bbdefb",
                "success": "#4CAF50",
                "warning": "#FF9800",
                "error": "#f44336", 
                "info": "#03a9f4",
                "profit": "#00e676",
                "loss": "#ff5252",
                "neutral": "#ffc107"
            },
            "fonts": {
                "default": ("Arial", 10),
                "heading": ("Arial", 12, "bold"),
                "monospace": ("Courier", 10),
                "small": ("Arial", 8)
            },
            "gradients": {
                "header": ["#1976d2", "#1565c0"],
                "button": ["#2196F3", "#1976d2"],
                "warning": ["#ff9800", "#f57c00"]
            }
        },
        
        "green": {
            "name": "绿色主题",
            "colors": {
                "bg_primary": "#1b5e20",
                "bg_secondary": "#2e7d32",
                "bg_accent": "#388e3c",
                "fg_primary": "#ffffff",
                "fg_secondary": "#e8f5e8",
                "fg_accent": "#c8e6c9",
                "success": "#66bb6a",
                "warning": "#FF9800",
                "error": "#f44336",
                "info": "#2196F3",
                "profit": "#00e676",
                "loss": "#ff5252",
                "neutral": "#ffc107"
            },
            "fonts": {
                "default": ("Arial", 10),
                "heading": ("Arial", 12, "bold"),
                "monospace": ("Courier", 10),
                "small": ("Arial", 8)
            },
            "gradients": {
                "header": ["#4caf50", "#388e3c"],
                "button": ["#66bb6a", "#4caf50"],
                "warning": ["#ff9800", "#f57c00"]
            }
        }
    }
    
    def __init__(self, theme_name: str = "dark"):
        """
        初始化主题管理器
        
        Args:
            theme_name: 主题名称
        """
        self.current_theme = theme_name
        self.theme_data = self.THEMES.get(theme_name, self.THEMES["dark"])
    
    def set_theme(self, theme_name: str) -> bool:
        """
        设置主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            是否设置成功
        """
        if theme_name in self.THEMES:
            self.current_theme = theme_name
            self.theme_data = self.THEMES[theme_name]
            return True
        return False
    
    def get_color(self, color_name: str) -> str:
        """
        获取颜色值
        
        Args:
            color_name: 颜色名称
            
        Returns:
            颜色值
        """
        return self.theme_data["colors"].get(color_name, "#000000")
    
    def get_font(self, font_name: str) -> tuple:
        """
        获取字体
        
        Args:
            font_name: 字体名称
            
        Returns:
            字体元组
        """
        return self.theme_data["fonts"].get(font_name, ("Arial", 10))
    
    def get_gradient(self, gradient_name: str) -> list:
        """
        获取渐变色
        
        Args:
            gradient_name: 渐变名称
            
        Returns:
            渐变色列表
        """
        return self.theme_data["gradients"].get(gradient_name, ["#000000", "#333333"])
    
    def apply_to_widget(self, widget: tk.Widget, style_type: str = "default") -> None:
        """
        应用主题到组件
        
        Args:
            widget: Tkinter组件
            style_type: 样式类型
        """
        try:
            if style_type == "frame":
                widget.configure(
                    bg=self.get_color("bg_primary"),
                    fg=self.get_color("fg_primary")
                )
            elif style_type == "label":
                widget.configure(
                    bg=self.get_color("bg_primary"),
                    fg=self.get_color("fg_primary"),
                    font=self.get_font("default")
                )
            elif style_type == "button":
                widget.configure(
                    bg=self.get_color("success"),
                    fg=self.get_color("fg_primary"),
                    font=self.get_font("default"),
                    activebackground=self.get_color("bg_accent")
                )
            elif style_type == "text":
                widget.configure(
                    bg=self.get_color("bg_secondary"),
                    fg=self.get_color("profit"),
                    font=self.get_font("monospace"),
                    insertbackground=self.get_color("fg_primary")
                )
            elif style_type == "entry":
                widget.configure(
                    bg=self.get_color("bg_secondary"),
                    fg=self.get_color("fg_primary"),
                    font=self.get_font("default"),
                    insertbackground=self.get_color("fg_primary")
                )
        except Exception as e:
            print(f"应用主题失败: {e}")
    
    def get_style_config(self, component_type: str) -> Dict[str, Any]:
        """
        获取组件样式配置
        
        Args:
            component_type: 组件类型
            
        Returns:
            样式配置字典
        """
        configs = {
            "window": {
                "bg": self.get_color("bg_primary")
            },
            "frame": {
                "bg": self.get_color("bg_primary"),
                "fg": self.get_color("fg_primary")
            },
            "labelframe": {
                "bg": self.get_color("bg_primary"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("heading")
            },
            "label": {
                "bg": self.get_color("bg_primary"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("default")
            },
            "button": {
                "bg": self.get_color("success"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("default"),
                "activebackground": self.get_color("bg_accent"),
                "relief": "flat",
                "bd": 0
            },
            "button_warning": {
                "bg": self.get_color("warning"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("default"),
                "activebackground": self.get_color("bg_accent")
            },
            "button_error": {
                "bg": self.get_color("error"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("default"),
                "activebackground": self.get_color("bg_accent")
            },
            "text": {
                "bg": self.get_color("bg_secondary"),
                "fg": self.get_color("profit"),
                "font": self.get_font("monospace"),
                "insertbackground": self.get_color("fg_primary"),
                "selectbackground": self.get_color("bg_accent")
            },
            "entry": {
                "bg": self.get_color("bg_secondary"),
                "fg": self.get_color("fg_primary"),
                "font": self.get_font("default"),
                "insertbackground": self.get_color("fg_primary"),
                "selectbackground": self.get_color("bg_accent")
            },
            "scale": {
                "bg": self.get_color("bg_primary"),
                "fg": self.get_color("fg_primary"),
                "troughcolor": self.get_color("bg_secondary"),
                "activebackground": self.get_color("success")
            }
        }
        
        return configs.get(component_type, {})
    
    def create_gradient_frame(self, parent: tk.Widget, gradient_name: str = "header") -> tk.Frame:
        """
        创建渐变背景框架
        
        Args:
            parent: 父组件
            gradient_name: 渐变名称
            
        Returns:
            框架组件
        """
        frame = tk.Frame(parent)
        colors = self.get_gradient(gradient_name)
        
        # 简化版渐变效果（使用单色）
        frame.configure(bg=colors[0])
        
        return frame
    
    def get_available_themes(self) -> Dict[str, str]:
        """
        获取可用主题列表
        
        Returns:
            主题字典 {主题名: 显示名}
        """
        return {name: data["name"] for name, data in self.THEMES.items()}
    
    def export_theme(self, theme_name: str, file_path: str) -> bool:
        """
        导出主题到文件
        
        Args:
            theme_name: 主题名称
            file_path: 文件路径
            
        Returns:
            是否导出成功
        """
        try:
            import json
            
            if theme_name in self.THEMES:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.THEMES[theme_name], f, indent=2, ensure_ascii=False)
                return True
            return False
            
        except Exception as e:
            print(f"导出主题失败: {e}")
            return False
    
    def import_theme(self, theme_name: str, file_path: str) -> bool:
        """
        从文件导入主题
        
        Args:
            theme_name: 主题名称
            file_path: 文件路径
            
        Returns:
            是否导入成功
        """
        try:
            import json
            
            with open(file_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            
            # 验证主题数据结构
            required_keys = ["colors", "fonts", "gradients"]
            if all(key in theme_data for key in required_keys):
                self.THEMES[theme_name] = theme_data
                return True
            return False
            
        except Exception as e:
            print(f"导入主题失败: {e}")
            return False


# 全局主题管理器实例
_theme_manager = None


def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器实例"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager


def get_color(color_name: str) -> str:
    """快捷方式：获取颜色值"""
    return get_theme_manager().get_color(color_name)


def get_font(font_name: str) -> tuple:
    """快捷方式：获取字体"""
    return get_theme_manager().get_font(font_name)


if __name__ == "__main__":
    # 测试代码
    print("🎨 主题管理器测试")
    
    theme = ThemeManager()
    
    # 测试获取颜色
    print(f"主背景色: {theme.get_color('bg_primary')}")
    print(f"成功色: {theme.get_color('success')}")
    
    # 测试切换主题
    theme.set_theme("light")
    print(f"浅色主题背景色: {theme.get_color('bg_primary')}")
    
    # 测试可用主题
    themes = theme.get_available_themes()
    print(f"可用主题: {themes}")
    
    print("🎉 主题管理器测试完成")

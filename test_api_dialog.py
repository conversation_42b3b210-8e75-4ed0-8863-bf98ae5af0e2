#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API登录对话框
直接显示Gate.io API登录界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

def show_api_login_dialog():
    """显示API登录对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 专业颜色配置
    PROFESSIONAL_COLORS = {
        "bg_primary": "#1e1e1e",
        "bg_secondary": "#2d2d2d", 
        "bg_panel": "#3c3c3c",
        "text_primary": "#ffffff",
        "text_secondary": "#cccccc",
        "text_accent": "#4fc3f7",
        "accent": "#2196f3",
        "profit": "#4caf50",
        "loss": "#f44336",
        "warning": "#ff9800",
        "info": "#2196f3"
    }
    
    PROFESSIONAL_FONTS = {
        "heading": ("Arial", 14, "bold"),
        "body": ("Arial", 10),
        "small": ("Arial", 9),
        "data": ("Consolas", 9)
    }
    
    try:
        dialog = tk.Toplevel(root)
        dialog.title("🔐 Gate.io API登录配置")
        dialog.geometry("500x700")
        dialog.configure(bg=PROFESSIONAL_COLORS["bg_primary"])
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (700 // 2)
        dialog.geometry(f"500x700+{x}+{y}")

        # 标题
        title_label = tk.Label(
            dialog,
            text="🔐 Gate.io API登录配置",
            font=("Arial", 16, "bold"),
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_accent"]
        )
        title_label.pack(pady=20)

        # 说明文本
        info_text = """
配置您的Gate.io交易所API凭证以启用实时交易功能

⚠️ 重要提示：
• 请确保API密钥具有现货交易权限
• 建议使用沙盒环境进行测试
• 请妥善保管您的API凭证
        """
        info_label = tk.Label(
            dialog,
            text=info_text,
            font=PROFESSIONAL_FONTS["small"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_secondary"],
            justify="left"
        )
        info_label.pack(pady=10, padx=20)

        # 交易所选择
        exchange_frame = tk.Frame(dialog, bg=PROFESSIONAL_COLORS["bg_primary"])
        exchange_frame.pack(fill="x", padx=30, pady=10)

        tk.Label(
            exchange_frame,
            text="选择交易所:",
            font=PROFESSIONAL_FONTS["body"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_primary"]
        ).pack(anchor="w", pady=(0, 5))

        exchange_var = tk.StringVar(value="Gate.io")
        exchange_combo = ttk.Combobox(
            exchange_frame,
            textvariable=exchange_var,
            values=["Gate.io", "Binance", "OKX", "Huobi"],
            state="readonly",
            width=20
        )
        exchange_combo.pack(fill="x", pady=(0, 10))

        # 交易所信息显示
        exchange_info_label = tk.Label(
            exchange_frame,
            text="",
            font=PROFESSIONAL_FONTS["small"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["info"],
            justify="left"
        )
        exchange_info_label.pack(anchor="w", pady=(0, 10))

        def update_exchange_info():
            """更新交易所信息"""
            exchange = exchange_var.get()
            if exchange == "Gate.io":
                info = """✅ Gate.io 交易所
• 支持现货交易
• 沙盒环境: testnet.gate.io
• 需要: API Key + Secret Key
• 官网: gate.io"""
            elif exchange == "Binance":
                info = """⚠️ Binance 交易所
• 支持现货交易
• 沙盒环境: testnet.binance.vision
• 需要: API Key + Secret Key
• 官网: binance.com"""
            elif exchange == "OKX":
                info = """⚠️ OKX 交易所
• 支持现货交易
• 沙盒环境: aws.okx.com
• 需要: API Key + Secret Key + Passphrase
• 官网: okx.com"""
            elif exchange == "Huobi":
                info = """⚠️ Huobi 交易所
• 支持现货交易
• 沙盒环境: api.testnet.huobi.pro
• 需要: API Key + Secret Key
• 官网: huobi.com"""
            else:
                info = "请选择交易所"
            
            exchange_info_label.config(text=info)

        exchange_combo.bind("<<ComboboxSelected>>", lambda e: update_exchange_info())
        update_exchange_info()  # 初始化显示

        # 创建输入框架
        input_frame = tk.Frame(dialog, bg=PROFESSIONAL_COLORS["bg_primary"])
        input_frame.pack(fill="x", padx=30, pady=20)

        # API Key输入
        tk.Label(
            input_frame,
            text="API Key:",
            font=PROFESSIONAL_FONTS["body"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_primary"]
        ).pack(anchor="w", pady=(10, 5))

        api_key_var = tk.StringVar()
        api_key_entry = tk.Entry(
            input_frame,
            textvariable=api_key_var,
            font=PROFESSIONAL_FONTS["data"],
            bg=PROFESSIONAL_COLORS["bg_panel"],
            fg=PROFESSIONAL_COLORS["text_primary"],
            width=50,
            show="*"
        )
        api_key_entry.pack(fill="x", pady=(0, 10))

        # Secret Key输入
        tk.Label(
            input_frame,
            text="Secret Key:",
            font=PROFESSIONAL_FONTS["body"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_primary"]
        ).pack(anchor="w", pady=(10, 5))

        secret_key_var = tk.StringVar()
        secret_key_entry = tk.Entry(
            input_frame,
            textvariable=secret_key_var,
            font=PROFESSIONAL_FONTS["data"],
            bg=PROFESSIONAL_COLORS["bg_panel"],
            fg=PROFESSIONAL_COLORS["text_primary"],
            width=50,
            show="*"
        )
        secret_key_entry.pack(fill="x", pady=(0, 10))

        # Passphrase输入（可选）
        tk.Label(
            input_frame,
            text="Passphrase (可选):",
            font=PROFESSIONAL_FONTS["body"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_primary"]
        ).pack(anchor="w", pady=(10, 5))

        passphrase_var = tk.StringVar()
        passphrase_entry = tk.Entry(
            input_frame,
            textvariable=passphrase_var,
            font=PROFESSIONAL_FONTS["data"],
            bg=PROFESSIONAL_COLORS["bg_panel"],
            fg=PROFESSIONAL_COLORS["text_primary"],
            width=50,
            show="*"
        )
        passphrase_entry.pack(fill="x", pady=(0, 10))

        # 环境选择
        env_frame = tk.Frame(input_frame, bg=PROFESSIONAL_COLORS["bg_primary"])
        env_frame.pack(fill="x", pady=10)

        tk.Label(
            env_frame,
            text="交易环境:",
            font=PROFESSIONAL_FONTS["body"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        env_var = tk.StringVar(value="沙盒环境")
        env_combo = ttk.Combobox(
            env_frame,
            textvariable=env_var,
            values=["沙盒环境", "实盘环境"],
            state="readonly",
            width=15
        )
        env_combo.pack(side="right")

        # 状态显示
        status_label = tk.Label(
            dialog,
            text="请输入您的Gate.io API凭证",
            font=PROFESSIONAL_FONTS["small"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_secondary"]
        )
        status_label.pack(pady=10)

        # 按钮框架
        button_frame = tk.Frame(dialog, bg=PROFESSIONAL_COLORS["bg_primary"])
        button_frame.pack(fill="x", padx=30, pady=20)

        def test_connection():
            """测试API连接"""
            api_key = api_key_var.get().strip()
            secret_key = secret_key_var.get().strip()
            selected_exchange = exchange_var.get()
            
            if not api_key or not secret_key:
                status_label.config(text="❌ 请输入API Key和Secret Key", fg=PROFESSIONAL_COLORS["loss"])
                return
            
            status_label.config(text=f"🔄 正在测试{selected_exchange}连接...", fg=PROFESSIONAL_COLORS["info"])
            dialog.update()
            
            # 模拟连接测试
            dialog.after(2000, lambda: status_label.config(
                text=f"✅ {selected_exchange}连接测试成功！", 
                fg=PROFESSIONAL_COLORS["profit"]
            ))

        def save_and_connect():
            """保存配置并连接"""
            api_key = api_key_var.get().strip()
            secret_key = secret_key_var.get().strip()
            selected_exchange = exchange_var.get()
            
            if not api_key or not secret_key:
                messagebox.showerror("错误", "请输入API Key和Secret Key")
                return
            
            if selected_exchange == "Gate.io":
                messagebox.showinfo("成功", f"Gate.io API配置已保存！\n\n这是演示界面，实际连接需要在主程序中进行。")
                dialog.destroy()
                root.quit()
            else:
                messagebox.showinfo("提示", f"暂时只支持Gate.io交易所\n{selected_exchange}支持正在开发中")

        # 测试连接按钮
        test_btn = tk.Button(
            button_frame,
            text="🔍 测试连接",
            command=test_connection,
            bg=PROFESSIONAL_COLORS["info"],
            fg="white",
            font=PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=8
        )
        test_btn.pack(side="left", padx=(0, 10))

        # 保存并连接按钮
        save_btn = tk.Button(
            button_frame,
            text="💾 保存并连接",
            command=save_and_connect,
            bg=PROFESSIONAL_COLORS["profit"],
            fg="white",
            font=PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=8
        )
        save_btn.pack(side="left", padx=(0, 10))

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="❌ 取消",
            command=lambda: (dialog.destroy(), root.quit()),
            bg=PROFESSIONAL_COLORS["loss"],
            fg="white",
            font=PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=8
        )
        cancel_btn.pack(side="right")

        # 显示对话框
        dialog.deiconify()
        root.mainloop()

    except Exception as e:
        messagebox.showerror("错误", f"无法显示登录对话框: {e}")

if __name__ == "__main__":
    print("🔐 显示Gate.io API登录对话框...")
    show_api_login_dialog()

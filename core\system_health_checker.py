#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统健康检查工具
System Health Checker

检查系统各组件的健康状态，发现潜在问题
"""

import importlib
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Tuple

import psutil


class HealthChecker:
    """系统健康检查器"""

    def __init__(self):
        self.checks = []
        self.results = {}

    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        print("🔍 开始系统健康检查...")
        print("=" * 50)

        self.results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "UNKNOWN",
            "checks": {},
        }

        # 运行各项检查
        checks = [
            ("Python环境", self._check_python_environment),
            ("依赖包", self._check_dependencies),
            ("文件结构", self._check_file_structure),
            ("配置文件", self._check_configuration),
            ("数据库", self._check_database),
            ("网络连接", self._check_network),
            ("系统资源", self._check_system_resources),
            ("代码质量", self._check_code_quality),
            ("安全配置", self._check_security),
            ("日志系统", self._check_logging),
        ]

        passed = 0
        total = len(checks)

        for name, check_func in checks:
            try:
                result = check_func()
                self.results["checks"][name] = result

                if result["status"] == "PASS":
                    print(f"✅ {name}: {result['message']}")
                    passed += 1
                elif result["status"] == "WARN":
                    print(f"⚠️ {name}: {result['message']}")
                    passed += 0.5
                else:
                    print(f"❌ {name}: {result['message']}")

            except Exception as e:
                error_result = {
                    "status": "FAIL",
                    "message": f"检查失败: {str(e)}",
                    "details": {},
                }
                self.results["checks"][name] = error_result
                print(f"❌ {name}: 检查失败 - {str(e)}")

        # 计算总体状态
        success_rate = passed / total
        if success_rate >= 0.9:
            self.results["overall_status"] = "HEALTHY"
        elif success_rate >= 0.7:
            self.results["overall_status"] = "WARNING"
        else:
            self.results["overall_status"] = "CRITICAL"

        print("\n" + "=" * 50)
        print(f"📊 检查完成: {passed}/{total} 项通过")
        print(f"🎯 系统状态: {self.results['overall_status']}")

        return self.results

    def _check_python_environment(self) -> Dict[str, Any]:
        """检查Python环境"""
        details = {
            "python_version": sys.version,
            "python_executable": sys.executable,
            "platform": sys.platform,
            "architecture": sys.maxsize > 2**32 and "64-bit" or "32-bit",
        }

        # 检查Python版本
        version_info = sys.version_info
        if version_info.major == 3 and version_info.minor >= 8:
            return {
                "status": "PASS",
                "message": f"Python {version_info.major}.{version_info.minor}.{version_info.micro}",
                "details": details,
            }
        else:
            return {
                "status": "FAIL",
                "message": f"Python版本过低: {version_info.major}.{version_info.minor}",
                "details": details,
            }

    def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖包"""
        required_packages = ["ccxt", "pandas", "numpy", "requests", "flask"]

        optional_packages = [
            "scipy",
            "scikit-learn",
            "statsmodels",
            "cryptography",
            "websocket-client",
        ]

        missing_required = []
        missing_optional = []
        installed_packages = {}

        for package in required_packages:
            try:
                module = importlib.import_module(package)
                version = getattr(module, "__version__", "unknown")
                installed_packages[package] = version
            except ImportError:
                missing_required.append(package)

        for package in optional_packages:
            try:
                module = importlib.import_module(package)
                version = getattr(module, "__version__", "unknown")
                installed_packages[package] = version
            except ImportError:
                missing_optional.append(package)

        details = {
            "installed_packages": installed_packages,
            "missing_required": missing_required,
            "missing_optional": missing_optional,
        }

        if missing_required:
            return {
                "status": "FAIL",
                "message": f'缺少必需包: {", ".join(missing_required)}',
                "details": details,
            }
        elif missing_optional:
            return {
                "status": "WARN",
                "message": f'缺少可选包: {", ".join(missing_optional)}',
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"所有依赖包已安装 ({len(installed_packages)}个)",
                "details": details,
            }

    def _check_file_structure(self) -> Dict[str, Any]:
        """检查文件结构"""
        required_files = [
            "requirements.txt",
            "core/config_manager.py",
            "core/error_handler.py",
            "core/logging_config.py",
        ]

        important_files = [
            "core/gate_io_client.py",
            "core/ultimate_trading_gui.py",
            "core/secure_key_management.py",
        ]

        missing_required = []
        missing_important = []
        file_sizes = {}

        for file_path in required_files:
            if os.path.exists(file_path):
                file_sizes[file_path] = os.path.getsize(file_path)
            else:
                missing_required.append(file_path)

        for file_path in important_files:
            if os.path.exists(file_path):
                file_sizes[file_path] = os.path.getsize(file_path)
            else:
                missing_important.append(file_path)

        details = {
            "file_sizes": file_sizes,
            "missing_required": missing_required,
            "missing_important": missing_important,
        }

        if missing_required:
            return {
                "status": "FAIL",
                "message": f'缺少必需文件: {", ".join(missing_required)}',
                "details": details,
            }
        elif missing_important:
            return {
                "status": "WARN",
                "message": f'缺少重要文件: {", ".join(missing_important)}',
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"文件结构完整 ({len(file_sizes)}个文件)",
                "details": details,
            }

    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置文件"""
        config_files = ["config.json", "core/gui_config.json"]

        valid_configs = []
        invalid_configs = []

        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r", encoding="utf-8") as f:
                        json.load(f)
                    valid_configs.append(config_file)
                except json.JSONDecodeError:
                    invalid_configs.append(config_file)

        details = {
            "valid_configs": valid_configs,
            "invalid_configs": invalid_configs,
            "missing_configs": [
                f for f in config_files if not os.path.exists(f)
            ],
        }

        if invalid_configs:
            return {
                "status": "FAIL",
                "message": f'配置文件格式错误: {", ".join(invalid_configs)}',
                "details": details,
            }
        elif len(valid_configs) == 0:
            return {
                "status": "WARN",
                "message": "未找到配置文件，将使用默认配置",
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"配置文件正常 ({len(valid_configs)}个)",
                "details": details,
            }

    def _check_database(self) -> Dict[str, Any]:
        """检查数据库"""
        db_files = ["core/monitoring.db", "core/secure_vault.db"]

        accessible_dbs = []
        inaccessible_dbs = []

        for db_file in db_files:
            if os.path.exists(db_file):
                try:
                    # 简单检查文件是否可读
                    with open(db_file, "rb") as f:
                        f.read(16)  # 读取文件头
                    accessible_dbs.append(db_file)
                except Exception:
                    inaccessible_dbs.append(db_file)

        details = {
            "accessible_dbs": accessible_dbs,
            "inaccessible_dbs": inaccessible_dbs,
            "missing_dbs": [f for f in db_files if not os.path.exists(f)],
        }

        if inaccessible_dbs:
            return {
                "status": "FAIL",
                "message": f'数据库文件损坏: {", ".join(inaccessible_dbs)}',
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"数据库状态正常 ({len(accessible_dbs)}个)",
                "details": details,
            }

    def _check_network(self) -> Dict[str, Any]:
        """检查网络连接"""
        test_urls = [
            "https://api.gateio.ws",
            "https://api.binance.com",
            "https://www.google.com",
        ]

        try:
            import requests

            accessible_urls = []
            failed_urls = []

            for url in test_urls:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code < 400:
                        accessible_urls.append(url)
                    else:
                        failed_urls.append(f"{url} ({response.status_code})")
                except Exception as e:
                    failed_urls.append(f"{url} ({str(e)})")

            details = {
                "accessible_urls": accessible_urls,
                "failed_urls": failed_urls,
            }

            if len(accessible_urls) == 0:
                return {
                    "status": "FAIL",
                    "message": "无法访问任何测试URL",
                    "details": details,
                }
            elif len(failed_urls) > 0:
                return {
                    "status": "WARN",
                    "message": f"部分网络连接失败: {len(failed_urls)}个",
                    "details": details,
                }
            else:
                return {
                    "status": "PASS",
                    "message": "网络连接正常",
                    "details": details,
                }

        except ImportError:
            return {
                "status": "FAIL",
                "message": "缺少requests包，无法测试网络",
                "details": {},
            }

    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用情况
            memory = psutil.virtual_memory()

            # 磁盘使用情况
            disk = psutil.disk_usage(".")

            details = {
                "cpu_percent": cpu_percent,
                "memory_total": memory.total,
                "memory_available": memory.available,
                "memory_percent": memory.percent,
                "disk_total": disk.total,
                "disk_free": disk.free,
                "disk_percent": (disk.used / disk.total) * 100,
            }

            # 检查资源是否充足
            issues = []
            if cpu_percent > 90:
                issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            if memory.percent > 90:
                issues.append(f"内存使用率过高: {memory.percent:.1f}%")
            if details["disk_percent"] > 90:
                issues.append(
                    f'磁盘使用率过高: {details["disk_percent"]:.1f}%'
                )

            if issues:
                return {
                    "status": "WARN",
                    "message": "; ".join(issues),
                    "details": details,
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"系统资源充足 (CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%)",
                    "details": details,
                }

        except ImportError:
            return {
                "status": "WARN",
                "message": "缺少psutil包，无法检查系统资源",
                "details": {},
            }

    def _check_code_quality(self) -> Dict[str, Any]:
        """检查代码质量"""
        python_files = list(Path("core").glob("*.py"))

        syntax_errors = []
        import_errors = []

        for py_file in python_files:
            # 检查语法
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    compile(f.read(), py_file, "exec")
            except SyntaxError as e:
                syntax_errors.append(f"{py_file}: {str(e)}")
            except Exception:
                pass  # 忽略其他编译错误

        details = {
            "total_files": len(python_files),
            "syntax_errors": syntax_errors,
            "import_errors": import_errors,
        }

        if syntax_errors:
            return {
                "status": "FAIL",
                "message": f"发现语法错误: {len(syntax_errors)}个",
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"代码语法正常 ({len(python_files)}个文件)",
                "details": details,
            }

    def _check_security(self) -> Dict[str, Any]:
        """检查安全配置"""
        security_issues = []

        # 检查敏感文件权限
        sensitive_files = ["core/secure_vault.db", "core/encrypted_keys.json"]

        for file_path in sensitive_files:
            if os.path.exists(file_path):
                try:
                    file_stat = os.stat(file_path)
                    # 在Unix系统上检查文件权限
                    if hasattr(file_stat, "st_mode"):
                        file_mode = oct(file_stat.st_mode)[-3:]
                        if file_mode != "600":
                            security_issues.append(
                                f"{file_path}: 文件权限不安全 ({file_mode})"
                            )
                except Exception:
                    pass

        # 检查是否有硬编码的密钥
        config_files = ["core/gui_config.json"]
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r", encoding="utf-8") as f:
                        content = f.read()
                        if "api_key" in content and len(content) > 100:
                            security_issues.append(
                                f"{config_file}: 可能包含硬编码密钥"
                            )
                except Exception:
                    pass

        details = {
            "security_issues": security_issues,
            "checked_files": sensitive_files + config_files,
        }

        if security_issues:
            return {
                "status": "WARN",
                "message": f"发现安全问题: {len(security_issues)}个",
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": "安全配置正常",
                "details": details,
            }

    def _check_logging(self) -> Dict[str, Any]:
        """检查日志系统"""
        log_dir = Path("logs")

        if not log_dir.exists():
            return {
                "status": "WARN",
                "message": "日志目录不存在",
                "details": {"log_dir_exists": False},
            }

        log_files = list(log_dir.glob("*.log"))
        large_logs = []

        for log_file in log_files:
            size = log_file.stat().st_size
            if size > 50 * 1024 * 1024:  # 50MB
                large_logs.append(
                    f"{log_file.name}: {size / 1024 / 1024:.1f}MB"
                )

        details = {
            "log_dir_exists": True,
            "log_files_count": len(log_files),
            "large_logs": large_logs,
        }

        if large_logs:
            return {
                "status": "WARN",
                "message": f"日志文件过大: {len(large_logs)}个",
                "details": details,
            }
        else:
            return {
                "status": "PASS",
                "message": f"日志系统正常 ({len(log_files)}个文件)",
                "details": details,
            }

    def save_report(self, filename: str = None):
        """保存健康检查报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"health_check_report_{timestamp}.json"

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        print(f"📄 健康检查报告已保存: {filename}")


def main():
    """主函数"""
    checker = HealthChecker()
    results = checker.run_all_checks()
    checker.save_report()

    return results["overall_status"] == "HEALTHY"


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

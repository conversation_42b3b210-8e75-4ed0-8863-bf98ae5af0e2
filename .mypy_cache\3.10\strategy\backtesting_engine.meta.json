{"data_mtime": 1748491189, "dep_lines": [11, 13, 14, 15, 16, 17, 441, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["logging", "numpy", "datetime", "typing", "dataclasses", "enum", "random", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "types", "typing_extensions"], "hash": "c9d9e56da122600ceb8548fb0867370ba6e60031", "id": "strategy.backtesting_engine", "ignore_all": true, "interface_hash": "27e43c0a8b1fba54307a81b7eea9b19f72843253", "mtime": 1748490700, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\strategy\\backtesting_engine.py", "plugin_data": null, "size": 16875, "suppressed": ["pandas"], "version_id": "1.15.0"}
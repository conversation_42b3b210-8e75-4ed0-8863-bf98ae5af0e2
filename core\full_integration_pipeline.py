#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全量策略集成管道
Full Strategy Integration Pipeline

实现下一步行动计划：全量集成290个策略，优化表现，构建投资组合
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_integration import StrategyIntegrationManager
from strategy_optimization import StrategyOptimizer, create_sample_optimization_configs
from portfolio_management import InstitutionalPortfolioManager
from institutional_manager import InstitutionalFrameworkManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FullIntegrationPipeline:
    """
    全量集成管道
    
    实现完整的策略集成、优化、组合构建流程
    """
    
    def __init__(self):
        """初始化集成管道"""
        self.framework = InstitutionalFrameworkManager()
        self.integration_manager = StrategyIntegrationManager()
        self.optimizer = StrategyOptimizer(self.framework)
        self.portfolio_manager = InstitutionalPortfolioManager(self.framework)
        
        # 流程状态
        self.pipeline_status = {
            'stage': 'INITIALIZED',
            'total_strategies': 0,
            'processed_strategies': 0,
            'successful_integrations': 0,
            'optimization_candidates': 0,
            'optimized_strategies': 0,
            'final_portfolios': 0,
            'start_time': None,
            'current_stage_start': None
        }
        
        # 结果存储
        self.integration_results = {}
        self.optimization_results = {}
        self.portfolio_results = {}
        
        logger.info("全量集成管道初始化完成")
    
    def execute_full_pipeline(self, max_strategies: Optional[int] = None, 
                            enable_optimization: bool = True,
                            enable_portfolio_construction: bool = True) -> Dict[str, Any]:
        """
        执行完整的集成管道
        
        Args:
            max_strategies: 最大处理策略数量（None表示全部）
            enable_optimization: 是否启用策略优化
            enable_portfolio_construction: 是否启用组合构建
            
        Returns:
            Dict[str, Any]: 完整的执行结果
        """
        logger.info("🚀 开始执行全量策略集成管道")
        self.pipeline_status['start_time'] = datetime.now()
        
        try:
            # 阶段1: 全量策略集成
            logger.info("=" * 60)
            logger.info("阶段1: 全量策略集成")
            logger.info("=" * 60)
            self._update_stage('INTEGRATION')
            integration_summary = self._execute_full_integration(max_strategies)
            
            # 阶段2: 策略优化（可选）
            if enable_optimization and integration_summary['successful_integrations'] > 0:
                logger.info("=" * 60)
                logger.info("阶段2: 策略参数优化")
                logger.info("=" * 60)
                self._update_stage('OPTIMIZATION')
                optimization_summary = self._execute_strategy_optimization()
            else:
                optimization_summary = {'skipped': True}
            
            # 阶段3: 组合构建（可选）
            if enable_portfolio_construction and integration_summary['successful_integrations'] >= 5:
                logger.info("=" * 60)
                logger.info("阶段3: 投资组合构建")
                logger.info("=" * 60)
                self._update_stage('PORTFOLIO_CONSTRUCTION')
                portfolio_summary = self._execute_portfolio_construction()
            else:
                portfolio_summary = {'skipped': True}
            
            # 阶段4: 生成最终报告
            logger.info("=" * 60)
            logger.info("阶段4: 生成最终报告")
            logger.info("=" * 60)
            self._update_stage('REPORTING')
            final_report = self._generate_final_report(integration_summary, optimization_summary, portfolio_summary)
            
            self._update_stage('COMPLETED')
            logger.info("🎉 全量集成管道执行完成！")
            
            return {
                'pipeline_status': self.pipeline_status,
                'integration_summary': integration_summary,
                'optimization_summary': optimization_summary,
                'portfolio_summary': portfolio_summary,
                'final_report': final_report
            }
            
        except Exception as e:
            logger.error(f"管道执行失败: {e}")
            self._update_stage('FAILED')
            raise
    
    def _update_stage(self, stage: str):
        """更新管道阶段"""
        self.pipeline_status['stage'] = stage
        self.pipeline_status['current_stage_start'] = datetime.now()
        logger.info(f"进入阶段: {stage}")
    
    def _execute_full_integration(self, max_strategies: Optional[int]) -> Dict[str, Any]:
        """执行全量策略集成"""
        logger.info("开始全量策略集成...")
        
        # 发现所有策略
        strategy_files = self.integration_manager.discover_strategies()
        total_strategies = len(strategy_files)
        
        if max_strategies:
            strategy_files = strategy_files[:max_strategies]
            logger.info(f"限制处理策略数量为: {max_strategies}")
        
        self.pipeline_status['total_strategies'] = len(strategy_files)
        logger.info(f"发现策略文件: {total_strategies}个，将处理: {len(strategy_files)}个")
        
        # 批量集成策略
        successful_count = 0
        failed_count = 0
        
        # 使用线程池加速处理
        with ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有任务
            future_to_strategy = {
                executor.submit(self.integration_manager.integrate_single_strategy, strategy_file): strategy_file
                for strategy_file in strategy_files
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_strategy):
                strategy_file = future_to_strategy[future]
                try:
                    result = future.result()
                    
                    if result['status'] == 'SUCCESS':
                        self.integration_results[result['strategy_name']] = result
                        successful_count += 1
                        
                        # 实时更新进度
                        self.pipeline_status['processed_strategies'] += 1
                        self.pipeline_status['successful_integrations'] = successful_count
                        
                        if successful_count % 10 == 0:
                            logger.info(f"已成功集成 {successful_count} 个策略...")
                    else:
                        failed_count += 1
                        logger.warning(f"策略集成失败: {strategy_file}")
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"处理策略 {strategy_file} 时出错: {e}")
        
        # 生成集成排名
        self._generate_integration_rankings()
        
        # 保存中间结果
        self._save_integration_results()
        
        summary = {
            'total_strategies_found': total_strategies,
            'strategies_processed': len(strategy_files),
            'successful_integrations': successful_count,
            'failed_integrations': failed_count,
            'success_rate': successful_count / len(strategy_files) if strategy_files else 0,
            'top_strategies': self._get_top_strategies(10)
        }
        
        logger.info(f"集成完成: {successful_count}/{len(strategy_files)} 成功 ({summary['success_rate']:.1%})")
        return summary
    
    def _execute_strategy_optimization(self) -> Dict[str, Any]:
        """执行策略参数优化"""
        logger.info("开始策略参数优化...")
        
        # 筛选需要优化的策略（60-79分）
        optimization_candidates = []
        for strategy_name, result in self.integration_results.items():
            score = result.get('validation_score', 0)
            if 60 <= score < 80:
                optimization_candidates.append(strategy_name)
        
        self.pipeline_status['optimization_candidates'] = len(optimization_candidates)
        logger.info(f"发现 {len(optimization_candidates)} 个策略需要优化")
        
        if len(optimization_candidates) == 0:
            return {'message': '没有需要优化的策略'}
        
        # 为每个策略创建优化配置
        optimization_configs = self._create_optimization_configs(optimization_candidates[:5])  # 限制前5个
        
        # 执行批量优化
        optimization_summary = self.optimizer.batch_optimize_strategies(optimization_configs, max_strategies=5)
        
        # 更新结果
        for strategy_name, opt_result in self.optimizer.optimization_results.items():
            if opt_result['optimization_success']:
                # 更新原始结果
                original_result = self.integration_results.get(strategy_name.replace('_optimized', ''), {})
                comparison = self.optimizer.compare_optimization_results(strategy_name, original_result)
                self.optimization_results[strategy_name] = {
                    'optimization_result': opt_result,
                    'comparison': comparison
                }
                self.pipeline_status['optimized_strategies'] += 1
        
        logger.info(f"优化完成: {self.pipeline_status['optimized_strategies']} 个策略")
        return optimization_summary
    
    def _execute_portfolio_construction(self) -> Dict[str, Any]:
        """执行投资组合构建"""
        logger.info("开始构建投资组合...")
        
        # 创建模拟收益数据
        returns_data = self._create_returns_data()
        
        # 构建多个组合
        portfolios_created = []
        
        # 组合1: 保守型（高评分策略）
        try:
            conservative_portfolio = self.portfolio_manager.create_portfolio(
                portfolio_name="Conservative_Portfolio",
                integration_results=self.integration_results,
                returns_data=returns_data,
                optimization_objective='min_variance'
            )
            portfolios_created.append('Conservative_Portfolio')
            
            # 模拟表现
            self.portfolio_manager.simulate_portfolio_performance(
                'Conservative_Portfolio', returns_data
            )
        except Exception as e:
            logger.warning(f"保守型组合创建失败: {e}")
        
        # 组合2: 平衡型（夏普比率最优）
        try:
            balanced_portfolio = self.portfolio_manager.create_portfolio(
                portfolio_name="Balanced_Portfolio",
                integration_results=self.integration_results,
                returns_data=returns_data,
                optimization_objective='max_sharpe'
            )
            portfolios_created.append('Balanced_Portfolio')
            
            # 模拟表现
            self.portfolio_manager.simulate_portfolio_performance(
                'Balanced_Portfolio', returns_data
            )
        except Exception as e:
            logger.warning(f"平衡型组合创建失败: {e}")
        
        # 组合3: 进取型（最大收益）
        try:
            aggressive_portfolio = self.portfolio_manager.create_portfolio(
                portfolio_name="Aggressive_Portfolio",
                integration_results=self.integration_results,
                returns_data=returns_data,
                optimization_objective='max_return'
            )
            portfolios_created.append('Aggressive_Portfolio')
            
            # 模拟表现
            self.portfolio_manager.simulate_portfolio_performance(
                'Aggressive_Portfolio', returns_data
            )
        except Exception as e:
            logger.warning(f"进取型组合创建失败: {e}")
        
        # 保存组合结果
        self.portfolio_manager.save_portfolio_results("pipeline_portfolios")
        
        self.pipeline_status['final_portfolios'] = len(portfolios_created)
        
        summary = {
            'portfolios_created': portfolios_created,
            'portfolio_count': len(portfolios_created),
            'portfolio_details': {}
        }
        
        # 收集组合详情
        for portfolio_name in portfolios_created:
            if portfolio_name in self.portfolio_manager.portfolios:
                portfolio = self.portfolio_manager.portfolios[portfolio_name]
                performance = self.portfolio_manager.portfolio_performance.get(portfolio_name)
                
                summary['portfolio_details'][portfolio_name] = {
                    'strategy_count': portfolio['portfolio_statistics']['n_strategies'],
                    'expected_return': portfolio['expected_performance']['return'],
                    'expected_sharpe': portfolio['expected_performance']['sharpe_ratio'],
                    'simulated_return': performance['performance_metrics']['annualized_return'] if performance else None,
                    'simulated_sharpe': performance['performance_metrics']['sharpe_ratio'] if performance else None
                }
        
        logger.info(f"组合构建完成: {len(portfolios_created)} 个组合")
        return summary
    
    def _generate_integration_rankings(self):
        """生成集成排名"""
        rankings = []
        for strategy_name, result in self.integration_results.items():
            rankings.append({
                'strategy_name': strategy_name,
                'validation_score': result['validation_score'],
                'sharpe_ratio': result['sharpe_ratio'],
                'max_drawdown': result['max_drawdown'],
                'recommendation': result['recommendation']
            })
        
        # 按验证评分排序
        rankings.sort(key=lambda x: x['validation_score'], reverse=True)
        self.integration_manager.strategy_rankings = rankings
    
    def _get_top_strategies(self, n: int) -> List[Dict]:
        """获取前N个策略"""
        if hasattr(self.integration_manager, 'strategy_rankings'):
            return self.integration_manager.strategy_rankings[:n]
        return []
    
    def _create_optimization_configs(self, strategy_names: List[str]) -> Dict[str, Dict]:
        """为策略创建优化配置"""
        configs = {}
        
        for strategy_name in strategy_names:
            # 根据策略类型创建配置
            if 'trend' in strategy_name.lower() or 'momentum' in strategy_name.lower():
                configs[strategy_name] = {
                    'parameter_ranges': {
                        'lookback': (5, 30),
                        'momentum_factor': (0.1, 2.0),
                        'volatility': (0.005, 0.025)
                    },
                    'data_generator': self._create_trend_generator(strategy_name)
                }
            elif 'reversion' in strategy_name.lower() or 'mean' in strategy_name.lower():
                configs[strategy_name] = {
                    'parameter_ranges': {
                        'reversion_speed': (0.1, 1.0),
                        'threshold': (0.01, 0.1),
                        'volatility': (0.005, 0.020)
                    },
                    'data_generator': self._create_reversion_generator(strategy_name)
                }
            else:
                # 默认配置
                configs[strategy_name] = {
                    'parameter_ranges': {
                        'param1': (0.1, 2.0),
                        'param2': (0.01, 0.1),
                        'volatility': (0.005, 0.025)
                    },
                    'data_generator': self._create_default_generator(strategy_name)
                }
        
        return configs
    
    def _create_trend_generator(self, strategy_name: str):
        """创建趋势策略数据生成器"""
        def generator(params):
            dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
            returns = []
            for i in range(len(dates)):
                if i == 0:
                    daily_return = np.random.normal(0.0008, params.get('volatility', 0.015))
                else:
                    momentum = returns[i-1] * params.get('momentum_factor', 0.15)
                    daily_return = np.random.normal(0.0006 + momentum, params.get('volatility', 0.012))
                returns.append(daily_return)
            return pd.Series(returns, index=dates)
        return generator
    
    def _create_reversion_generator(self, strategy_name: str):
        """创建均值回归策略数据生成器"""
        def generator(params):
            dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
            returns = []
            price_level = 100
            for i in range(len(dates)):
                deviation = (price_level - 100) / 100
                if abs(deviation) > params.get('threshold', 0.05):
                    expected_return = -deviation * params.get('reversion_speed', 0.1)
                else:
                    expected_return = 0
                daily_return = np.random.normal(expected_return, params.get('volatility', 0.010))
                returns.append(daily_return)
                price_level *= (1 + daily_return)
            return pd.Series(returns, index=dates)
        return generator
    
    def _create_default_generator(self, strategy_name: str):
        """创建默认数据生成器"""
        def generator(params):
            dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
            returns = np.random.normal(0.0005, params.get('volatility', 0.015), len(dates))
            return pd.Series(returns, index=dates)
        return generator
    
    def _create_returns_data(self) -> Dict[str, pd.Series]:
        """创建策略收益数据"""
        returns_data = {}
        dates = pd.date_range('2022-01-01', '2024-12-31', freq='D')
        
        for strategy_name, result in self.integration_results.items():
            # 根据验证评分生成相应质量的收益数据
            score = result['validation_score']
            if score >= 80:
                # 优秀策略
                returns = np.random.normal(0.001, 0.012, len(dates))
            elif score >= 60:
                # 良好策略
                returns = np.random.normal(0.0006, 0.015, len(dates))
            else:
                # 一般策略
                returns = np.random.normal(0.0002, 0.020, len(dates))
            
            returns_data[strategy_name] = pd.Series(returns, index=dates)
        
        return returns_data
    
    def _save_integration_results(self):
        """保存集成结果"""
        # 保存到integration_manager
        self.integration_manager.integration_results = self.integration_results
        self.integration_manager._save_final_results()
    
    def _generate_final_report(self, integration_summary: Dict, 
                             optimization_summary: Dict, 
                             portfolio_summary: Dict) -> str:
        """生成最终报告"""
        total_time = datetime.now() - self.pipeline_status['start_time']
        
        report = f"""
=== 全量策略集成管道执行报告 ===
执行时间: {self.pipeline_status['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {total_time}

=== 执行概况 ===
管道状态: {self.pipeline_status['stage']}
处理策略总数: {self.pipeline_status['total_strategies']}
成功集成: {self.pipeline_status['successful_integrations']}
优化策略: {self.pipeline_status['optimized_strategies']}
构建组合: {self.pipeline_status['final_portfolios']}

=== 阶段1: 策略集成结果 ===
发现策略: {integration_summary['total_strategies_found']}个
处理策略: {integration_summary['strategies_processed']}个
成功集成: {integration_summary['successful_integrations']}个
成功率: {integration_summary['success_rate']:.1%}

前10名策略:"""
        
        for i, strategy in enumerate(integration_summary['top_strategies'], 1):
            report += f"""
{i}. {strategy['strategy_name']}
   评分: {strategy['validation_score']:.1f}/100
   夏普比率: {strategy['sharpe_ratio']:.3f}
   建议: {strategy['recommendation']}"""
        
        if not optimization_summary.get('skipped'):
            report += f"""

=== 阶段2: 策略优化结果 ===
优化候选: {self.pipeline_status['optimization_candidates']}个
成功优化: {optimization_summary.get('successful_optimizations', 0)}个
优化成功率: {optimization_summary.get('success_rate', 0):.1%}"""
        
        if not portfolio_summary.get('skipped'):
            report += f"""

=== 阶段3: 组合构建结果 ===
构建组合: {portfolio_summary['portfolio_count']}个
组合类型: {', '.join(portfolio_summary['portfolios_created'])}

组合表现:"""
            
            for name, details in portfolio_summary['portfolio_details'].items():
                report += f"""
{name}:
  策略数量: {details['strategy_count']}
  预期收益: {details['expected_return']:.2%}
  预期夏普: {details['expected_sharpe']:.3f}"""
                if details['simulated_return']:
                    report += f"""
  模拟收益: {details['simulated_return']:.2%}
  模拟夏普: {details['simulated_sharpe']:.3f}"""
        
        report += f"""

=== 下一步建议 ===
1. 对评分80分以上的策略进行实盘验证
2. 继续优化60-79分的策略参数
3. 监控组合表现，及时调整权重
4. 建立定期重新验证机制

=== 文件输出 ===
- 集成结果: integration_results/
- 优化结果: 优化器内部存储
- 组合结果: pipeline_portfolios/

执行完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report

def main():
    """主函数"""
    print("🚀 启动全量策略集成管道")
    print("=" * 80)
    
    # 创建管道
    pipeline = FullIntegrationPipeline()
    
    # 执行管道（先用较小规模测试）
    try:
        results = pipeline.execute_full_pipeline(
            max_strategies=50,  # 先处理50个策略
            enable_optimization=True,
            enable_portfolio_construction=True
        )
        
        # 打印最终报告
        print("\n" + "=" * 80)
        print("📊 执行完成！最终报告：")
        print("=" * 80)
        print(results['final_report'])
        
        # 保存完整结果
        with open('pipeline_execution_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print("\n✅ 完整结果已保存到: pipeline_execution_results.json")
        
    except Exception as e:
        print(f"\n❌ 管道执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

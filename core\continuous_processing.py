#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
连续处理脚本
Continuous Processing Script

自动化执行多轮策略集成、优化和组合构建
"""

import os
import sys
import time
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any

# 设置日志
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContinuousProcessor:
    """
    连续处理器
    
    自动化执行多轮策略处理
    """
    
    def __init__(self, max_rounds: int = 20, max_strategies_per_round: int = 10):
        """
        初始化连续处理器
        
        Args:
            max_rounds: 最大处理轮数
            max_strategies_per_round: 每轮最大策略数
        """
        self.max_rounds = max_rounds
        self.max_strategies_per_round = max_strategies_per_round
        self.current_round = 0
        self.total_processed = 0
        self.total_successful = 0
        self.excellent_strategies = []
        self.good_strategies = []
        self.optimized_strategies = []
        self.portfolios_created = []
        
        # 执行历史
        self.execution_history = []
        
        logger.info(f"连续处理器初始化完成，最大轮数: {max_rounds}")
    
    def run_continuous_processing(self) -> Dict[str, Any]:
        """
        运行连续处理
        
        Returns:
            Dict[str, Any]: 处理结果总结
        """
        logger.info("🚀 开始连续处理")
        start_time = datetime.now()
        
        try:
            while self.current_round < self.max_rounds:
                self.current_round += 1
                logger.info(f"=" * 60)
                logger.info(f"开始第 {self.current_round} 轮处理")
                logger.info(f"=" * 60)
                
                # 执行一轮完整的8步骤处理
                round_result = self._execute_single_round()
                
                # 记录轮次结果
                self.execution_history.append({
                    'round': self.current_round,
                    'timestamp': datetime.now(),
                    'result': round_result
                })
                
                # 检查是否还有策略需要处理
                if round_result.get('no_more_strategies', False):
                    logger.info("✅ 所有策略已处理完成")
                    break
                
                # 更新统计信息
                self._update_statistics(round_result)
                
                # 显示进度
                self._display_progress()
                
                # 短暂休息
                time.sleep(2)
            
            # 生成最终报告
            final_report = self._generate_final_report(start_time)
            
            logger.info("🎉 连续处理完成！")
            return final_report
            
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断处理")
            return self._generate_final_report(start_time, interrupted=True)
        except Exception as e:
            logger.error(f"❌ 连续处理失败: {e}")
            return self._generate_final_report(start_time, error=str(e))
    
    def _execute_single_round(self) -> Dict[str, Any]:
        """执行单轮8步骤处理"""
        round_result = {
            'steps_completed': 0,
            'successful_integrations': 0,
            'excellent_strategies': [],
            'good_strategies': [],
            'optimized_strategies': [],
            'portfolios_created': [],
            'no_more_strategies': False
        }
        
        # 执行8个步骤
        for step in range(1, 9):
            try:
                logger.info(f"执行步骤 {step}/8")
                
                # 运行步骤脚本
                result = subprocess.run(
                    ['python', 'step_by_step_implementation.py'],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )
                
                if result.returncode == 0:
                    round_result['steps_completed'] += 1
                    
                    # 解析输出
                    output = result.stdout
                    if "成功集成:" in output:
                        # 提取集成数量
                        import re
                        match = re.search(r'成功集成: (\d+) 个策略', output)
                        if match:
                            round_result['successful_integrations'] = int(match.group(1))
                    
                    if "所有策略已处理完成" in output:
                        round_result['no_more_strategies'] = True
                        break
                        
                else:
                    logger.warning(f"步骤 {step} 执行失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                logger.warning(f"步骤 {step} 执行超时")
            except Exception as e:
                logger.error(f"步骤 {step} 执行出错: {e}")
        
        return round_result
    
    def _update_statistics(self, round_result: Dict[str, Any]):
        """更新统计信息"""
        self.total_processed += round_result.get('successful_integrations', 0)
        self.total_successful += round_result.get('successful_integrations', 0)
        
        # 这里可以添加更详细的统计信息收集
        # 例如从集成结果文件中读取策略评分等
    
    def _display_progress(self):
        """显示处理进度"""
        logger.info(f"📊 当前进度:")
        logger.info(f"   已完成轮数: {self.current_round}/{self.max_rounds}")
        logger.info(f"   已处理策略: {self.total_processed}")
        logger.info(f"   成功集成: {self.total_successful}")
        
        # 计算预估剩余时间
        if self.current_round > 0:
            avg_time_per_round = (datetime.now() - self.execution_history[0]['timestamp']).total_seconds() / self.current_round
            remaining_rounds = self.max_rounds - self.current_round
            estimated_remaining = remaining_rounds * avg_time_per_round / 60  # 转换为分钟
            logger.info(f"   预估剩余时间: {estimated_remaining:.1f} 分钟")
    
    def _generate_final_report(self, start_time: datetime, interrupted: bool = False, error: str = None) -> Dict[str, Any]:
        """生成最终报告"""
        end_time = datetime.now()
        total_time = end_time - start_time
        
        # 尝试读取最新的集成结果
        try:
            if os.path.exists('integration_results/integration_results.json'):
                with open('integration_results/integration_results.json', 'r', encoding='utf-8') as f:
                    integration_results = json.load(f)
                
                # 分析策略质量
                excellent = [name for name, result in integration_results.items() 
                           if result.get('validation_score', 0) >= 80]
                good = [name for name, result in integration_results.items() 
                       if 60 <= result.get('validation_score', 0) < 80]
                
                self.excellent_strategies = excellent
                self.good_strategies = good
                
        except Exception as e:
            logger.warning(f"读取集成结果失败: {e}")
        
        report = {
            'execution_summary': {
                'start_time': start_time,
                'end_time': end_time,
                'total_time': str(total_time),
                'completed_rounds': self.current_round,
                'max_rounds': self.max_rounds,
                'interrupted': interrupted,
                'error': error
            },
            'processing_statistics': {
                'total_strategies_processed': self.total_processed,
                'successful_integrations': self.total_successful,
                'excellent_strategies_count': len(self.excellent_strategies),
                'good_strategies_count': len(self.good_strategies),
                'excellent_strategies': self.excellent_strategies,
                'good_strategies': self.good_strategies
            },
            'execution_history': self.execution_history,
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_file = f"continuous_processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📋 最终报告已保存到: {report_file}")
        
        # 打印摘要
        self._print_summary(report)
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if len(self.excellent_strategies) > 0:
            recommendations.append(f"发现 {len(self.excellent_strategies)} 个优秀策略，建议优先部署")
        
        if len(self.good_strategies) > 0:
            recommendations.append(f"发现 {len(self.good_strategies)} 个良好策略，建议进行参数优化")
        
        if self.total_processed > 50:
            recommendations.append("已处理大量策略，建议构建多样化投资组合")
        
        if self.current_round >= self.max_rounds:
            recommendations.append("已达到最大轮数，建议分析处理结果并制定下一步计划")
        
        return recommendations
    
    def _print_summary(self, report: Dict[str, Any]):
        """打印摘要"""
        print("\n" + "=" * 80)
        print("🎉 连续处理完成！最终摘要")
        print("=" * 80)
        
        exec_summary = report['execution_summary']
        proc_stats = report['processing_statistics']
        
        print(f"⏱️  执行时间: {exec_summary['total_time']}")
        print(f"🔄 完成轮数: {exec_summary['completed_rounds']}/{exec_summary['max_rounds']}")
        print(f"📊 处理策略: {proc_stats['total_strategies_processed']} 个")
        print(f"✅ 成功集成: {proc_stats['successful_integrations']} 个")
        print(f"⭐ 优秀策略: {proc_stats['excellent_strategies_count']} 个")
        print(f"👍 良好策略: {proc_stats['good_strategies_count']} 个")
        
        if proc_stats['excellent_strategies']:
            print(f"\n🏆 优秀策略列表:")
            for i, strategy in enumerate(proc_stats['excellent_strategies'][:5], 1):
                print(f"   {i}. {strategy}")
            if len(proc_stats['excellent_strategies']) > 5:
                print(f"   ... 还有 {len(proc_stats['excellent_strategies']) - 5} 个")
        
        print(f"\n💡 建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        print("=" * 80)

def main():
    """主函数"""
    print("🚀 启动连续处理系统")
    print("=" * 60)
    
    # 创建处理器
    processor = ContinuousProcessor(max_rounds=20, max_strategies_per_round=10)
    
    try:
        # 运行连续处理
        final_report = processor.run_continuous_processing()
        
        print("\n✅ 连续处理系统执行完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

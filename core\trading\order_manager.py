#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单管理系统
Order Management System

负责处理所有交易订单的创建、修改、取消和状态跟踪
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"           # 市价单
    LIMIT = "limit"             # 限价单
    STOP_LOSS = "stop_loss"     # 止损单
    TAKE_PROFIT = "take_profit" # 止盈单
    STOP_LIMIT = "stop_limit"   # 止损限价单


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"         # 待提交
    SUBMITTED = "submitted"     # 已提交
    PARTIAL = "partial"         # 部分成交
    FILLED = "filled"           # 完全成交
    CANCELLED = "cancelled"     # 已取消
    REJECTED = "rejected"       # 被拒绝
    EXPIRED = "expired"         # 已过期


@dataclass
class OrderData:
    """订单数据结构"""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    amount: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_amount: float = 0.0
    average_price: float = 0.0
    timestamp: datetime = None
    updated_at: datetime = None
    strategy: Optional[str] = None
    client_order_id: Optional[str] = None
    exchange_order_id: Optional[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.client_order_id is None:
            self.client_order_id = str(uuid.uuid4())


class OrderManager:
    """订单管理器"""
    
    def __init__(self, api_connector, risk_manager=None):
        """
        初始化订单管理器
        
        Args:
            api_connector: API连接器实例
            risk_manager: 风险管理器实例
        """
        self.api = api_connector
        self.risk_manager = risk_manager
        
        # 订单存储
        self.active_orders: Dict[str, OrderData] = {}
        self.order_history: List[OrderData] = []
        
        # 订单队列
        self.order_queue = asyncio.Queue()
        self.processing = False
        
        # 统计信息
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'cancelled_orders': 0
        }
        
        logger.info("订单管理器初始化完成")
    
    async def place_order(self, order_data: Dict) -> Tuple[bool, str, Optional[OrderData]]:
        """
        提交订单
        
        Args:
            order_data: 订单数据字典
            
        Returns:
            (成功标志, 消息, 订单对象)
        """
        try:
            # 创建订单对象
            order = OrderData(
                id=str(uuid.uuid4()),
                symbol=order_data['symbol'],
                side=OrderSide(order_data['side']),
                type=OrderType(order_data['type']),
                amount=float(order_data['amount']),
                price=order_data.get('price'),
                stop_price=order_data.get('stop_price'),
                strategy=order_data.get('strategy')
            )
            
            # 风险检查
            if self.risk_manager:
                risk_check = await self.risk_manager.validate_order(order)
                if not risk_check['valid']:
                    order.status = OrderStatus.REJECTED
                    order.error_message = risk_check['reason']
                    self.order_history.append(order)
                    self.stats['failed_orders'] += 1
                    logger.warning(f"订单风险检查失败: {risk_check['reason']}")
                    return False, f"风险检查失败: {risk_check['reason']}", order
            
            # 添加到活跃订单
            self.active_orders[order.id] = order
            order.status = OrderStatus.SUBMITTED
            
            # 提交到交易所
            success, result = await self._submit_to_exchange(order)
            
            if success:
                order.exchange_order_id = result.get('id')
                order.status = OrderStatus.SUBMITTED
                self.stats['successful_orders'] += 1
                logger.info(f"订单提交成功: {order.id} -> {order.exchange_order_id}")
                return True, "订单提交成功", order
            else:
                order.status = OrderStatus.REJECTED
                order.error_message = result
                self._move_to_history(order.id)
                self.stats['failed_orders'] += 1
                logger.error(f"订单提交失败: {result}")
                return False, f"订单提交失败: {result}", order
                
        except Exception as e:
            error_msg = f"提交订单时发生错误: {str(e)}"
            logger.error(error_msg)
            self.stats['failed_orders'] += 1
            return False, error_msg, None
    
    async def cancel_order(self, order_id: str) -> Tuple[bool, str]:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            (成功标志, 消息)
        """
        try:
            if order_id not in self.active_orders:
                return False, "订单不存在或已完成"
            
            order = self.active_orders[order_id]
            
            # 如果订单还没有交易所ID，直接取消
            if not order.exchange_order_id:
                order.status = OrderStatus.CANCELLED
                self._move_to_history(order_id)
                self.stats['cancelled_orders'] += 1
                logger.info(f"本地订单已取消: {order_id}")
                return True, "订单已取消"
            
            # 向交易所发送取消请求
            success, result = await self._cancel_on_exchange(order)
            
            if success:
                order.status = OrderStatus.CANCELLED
                self._move_to_history(order_id)
                self.stats['cancelled_orders'] += 1
                logger.info(f"交易所订单已取消: {order_id}")
                return True, "订单已取消"
            else:
                logger.error(f"取消订单失败: {result}")
                return False, f"取消订单失败: {result}"
                
        except Exception as e:
            error_msg = f"取消订单时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> Tuple[int, int]:
        """
        取消所有订单
        
        Args:
            symbol: 可选，只取消指定交易对的订单
            
        Returns:
            (成功取消数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        orders_to_cancel = []
        for order_id, order in self.active_orders.items():
            if symbol is None or order.symbol == symbol:
                orders_to_cancel.append(order_id)
        
        for order_id in orders_to_cancel:
            success, _ = await self.cancel_order(order_id)
            if success:
                success_count += 1
            else:
                failed_count += 1
        
        logger.info(f"批量取消订单完成: 成功{success_count}, 失败{failed_count}")
        return success_count, failed_count
    
    def get_order_status(self, order_id: str) -> Optional[OrderData]:
        """
        获取订单状态
        
        Args:
            order_id: 订单ID
            
        Returns:
            订单对象或None
        """
        # 先查找活跃订单
        if order_id in self.active_orders:
            return self.active_orders[order_id]
        
        # 再查找历史订单
        for order in self.order_history:
            if order.id == order_id:
                return order
        
        return None
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[OrderData]:
        """
        获取活跃订单列表
        
        Args:
            symbol: 可选，只返回指定交易对的订单
            
        Returns:
            订单列表
        """
        orders = list(self.active_orders.values())
        if symbol:
            orders = [order for order in orders if order.symbol == symbol]
        return orders
    
    def get_order_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[OrderData]:
        """
        获取订单历史
        
        Args:
            symbol: 可选，只返回指定交易对的订单
            limit: 返回数量限制
            
        Returns:
            订单历史列表
        """
        history = self.order_history
        if symbol:
            history = [order for order in history if order.symbol == symbol]
        
        # 按时间倒序排列
        history.sort(key=lambda x: x.timestamp, reverse=True)
        return history[:limit]
    
    def get_statistics(self) -> Dict:
        """获取订单统计信息"""
        return {
            **self.stats,
            'active_orders': len(self.active_orders),
            'total_history': len(self.order_history)
        }
    
    async def update_order_status(self, order_id: str) -> bool:
        """
        更新订单状态（从交易所获取最新状态）
        
        Args:
            order_id: 订单ID
            
        Returns:
            更新成功标志
        """
        try:
            if order_id not in self.active_orders:
                return False
            
            order = self.active_orders[order_id]
            if not order.exchange_order_id:
                return False
            
            # 从交易所获取订单状态
            success, status_data = await self._fetch_order_status(order)
            
            if success and status_data:
                # 更新订单信息
                old_status = order.status
                order.status = OrderStatus(status_data.get('status', order.status.value))
                order.filled_amount = float(status_data.get('filled', 0))
                order.average_price = float(status_data.get('average', 0))
                order.updated_at = datetime.now()
                
                # 如果订单已完成，移到历史记录
                if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]:
                    self._move_to_history(order_id)
                
                # 记录状态变化
                if old_status != order.status:
                    logger.info(f"订单状态更新: {order_id} {old_status.value} -> {order.status.value}")
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新订单状态失败: {str(e)}")
            return False
    
    async def start_status_monitor(self):
        """启动订单状态监控"""
        logger.info("启动订单状态监控")
        while True:
            try:
                # 更新所有活跃订单状态
                for order_id in list(self.active_orders.keys()):
                    await self.update_order_status(order_id)
                
                # 等待5秒后再次检查
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"订单状态监控错误: {str(e)}")
                await asyncio.sleep(10)
    
    async def _submit_to_exchange(self, order: OrderData) -> Tuple[bool, Any]:
        """向交易所提交订单"""
        try:
            if not self.api or not self.api.is_connected:
                return False, "API未连接"
            
            # 构建订单参数
            order_params = {
                'symbol': order.symbol,
                'side': order.side.value,
                'type': order.type.value,
                'amount': order.amount
            }
            
            # 添加价格参数
            if order.type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                if order.price is None:
                    return False, "限价单必须指定价格"
                order_params['price'] = order.price
            
            if order.type in [OrderType.STOP_LOSS, OrderType.STOP_LIMIT]:
                if order.stop_price is None:
                    return False, "止损单必须指定止损价格"
                order_params['stopPrice'] = order.stop_price
            
            # 调用API提交订单
            result = await self.api.create_order(**order_params)
            return True, result
            
        except Exception as e:
            return False, str(e)
    
    async def _cancel_on_exchange(self, order: OrderData) -> Tuple[bool, Any]:
        """在交易所取消订单"""
        try:
            if not self.api or not self.api.is_connected:
                return False, "API未连接"
            
            result = await self.api.cancel_order(order.exchange_order_id, order.symbol)
            return True, result
            
        except Exception as e:
            return False, str(e)
    
    async def _fetch_order_status(self, order: OrderData) -> Tuple[bool, Any]:
        """从交易所获取订单状态"""
        try:
            if not self.api or not self.api.is_connected:
                return False, None
            
            result = await self.api.fetch_order(order.exchange_order_id, order.symbol)
            return True, result
            
        except Exception as e:
            logger.error(f"获取订单状态失败: {str(e)}")
            return False, None
    
    def _move_to_history(self, order_id: str):
        """将订单移动到历史记录"""
        if order_id in self.active_orders:
            order = self.active_orders.pop(order_id)
            order.updated_at = datetime.now()
            self.order_history.append(order)
            
            # 限制历史记录数量
            if len(self.order_history) > 1000:
                self.order_history = self.order_history[-1000:]
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'active_orders': {oid: asdict(order) for oid, order in self.active_orders.items()},
            'order_history': [asdict(order) for order in self.order_history[-100:]],  # 只返回最近100条
            'statistics': self.get_statistics()
        }


# 全局订单管理器实例
_order_manager_instance = None


def get_order_manager(api_connector=None, risk_manager=None):
    """获取订单管理器实例（单例模式）"""
    global _order_manager_instance
    if _order_manager_instance is None:
        if api_connector is None:
            raise ValueError("首次创建订单管理器需要提供API连接器")
        _order_manager_instance = OrderManager(api_connector, risk_manager)
    return _order_manager_instance


if __name__ == "__main__":
    # 测试代码
    print("订单管理系统模块加载完成")
    print("支持的订单类型:", [t.value for t in OrderType])
    print("支持的订单状态:", [s.value for s in OrderStatus])

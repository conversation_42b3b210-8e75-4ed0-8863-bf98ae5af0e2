#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动现货滚雪球策略
Launch Spot Snowball Strategy

专门启动现货滚雪球交易策略的脚本
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("❄️" + "=" * 58 + "❄️")
    print("❄️                                                        ❄️")
    print("❄️     现货滚雪球交易策略 - 复利增长系统                  ❄️")
    print("❄️     Spot Snowball Strategy - Compound Growth          ❄️")
    print("❄️                                                        ❄️")
    print("❄️     版本: 1.0.0                                       ❄️")
    print("❄️     策略: 现货滚雪球                                   ❄️")
    print("❄️                                                        ❄️")
    print("❄️" + "=" * 58 + "❄️")

def check_snowball_requirements():
    """检查滚雪球策略要求"""
    print("\n🔍 检查滚雪球策略要求...")
    
    issues = []
    
    # 检查核心文件
    required_files = [
        'core/spot_snowball_strategy.py',
        'core/spot_trading_engine.py',
        'core/spot_risk_manager.py',
        'core/spot_snowball_gui.py'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            issues.append(f"缺少核心文件: {file_path}")
    
    # 检查配置
    env_file = Path('.env')
    if not env_file.exists():
        issues.append("缺少配置文件 (.env)")
    else:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'YOUR_GATE_IO_API_KEY_HERE' in content or 'demo_gate_api_key' in content:
            issues.append("API密钥未配置")
    
    return issues

def show_strategy_info():
    """显示策略信息"""
    print("\n📊 现货滚雪球策略特点:")
    print("-" * 40)
    print("🎯 核心理念:")
    print("  • 只做现货，不用杠杆")
    print("  • 复利增长，滚雪球效应")
    print("  • 严格风控，保护本金")
    print("  • 高胜率，稳定盈利")
    
    print("\n📈 策略优势:")
    print("  • 正向学习: 确保每笔交易期望收益为正")
    print("  • 方向准确: 多时间框架趋势分析")
    print("  • 盈亏比优: 最小2:1盈亏比要求")
    print("  • 胜率高: 60%以上胜率目标")
    print("  • 信号强: 70%以上信号强度过滤")
    print("  • 执行快: 异步执行引擎")
    print("  • 止损严: 多层止损保护")
    print("  • 止盈活: 动态止盈策略")
    
    print("\n🛡️ 风险控制:")
    print("  • 每笔风险: 2%")
    print("  • 最大回撤: 10%")
    print("  • 日亏损限制: 3%")
    print("  • 单仓位限制: 15%")
    print("  • 总敞口限制: 80%")

def show_menu():
    """显示菜单"""
    print("\n📋 请选择操作:")
    print("1. 🚀 启动滚雪球GUI")
    print("2. 📊 查看策略分析报告")
    print("3. 🔧 配置API密钥")
    print("4. 🔍 系统健康检查")
    print("5. 📈 运行策略演示")
    print("6. ❌ 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-6): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6']:
                return choice
            else:
                print("❌ 无效选择，请输入1-6")
        except KeyboardInterrupt:
            return '6'

def launch_snowball_gui():
    """启动滚雪球GUI"""
    print("\n🚀 启动现货滚雪球交易界面...")
    
    try:
        gui_file = Path('core/spot_snowball_gui.py')
        if gui_file.exists():
            subprocess.Popen([sys.executable, str(gui_file)])
            print("✅ 滚雪球交易界面已启动")
            return True
        else:
            print("❌ GUI文件不存在")
            return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_analysis_report():
    """显示分析报告"""
    print("\n📊 打开策略分析报告...")
    
    report_file = Path('现货交易系统深度分析报告.md')
    if report_file.exists():
        try:
            os.startfile(str(report_file))
            print("✅ 分析报告已打开")
        except Exception:
            print("💡 请查看文件: 现货交易系统深度分析报告.md")
    else:
        print("❌ 分析报告文件不存在")

def configure_api():
    """配置API"""
    print("\n🔧 启动API配置工具...")
    
    try:
        subprocess.run([sys.executable, 'quick_config.py'])
        return True
    except Exception as e:
        print(f"❌ 配置工具启动失败: {e}")
        return False

def run_health_check():
    """运行健康检查"""
    print("\n🔍 运行系统健康检查...")
    
    try:
        result = subprocess.run([sys.executable, 'core/system_health_checker.py'], 
                              capture_output=True, text=True, timeout=60)
        
        # 显示关键结果
        if "系统状态: HEALTHY" in result.stdout:
            print("✅ 系统状态: 健康")
        elif "系统状态: WARNING" in result.stdout:
            print("⚠️ 系统状态: 警告")
        else:
            print("❌ 系统状态: 需要关注")
        
        # 显示通过率
        lines = result.stdout.split('\n')
        for line in lines:
            if "检查完成:" in line:
                print(f"📊 {line.strip()}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def run_strategy_demo():
    """运行策略演示"""
    print("\n📈 启动策略演示...")
    
    try:
        subprocess.run([sys.executable, 'trading_demo.py'])
        return True
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查要求
    issues = check_snowball_requirements()
    
    if issues:
        print("❌ 发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        print("\n💡 解决方案:")
        if any("API密钥" in issue for issue in issues):
            print("  1. 运行配置工具: python quick_config.py")
        if any("缺少核心文件" in issue for issue in issues):
            print("  2. 重新运行系统优化")
        
        fix_now = input("\n是否现在解决问题? (y/n): ").strip().lower()
        if fix_now in ['y', 'yes']:
            configure_api()
            # 重新检查
            issues = check_snowball_requirements()
            if issues:
                print("❌ 仍有问题需要手动解决")
                return False
        else:
            return False
    
    print("✅ 滚雪球策略系统检查通过")
    
    # 显示策略信息
    show_strategy_info()
    
    # 主菜单循环
    while True:
        try:
            choice = show_menu()
            
            if choice == '1':
                if launch_snowball_gui():
                    print("\n🎉 滚雪球交易界面已启动！")
                    print("\n📋 在GUI中的操作步骤:")
                    print("1. 点击'测试连接'验证API")
                    print("2. 配置滚雪球策略参数")
                    print("3. 查看信号分析")
                    print("4. 开始滚雪球交易")
                    
                    print("\n💡 滚雪球策略提醒:")
                    print("- 从小额资金开始测试")
                    print("- 关注复利增长效果")
                    print("- 严格遵守风险控制")
                    print("- 耐心等待高质量信号")
                    break
                    
            elif choice == '2':
                show_analysis_report()
                
            elif choice == '3':
                configure_api()
                
            elif choice == '4':
                run_health_check()
                
            elif choice == '5':
                run_strategy_demo()
                
            elif choice == '6':
                print("\n👋 再见！祝您滚雪球越滚越大！")
                break
            
            # 询问是否继续
            if choice != '6':
                continue_choice = input("\n是否继续使用菜单? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

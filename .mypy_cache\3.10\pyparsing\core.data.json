{".class": "MypyFile", "_fullname": "pyparsing.core", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<callable subtype of object>": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.<callable subtype of object>", "name": "object", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.<callable subtype of object>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.<callable subtype of object>", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.<callable subtype of object>.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "And": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseExpression"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.And", "name": "And", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.And", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.And", "pyparsing.core.ParseExpression", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_ErrorStop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Empty"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.And._ErrorStop", "name": "_ErrorStop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.And._ErrorStop", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.And._ErrorStop", "pyparsing.core.Empty", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And._ErrorStop.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pyparsing.core.And._ErrorStop", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ErrorStop", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And._ErrorStop._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.And._ErrorStop"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of _ErrorStop", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.And._ErrorStop.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.And._ErrorStop", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And.__iadd__", "name": "__iadd__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs_arg", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs_arg", "savelist"], "arg_types": ["pyparsing.core.And", {".class": "Instance", "args": [{".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of And", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkRecursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parseElementList"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And._checkRecursion", "name": "_checkRecursion", "type": null}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.And"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of And", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And.parseImpl", "name": "parseImpl", "type": null}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.And.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.And"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of And", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.And.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.And", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AtLineStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.AtLineStart", "name": "AtLineStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtLineStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.AtLineStart", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtLineStart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["pyparsing.core.AtLineStart", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AtLineStart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtLineStart.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.AtLineStart", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of AtLineStart", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.AtLineStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.AtLineStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AtStringStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.AtStringStart", "name": "AtStringStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtStringStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.AtStringStart", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtStringStart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["pyparsing.core.AtStringStart", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AtStringStart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.AtStringStart.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.AtStringStart", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of AtStringStart", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.AtStringStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.AtStringStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CaselessKeyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Keyword"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.CaselessKeyword", "name": "CaselessKeyword", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.CaselessKeyword", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.CaselessKeyword", "pyparsing.core.Keyword", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "match_string", "ident_chars", "matchString", "identChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CaselessKeyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "match_string", "ident_chars", "matchString", "identChars"], "arg_types": ["pyparsing.core.CaselessKeyword", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CaselessKeyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.CaselessKeyword.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.CaselessKeyword", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaselessLiteral": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.CaselessLiteral", "name": "CaselessLiteral", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.CaselessLiteral", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.CaselessLiteral", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CaselessLiteral.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "arg_types": ["pyparsing.core.CaselessLiteral", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CaselessLiteral", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CaselessLiteral.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.CaselessLiteral", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of CaselessLiteral", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "returnString": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CaselessLiteral.returnString", "name": "returnString", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.CaselessLiteral.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.CaselessLiteral", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Char": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Word"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Char", "name": "Char", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Char", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Char", "pyparsing.core.Word", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["self", "charset", "as_keyword", "exclude_chars", "asKeyword", "excludeChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Char.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["self", "charset", "as_keyword", "exclude_chars", "asKeyword", "excludeChars"], "arg_types": ["pyparsing.core.Char", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>r", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Char.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Char", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CharsNotIn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.CharsNotIn", "name": "CharsNotIn", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.CharsNotIn", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.CharsNotIn", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["self", "not_chars", "min", "max", "exact", "notChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CharsNotIn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["self", "not_chars", "min", "max", "exact", "notChars"], "arg_types": ["pyparsing.core.CharsNotIn", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CharsNotIn", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CharsNotIn._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.CharsNotIn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of CharsNotIn", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maxLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CharsNotIn.maxLen", "name": "maxLen", "type": "builtins.int"}}, "minLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CharsNotIn.minLen", "name": "minLen", "type": "builtins.int"}}, "notChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CharsNotIn.notChars", "name": "notChars", "type": "builtins.str"}}, "notCharsSet": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CharsNotIn.notCharsSet", "name": "notCharsSet", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CharsNotIn.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.CharsNotIn", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of CharsNotIn", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.CharsNotIn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.CharsNotIn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CloseMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.CloseMatch", "name": "CloseMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.CloseMatch", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.CloseMatch", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "match_string", "max_mismatches", "max<PERSON><PERSON><PERSON><PERSON>", "caseless"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CloseMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "match_string", "max_mismatches", "max<PERSON><PERSON><PERSON><PERSON>", "caseless"], "arg_types": ["pyparsing.core.CloseMatch", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CloseMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CloseMatch._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.CloseMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of CloseMatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caseless": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CloseMatch.caseless", "name": "caseless", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "match_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CloseMatch.match_string", "name": "match_string", "type": "builtins.str"}}, "maxMismatches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.CloseMatch.maxMismatches", "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.CloseMatch.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.CloseMatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of CloseMatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.CloseMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.CloseMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Combine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.TokenConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Combine", "name": "Combine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Combine", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Combine", "pyparsing.core.TokenConverter", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "expr", "join_string", "adjacent", "joinString"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Combine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "expr", "join_string", "adjacent", "joinString"], "arg_types": ["pyparsing.core.Combine", "pyparsing.core.ParserElement", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Combine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjacent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Combine.adjacent", "name": "adjacent", "type": "builtins.bool"}}, "ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Combine.ignore", "name": "ignore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.Combine", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore of Combine", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "joinString": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Combine.joinString", "name": "joinString", "type": "builtins.str"}}, "postParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instring", "loc", "tokenlist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Combine.postParse", "name": "postP<PERSON>e", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Combine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Combine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DebugExceptionAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.DebugExceptionAction", "line": 229, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.core.ParserElement", "builtins.Exception", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DebugStartAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.DebugStartAction", "line": 225, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DebugSuccessAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.DebugSuccessAction", "line": 226, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "pyparsing.core.ParserElement", "pyparsing.results.ParseResults", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DelimitedList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.DelimitedList", "name": "DelimitedList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.DelimitedList", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.DelimitedList", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 5], "arg_names": ["self", "expr", "delim", "combine", "min", "max", "allow_trailing_delim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.DelimitedList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 5], "arg_names": ["self", "expr", "delim", "combine", "min", "max", "allow_trailing_delim"], "arg_types": ["pyparsing.core.DelimitedList", {".class": "UnionType", "items": ["builtins.str", "pyparsing.core.ParserElement"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "pyparsing.core.ParserElement"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DelimitedList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.DelimitedList._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.DelimitedList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of DelimitedList", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_trailing_delim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.allow_trailing_delim", "name": "allow_trailing_delim", "type": "builtins.bool"}}, "combine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.combine", "name": "combine", "type": "builtins.bool"}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.content", "name": "content", "type": "pyparsing.core.ParserElement"}}, "delim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.delim", "name": "delim", "type": {".class": "UnionType", "items": ["builtins.str", "pyparsing.core.ParserElement"], "uses_pep604_syntax": false}}}, "max": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.max", "name": "max", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "min": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.min", "name": "min", "type": "builtins.int"}}, "raw_delim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.DelimitedList.raw_delim", "name": "raw_delim", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.DelimitedList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.DelimitedList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Diagnostics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Diagnostics", "name": "Diagnostics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "pyparsing.core.Diagnostics", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Diagnostics", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "enable_debug_on_named_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.enable_debug_on_named_expressions", "name": "enable_debug_on_named_expressions", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "warn_multiple_tokens_in_named_alternation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_multiple_tokens_in_named_alternation", "name": "warn_multiple_tokens_in_named_alternation", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "warn_name_set_on_empty_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_name_set_on_empty_Forward", "name": "warn_name_set_on_empty_Forward", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "warn_on_assignment_to_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_on_assignment_to_Forward", "name": "warn_on_assignment_to_Forward", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "warn_on_match_first_with_lshift_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_on_match_first_with_lshift_operator", "name": "warn_on_match_first_with_lshift_operator", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "warn_on_multiple_string_args_to_oneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_on_multiple_string_args_to_oneof", "name": "warn_on_multiple_string_args_to_oneof", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "warn_on_parse_using_empty_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_on_parse_using_empty_Forward", "name": "warn_on_parse_using_empty_Forward", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "warn_ungrouped_named_tokens_in_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Diagnostics.warn_ungrouped_named_tokens_in_collection", "name": "warn_ungrouped_named_tokens_in_collection", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Diagnostics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Diagnostics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.TokenConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Dict", "name": "Dict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Dict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Dict", "pyparsing.core.TokenConverter", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "asdict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Dict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "asdict"], "arg_types": ["pyparsing.core.Dict", "pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_asPythonDict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Dict._asPythonDict", "name": "_asPythonDict", "type": "builtins.bool"}}, "postParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instring", "loc", "tokenlist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Dict.postParse", "name": "postP<PERSON>e", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Dict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Dict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Each": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseExpression"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Each", "name": "Each", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Each", "pyparsing.core.ParseExpression", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__iand__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each.__iand__", "name": "__iand__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "arg_types": ["pyparsing.core.Each", {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Each", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Each"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Each", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initExprGroups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.initExprGroups", "name": "initExprGroups", "type": "builtins.bool"}}, "multioptionals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.multioptionals", "name": "multioptionals", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "multirequired": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.multirequired", "name": "multirequired", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "opt1map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.opt1map", "name": "opt1map", "type": {".class": "Instance", "args": ["builtins.int", "pyparsing.core.Opt"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "optionals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.optionals", "name": "optionals", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Each", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Each", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "required": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Each.required", "name": "required", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Each.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Each"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of Each", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Each.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Each", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Empty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Empty", "name": "Empty", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Empty", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Empty", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Empty.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "arg_types": ["pyparsing.core.Empty", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Empty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Empty._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Empty"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Empty", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Empty.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Empty", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Empty", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Empty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Empty", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "FollowedBy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.FollowedBy", "name": "FollowedBy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.FollowedBy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.FollowedBy", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.FollowedBy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["pyparsing.core.FollowedBy", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FollowedBy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.FollowedBy.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.FollowedBy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of FollowedBy", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.FollowedBy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.FollowedBy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Forward", "name": "Forward", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Forward", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.__del__", "name": "__del__", "type": null}}, "__ilshift__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.__ilshift__", "name": "__ilshift__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ilshift__ of Forward", "ret_type": "pyparsing.core.Forward", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.Forward", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Forward", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__lshift__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.__lshift__", "name": "__lshift__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lshift__ of Forward", "ret_type": "pyparsing.core.Forward", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Forward"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Forward", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caller_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Forward.caller_frame", "name": "caller_frame", "type": "traceback.FrameSummary"}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Forward"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignoreWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Forward.ignoreWhitespace", "name": "ignoreWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.Forward", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.ignore_whitespace", "name": "ignore_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.Forward", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_whitespace of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leaveWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Forward.leaveWhitespace", "name": "leaveWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.Forward", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.leave_whitespace", "name": "leave_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.Forward", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_whitespace of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lshift_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Forward.lshift_line", "name": "lshift_line", "type": {".class": "NoneType"}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Forward", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Forward"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of Forward", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Forward.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "arg_types": ["pyparsing.core.Forward", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of Forward", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Forward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Forward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "GoToColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.GoToColumn", "name": "GoToColumn", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.GoToColumn", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.GoToColumn", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "colno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.GoToColumn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "colno"], "arg_types": ["pyparsing.core.GoToColumn", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GoToColumn", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "col": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.GoToColumn.col", "name": "col", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.GoToColumn.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.GoToColumn", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of GoToColumn", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.GoToColumn.preParse", "name": "preParse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "arg_types": ["pyparsing.core.GoToColumn", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preParse of GoToColumn", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.GoToColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.GoToColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.TokenConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Group", "name": "Group", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Group", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Group", "pyparsing.core.TokenConverter", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "aslist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Group.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "aslist"], "arg_types": ["pyparsing.core.Group", "pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Group", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_asPythonList": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Group._asPythonList", "name": "_asPythonList", "type": "builtins.bool"}}, "postParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instring", "loc", "tokenlist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Group.postParse", "name": "postP<PERSON>e", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Group.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Group", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IndentedBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.IndentedBlock", "name": "IndentedBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.IndentedBlock", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_Indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Empty"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.IndentedBlock._Indent", "name": "_Indent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock._Indent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.IndentedBlock._Indent", "pyparsing.core.Empty", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock._Indent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref_col"], "arg_types": ["pyparsing.core.IndentedBlock._Indent", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Indent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.IndentedBlock._Indent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.IndentedBlock._Indent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_IndentGreater": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Empty"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.IndentedBlock._IndentGreater", "name": "_IndentGreater", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock._IndentGreater", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.IndentedBlock._IndentGreater", "pyparsing.core.Empty", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock._IndentGreater.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref_col"], "arg_types": ["pyparsing.core.IndentedBlock._IndentGreater", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _IndentGreater", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.IndentedBlock._IndentGreater.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.IndentedBlock._IndentGreater", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "expr", "recursive", "grouped"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "expr", "recursive", "grouped"], "arg_types": ["pyparsing.core.IndentedBlock", "pyparsing.core.ParserElement", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IndentedBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_grouped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.IndentedBlock._grouped", "name": "_grouped", "type": "builtins.bool"}}, "_recursive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.IndentedBlock._recursive", "name": "_recursive", "type": "builtins.bool"}}, "parent_anchor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.IndentedBlock.parent_anchor", "name": "parent_anchor", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.IndentedBlock.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.IndentedBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of IndentedBlock", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.IndentedBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.IndentedBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Keyword", "name": "Keyword", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Keyword", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Keyword", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_KEYWORD_CHARS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Keyword.DEFAULT_KEYWORD_CHARS", "name": "DEFAULT_KEYWORD_CHARS", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 5], "arg_names": ["self", "match_string", "ident_chars", "caseless", "matchString", "identChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5], "arg_names": ["self", "match_string", "ident_chars", "caseless", "matchString", "identChars"], "arg_types": ["pyparsing.core.Keyword", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Keyword._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Keyword"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Keyword", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caseless": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.caseless", "name": "caseless", "type": "builtins.bool"}}, "caselessmatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.caselessmatch", "name": "caselessmatch", "type": "builtins.str"}}, "firstMatchChar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.firstMatchChar", "name": "firstMatchChar", "type": "builtins.str"}}, "identChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.identChars", "name": "identChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.match", "name": "match", "type": "builtins.str"}}, "matchLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Keyword.matchLen", "name": "matchLen", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Keyword.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Keyword", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Keyword", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDefaultKeywordChars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Keyword.setDefaultKeywordChars", "name": "setDefaultKeywordChars", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "set_default_keyword_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.Keyword.set_default_keyword_chars", "name": "set_default_keyword_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_keyword_chars of Keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.Keyword.set_default_keyword_chars", "name": "set_default_keyword_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_keyword_chars of Keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Keyword.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Keyword", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.LineEnd", "name": "LineEnd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineEnd", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.LineEnd", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineEnd.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.LineEnd"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LineEnd", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineEnd.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.LineEnd", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of LineEnd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.LineEnd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.LineEnd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.LineStart", "name": "LineStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.LineStart", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineStart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.LineStart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LineStart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "orig_whiteChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.LineStart.orig_whiteChars", "name": "orig_whiteChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineStart.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.LineStart", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of LineStart", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.LineStart.preParse", "name": "preParse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "arg_types": ["pyparsing.core.LineStart", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preParse of LineStart", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skipper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.LineStart.skipper", "name": "skipper", "type": "pyparsing.core.ParserElement"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.LineStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.LineStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Literal", "name": "Literal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Literal", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__getnewargs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Literal.__getnewargs__", "name": "__getnewargs__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Literal.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "match_string", "matchString"], "arg_types": ["pyparsing.core.Literal", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Literal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["cls", "match_string", "matchString"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pyparsing.core.Literal.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["cls", "match_string", "matchString"], "arg_types": [{".class": "TypeType", "item": "pyparsing.core.Literal"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Literal", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Literal._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Literal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Literal", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "firstMatchChar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Literal.firstMatchChar", "name": "firstMatchChar", "type": "builtins.str"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Literal.match", "name": "match", "type": "builtins.str"}}, "matchLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Literal.matchLen", "name": "matchLen", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Literal.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Literal", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Literal", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Literal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Literal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Located": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Located", "name": "Located", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Located", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Located", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Located.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Located", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Located", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Located.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Located", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchFirst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseExpression"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.MatchFirst", "name": "MatchFirst", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.MatchFirst", "pyparsing.core.ParseExpression", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "arg_types": ["pyparsing.core.MatchFirst", {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchFirst", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ior__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst.__ior__", "name": "__ior__", "type": null}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.MatchFirst"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of MatchFirst", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core.MatchFirst", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of MatchFirst", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.MatchFirst", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of MatchFirst", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.MatchFirst.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.MatchFirst"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of MatchFirst", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.MatchFirst.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.MatchFirst", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NoMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.NoMatch", "name": "NoMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.NoMatch", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.NoMatch", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.NoMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.NoMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.NoMatch.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.NoMatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of NoMatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.NoMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.NoMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotAny": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.NotAny", "name": "NotAny", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.NotAny", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.NotAny", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.NotAny.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["pyparsing.core.NotAny", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotAny", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.NotAny._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.NotAny"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of NotAny", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.NotAny.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.NotAny", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of NotAny", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.NotAny.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.NotAny", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OneOrMore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core._MultipleMatch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.OneOrMore", "name": "OneOrMore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.OneOrMore", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.OneOrMore", "pyparsing.core._MultipleMatch", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.OneOrMore._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.OneOrMore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of OneOrMore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.OneOrMore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.OneOrMore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnlyOnce": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.OnlyOnce", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Opt", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Opt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Opt", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Opt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "default"], "arg_types": ["pyparsing.core.Opt", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Opt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__optionalNotMatched": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.Opt.__optionalNotMatched", "name": "__optionalNotMatched", "type": "pyparsing.core._NullToken"}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Opt._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Opt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Opt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defaultValue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Opt.defaultValue", "name": "defaultValue", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Opt.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Opt", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Opt", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Opt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Opt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.Optional", "line": 5389, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pyparsing.core.Opt"}}, "Or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseExpression"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Or", "name": "Or", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Or", "pyparsing.core.ParseExpression", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "arg_types": ["pyparsing.core.Or", {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Or", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ixor__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or.__ixor__", "name": "__ixor__", "type": null}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Or"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Or", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core.Or", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of Or", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Or", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Or", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Or.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Or"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of Or", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Or.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Or", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParseAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.ParseAction", "kind": "Gdef"}, "ParseBaseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseBaseException", "kind": "Gdef"}, "ParseCondition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.ParseCondition", "line": 218, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "ParseElementEnhance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParserElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParseElementEnhance", "name": "ParseElementEnhance", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "arg_types": ["pyparsing.core.ParseElementEnhance", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParseElementEnhance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkRecursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parseElementList"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance._checkRecursion", "name": "_checkRecursion", "type": null}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseElementEnhance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of ParseElementEnhance", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParseElementEnhance.expr", "name": "expr", "type": "pyparsing.core.ParserElement"}}, "ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.ignore", "name": "ignore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.ParseElementEnhance", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore of ParseElementEnhance", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignoreWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParseElementEnhance.ignoreWhitespace", "name": "ignoreWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseElementEnhance", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.ignore_whitespace", "name": "ignore_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseElementEnhance", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_whitespace of ParseElementEnhance", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leaveWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParseElementEnhance.leaveWhitespace", "name": "leaveWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseElementEnhance", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.leave_whitespace", "name": "leave_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseElementEnhance", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_whitespace of ParseElementEnhance", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.parseImpl", "name": "parseImpl", "type": null}}, "recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.recurse", "name": "recurse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseElementEnhance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recurse of ParseElementEnhance", "ret_type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseElementEnhance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of ParseElementEnhance", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseElementEnhance.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "arg_types": ["pyparsing.core.ParseElementEnhance", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of ParseElementEnhance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParseElementEnhance.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ParseElementEnhance", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseException", "kind": "Gdef"}, "ParseExpression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParserElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParseExpression", "name": "ParseExpression", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParseExpression", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exprs", "savelist"], "arg_types": ["pyparsing.core.ParseExpression", {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParseExpression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseExpression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of ParseExpression", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core.ParseExpression", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.ParseExpression", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseExpression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exprs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParseExpression.exprs", "name": "exprs", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.ignore", "name": "ignore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.ParseExpression", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignoreWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParseExpression.ignoreWhitespace", "name": "ignoreWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseExpression", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.ignore_whitespace", "name": "ignore_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseExpression", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_whitespace of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leaveWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParseExpression.leaveWhitespace", "name": "leaveWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseExpression", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.leave_whitespace", "name": "leave_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParseExpression", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_whitespace of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.recurse", "name": "recurse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseExpression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recurse of ParseExpression", "ret_type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParseExpression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of ParseExpression", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParseExpression.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "arg_types": ["pyparsing.core.ParseExpression", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of ParseExpression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParseExpression.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ParseExpression", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParseFailAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.ParseFailAction", "line": 224, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "pyparsing.core.ParserElement", "builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ParseFatalException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseFatalException", "kind": "Gdef"}, "ParseImplReturnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.ParseImplReturnType", "line": 215, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ParseResults": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results.ParseResults", "kind": "Gdef"}, "ParseSyntaxException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseSyntaxException", "kind": "Gdef"}, "ParserElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_generateDefaultName", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParserElement", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "pyparsing.core.ParserElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_WHITE_CHARS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.DEFAULT_WHITE_CHARS", "name": "DEFAULT_WHITE_CHARS", "type": "builtins.str"}}, "DebugActions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParserElement.DebugActions", "name": "DebugActions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "pyparsing.core.ParserElement.DebugActions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["debug_try", "debug_match", "debug_fail"]}}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParserElement.DebugActions", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "debug_try"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "debug_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "debug_fail"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "debug_try", "debug_match", "debug_fail"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pyparsing.core.ParserElement.DebugActions.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "debug_try", "debug_match", "debug_fail"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of DebugActions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.DebugActions._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of DebugActions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pyparsing.core.ParserElement.DebugActions._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DebugActions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DebugActions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "debug_try", "debug_match", "debug_fail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.DebugActions._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "debug_try", "debug_match", "debug_fail"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of DebugActions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.core.ParserElement.DebugActions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions._source", "name": "_source", "type": "builtins.str"}}, "debug_fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.debug_fail", "name": "debug_fail", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "debug_fail-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement.DebugActions.debug_fail", "kind": "<PERSON><PERSON><PERSON>"}, "debug_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.debug_match", "name": "debug_match", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "debug_match-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement.DebugActions.debug_match", "kind": "<PERSON><PERSON><PERSON>"}, "debug_try": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.core.ParserElement.DebugActions.debug_try", "name": "debug_try", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "debug_try-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement.DebugActions.debug_try", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.DebugActions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "pyparsing.core.ParserElement.DebugActions"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "NullCache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParserElement.NullCache", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.NullCache", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParserElement.NullCache", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.NullCache.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement.NullCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.NullCache.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["pyparsing.core.ParserElement.NullCache", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of <PERSON>ull<PERSON>ache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_in_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.NullCache.not_in_cache", "name": "not_in_cache", "type": "builtins.bool"}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.NullCache.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["pyparsing.core.ParserElement.NullCache", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of NullCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.NullCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ParserElement.NullCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CacheType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["clear", 2], ["get", 2], ["not_in_cache", 1], ["set", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ParserElement._CacheType", "name": "_CacheType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pyparsing.core.ParserElement._CacheType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ParserElement._CacheType", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pyparsing.core.ParserElement._CacheType.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement._CacheType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _CacheType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pyparsing.core.ParserElement._CacheType.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["pyparsing.core.ParserElement._CacheType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of _CacheType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_in_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "pyparsing.core.ParserElement._CacheType.not_in_cache", "name": "not_in_cache", "type": "builtins.bool"}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pyparsing.core.ParserElement._CacheType.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["pyparsing.core.ParserElement._CacheType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of _CacheType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement._CacheType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ParserElement._CacheType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__and__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__and__", "name": "__and__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__and__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__eq__", "name": "__eq__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__getitem__", "name": "__getitem__", "type": null}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__hash__", "name": "__hash__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "savelist"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__invert__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__invert__", "name": "__invert__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__invert__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.__iter__", "name": "__iter__", "type": {".class": "NoneType"}}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__radd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rand__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__rand__", "name": "__rand__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rand__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ror__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rsub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rxor__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__rxor__", "name": "__rxor__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rxor__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__xor__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.__xor__", "name": "__xor__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__xor__ of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkRecursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parseElementList"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement._checkRecursion", "name": "_checkRecursion", "type": null}}, "_defaultName": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement._defaultName", "name": "_defaultName", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "pyparsing.core.ParserElement._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_left_recursion_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement._left_recursion_enabled", "name": "_left_recursion_enabled", "type": "builtins.bool"}}, "_literalStringClass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement._literalStringClass", "name": "_literalStringClass", "type": "builtins.type"}}, "_may_return_empty": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement._may_return_empty", "name": "_may_return_empty", "type": "builtins.bool"}}, "_packratEnabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement._packratEnabled", "name": "_packratEnabled", "type": "builtins.bool"}}, "_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement._parse", "name": "_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions", "callPreParse"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "pyparsing.results.ParseResults"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parseCache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions", "callPreParse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement._parseCache", "name": "_parseCache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions", "callPreParse"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parseCache of ParserElement", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "pyparsing.results.ParseResults"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parseNoCache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions", "callPreParse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement._parseNoCache", "name": "_parseNoCache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions", "callPreParse"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parseNoCache of ParserElement", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "pyparsing.results.ParseResults"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_skipIgnorables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement._skipIgnorables", "name": "_skipIgnorables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_skipIgnorables of ParserElement", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addCondition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.addCondition", "name": "addCondition", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseCondition"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addParseAction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.addParseAction", "name": "addParseAction", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.add_condition", "name": "add_condition", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseCondition"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_condition of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_parse_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.add_parse_action", "name": "add_parse_action", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_parse_action of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callDuringTry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.callDuringTry", "name": "callDuringTry", "type": "builtins.bool"}}, "callPreparse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.callPreparse", "name": "callPreparse", "type": "builtins.bool"}}, "canParseNext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.canParseNext", "name": "canParseNext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_parse_next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.can_parse_next", "name": "can_parse_next", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_parse_next of ParserElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copyDefaultWhiteChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.copyDefaultWhiteChars", "name": "copyDefaultWhiteChars", "type": "builtins.bool"}}, "create_diagram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "output_html", "vertical", "show_results_names", "show_groups", "embed", "show_hidden", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.create_diagram", "name": "create_diagram", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "output_html", "vertical", "show_results_names", "show_groups", "embed", "show_hidden", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["typing.TextIO", "pathlib.Path", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_diagram of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "customName": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.customName", "name": "customName", "type": "builtins.str"}}, "debug": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.debug", "name": "debug", "type": "builtins.bool"}}, "debugActions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.debugActions", "name": "debugActions", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "pyparsing.core.ParserElement.DebugActions"}}}, "defaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.defaultName", "name": "defaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pyparsing.core.ParserElement.default_name", "name": "default_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_name of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.default_name", "name": "default_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_name of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "disableMemoization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.disableMemoization", "name": "disableMemoization", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "disable_memoization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.disable_memoization", "name": "disable_memoization", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_memoization of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.disable_memoization", "name": "disable_memoization", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_memoization of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enableLeftRecursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.enableLeftRecursion", "name": "enableLeftRecursion", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "enablePackrat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.enablePackrat", "name": "enablePackrat", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "enable_left_recursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.enable_left_recursion", "name": "enable_left_recursion", "type": {".class": "CallableType", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_left_recursion of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.enable_left_recursion", "name": "enable_left_recursion", "type": {".class": "CallableType", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_left_recursion of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_packrat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.enable_packrat", "name": "enable_packrat", "type": {".class": "CallableType", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_packrat of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.enable_packrat", "name": "enable_packrat", "type": {".class": "CallableType", "arg_kinds": [1, 5], "arg_names": ["cache_size_limit", "force"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_packrat of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "errmsg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.errmsg", "name": "errmsg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "failAction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.failAction", "name": "failAction", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseFailAction"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.ignore", "name": "ignore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["pyparsing.core.ParserElement", "pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignoreExprs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.ignoreExprs", "name": "ignoreExprs", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ignoreWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.ignoreWhitespace", "name": "ignoreWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.ignore_whitespace", "name": "ignore_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_whitespace of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inlineLiteralsUsing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.inlineLiteralsUsing", "name": "inlineLiteralsUsing", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["builtins.type"], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "inline_literals_using": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.inline_literals_using", "name": "inline_literals_using", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_literals_using of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.inline_literals_using", "name": "inline_literals_using", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_literals_using of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "keepTabs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.keepTabs", "name": "keepTabs", "type": "builtins.bool"}}, "leaveWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.leaveWhitespace", "name": "leaveWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.leave_whitespace", "name": "leave_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recursive"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_whitespace of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "test_string", "parse_all", "parseAll"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "test_string", "parse_all", "parseAll"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of ParserElement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mayIndexError": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.mayIndexError", "name": "mayIndexError", "type": "builtins.bool"}}, "mayReturnEmpty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "pyparsing.core.ParserElement.mayReturnEmpty", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "pyparsing.core.ParserElement.mayReturnEmpty", "name": "mayReturnEmpty", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.mayReturnEmpty", "name": "mayReturnEmpty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "may<PERSON><PERSON>urnEmpty of ParserElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.core.ParserElement.mayReturnEmpty", "name": "mayReturnEmpty", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "mayReturnEmpty", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mayReturnEmpty", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "modalResults": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.modalResults", "name": "modalResults", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "pyparsing.core.ParserElement.name", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "pyparsing.core.ParserElement.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.core.ParserElement.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_name"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "name", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "packrat_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.packrat_cache", "name": "packrat_cache", "type": "pyparsing.core.ParserElement._CacheType"}}, "packrat_cache_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.packrat_cache_lock", "name": "packrat_cache_lock", "type": "_thread.RLock"}}, "packrat_cache_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.packrat_cache_stats", "name": "packrat_cache_stats", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parseAction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.parseAction", "name": "parseAction", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parseFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.parseFile", "name": "parseFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "file_or_filename", "encoding", "parse_all", "parseAll"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "typing.TextIO"], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of ParserElement", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.parseString", "name": "parseString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "instring", "parse_all", "parseAll"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseWithTabs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.parseWithTabs", "name": "parseWithTabs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "file_or_filename", "encoding", "parse_all", "parseAll"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.parse_file", "name": "parse_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "file_or_filename", "encoding", "parse_all", "parseAll"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "typing.TextIO"], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_file of ParserElement", "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "instring", "parse_all", "parseAll"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.parse_string", "name": "parse_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "instring", "parse_all", "parseAll"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_string of ParserElement", "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_with_tabs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.parse_with_tabs", "name": "parse_with_tabs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_with_tabs of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "postParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instring", "loc", "tokenlist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.postParse", "name": "postP<PERSON>e", "type": null}}, "preParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.preParse", "name": "preParse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instring", "loc"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preParse of ParserElement", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.recurse", "name": "recurse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recurse of ParserElement", "ret_type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recursion_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.recursion_lock", "name": "recursion_lock", "type": "_thread.RLock"}}, "recursion_memos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.recursion_memos", "name": "recursion_memos", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "pyparsing.core.Forward", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["pyparsing.results.ParseResults", "builtins.Exception"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "resetCache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.resetCache", "name": "resetCache", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "reset_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.reset_cache", "name": "reset_cache", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_cache of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.reset_cache", "name": "reset_cache", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_cache of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resultsName": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.resultsName", "name": "resultsName", "type": "builtins.str"}}, "runTests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.runTests", "name": "runTests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "tests", "parse_all", "comment", "full_dump", "print_results", "failure_tests", "post_parse", "file", "with_line_numbers", "parseAll", "fullDump", "printResults", "failureTests", "postP<PERSON>e"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["pyparsing.results.ParseResults", "builtins.Exception"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "tests", "parse_all", "comment", "full_dump", "print_results", "failure_tests", "post_parse", "file", "with_line_numbers", "parseAll", "fullDump", "printResults", "failureTests", "postP<PERSON>e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.run_tests", "name": "run_tests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "tests", "parse_all", "comment", "full_dump", "print_results", "failure_tests", "post_parse", "file", "with_line_numbers", "parseAll", "fullDump", "printResults", "failureTests", "postP<PERSON>e"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_tests of ParserElement", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["pyparsing.results.ParseResults", "builtins.Exception"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "saveAsList": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.saveAsList", "name": "saveAsList", "type": "builtins.bool"}}, "scanString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.scanString", "name": "scanString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "overlap", "always_skip_whitespace", "debug", "max<PERSON><PERSON><PERSON>"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["pyparsing.results.ParseResults", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scan_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "overlap", "always_skip_whitespace", "debug", "max<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "pyparsing.core.ParserElement.scan_string", "name": "scan_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "overlap", "always_skip_whitespace", "debug", "max<PERSON><PERSON><PERSON>"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scan_string of ParserElement", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["pyparsing.results.ParseResults", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "searchString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.searchString", "name": "searchString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "debug", "max<PERSON><PERSON><PERSON>"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "debug", "max<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.search_string", "name": "search_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "instring", "max_matches", "debug", "max<PERSON><PERSON><PERSON>"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_string of ParserElement", "ret_type": "pyparsing.results.ParseResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setBreak": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setBreak", "name": "setBreak", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "break_flag"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDebug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setDebug", "name": "setDebug", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "flag", "recurse"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDebugActions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setDebugActions", "name": "setDebugActions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start_action", "success_action", "exception_action"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDefaultWhitespaceChars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setDefaultWhitespaceChars", "name": "setDefaultWhitespaceChars", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "setFailAction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setFailAction", "name": "setFailAction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseFailAction"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setName", "name": "setName", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setParseAction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setParseAction", "name": "setParseAction", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setResultsName", "name": "setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "name", "list_all_matches", "listAllMatches"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setWhitespaceChars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.setWhitespaceChars", "name": "setWhitespaceChars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "chars", "copy_defaults"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_break": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "break_flag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_break", "name": "set_break", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "break_flag"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_break of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "flag", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_debug", "name": "set_debug", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "flag", "recurse"], "arg_types": ["pyparsing.core.ParserElement", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_debug of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_debug_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start_action", "success_action", "exception_action"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_debug_actions", "name": "set_debug_actions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start_action", "success_action", "exception_action"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugStartAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugSuccessAction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.DebugExceptionAction"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_debug_actions of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_whitespace_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pyparsing.core.ParserElement.set_default_whitespace_chars", "name": "set_default_whitespace_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_whitespace_chars of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.set_default_whitespace_chars", "name": "set_default_whitespace_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chars"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_whitespace_chars of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_fail_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_fail_action", "name": "set_fail_action", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseFailAction"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fail_action of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_name", "name": "set_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_parse_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_parse_action", "name": "set_parse_action", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fns", "kwargs"], "arg_types": ["pyparsing.core.ParserElement", {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_parse_action of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_results_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "name", "list_all_matches", "listAllMatches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_results_name", "name": "set_results_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "name", "list_all_matches", "listAllMatches"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_results_name of Parser<PERSON><PERSON>", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_whitespace_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "chars", "copy_defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.set_whitespace_chars", "name": "set_whitespace_chars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "chars", "copy_defaults"], "arg_types": ["pyparsing.core.ParserElement", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_whitespace_chars of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_in_diagram": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.show_in_diagram", "name": "show_in_diagram", "type": "builtins.bool"}}, "skipWhitespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.skipWhitespace", "name": "skipWhitespace", "type": "builtins.bool"}}, "split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "instring", "maxsplit", "include_separators", "includeSeparators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "instring", "maxsplit", "include_separators", "includeSeparators"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split of ParserElement", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.streamline", "name": "streamline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "streamline of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streamlined": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.streamlined", "name": "streamlined", "type": "builtins.bool"}}, "suppress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.suppress", "name": "suppress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ParserElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "suppress of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "suppress_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "warning_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.suppress_warning", "name": "suppress_warning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "warning_type"], "arg_types": ["pyparsing.core.ParserElement", "pyparsing.core.Diagnostics"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "suppress_warning of ParserElement", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "suppress_warnings_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.suppress_warnings_", "name": "suppress_warnings_", "type": {".class": "Instance", "args": ["pyparsing.core.Diagnostics"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "transformString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.transformString", "name": "transformString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "instring", "debug"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "instring", "debug"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.transform_string", "name": "transform_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "instring", "debug"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_string of ParserElement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tryParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.tryParse", "name": "try<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "instring", "loc", "raise_fatal", "do_actions"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "instring", "loc", "raise_fatal", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.try_parse", "name": "try_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "instring", "loc", "raise_fatal", "do_actions"], "arg_types": ["pyparsing.core.ParserElement", "builtins.str", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_parse of ParserElement", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "using_each": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "seq", "class_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pyparsing.core.ParserElement.using_each", "name": "using_each", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.ParserElement.using_each", "name": "using_each", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "seq", "class_kwargs"], "arg_types": [{".class": "TypeType", "item": "pyparsing.core.ParserElement"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "using_each of ParserElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "validateTrace"], "arg_types": ["pyparsing.core.ParserElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of ParserElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verbose_stacktrace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.core.ParserElement.verbose_stacktrace", "name": "verbose_stacktrace", "type": "builtins.bool"}}, "visit_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ParserElement.visit_all", "name": "visit_all", "type": null}}, "whiteChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.ParserElement.whiteChars", "name": "whiteChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ParserElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ParserElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PositionToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.PositionToken", "name": "PositionToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.PositionToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.PositionToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.PositionToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PositionToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.PositionToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.PositionToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PostParseReturnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core.PostParseReturnType", "line": 216, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pyparsing.results.ParseResults", {".class": "Instance", "args": ["pyparsing.results.ParseResults"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}}}, "PrecededBy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.PrecededBy", "name": "PrecededBy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.PrecededBy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.PrecededBy", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "retreat"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.PrecededBy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "retreat"], "arg_types": ["pyparsing.core.PrecededBy", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PrecededBy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exact": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.PrecededBy.exact", "name": "exact", "type": "builtins.bool"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.PrecededBy.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.PrecededBy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of PrecededBy", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retreat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.PrecededBy.retreat", "name": "retreat", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.PrecededBy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.PrecededBy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuotedString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.QuotedString", "name": "QuotedString", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.QuotedString", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.QuotedString", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "quote_char", "esc_char", "esc_quote", "multiline", "unquote_results", "end_quote_char", "convert_whitespace_escapes", "quoteChar", "escChar", "escQuote", "unquoteResults", "endQuoteChar", "convertWhitespaceEscapes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.QuotedString.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "quote_char", "esc_char", "esc_quote", "multiline", "unquote_results", "end_quote_char", "convert_whitespace_escapes", "quoteChar", "escChar", "escQuote", "unquoteResults", "endQuoteChar", "convertWhitespaceEscapes"], "arg_types": ["pyparsing.core.QuotedString", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuotedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.QuotedString._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.QuotedString"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of QuotedString", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_whitespace_escapes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.convert_whitespace_escapes", "name": "convert_whitespace_escapes", "type": "builtins.bool"}}, "end_quote_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.end_quote_char", "name": "end_quote_char", "type": "builtins.str"}}, "end_quote_char_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.end_quote_char_len", "name": "end_quote_char_len", "type": "builtins.int"}}, "esc_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.esc_char", "name": "esc_char", "type": "builtins.str"}}, "esc_quote": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.esc_quote", "name": "esc_quote", "type": "builtins.str"}}, "first_quote_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.first_quote_char", "name": "first_quote_char", "type": "builtins.str"}}, "has_esc_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.has_esc_char", "name": "has_esc_char", "type": "builtins.bool"}}, "multiline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.multiline", "name": "multiline", "type": "builtins.bool"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.QuotedString.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.QuotedString", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of QuotedString", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.pattern", "name": "pattern", "type": "builtins.str"}}, "quote_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.quote_char", "name": "quote_char", "type": "builtins.str"}}, "quote_char_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.quote_char_len", "name": "quote_char_len", "type": "builtins.int"}}, "re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.re", "name": "re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "reString": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.reString", "name": "reString", "type": "builtins.str"}}, "re_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.re_flags", "name": "re_flags", "type": "re.RegexFlag"}}, "re_match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.re_match", "name": "re_match", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["string", "pos", "endpos"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["string", "pos", "endpos"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unquote_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core.QuotedString.unquote_results", "name": "unquote_results", "type": "builtins.bool"}}, "unquote_scan_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.QuotedString.unquote_scan_re", "name": "unquote_scan_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ws_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.QuotedString.ws_map", "name": "ws_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.QuotedString.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.QuotedString", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RLock": {".class": "SymbolTableNode", "cross_ref": "threading.RLock", "kind": "Gdef"}, "RecursiveGrammarException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.RecursiveGrammarException", "kind": "Gdef"}, "Regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Regex", "name": "Regex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Regex", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5], "arg_names": ["self", "pattern", "flags", "as_group_list", "as_match", "asGroupList", "asMatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5], "arg_names": ["self", "pattern", "flags", "as_group_list", "as_match", "asGroupList", "asMatch"], "arg_types": ["pyparsing.core.Regex", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["re.RegexFlag", "builtins.int"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Regex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Regex", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex._re", "name": "_re", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "asGroupList": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex.asGroupList", "name": "asGroupList", "type": "builtins.bool"}}, "asMatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex.asMatch", "name": "asMatch", "type": "builtins.bool"}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex.flags", "name": "flags", "type": {".class": "UnionType", "items": ["re.RegexFlag", "builtins.int"], "uses_pep604_syntax": false}}}, "mayReturnEmpty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "pyparsing.core.Regex.mayReturnEmpty", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "pyparsing.core.Regex.mayReturnEmpty", "name": "mayReturnEmpty", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.Regex.mayReturnEmpty", "name": "mayReturnEmpty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mayReturnEmpty of Regex", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.core.Regex.mayReturnEmpty", "name": "mayReturnEmpty", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "mayReturnEmpty", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mayReturnEmpty", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Regex", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Regex", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImplAsGroupList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex.parseImplAsGroupList", "name": "parseImplAsGroupList", "type": null}}, "parseImplAsMatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex.parseImplAsMatch", "name": "parseImplAsMatch", "type": null}}, "pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex.pattern", "name": "pattern", "type": "builtins.object"}}, "re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pyparsing.core.Regex.re", "name": "re", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re of Regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.Regex.re", "name": "re", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re of Regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reString": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Regex.reString", "name": "reString", "type": "builtins.object"}}, "re_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pyparsing.core.Regex.re_match", "name": "re_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re_match of Regex", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pyparsing.core.Regex.re_match", "name": "re_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Regex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "re_match of Regex", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Regex.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repl"], "arg_types": ["pyparsing.core.Regex", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub of Regex", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Regex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Regex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SkipTo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.SkipTo", "name": "SkipTo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.SkipTo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.SkipTo", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["self", "other", "include", "ignore", "fail_on", "failOn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.SkipTo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["self", "other", "include", "ignore", "fail_on", "failOn"], "arg_types": ["pyparsing.core.SkipTo", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SkipTo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_ignorer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.SkipTo._update_ignorer", "name": "_update_ignorer", "type": null}}, "failOn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.SkipTo.failOn", "name": "failOn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.SkipTo.ignore", "name": "ignore", "type": null}}, "ignoreExpr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.SkipTo.ignoreExpr", "name": "ignoreExpr", "type": {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ignorer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.SkipTo.ignorer", "name": "ignorer", "type": "pyparsing.core.ParserElement"}}, "includeMatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.SkipTo.includeMatch", "name": "includeMatch", "type": "builtins.bool"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.SkipTo.parseImpl", "name": "parseImpl", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.SkipTo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.SkipTo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.StringEnd", "name": "StringEnd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringEnd", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.StringEnd", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringEnd.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.StringEnd"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StringEnd", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringEnd.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.StringEnd", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of StringEnd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.StringEnd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.StringEnd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.StringStart", "name": "StringStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.StringStart", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringStart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.StringStart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StringStart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.StringStart.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.StringStart", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of StringStart", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.StringStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.StringStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Suppress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.TokenConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Suppress", "name": "Suppress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Suppress", "pyparsing.core.TokenConverter", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.Suppress", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Suppress", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "arg_types": ["pyparsing.core.Suppress", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Suppress", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core.Suppress", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Suppress", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "postParse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instring", "loc", "tokenlist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress.postParse", "name": "postP<PERSON>e", "type": null}}, "suppress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Suppress.suppress", "name": "suppress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Suppress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "suppress of Suppress", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Suppress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Suppress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Tag", "name": "Tag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Tag", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Tag", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag_name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Tag.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag_name", "value"], "arg_types": ["pyparsing.core.Tag", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Tag", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Tag._add_tag", "name": "_add_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["pyparsing.core.Tag", "pyparsing.results.ParseResults"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_tag of Tag", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Tag._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Tag", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Tag.tag_name", "name": "tag_name", "type": "builtins.str"}}, "tag_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Tag.tag_value", "name": "tag_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Tag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Tag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParserElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Token", "name": "Token", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Token", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Token.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Token"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Token", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Token._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Token"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Token", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Token.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Token", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.TokenConverter", "name": "TokenConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.TokenConverter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.TokenConverter", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.TokenConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "savelist"], "arg_types": ["pyparsing.core.TokenConverter", {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TokenConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.TokenConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.TokenConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "White": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.White", "name": "White", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.White", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.White", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "ws", "min", "max", "exact"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.White.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "ws", "min", "max", "exact"], "arg_types": ["pyparsing.core.White", "builtins.str", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of White", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.White._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.White"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDef<PERSON><PERSON>ame of White", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matchWhite": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.White.matchWhite", "name": "matchWhite", "type": "builtins.str"}}, "maxLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.White.maxLen", "name": "maxLen", "type": "builtins.int"}}, "minLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.White.minLen", "name": "minLen", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.White.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.White", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of White", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "whiteStrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.White.whiteStrs", "name": "whiteStrs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.White.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.White", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Word": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.Word", "name": "Word", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.Word", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.Word", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5], "arg_names": ["self", "init_chars", "body_chars", "min", "max", "exact", "as_keyword", "exclude_chars", "initChars", "bodyChars", "asKeyword", "excludeChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Word.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5], "arg_names": ["self", "init_chars", "body_chars", "min", "max", "exact", "as_keyword", "exclude_chars", "initChars", "bodyChars", "asKeyword", "excludeChars"], "arg_types": ["pyparsing.core.Word", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Word", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Word._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.Word"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of Word", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "asKeyword": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.asKeyword", "name": "asKeyword", "type": "builtins.bool"}}, "bodyChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.bodyChars", "name": "bodyChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "bodyCharsOrig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.bodyCharsOrig", "name": "bodyCharsOrig", "type": "builtins.str"}}, "initChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.initChars", "name": "initChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "initCharsOrig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.initCharsOrig", "name": "initCharsOrig", "type": "builtins.str"}}, "maxLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.maxLen", "name": "maxLen", "type": "builtins.int"}}, "maxSpecified": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.maxSpecified", "name": "maxSpecified", "type": "builtins.bool"}}, "minLen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.minLen", "name": "minLen", "type": "builtins.int"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Word.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Word", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of Word", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.Word.parseImpl_regex", "name": "parseImpl_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.Word", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl_regex of Word", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.re", "name": "re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "reString": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.reString", "name": "reString", "type": "builtins.str"}}, "re_match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.Word.re_match", "name": "re_match", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["string", "pos", "endpos"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["string", "pos", "endpos"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.Word.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.Word", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WordEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.WordEnd", "name": "WordEnd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordEnd", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.WordEnd", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "word_chars", "wordChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordEnd.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "word_chars", "wordChars"], "arg_types": ["pyparsing.core.WordEnd", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WordEnd", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordEnd.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.WordEnd", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of WordEnd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wordChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.WordEnd.wordChars", "name": "wordChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.WordEnd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.WordEnd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WordStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.PositionToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.WordStart", "name": "WordStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.WordStart", "pyparsing.core.PositionToken", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "word_chars", "wordChars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordStart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "word_chars", "wordChars"], "arg_types": ["pyparsing.core.WordStart", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WordStart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.WordStart.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.WordStart", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of WordStart", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wordChars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core.WordStart.wordChars", "name": "wordChars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.WordStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.WordStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroOrMore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core._MultipleMatch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.ZeroOrMore", "name": "ZeroOrMore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.ZeroOrMore", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.ZeroOrMore", "pyparsing.core._MultipleMatch", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "expr", "stop_on", "stopOn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ZeroOrMore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "expr", "stop_on", "stopOn"], "arg_types": ["pyparsing.core.ZeroOrMore", {".class": "UnionType", "items": ["builtins.str", "pyparsing.core.ParserElement"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ZeroOrMore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ZeroOrMore._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core.ZeroOrMore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of ZeroOrMore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.ZeroOrMore.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core.ZeroOrMore", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of ZeroOrMore", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.ZeroOrMore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.ZeroOrMore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FifoCache": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util._FifoCache", "kind": "Gdef"}, "_LRUMemo": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.LRUMemo", "kind": "Gdef"}, "_MAX_INT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._MAX_INT", "name": "_MAX_INT", "type": "builtins.int"}}, "_MultipleMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParseElementEnhance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core._MultipleMatch", "name": "_MultipleMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core._MultipleMatch", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core._MultipleMatch", "pyparsing.core.ParseElementEnhance", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "expr", "stop_on", "stopOn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._MultipleMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "expr", "stop_on", "stopOn"], "arg_types": ["pyparsing.core._MultipleMatch", {".class": "UnionType", "items": ["builtins.str", "pyparsing.core.ParserElement"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pyparsing.core.ParserElement", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MultipleMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setResultsName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._MultipleMatch._setResultsName", "name": "_setResultsName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "list_all_matches"], "arg_types": ["pyparsing.core._MultipleMatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setResultsName of _MultipleMatch", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_ender": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core._MultipleMatch.not_ender", "name": "not_ender", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._MultipleMatch.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core._MultipleMatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of _MultipleMatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stopOn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ender"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._MultipleMatch.stopOn", "name": "stopOn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ender"], "arg_types": ["pyparsing.core._MultipleMatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stopOn of _MultipleMatch", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core._MultipleMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core._MultipleMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NullToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core._NullToken", "name": "_NullToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core._NullToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core._NullToken", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._NullToken.__bool__", "name": "__bool__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._NullToken.__str__", "name": "__str__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core._NullToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core._NullToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ParseActionIndexError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core._ParseActionIndexError", "name": "_ParseActionIndexError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core._ParseActionIndexError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core._ParseActionIndexError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._ParseActionIndexError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "exc"], "arg_types": ["pyparsing.core._ParseActionIndexError", "builtins.str", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ParseActionIndexError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core._ParseActionIndexError.exc", "name": "exc", "type": "builtins.BaseException"}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pyparsing.core._ParseActionIndexError.msg", "name": "msg", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core._ParseActionIndexError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core._ParseActionIndexError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ParseResultsWithOffset": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results._ParseResultsWithOffset", "kind": "Gdef"}, "_PendingSkip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.ParserElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core._PendingSkip", "name": "_PendingSkip", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core._PendingSkip", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pyparsing.core._PendingSkip", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of _PendingSkip", "ret_type": "pyparsing.core.ParserElement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "must_skip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "must_skip"], "arg_types": ["pyparsing.core._PendingSkip", "pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _PendingSkip", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip.__repr__", "name": "__repr__", "type": null}}, "_generateDefaultName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip._generateDefaultName", "name": "_generateDefaultName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pyparsing.core._PendingSkip"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generateDefaultName of _PendingSkip", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anchor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core._PendingSkip.anchor", "name": "anchor", "type": "pyparsing.core.ParserElement"}}, "must_skip": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.core._PendingSkip.must_skip", "name": "must_skip", "type": "builtins.bool"}}, "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._PendingSkip.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["pyparsing.core._PendingSkip", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of _PendingSkip", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core._PendingSkip.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core._PendingSkip", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SingleCharLiteral": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.core.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core._SingleCharLiteral", "name": "_SingleCharLiteral", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core._SingleCharLiteral", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core._SingleCharLiteral", "pyparsing.core.Literal", "pyparsing.core.Token", "pyparsing.core.ParserElement", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "parseImpl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._SingleCharLiteral.parseImpl", "name": "parseImpl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "instring", "loc", "do_actions"], "arg_types": ["pyparsing.core._SingleCharLiteral", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseImpl of _SingleCharLiteral", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseImplReturnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core._SingleCharLiteral.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core._SingleCharLiteral", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UnboundedCache": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util._UnboundedCache", "kind": "Gdef"}, "_UnboundedMemo": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.UnboundedMemo", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__compat__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.util.__config_flags"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.__compat__", "name": "__compat__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.__compat__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.__compat__", "pyparsing.util.__config_flags", "builtins.object"], "names": {".class": "SymbolTable", "_all_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__compat__._all_names", "name": "_all_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_fixed_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__compat__._fixed_names", "name": "_fixed_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_type_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__compat__._type_desc", "name": "_type_desc", "type": "builtins.str"}}, "collect_all_And_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__compat__.collect_all_And_tokens", "name": "collect_all_And_tokens", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.__compat__.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.__compat__", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__config_flags": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.__config_flags", "kind": "Gdef"}, "__diag__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.util.__config_flags"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.core.__diag__", "name": "__diag__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.core.__diag__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.core", "mro": ["pyparsing.core.__diag__", "pyparsing.util.__config_flags", "builtins.object"], "names": {".class": "SymbolTable", "_all_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__._all_names", "name": "_all_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_debug_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__._debug_names", "name": "_debug_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_type_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__._type_desc", "name": "_type_desc", "type": "builtins.str"}}, "_warning_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__._warning_names", "name": "_warning_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "enable_all_warnings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pyparsing.core.__diag__.enable_all_warnings", "name": "enable_all_warnings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pyparsing.core.__diag__"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_all_warnings of __diag__", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pyparsing.core.__diag__.enable_all_warnings", "name": "enable_all_warnings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pyparsing.core.__diag__"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_all_warnings of __diag__", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_debug_on_named_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.enable_debug_on_named_expressions", "name": "enable_debug_on_named_expressions", "type": "builtins.bool"}}, "warn_multiple_tokens_in_named_alternation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_multiple_tokens_in_named_alternation", "name": "warn_multiple_tokens_in_named_alternation", "type": "builtins.bool"}}, "warn_name_set_on_empty_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_name_set_on_empty_Forward", "name": "warn_name_set_on_empty_Forward", "type": "builtins.bool"}}, "warn_on_assignment_to_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_on_assignment_to_Forward", "name": "warn_on_assignment_to_Forward", "type": "builtins.bool"}}, "warn_on_match_first_with_lshift_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_on_match_first_with_lshift_operator", "name": "warn_on_match_first_with_lshift_operator", "type": "builtins.bool"}}, "warn_on_multiple_string_args_to_oneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_on_multiple_string_args_to_oneof", "name": "warn_on_multiple_string_args_to_oneof", "type": "builtins.bool"}}, "warn_on_parse_using_empty_Forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_on_parse_using_empty_Forward", "name": "warn_on_parse_using_empty_Forward", "type": "builtins.bool"}}, "warn_ungrouped_named_tokens_in_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.__diag__.warn_ungrouped_named_tokens_in_collection", "name": "warn_ungrouped_named_tokens_in_collection", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.core.__diag__.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.core.__diag__", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_builtin_exprs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core._builtin_exprs", "name": "_builtin_exprs", "type": {".class": "Instance", "args": ["pyparsing.core.ParserElement"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_charRange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._char<PERSON><PERSON>e", "name": "_char<PERSON><PERSON>e", "type": "pyparsing.core.Group"}}, "_collapse_string_to_ranges": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util._collapse_string_to_ranges", "kind": "Gdef"}, "_default_exception_debug_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["instring", "loc", "expr", "exc", "cache_hit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._default_exception_debug_action", "name": "_default_exception_debug_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["instring", "loc", "expr", "exc", "cache_hit"], "arg_types": ["builtins.str", "builtins.int", "pyparsing.core.ParserElement", "builtins.Exception", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_exception_debug_action", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_start_debug_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["instring", "loc", "expr", "cache_hit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._default_start_debug_action", "name": "_default_start_debug_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["instring", "loc", "expr", "cache_hit"], "arg_types": ["builtins.str", "builtins.int", "pyparsing.core.ParserElement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_start_debug_action", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_success_debug_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["instring", "startloc", "endloc", "expr", "toks", "cache_hit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._default_success_debug_action", "name": "_default_success_debug_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["instring", "startloc", "endloc", "expr", "toks", "cache_hit"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "pyparsing.core.ParserElement", "pyparsing.results.ParseResults", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_success_debug_action", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_escape_regex_range_chars": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util._escape_regex_range_chars", "kind": "Gdef"}, "_escapedHexChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._escapedHexChar", "name": "_escapedHexChar", "type": "pyparsing.core.ParserElement"}}, "_escapedOctChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._escapedOctChar", "name": "_escapedOctChar", "type": "pyparsing.core.ParserElement"}}, "_escapedPunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._escapedPunc", "name": "_escapedPunc", "type": "pyparsing.core.ParserElement"}}, "_flatten": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util._flatten", "kind": "Gdef"}, "_generatorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.core._generatorType", "line": 214, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "types.GeneratorType"}}}, "_reBracketExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._reBracketExpr", "name": "_reBracketExpr", "type": "pyparsing.core.ParserElement"}}, "_should_enable_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cmd_line_warn_options", "warn_env_var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._should_enable_warnings", "name": "_should_enable_warnings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cmd_line_warn_options", "warn_env_var"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_enable_warnings", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_singleChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._singleChar", "name": "_singleChar", "type": "pyparsing.core.ParserElement"}}, "_single_arg_builtins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core._single_arg_builtins", "name": "_single_arg_builtins", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_trim_arity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["func", "max_limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core._trim_arity", "name": "_trim_arity", "type": null}}, "_trim_arity_call_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core._trim_arity_call_line", "name": "_trim_arity_call_line", "type": "traceback.StackSummary"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "alphanums": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.alphanums", "name": "alphanums", "type": "builtins.str"}}, "alphas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.alphas", "name": "alphas", "type": "builtins.str"}}, "alphas8bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.alphas8bit", "name": "alphas8bit", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "autoname_elements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.autoname_elements", "name": "autoname_elements", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoname_elements", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "col": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.col", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "conditionAsParseAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.conditionAsParseAction", "name": "conditionAsParseAction", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["fn", "message", "fatal"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseCondition"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "condition_as_parse_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["fn", "message", "fatal"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.condition_as_parse_action", "name": "condition_as_parse_action", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["fn", "message", "fatal"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.core.ParseCondition"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "condition_as_parse_action", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "dblQuotedString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.dblQuotedString", "name": "dblQuotedString", "type": "pyparsing.core.ParserElement"}}, "dbl_quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.dbl_quoted_string", "name": "dbl_quoted_string", "type": "pyparsing.core.ParserElement"}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "disable_diag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["diag_enum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.disable_diag", "name": "disable_diag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["diag_enum"], "arg_types": ["pyparsing.core.Diagnostics"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_diag", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "empty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.empty", "name": "empty", "type": "pyparsing.core.ParserElement"}}, "enable_all_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.enable_all_warnings", "name": "enable_all_warnings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_all_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_diag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["diag_enum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.enable_diag", "name": "enable_diag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["diag_enum"], "arg_types": ["pyparsing.core.Diagnostics"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_diag", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hexnums": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.hexnums", "name": "hexnums", "type": "builtins.str"}}, "identbodychars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.identbodychars", "name": "identbodychars", "type": "builtins.str"}}, "identchars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.identchars", "name": "identchars", "type": "builtins.str"}}, "itemgetter": {".class": "SymbolTableNode", "cross_ref": "operator.itemgetter", "kind": "Gdef"}, "line": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.line", "kind": "Gdef"}, "lineEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.lineEnd", "name": "lineEnd", "type": "pyparsing.core.ParserElement"}}, "lineStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.lineStart", "name": "lineStart", "type": "pyparsing.core.ParserElement"}}, "line_end": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.line_end", "name": "line_end", "type": "pyparsing.core.ParserElement"}}, "line_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.line_start", "name": "line_start", "type": "pyparsing.core.ParserElement"}}, "lineno": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.lineno", "kind": "Gdef"}, "matchOnlyAtCol": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.matchOnlyAtCol", "kind": "Gdef"}, "match_only_at_col": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.match_only_at_col", "kind": "Gdef"}, "nullDebugAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.nullDebugAction", "name": "nullDebugAction", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "null_debug_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.null_debug_action", "name": "null_debug_action", "type": null}}, "nums": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.nums", "name": "nums", "type": "builtins.str"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pa_call_line_synth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.pa_call_line_synth", "name": "pa_call_line_synth", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ppu": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.pyparsing_unicode", "kind": "Gdef"}, "printables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.printables", "name": "printables", "type": "builtins.str"}}, "punc8bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.punc8bit", "name": "punc8bit", "type": "builtins.str"}}, "pyparsing_unicode": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.pyparsing_unicode", "kind": "Gdef"}, "python_quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.python_quoted_string", "name": "python_quoted_string", "type": "pyparsing.core.ParserElement"}}, "quotedString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.quotedString", "name": "quotedString", "type": "pyparsing.core.ParserElement"}}, "quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.quoted_string", "name": "quoted_string", "type": "pyparsing.core.ParserElement"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "removeQuotes": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.removeQuotes", "kind": "Gdef"}, "remove_quotes": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.remove_quotes", "kind": "Gdef"}, "replaceWith": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.replaceWith", "kind": "Gdef"}, "replace_with": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.replace_with", "kind": "Gdef"}, "replaced_by_pep8": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.replaced_by_pep8", "kind": "Gdef"}, "sglQuotedString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.sglQuotedString", "name": "sglQuotedString", "type": "pyparsing.core.ParserElement"}}, "sgl_quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.sgl_quoted_string", "name": "sgl_quoted_string", "type": "pyparsing.core.ParserElement"}}, "srange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.srange", "name": "srange", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "srange", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pyparsing.core.str_type", "name": "str_type", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}, "stringEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.stringEnd", "name": "stringEnd", "type": "pyparsing.core.ParserElement"}}, "stringStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.stringStart", "name": "stringStart", "type": "pyparsing.core.ParserElement"}}, "string_end": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.string_end", "name": "string_end", "type": "pyparsing.core.ParserElement"}}, "string_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.string_start", "name": "string_start", "type": "pyparsing.core.ParserElement"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tokenMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.tokenMap", "name": "tokenMap", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["func", "args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "token_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["func", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.token_map", "name": "token_map", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["func", "args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "token_map", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traceParseAction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.traceParseAction", "name": "traceParseAction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "trace_parse_action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.core.trace_parse_action", "name": "trace_parse_action", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace_parse_action", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.actions.ParseAction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "unicodeString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.unicodeString", "name": "unicodeString", "type": "pyparsing.core.ParserElement"}}, "unicode_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.core.unicode_string", "name": "unicode_string", "type": "pyparsing.core.ParserElement"}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "withAttribute": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.withAttribute", "kind": "Gdef"}, "withClass": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.withClass", "kind": "Gdef"}, "with_attribute": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.with_attribute", "kind": "Gdef"}, "with_class": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.with_class", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\core.py"}
{"timestamp": "2025-05-26T08:18:53.688155", "overall_status": "CRITICAL", "checks": {"Python环境": {"status": "PASS", "message": "Python 3.10.11", "details": {"python_version": "3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]", "python_executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe", "platform": "win32", "architecture": "64-bit"}}, "依赖包": {"status": "WARN", "message": "缺少可选包: scikit-learn, websocket-client", "details": {"installed_packages": {"ccxt": "4.4.82", "pandas": "2.2.3", "numpy": "1.24.3", "requests": "2.32.3", "flask": "3.0.3", "scipy": "1.15.3", "statsmodels": "0.14.4", "cryptography": "44.0.3"}, "missing_required": [], "missing_optional": ["scikit-learn", "websocket-client"]}}, "文件结构": {"status": "FAIL", "message": "缺少必需文件: requirements.txt, core/config_manager.py, core/error_handler.py, core/logging_config.py", "details": {"file_sizes": {}, "missing_required": ["requirements.txt", "core/config_manager.py", "core/error_handler.py", "core/logging_config.py"], "missing_important": ["core/gate_io_client.py", "core/ultimate_trading_gui.py", "core/secure_key_management.py"]}}, "配置文件": {"status": "WARN", "message": "未找到配置文件，将使用默认配置", "details": {"valid_configs": [], "invalid_configs": [], "missing_configs": ["config.json", "core/gui_config.json"]}}, "数据库": {"status": "PASS", "message": "数据库状态正常 (0个)", "details": {"accessible_dbs": [], "inaccessible_dbs": [], "missing_dbs": ["core/monitoring.db", "core/secure_vault.db"]}}, "网络连接": {"status": "FAIL", "message": "无法访问任何测试URL", "details": {"accessible_urls": [], "failed_urls": ["https://api.gateio.ws (503)", "https://api.binance.com (451)", "https://www.google.com (429)"]}}, "系统资源": {"status": "PASS", "message": "系统资源充足 (CPU: 9.1%, 内存: 39.8%)", "details": {"cpu_percent": 9.1, "memory_total": 34226843648, "memory_available": 20618489856, "memory_percent": 39.8, "disk_total": 382584483840, "disk_free": 282558115840, "disk_percent": 26.14490974543334}}, "代码质量": {"status": "PASS", "message": "代码语法正常 (0个文件)", "details": {"total_files": 0, "syntax_errors": [], "import_errors": []}}, "安全配置": {"status": "PASS", "message": "安全配置正常", "details": {"security_issues": [], "checked_files": ["core/secure_vault.db", "core/encrypted_keys.json", "core/gui_config.json"]}}, "日志系统": {"status": "WARN", "message": "日志目录不存在", "details": {"log_dir_exists": false}}}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统集成模块
System Integration Module - 企业级现货交易系统优化集成
"""

import asyncio
import json
import logging
import os
import sys
import threading
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, ttk
from typing import Any, Dict, List, Optional

from config_manager import ConfigManager
# 导入优化模块
from strategy_optimizer import StrategyOptimizer
from testing_framework import ComponentTester, IntegrationTester, TestRunner
from ux_enhancement import (ContextualHelpSystem, SmartHintSystem,
                            UXEnhancementGUI)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OptimizedTradingSystem:
    """优化后的交易系统集成类"""

    def __init__(self, main_gui_instance=None):
        """
        初始化优化交易系统

        Args:
            main_gui_instance: 主GUI实例的引用
        """
        self.main_gui = main_gui_instance
        self.config_manager = ConfigManager()

        # 优化组件初始化
        self.strategy_optimizer = None
        self.smart_hints = SmartHintSystem()
        self.help_system = ContextualHelpSystem()
        self.test_runner = None

        # 系统状态
        self.optimization_active = False
        self.testing_enabled = False
        self.ux_enhancements_active = True

        # 初始化组件
        self._initialize_components()

    def _initialize_components(self):
        """初始化优化组件"""
        try:
            # 初始化策略优化器
            self.strategy_optimizer = StrategyOptimizer()
            logger.info("策略优化器初始化完成")

            # 初始化测试框架
            self.test_runner = TestRunner()
            logger.info("测试框架初始化完成")

            # 启动智能提示系统
            self.smart_hints.start_monitoring()
            logger.info("智能提示系统启动完成")

        except Exception as e:
            logger.error(f"组件初始化失败: {e}")

    def integrate_with_main_gui(self, main_gui):
        """与主GUI集成"""
        self.main_gui = main_gui

        # 添加优化菜单
        self._add_optimization_menu()

        # 集成智能提示
        self._integrate_smart_hints()

        # 添加优化状态指示器
        self._add_optimization_indicators()

    def _add_optimization_menu(self):
        """添加优化功能菜单"""
        if not hasattr(self.main_gui, "notebook"):
            return

        # 创建优化选项卡
        optimization_frame = ttk.Frame(self.main_gui.notebook)
        self.main_gui.notebook.add(optimization_frame, text="🚀 系统优化")

        # 策略优化区域
        strategy_group = ttk.LabelFrame(optimization_frame, text="策略优化")
        strategy_group.pack(fill="x", padx=10, pady=5)

        ttk.Button(
            strategy_group,
            text="📈 启动策略优化",
            command=self.start_strategy_optimization,
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            strategy_group,
            text="📊 查看优化结果",
            command=self.show_optimization_results,
        ).pack(side="left", padx=5, pady=5)

        # 系统测试区域
        testing_group = ttk.LabelFrame(optimization_frame, text="系统测试")
        testing_group.pack(fill="x", padx=10, pady=5)

        ttk.Button(
            testing_group,
            text="🧪 运行系统测试",
            command=self.run_system_tests,
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            testing_group, text="📋 测试报告", command=self.show_test_reports
        ).pack(side="left", padx=5, pady=5)

        # UX增强区域
        ux_group = ttk.LabelFrame(optimization_frame, text="用户体验")
        ux_group.pack(fill="x", padx=10, pady=5)

        ttk.Button(
            ux_group, text="💡 智能提示设置", command=self.open_hint_settings
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            ux_group, text="🎨 界面主题", command=self.open_theme_settings
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            ux_group, text="❓ 系统帮助", command=self.open_help_system
        ).pack(side="left", padx=5, pady=5)

    def _integrate_smart_hints(self):
        """集成智能提示系统"""
        if not self.main_gui:
            return

        # 创建提示显示区域
        hint_frame = tk.Frame(self.main_gui.root, bg="#2c3e50", height=30)
        hint_frame.pack(fill="x", side="bottom")
        hint_frame.pack_propagate(False)

        self.hint_label = tk.Label(
            hint_frame,
            text="💡 智能提示: 系统就绪，开始您的交易之旅！",
            bg="#2c3e50",
            fg="#ecf0f1",
            font=("Arial", 9),
        )
        self.hint_label.pack(side="left", padx=10, pady=5)

        # 启动提示更新线程
        threading.Thread(target=self._update_hints_loop, daemon=True).start()

    def _update_hints_loop(self):
        """更新提示循环"""
        while True:
            try:
                if hasattr(self, "hint_label"):
                    current_hint = self.smart_hints.get_current_hint()
                    if current_hint:
                        self.hint_label.config(
                            text=f"💡 {current_hint.title}: {current_hint.content}"
                        )
                threading.Event().wait(5)  # 每5秒更新一次
            except Exception as e:
                logger.error(f"提示更新失败: {e}")
                threading.Event().wait(10)

    def _add_optimization_indicators(self):
        """添加优化状态指示器"""
        if not self.main_gui or not hasattr(self.main_gui, "status_bar"):
            return

        # 在状态栏添加优化状态
        current_text = self.main_gui.status_bar.cget("text")
        new_text = f"{current_text} | 🚀 系统优化: 已激活"
        self.main_gui.status_bar.config(text=new_text)

    def start_strategy_optimization(self):
        """启动策略优化"""
        if not self.strategy_optimizer:
            messagebox.showerror("错误", "策略优化器未初始化")
            return

        def optimization_task():
            try:
                self.optimization_active = True

                # 显示优化进度窗口
                progress_window = tk.Toplevel(self.main_gui.root)
                progress_window.title("策略优化进行中")
                progress_window.geometry("400x200")
                progress_window.transient(self.main_gui.root)
                progress_window.grab_set()

                # 进度条
                progress_bar = ttk.Progressbar(
                    progress_window, mode="indeterminate"
                )
                progress_bar.pack(padx=20, pady=20, fill="x")
                progress_bar.start()

                # 状态标签
                status_label = tk.Label(
                    progress_window,
                    text="正在优化策略参数...",
                    font=("Arial", 10),
                )
                status_label.pack(pady=10)

                # 执行优化
                strategies_to_optimize = [
                    "CryptoBreakout",
                    "CryptoGrid",
                    "CryptoMomentum",
                ]

                for strategy in strategies_to_optimize:
                    status_label.config(text=f"正在优化 {strategy} 策略...")
                    progress_window.update()

                    # 运行优化
                    result = self.strategy_optimizer.optimize_strategy(
                        strategy_name=strategy,
                        optimization_method="grid_search",
                        iterations=50,
                    )

                    if result:
                        logger.info(
                            f"{strategy} 优化完成: {result.performance_score}"
                        )

                progress_bar.stop()
                progress_window.destroy()

                self.optimization_active = False
                messagebox.showinfo(
                    "优化完成", "策略优化已完成！请查看优化结果。"
                )

            except Exception as e:
                self.optimization_active = False
                logger.error(f"策略优化失败: {e}")
                messagebox.showerror(
                    "优化失败", f"策略优化过程中出现错误: {e}"
                )

        # 在后台线程中运行优化
        threading.Thread(target=optimization_task, daemon=True).start()

    def show_optimization_results(self):
        """显示优化结果"""
        if not self.strategy_optimizer:
            messagebox.showerror("错误", "策略优化器未初始化")
            return

        # 创建结果窗口
        results_window = tk.Toplevel(self.main_gui.root)
        results_window.title("策略优化结果")
        results_window.geometry("800x600")
        results_window.transient(self.main_gui.root)

        # 创建结果树形视图
        columns = (
            "策略",
            "优化得分",
            "总收益率",
            "夏普比率",
            "最大回撤",
            "胜率",
        )
        tree = ttk.Treeview(results_window, columns=columns, show="headings")

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # 获取优化结果
        try:
            optimization_history = (
                self.strategy_optimizer.get_optimization_history()
            )
            for result in optimization_history[-10:]:  # 显示最近10次优化结果
                tree.insert(
                    "",
                    "end",
                    values=(
                        "CryptoStrategy",
                        f"{result.performance_score:.4f}",
                        f"{result.total_return:.2%}",
                        f"{result.sharpe_ratio:.4f}",
                        f"{result.max_drawdown:.2%}",
                        f"{result.win_rate:.2%}",
                    ),
                )
        except Exception as e:
            logger.error(f"获取优化结果失败: {e}")

        tree.pack(fill="both", expand=True, padx=10, pady=10)

        # 关闭按钮
        ttk.Button(
            results_window, text="关闭", command=results_window.destroy
        ).pack(pady=10)

    def run_system_tests(self):
        """运行系统测试"""
        if not self.test_runner:
            messagebox.showerror("错误", "测试框架未初始化")
            return

        def testing_task():
            try:
                self.testing_enabled = True

                # 显示测试进度窗口
                test_window = tk.Toplevel(self.main_gui.root)
                test_window.title("系统测试进行中")
                test_window.geometry("500x300")
                test_window.transient(self.main_gui.root)
                test_window.grab_set()

                # 测试输出区域
                output_text = tk.Text(
                    test_window, wrap="word", font=("Consolas", 9)
                )
                scrollbar = ttk.Scrollbar(
                    test_window, orient="vertical", command=output_text.yview
                )
                output_text.configure(yscrollcommand=scrollbar.set)

                output_text.pack(
                    side="left",
                    fill="both",
                    expand=True,
                    padx=(10, 0),
                    pady=10,
                )
                scrollbar.pack(side="right", fill="y", padx=(0, 10), pady=10)

                def update_output(message):
                    output_text.insert(
                        "end",
                        f"{datetime.now().strftime('%H:%M:%S')} - {message}\n",
                    )
                    output_text.see("end")
                    test_window.update()

                update_output("开始系统测试...")

                # 运行组件测试
                component_tester = ComponentTester()
                test_suites = [
                    ("API连接测试", component_tester.test_api_connection),
                    ("数据流测试", component_tester.test_data_feed),
                    ("订单执行测试", component_tester.test_order_execution),
                    ("风险管理测试", component_tester.test_risk_management),
                    (
                        "性能监控测试",
                        component_tester.test_performance_monitoring,
                    ),
                ]

                results = []
                for test_name, test_func in test_suites:
                    update_output(f"正在运行: {test_name}")
                    try:
                        result = test_func()
                        status = (
                            "通过" if result.status == "passed" else "失败"
                        )
                        update_output(f"{test_name}: {status}")
                        results.append(result)
                    except Exception as e:
                        update_output(f"{test_name}: 错误 - {e}")

                # 运行集成测试
                integration_tester = IntegrationTester()
                update_output("正在运行集成测试...")
                try:
                    integration_result = (
                        integration_tester.test_trading_workflow()
                    )
                    status = (
                        "通过"
                        if integration_result.status == "passed"
                        else "失败"
                    )
                    update_output(f"集成测试: {status}")
                    results.append(integration_result)
                except Exception as e:
                    update_output(f"集成测试: 错误 - {e}")

                # 测试完成
                passed_count = sum(1 for r in results if r.status == "passed")
                total_count = len(results)

                update_output(
                    f"\n测试完成! {passed_count}/{total_count} 项测试通过"
                )

                self.testing_enabled = False

                # 添加关闭按钮
                ttk.Button(
                    test_window, text="关闭", command=test_window.destroy
                ).pack(pady=10)

                test_window.grab_release()

            except Exception as e:
                self.testing_enabled = False
                logger.error(f"系统测试失败: {e}")
                messagebox.showerror(
                    "测试失败", f"系统测试过程中出现错误: {e}"
                )

        # 在后台线程中运行测试
        threading.Thread(target=testing_task, daemon=True).start()

    def show_test_reports(self):
        """显示测试报告"""
        messagebox.showinfo("测试报告", "测试报告功能正在开发中...")

    def open_hint_settings(self):
        """打开智能提示设置"""
        settings_window = tk.Toplevel(self.main_gui.root)
        settings_window.title("智能提示设置")
        settings_window.geometry("400x300")
        settings_window.transient(self.main_gui.root)

        # 提示频率设置
        freq_frame = ttk.LabelFrame(settings_window, text="提示频率")
        freq_frame.pack(fill="x", padx=10, pady=5)

        self.hint_frequency = tk.StringVar(value="5秒")
        frequencies = ["1秒", "5秒", "10秒", "30秒", "1分钟"]

        for freq in frequencies:
            ttk.Radiobutton(
                freq_frame, text=freq, variable=self.hint_frequency, value=freq
            ).pack(anchor="w", padx=5, pady=2)

        # 提示类型设置
        type_frame = ttk.LabelFrame(settings_window, text="提示类型")
        type_frame.pack(fill="x", padx=10, pady=5)

        self.show_info_hints = tk.BooleanVar(value=True)
        self.show_warning_hints = tk.BooleanVar(value=True)
        self.show_tip_hints = tk.BooleanVar(value=True)

        ttk.Checkbutton(
            type_frame, text="信息提示", variable=self.show_info_hints
        ).pack(anchor="w", padx=5, pady=2)
        ttk.Checkbutton(
            type_frame, text="警告提示", variable=self.show_warning_hints
        ).pack(anchor="w", padx=5, pady=2)
        ttk.Checkbutton(
            type_frame, text="技巧提示", variable=self.show_tip_hints
        ).pack(anchor="w", padx=5, pady=2)

        # 按钮
        button_frame = tk.Frame(settings_window)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(
            button_frame, text="保存", command=settings_window.destroy
        ).pack(side="right", padx=5)
        ttk.Button(
            button_frame, text="取消", command=settings_window.destroy
        ).pack(side="right")

    def open_theme_settings(self):
        """打开主题设置"""
        # 创建UX增强GUI
        ux_gui = UXEnhancementGUI(parent=self.main_gui.root)

    def open_help_system(self):
        """打开帮助系统"""
        help_window = tk.Toplevel(self.main_gui.root)
        help_window.title("系统帮助")
        help_window.geometry("600x500")
        help_window.transient(self.main_gui.root)

        # 帮助内容
        help_text = tk.Text(help_window, wrap="word", font=("Arial", 10))
        scrollbar = ttk.Scrollbar(
            help_window, orient="vertical", command=help_text.yview
        )
        help_text.configure(yscrollcommand=scrollbar.set)

        help_content = """
🚀 企业级现货交易系统 - 用户帮助

📈 策略优化功能:
• 点击"启动策略优化"开始自动优化交易策略参数
• 优化完成后可查看详细的性能分析报告
• 支持多种优化算法：网格搜索、随机搜索、遗传算法

🧪 系统测试功能:
• 运行全面的系统组件测试
• 包括API连接、数据流、订单执行等关键功能测试
• 提供详细的测试报告和性能分析

💡 智能提示系统:
• 根据您的操作自动提供相关提示和建议
• 可自定义提示频率和类型
• 帮助您更好地使用系统功能

🎨 界面主题:
• 提供多种专业主题选择
• 支持深色/浅色模式切换
• 可调整界面布局和显示选项

📊 系统监控:
• 实时监控系统性能和交易状态
• 自动检测异常并提供解决方案
• 详细的日志记录和分析

如需更多帮助，请查看系统文档或联系技术支持。
        """

        help_text.insert("1.0", help_content)
        help_text.config(state="disabled")

        help_text.pack(
            side="left", fill="both", expand=True, padx=(10, 0), pady=10
        )
        scrollbar.pack(side="right", fill="y", padx=(0, 10), pady=10)

        # 关闭按钮
        ttk.Button(help_window, text="关闭", command=help_window.destroy).pack(
            pady=10
        )

    def get_system_status(self):
        """获取系统状态"""
        return {
            "optimization_active": self.optimization_active,
            "testing_enabled": self.testing_enabled,
            "ux_enhancements_active": self.ux_enhancements_active,
            "components_initialized": {
                "strategy_optimizer": self.strategy_optimizer is not None,
                "smart_hints": self.smart_hints is not None,
                "help_system": self.help_system is not None,
                "test_runner": self.test_runner is not None,
            },
        }

    def shutdown(self):
        """系统关闭清理"""
        try:
            if self.smart_hints:
                self.smart_hints.stop_monitoring()
            logger.info("系统优化模块已安全关闭")
        except Exception as e:
            logger.error(f"系统关闭时出现错误: {e}")


# 集成工厂类
class OptimizedSystemFactory:
    """优化系统工厂"""

    @staticmethod
    def create_integrated_system(main_gui_class):
        """
        创建集成了优化功能的交易系统

        Args:
            main_gui_class: 主GUI类

        Returns:
            集成了优化功能的GUI实例
        """
        # 创建主GUI实例
        main_gui = main_gui_class()

        # 创建优化系统实例
        optimization_system = OptimizedTradingSystem(main_gui)

        # 进行集成
        optimization_system.integrate_with_main_gui(main_gui)

        # 将优化系统引用添加到主GUI
        main_gui.optimization_system = optimization_system

        return main_gui

    @staticmethod
    def get_optimization_report():
        """获取优化报告"""
        return {
            "integration_date": datetime.now().isoformat(),
            "integrated_modules": [
                "Strategy Optimization Engine (Phase 5)",
                "User Experience Enhancement System (Phase 6)",
                "Automated Testing Framework (Phase 7)",
            ],
            "features_added": [
                "策略参数自动优化",
                "智能提示系统",
                "主题和界面定制",
                "全面的系统测试",
                "性能监控和分析",
                "用户引导和帮助",
            ],
            "status": "Integration Complete",
        }


if __name__ == "__main__":
    # 演示集成功能
    print("🚀 企业级现货交易系统 - 优化集成模块")
    print("系统集成完成，所有优化功能已就绪！")

    # 显示集成报告
    report = OptimizedSystemFactory.get_optimization_report()
    print(f"\n集成报告:")
    for key, value in report.items():
        print(f"  {key}: {value}")

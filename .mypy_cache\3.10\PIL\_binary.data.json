{".class": "MypyFile", "_fullname": "PIL._binary", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._binary.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "i16be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.i16be", "name": "i16be", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "i16be", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "i16le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.i16le", "name": "i16le", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "i16le", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "i32be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.i32be", "name": "i32be", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "i32be", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "i32le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.i32le", "name": "i32le", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "i32le", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "i8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.i8", "name": "i8", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "i8", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "o16be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.o16be", "name": "o16be", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "o16be", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "o16le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.o16le", "name": "o16le", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "o16le", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "o32be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.o32be", "name": "o32be", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "o32be", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "o32le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.o32le", "name": "o32le", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "o32le", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "o8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.o8", "name": "o8", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "o8", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pack": {".class": "SymbolTableNode", "cross_ref": "_struct.pack", "kind": "Gdef"}, "si16be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.si16be", "name": "si16be", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "si16be", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "si16le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.si16le", "name": "si16le", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "si16le", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "si32be": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.si32be", "name": "si32be", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "si32be", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "si32le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._binary.si32le", "name": "si32le", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "o"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "si32le", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unpack_from": {".class": "SymbolTableNode", "cross_ref": "_struct.unpack_from", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\PIL\\_binary.py"}
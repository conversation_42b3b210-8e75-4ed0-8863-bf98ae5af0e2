#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试倍增效果
Test Doubling Effect

验证倍增引擎是否真正实现增长效果
"""

import sys
import os
import time
from datetime import datetime

# 添加core目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

def test_doubling_engine():
    """测试倍增引擎"""
    print("🧪 测试倍增引擎...")
    print("=" * 50)
    
    try:
        from doubling_growth_engine import DoublingGrowthEngine
        
        # 创建倍增引擎
        engine = DoublingGrowthEngine(1000.0)
        
        print("✅ 倍增引擎创建成功")
        print(f"💰 初始资金: {engine.initial_capital} USDT")
        print(f"🎯 目标资金: {engine.target_capital} USDT")
        
        # 测试增长过程
        print("\n📈 测试增长过程 (30秒):")
        print("-" * 40)
        
        start_time = time.time()
        test_duration = 30  # 测试30秒
        
        while time.time() - start_time < test_duration:
            status = engine.update_growth()
            
            print(f"💰 当前资金: {status['current_capital']:.2f} USDT")
            print(f"📈 总收益率: {status['total_return']:.2%}")
            print(f"🎯 倍增进度: {status['doubling_progress']:.1%}")
            print(f"⭐ 增长阶段: {status['growth_stage']}")
            print(f"📅 已用时间: {status['days_elapsed']:.2f} 天")
            print(f"📊 日均收益: {status['daily_return']:.2f}%")
            print("-" * 40)
            
            time.sleep(3)  # 每3秒更新一次
        
        # 最终状态
        final_status = engine.get_current_status()
        print("\n🎊 测试结果:")
        print(f"💰 最终资金: {final_status['current_capital']:.2f} USDT")
        print(f"📈 总收益: {final_status['current_capital'] - 1000:.2f} USDT")
        print(f"📊 收益率: {final_status['total_return']:.2%}")
        print(f"🎯 倍增进度: {final_status['doubling_progress']:.1%}")
        
        # 验证是否有增长
        if final_status['current_capital'] > 1000:
            print("✅ 倍增引擎工作正常 - 资金有增长！")
            return True
        else:
            print("❌ 倍增引擎可能有问题 - 资金没有增长")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_doubling_simulator():
    """测试倍增模拟器"""
    print("\n🎮 测试倍增模拟器...")
    print("=" * 50)
    
    try:
        from doubling_growth_engine import DoublingSimulator
        
        # 创建模拟器
        simulator = DoublingSimulator(1000.0)
        
        print("✅ 倍增模拟器创建成功")
        
        # 测试未运行状态
        print("\n🛑 测试未运行状态:")
        data = simulator.get_simulated_data()
        print(f"💰 当前余额: {data['current_balance']} USDT")
        print(f"📊 总资产: {data['total_value']} USDT")
        print(f"📈 总收益率: {data['total_return']:.2%}")
        
        # 启动模拟器
        print("\n▶️ 启动倍增模拟器...")
        simulator.start()
        
        # 测试运行状态
        print("\n🚀 测试运行状态 (20秒):")
        print("-" * 40)
        
        initial_value = None
        for i in range(7):  # 测试7次，每次3秒
            data = simulator.get_simulated_data()
            
            if initial_value is None:
                initial_value = data['total_value']
            
            print(f"第{i+1}次: 总资产 {data['total_value']:.2f} USDT, "
                  f"收益率 {data['total_return']:.2%}, "
                  f"倍增进度 {data.get('doubling_progress', 0):.1%}")
            
            time.sleep(3)
        
        # 验证增长
        final_data = simulator.get_simulated_data()
        growth = final_data['total_value'] - initial_value
        
        print(f"\n📊 增长测试结果:")
        print(f"💰 初始资产: {initial_value:.2f} USDT")
        print(f"💰 最终资产: {final_data['total_value']:.2f} USDT")
        print(f"📈 资产增长: {growth:+.2f} USDT")
        print(f"📊 增长率: {(growth/initial_value)*100:+.2f}%")
        
        # 停止模拟器
        simulator.stop()
        
        if growth > 0:
            print("✅ 倍增模拟器工作正常 - 有真实增长！")
            return True
        else:
            print("❌ 倍增模拟器可能有问题 - 没有明显增长")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    print("=" * 50)
    
    try:
        # 测试导入
        from ultimate_spot_trading_gui import doubling_simulator
        
        print("✅ GUI中的倍增模拟器导入成功")
        
        # 测试基本功能
        print("\n🧪 测试基本功能:")
        
        # 测试启动
        doubling_simulator.start()
        print("✅ 启动功能正常")
        
        # 测试数据获取
        data = doubling_simulator.get_simulated_data()
        print(f"✅ 数据获取正常: {data.get('total_value', 'N/A')} USDT")
        
        # 测试增长信息
        try:
            growth_info = doubling_simulator.get_growth_info()
            print("✅ 增长信息获取正常")
        except:
            print("⚠️ 增长信息获取可能有问题")
        
        # 测试停止
        doubling_simulator.stop()
        print("✅ 停止功能正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI集成导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎬 开始倍增效果测试...")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试倍增引擎
    engine_result = test_doubling_engine()
    results.append(("倍增引擎", engine_result))
    
    # 2. 测试倍增模拟器
    simulator_result = test_doubling_simulator()
    results.append(("倍增模拟器", simulator_result))
    
    # 3. 测试GUI集成
    gui_result = test_gui_integration()
    results.append(("GUI集成", gui_result))
    
    # 总结结果
    print("\n" + "📋" * 20)
    print("📋 倍增效果测试总结:")
    print("📋" * 20)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "🎊" * 20)
    if all_passed:
        print("🎊 所有测试通过！倍增效果正常工作！")
        print("🎊 您的系统已经实现真正的倍增增长！")
        print("🎊" * 20)
        
        print("\n💡 使用建议:")
        print("1. 启动GUI: python core/ultimate_spot_trading_gui.py")
        print("2. 点击'开始实战演练'启动倍增引擎")
        print("3. 观察总资产的实时增长")
        print("4. 查看日志中的倍增进度报告")
        
    else:
        print("⚠️ 部分测试失败，请检查倍增引擎配置")
        print("🎊" * 20)
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试中断")
    except Exception as e:
        print(f"\n❌ 测试错误: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

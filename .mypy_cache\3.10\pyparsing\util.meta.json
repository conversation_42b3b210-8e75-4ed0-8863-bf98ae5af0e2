{"data_mtime": 1748490877, "dep_lines": [2, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["contextlib", "re", "functools", "inspect", "itertools", "types", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "ee1c9574315b49503bd23e7aa2ce1d360ea0689a", "id": "pyparsing.util", "ignore_all": true, "interface_hash": "447ecfb099ce923ab76d5550575d5ac1b9fbd62c", "mtime": 1748487517, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\util.py", "plugin_data": null, "size": 14344, "suppressed": [], "version_id": "1.15.0"}
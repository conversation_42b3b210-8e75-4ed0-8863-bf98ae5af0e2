{".class": "MypyFile", "_fullname": "core.constants.trading_constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "TRADING_CONSTANTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TRADING_CONSTANTS", "name": "TRADING_CONSTANTS", "type": "core.constants.trading_constants.TradingConstants"}}, "TradingConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "core.constants.trading_constants.TradingConstants", "name": "TradingConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "core.constants.trading_constants.TradingConstants", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "core.constants.trading_constants", "mro": ["core.constants.trading_constants.TradingConstants", "builtins.object"], "names": {".class": "SymbolTable", "ALL_MARKET_TRENDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_MARKET_TRENDS", "name": "ALL_MARKET_TRENDS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ALL_ORDER_STATUSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_ORDER_STATUSES", "name": "ALL_ORDER_STATUSES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ALL_ORDER_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_ORDER_TYPES", "name": "ALL_ORDER_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ALL_SUPPORTED_SYMBOLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_SUPPORTED_SYMBOLS", "name": "ALL_SUPPORTED_SYMBOLS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ALL_TIMEFRAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_TIMEFRAMES", "name": "ALL_TIMEFRAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ALL_VOLATILITY_LEVELS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ALL_VOLATILITY_LEVELS", "name": "ALL_VOLATILITY_LEVELS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "AMOUNT_PRECISION_ALTCOIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.AMOUNT_PRECISION_ALTCOIN", "name": "AMOUNT_PRECISION_ALTCOIN", "type": "builtins.int"}}, "AMOUNT_PRECISION_BTC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.AMOUNT_PRECISION_BTC", "name": "AMOUNT_PRECISION_BTC", "type": "builtins.int"}}, "AMOUNT_PRECISION_ETH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.AMOUNT_PRECISION_ETH", "name": "AMOUNT_PRECISION_ETH", "type": "builtins.int"}}, "AVERAGE_SHARPE_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.AVERAGE_SHARPE_RATIO", "name": "AVERAGE_SHARPE_RATIO", "type": "decimal.Decimal"}}, "AVERAGE_WIN_RATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.AVERAGE_WIN_RATE", "name": "AVERAGE_WIN_RATE", "type": "decimal.Decimal"}}, "DEFAULT_AMOUNT_PRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_AMOUNT_PRECISION", "name": "DEFAULT_AMOUNT_PRECISION", "type": "builtins.int"}}, "DEFAULT_CORRELATION_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_CORRELATION_LIMIT", "name": "DEFAULT_CORRELATION_LIMIT", "type": "decimal.Decimal"}}, "DEFAULT_DRAWDOWN_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_DRAWDOWN_LIMIT", "name": "DEFAULT_DRAWDOWN_LIMIT", "type": "decimal.Decimal"}}, "DEFAULT_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_INITIAL_CAPITAL", "name": "DEFAULT_INITIAL_CAPITAL", "type": "decimal.Decimal"}}, "DEFAULT_MAKER_FEE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_MAKER_FEE", "name": "DEFAULT_MAKER_FEE", "type": "decimal.Decimal"}}, "DEFAULT_MAX_DAILY_TRADES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_MAX_DAILY_TRADES", "name": "DEFAULT_MAX_DAILY_TRADES", "type": "builtins.int"}}, "DEFAULT_MAX_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_MAX_POSITIONS", "name": "DEFAULT_MAX_POSITIONS", "type": "builtins.int"}}, "DEFAULT_POSITION_SIZE_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_POSITION_SIZE_PCT", "name": "DEFAULT_POSITION_SIZE_PCT", "type": "decimal.Decimal"}}, "DEFAULT_PRICE_PRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_PRICE_PRECISION", "name": "DEFAULT_PRICE_PRECISION", "type": "builtins.int"}}, "DEFAULT_RISK_PER_TRADE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_RISK_PER_TRADE", "name": "DEFAULT_RISK_PER_TRADE", "type": "decimal.Decimal"}}, "DEFAULT_STOP_LOSS_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_STOP_LOSS_PCT", "name": "DEFAULT_STOP_LOSS_PCT", "type": "decimal.Decimal"}}, "DEFAULT_TAKER_FEE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_TAKER_FEE", "name": "DEFAULT_TAKER_FEE", "type": "decimal.Decimal"}}, "DEFAULT_TAKE_PROFIT_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_TAKE_PROFIT_PCT", "name": "DEFAULT_TAKE_PROFIT_PCT", "type": "decimal.Decimal"}}, "DEFAULT_TIMEFRAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_TIMEFRAME", "name": "DEFAULT_TIMEFRAME", "type": "builtins.str"}}, "DEFAULT_VAR_CONFIDENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFAULT_VAR_CONFIDENCE", "name": "DEFAULT_VAR_CONFIDENCE", "type": "decimal.Decimal"}}, "DEFI_SYMBOLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.DEFI_SYMBOLS", "name": "DEFI_SYMBOLS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "EXCELLENT_PROFIT_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.EXCELLENT_PROFIT_RATIO", "name": "EXCELLENT_PROFIT_RATIO", "type": "decimal.Decimal"}}, "EXCELLENT_SHARPE_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.EXCELLENT_SHARPE_RATIO", "name": "EXCELLENT_SHARPE_RATIO", "type": "decimal.Decimal"}}, "GOOD_PROFIT_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.GOOD_PROFIT_RATIO", "name": "GOOD_PROFIT_RATIO", "type": "decimal.Decimal"}}, "GOOD_SHARPE_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.GOOD_SHARPE_RATIO", "name": "GOOD_SHARPE_RATIO", "type": "decimal.Decimal"}}, "GOOD_WIN_RATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.GOOD_WIN_RATE", "name": "GOOD_WIN_RATE", "type": "decimal.Decimal"}}, "HIGH_VOLATILITY_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.HIGH_VOLATILITY_THRESHOLD", "name": "HIGH_VOLATILITY_THRESHOLD", "type": "decimal.Decimal"}}, "HIGH_WIN_RATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.HIGH_WIN_RATE", "name": "HIGH_WIN_RATE", "type": "decimal.Decimal"}}, "LOW_VOLATILITY_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.LOW_VOLATILITY_THRESHOLD", "name": "LOW_VOLATILITY_THRESHOLD", "type": "decimal.Decimal"}}, "MAJOR_SYMBOLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAJOR_SYMBOLS", "name": "MAJOR_SYMBOLS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MARKET_TREND_BEARISH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MARKET_TREND_BEARISH", "name": "MARKET_TREND_BEARISH", "type": "builtins.str"}}, "MARKET_TREND_BULLISH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MARKET_TREND_BULLISH", "name": "MARKET_TREND_BULLISH", "type": "builtins.str"}}, "MARKET_TREND_SIDEWAYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MARKET_TREND_SIDEWAYS", "name": "MARKET_TREND_SIDEWAYS", "type": "builtins.str"}}, "MARKET_TREND_UNKNOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MARKET_TREND_UNKNOWN", "name": "MARKET_TREND_UNKNOWN", "type": "builtins.str"}}, "MAX_CORRELATION_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_CORRELATION_LIMIT", "name": "MAX_CORRELATION_LIMIT", "type": "decimal.Decimal"}}, "MAX_DAILY_TRADES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_DAILY_TRADES", "name": "MAX_DAILY_TRADES", "type": "builtins.int"}}, "MAX_DRAWDOWN_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_DRAWDOWN_LIMIT", "name": "MAX_DRAWDOWN_LIMIT", "type": "decimal.Decimal"}}, "MAX_FEE_RATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_FEE_RATE", "name": "MAX_FEE_RATE", "type": "decimal.Decimal"}}, "MAX_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_INITIAL_CAPITAL", "name": "MAX_INITIAL_CAPITAL", "type": "decimal.Decimal"}}, "MAX_OPEN_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_OPEN_POSITIONS", "name": "MAX_OPEN_POSITIONS", "type": "builtins.int"}}, "MAX_POSITION_SIZE_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_POSITION_SIZE_PCT", "name": "MAX_POSITION_SIZE_PCT", "type": "decimal.Decimal"}}, "MAX_RISK_PER_TRADE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_RISK_PER_TRADE", "name": "MAX_RISK_PER_TRADE", "type": "decimal.Decimal"}}, "MAX_STOP_LOSS_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_STOP_LOSS_PCT", "name": "MAX_STOP_LOSS_PCT", "type": "decimal.Decimal"}}, "MAX_TAKE_PROFIT_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MAX_TAKE_PROFIT_PCT", "name": "MAX_TAKE_PROFIT_PCT", "type": "decimal.Decimal"}}, "MEDIUM_VOLATILITY_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MEDIUM_VOLATILITY_THRESHOLD", "name": "MEDIUM_VOLATILITY_THRESHOLD", "type": "decimal.Decimal"}}, "MINIMUM_PROFIT_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MINIMUM_PROFIT_RATIO", "name": "MINIMUM_PROFIT_RATIO", "type": "decimal.Decimal"}}, "MIN_CORRELATION_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_CORRELATION_LIMIT", "name": "MIN_CORRELATION_LIMIT", "type": "decimal.Decimal"}}, "MIN_DAILY_TRADES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_DAILY_TRADES", "name": "MIN_DAILY_TRADES", "type": "builtins.int"}}, "MIN_DRAWDOWN_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_DRAWDOWN_LIMIT", "name": "MIN_DRAWDOWN_LIMIT", "type": "decimal.Decimal"}}, "MIN_INITIAL_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_INITIAL_CAPITAL", "name": "MIN_INITIAL_CAPITAL", "type": "decimal.Decimal"}}, "MIN_MAX_POSITIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_MAX_POSITIONS", "name": "MIN_MAX_POSITIONS", "type": "builtins.int"}}, "MIN_POSITION_SIZE_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_POSITION_SIZE_PCT", "name": "MIN_POSITION_SIZE_PCT", "type": "decimal.Decimal"}}, "MIN_RISK_PER_TRADE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_RISK_PER_TRADE", "name": "MIN_RISK_PER_TRADE", "type": "decimal.Decimal"}}, "MIN_STOP_LOSS_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_STOP_LOSS_PCT", "name": "MIN_STOP_LOSS_PCT", "type": "decimal.Decimal"}}, "MIN_TAKE_PROFIT_PCT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_TAKE_PROFIT_PCT", "name": "MIN_TAKE_PROFIT_PCT", "type": "decimal.Decimal"}}, "MIN_TRADE_AMOUNT_BTC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_TRADE_AMOUNT_BTC", "name": "MIN_TRADE_AMOUNT_BTC", "type": "decimal.Decimal"}}, "MIN_TRADE_AMOUNT_ETH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_TRADE_AMOUNT_ETH", "name": "MIN_TRADE_AMOUNT_ETH", "type": "decimal.Decimal"}}, "MIN_TRADE_AMOUNT_USDT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.MIN_TRADE_AMOUNT_USDT", "name": "MIN_TRADE_AMOUNT_USDT", "type": "decimal.Decimal"}}, "ORDER_SIDES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_SIDES", "name": "ORDER_SIDES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ORDER_SIDE_BUY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_SIDE_BUY", "name": "ORDER_SIDE_BUY", "type": "builtins.str"}}, "ORDER_SIDE_SELL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_SIDE_SELL", "name": "ORDER_SIDE_SELL", "type": "builtins.str"}}, "ORDER_STATUS_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_CANCELLED", "name": "ORDER_STATUS_CANCELLED", "type": "builtins.str"}}, "ORDER_STATUS_EXPIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_EXPIRED", "name": "ORDER_STATUS_EXPIRED", "type": "builtins.str"}}, "ORDER_STATUS_FILLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_FILLED", "name": "ORDER_STATUS_FILLED", "type": "builtins.str"}}, "ORDER_STATUS_PARTIAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_PARTIAL", "name": "ORDER_STATUS_PARTIAL", "type": "builtins.str"}}, "ORDER_STATUS_PENDING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_PENDING", "name": "ORDER_STATUS_PENDING", "type": "builtins.str"}}, "ORDER_STATUS_REJECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_STATUS_REJECTED", "name": "ORDER_STATUS_REJECTED", "type": "builtins.str"}}, "ORDER_TYPE_ICEBERG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_ICEBERG", "name": "ORDER_TYPE_ICEBERG", "type": "builtins.str"}}, "ORDER_TYPE_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_LIMIT", "name": "ORDER_TYPE_LIMIT", "type": "builtins.str"}}, "ORDER_TYPE_MARKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_MARKET", "name": "ORDER_TYPE_MARKET", "type": "builtins.str"}}, "ORDER_TYPE_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_STOP", "name": "ORDER_TYPE_STOP", "type": "builtins.str"}}, "ORDER_TYPE_STOP_LIMIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_STOP_LIMIT", "name": "ORDER_TYPE_STOP_LIMIT", "type": "builtins.str"}}, "ORDER_TYPE_TWA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.ORDER_TYPE_TWA", "name": "ORDER_TYPE_TWA", "type": "builtins.str"}}, "PERCENTAGE_PRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.PERCENTAGE_PRECISION", "name": "PERCENTAGE_PRECISION", "type": "builtins.int"}}, "PRICE_PRECISION_ALTCOIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.PRICE_PRECISION_ALTCOIN", "name": "PRICE_PRECISION_ALTCOIN", "type": "builtins.int"}}, "PRICE_PRECISION_BTC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.PRICE_PRECISION_BTC", "name": "PRICE_PRECISION_BTC", "type": "builtins.int"}}, "PRICE_PRECISION_ETH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.PRICE_PRECISION_ETH", "name": "PRICE_PRECISION_ETH", "type": "builtins.int"}}, "PRICE_PRECISION_STABLECOIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.PRICE_PRECISION_STABLECOIN", "name": "PRICE_PRECISION_STABLECOIN", "type": "builtins.int"}}, "STABLECOIN_SYMBOLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.STABLECOIN_SYMBOLS", "name": "STABLECOIN_SYMBOLS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TIMEFRAME_15M": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_15M", "name": "TIMEFRAME_15M", "type": "builtins.str"}}, "TIMEFRAME_1D": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_1D", "name": "TIMEFRAME_1D", "type": "builtins.str"}}, "TIMEFRAME_1H": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_1H", "name": "TIMEFRAME_1H", "type": "builtins.str"}}, "TIMEFRAME_1M": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_1M", "name": "TIMEFRAME_1M", "type": "builtins.str"}}, "TIMEFRAME_1M_PERIOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_1M_PERIOD", "name": "TIMEFRAME_1M_PERIOD", "type": "builtins.str"}}, "TIMEFRAME_1W": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_1W", "name": "TIMEFRAME_1W", "type": "builtins.str"}}, "TIMEFRAME_30M": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_30M", "name": "TIMEFRAME_30M", "type": "builtins.str"}}, "TIMEFRAME_4H": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_4H", "name": "TIMEFRAME_4H", "type": "builtins.str"}}, "TIMEFRAME_5M": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.TIMEFRAME_5M", "name": "TIMEFRAME_5M", "type": "builtins.str"}}, "VAR_CONFIDENCE_95": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VAR_CONFIDENCE_95", "name": "VAR_CONFIDENCE_95", "type": "decimal.Decimal"}}, "VAR_CONFIDENCE_99": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VAR_CONFIDENCE_99", "name": "VAR_CONFIDENCE_99", "type": "decimal.Decimal"}}, "VIP_MAKER_FEE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VIP_MAKER_FEE", "name": "VIP_MAKER_FEE", "type": "decimal.Decimal"}}, "VIP_TAKER_FEE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VIP_TAKER_FEE", "name": "VIP_TAKER_FEE", "type": "decimal.Decimal"}}, "VOLATILITY_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VOLATILITY_HIGH", "name": "VOLATILITY_HIGH", "type": "builtins.str"}}, "VOLATILITY_LOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VOLATILITY_LOW", "name": "VOLATILITY_LOW", "type": "builtins.str"}}, "VOLATILITY_MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VOLATILITY_MEDIUM", "name": "VOLATILITY_MEDIUM", "type": "builtins.str"}}, "VOLATILITY_VERY_HIGH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VOLATILITY_VERY_HIGH", "name": "VOLATILITY_VERY_HIGH", "type": "builtins.str"}}, "VOLATILITY_VERY_LOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.TradingConstants.VOLATILITY_VERY_LOW", "name": "VOLATILITY_VERY_LOW", "type": "builtins.str"}}, "calculate_position_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "capital", "risk_pct", "entry_price", "stop_loss_price"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.calculate_position_size", "name": "calculate_position_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "capital", "risk_pct", "entry_price", "stop_loss_price"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "decimal.Decimal", "decimal.Decimal", "decimal.Decimal", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_position_size of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.calculate_position_size", "name": "calculate_position_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "capital", "risk_pct", "entry_price", "stop_loss_price"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "decimal.Decimal", "decimal.Decimal", "decimal.Decimal", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_position_size of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "calculate_risk_reward_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "entry_price", "stop_loss_price", "take_profit_price"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.calculate_risk_reward_ratio", "name": "calculate_risk_reward_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "entry_price", "stop_loss_price", "take_profit_price"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "decimal.Decimal", "decimal.Decimal", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_risk_reward_ratio of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.calculate_risk_reward_ratio", "name": "calculate_risk_reward_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "entry_price", "stop_loss_price", "take_profit_price"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "decimal.Decimal", "decimal.Decimal", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_risk_reward_ratio of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_amount_precision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.get_amount_precision", "name": "get_amount_precision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_amount_precision of TradingConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.get_amount_precision", "name": "get_amount_precision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_amount_precision of TradingConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_min_trade_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.get_min_trade_amount", "name": "get_min_trade_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_min_trade_amount of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.get_min_trade_amount", "name": "get_min_trade_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_min_trade_amount of TradingConstants", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_price_precision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.get_price_precision", "name": "get_price_precision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_price_precision of TradingConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.get_price_precision", "name": "get_price_precision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_price_precision of TradingConstants", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_major_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.is_major_symbol", "name": "is_major_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_major_symbol of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.is_major_symbol", "name": "is_major_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "symbol"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_major_symbol of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid_order_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "order_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.is_valid_order_type", "name": "is_valid_order_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "order_type"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_order_type of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.is_valid_order_type", "name": "is_valid_order_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "order_type"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_order_type of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid_timeframe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "timeframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "core.constants.trading_constants.TradingConstants.is_valid_timeframe", "name": "is_valid_timeframe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "timeframe"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_timeframe of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "core.constants.trading_constants.TradingConstants.is_valid_timeframe", "name": "is_valid_timeframe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "timeframe"], "arg_types": [{".class": "TypeType", "item": "core.constants.trading_constants.TradingConstants"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_timeframe of TradingConstants", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "core.constants.trading_constants.TradingConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "core.constants.trading_constants.TradingConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "core.constants.trading_constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "position_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "core.constants.trading_constants.position_size", "name": "position_size", "type": "decimal.Decimal"}}}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\trading_constants.py"}
{".class": "MypyFile", "_fullname": "matplotlib.mathtext", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FontProperties": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.FontProperties", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MathTextParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.mathtext.MathTextParser", "name": "MathTextParser", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext._ParseType", "id": 1, "name": "_ParseType", "namespace": "matplotlib.mathtext.MathTextParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.mathtext.MathTextParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.mathtext", "mro": ["matplotlib.mathtext.MathTextParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.mathtext.MathTextParser.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.mathtext.MathTextParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.mathtext.MathTextParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.mathtext.MathTextParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "agg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raster"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "macosx"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.mathtext.MathTextParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "agg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raster"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "macosx"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "agg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raster"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "macosx"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MathTextParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "s", "dpi", "prop", "antialiased"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.mathtext.MathTextParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "s", "dpi", "prop", "antialiased"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext._ParseType", "id": 1, "name": "_ParseType", "namespace": "matplotlib.mathtext.MathTextParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "variance": 0}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, "builtins.str", "builtins.float", {".class": "UnionType", "items": ["matplotlib.font_manager.FontProperties", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of MathTextParser", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext._ParseType", "id": 1, "name": "_ParseType", "namespace": "matplotlib.mathtext.MathTextParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext.MathTextParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext._ParseType", "id": 1, "name": "_ParseType", "namespace": "matplotlib.mathtext.MathTextParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "variance": 0}], "extra_attrs": null, "type_ref": "matplotlib.mathtext.MathTextParser"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ParseType"], "typeddict_type": null}}, "RasterParse": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.RasterParse", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VectorParse": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.VectorParse", "kind": "Gdef"}, "_ParseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.mathtext._ParseType", "name": "_ParseType", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.RasterParse"}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib._mathtext.VectorParse"}], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.mathtext.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_unicode_index": {".class": "SymbolTableNode", "cross_ref": "matplotlib._mathtext.get_unicode_index", "kind": "Gdef"}, "math_to_image": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["s", "filename_or_obj", "prop", "dpi", "format", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.mathtext.math_to_image", "name": "math_to_image", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["s", "filename_or_obj", "prop", "dpi", "format", "color"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["matplotlib.font_manager.FontProperties", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "math_to_image", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\mathtext.pyi"}
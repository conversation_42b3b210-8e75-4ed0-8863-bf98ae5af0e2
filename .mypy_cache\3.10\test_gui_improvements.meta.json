{"data_mtime": 1748499429, "dep_lines": [25, 62, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["core.constants.chinese_ui_constants", "core.professional_trading_gui", "sys", "os", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "core", "core.constants", "typing"], "hash": "67e7f6e8ede24ddf68e31a3cc3469d32a42eabcf", "id": "test_gui_improvements", "ignore_all": false, "interface_hash": "b12113967e7c4ebbd7df95bf3b21386090c31eec", "mtime": 1748499426, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\test_gui_improvements.py", "plugin_data": null, "size": 10253, "suppressed": [], "version_id": "1.15.0"}
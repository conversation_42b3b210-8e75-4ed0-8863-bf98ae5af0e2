{"data_mtime": 1748490921, "dep_lines": [38, 30, 30, 31, 33, 34, 35, 36, 37, 24, 25, 27, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.backends._backend_agg", "matplotlib._api", "matplotlib.cbook", "matplotlib.backend_bases", "matplotlib.font_manager", "matplotlib.ft2font", "matplotlib.mathtext", "matplotlib.path", "matplotlib.transforms", "contextlib", "math", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "matplotlib._mathtext", "matplotlib.artist", "matplotlib.figure", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "types", "typing", "typing_extensions"], "hash": "c3e13ff04f8d8ecdcf64fda84303a000cd3e62c9", "id": "matplotlib.backends.backend_agg", "ignore_all": true, "interface_hash": "750ad4f63356045608ceaa65224c4378fce37f00", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py", "plugin_data": null, "size": 19888, "suppressed": [], "version_id": "1.15.0"}
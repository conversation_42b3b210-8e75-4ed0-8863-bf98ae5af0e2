#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级密钥管理系统
Advanced Key Management System with Enhanced Security
"""

import os
import json
import base64
import hashlib
import secrets
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import sqlite3
from dataclasses import dataclass
import threading


class KeyStatus(Enum):
    """密钥状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    REVOKED = "revoked"
    PENDING = "pending"


class AccessLevel(Enum):
    """访问级别"""
    READ_ONLY = "read_only"
    TRADE_ONLY = "trade_only"
    FULL_ACCESS = "full_access"
    ADMIN = "admin"


@dataclass
class KeyMetadata:
    """密钥元数据"""
    key_id: str
    exchange_id: str
    exchange_name: str
    access_level: AccessLevel
    status: KeyStatus
    created_at: datetime
    expires_at: Optional[datetime]
    last_used: Optional[datetime]
    usage_count: int
    ip_whitelist: List[str]
    permissions: List[str]
    rotation_interval_days: int
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'key_id': self.key_id,
            'exchange_id': self.exchange_id,
            'exchange_name': self.exchange_name,
            'access_level': self.access_level.value,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'usage_count': self.usage_count,
            'ip_whitelist': self.ip_whitelist,
            'permissions': self.permissions,
            'rotation_interval_days': self.rotation_interval_days
        }


class SecureKeyVault:
    """安全密钥保险库"""
    
    def __init__(self, vault_path: str = "secure_vault.db", master_key: str = None):
        """初始化安全密钥保险库"""
        self.vault_path = vault_path
        self.master_key = master_key or self._generate_master_key()
        self.session_token = None
        self.session_expires = None
        
        # 访问控制
        self.failed_attempts = 0
        self.lockout_until = None
        self.max_failed_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        
        # 审计日志
        self.audit_log = []
        
        # 初始化数据库
        self._init_vault_database()
    
    def _generate_master_key(self) -> str:
        """生成主密钥"""
        return secrets.token_urlsafe(32)
    
    def _init_vault_database(self):
        """初始化保险库数据库"""
        try:
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            # 密钥表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS secure_keys (
                    key_id TEXT PRIMARY KEY,
                    exchange_id TEXT NOT NULL,
                    encrypted_api_key TEXT NOT NULL,
                    encrypted_api_secret TEXT NOT NULL,
                    encrypted_passphrase TEXT,
                    metadata TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # 审计日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    action TEXT NOT NULL,
                    key_id TEXT,
                    user_id TEXT,
                    ip_address TEXT,
                    success INTEGER NOT NULL,
                    details TEXT
                )
            ''')
            
            # 会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    expires_at TEXT NOT NULL,
                    ip_address TEXT,
                    active INTEGER DEFAULT 1
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print("✅ 安全保险库数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 保险库数据库初始化失败: {e}")
    
    def authenticate(self, password: str, user_id: str = "default") -> bool:
        """身份认证"""
        try:
            # 检查锁定状态
            if self.lockout_until and datetime.now() < self.lockout_until:
                remaining = (self.lockout_until - datetime.now()).total_seconds()
                print(f"❌ 账户已锁定，剩余时间: {remaining:.0f}秒")
                return False
            
            # 验证密码（简化版，实际应该使用更安全的方法）
            expected_hash = hashlib.sha256(password.encode()).hexdigest()
            stored_hash = hashlib.sha256(self.master_key.encode()).hexdigest()
            
            if expected_hash == stored_hash:
                # 认证成功
                self.failed_attempts = 0
                self.lockout_until = None
                
                # 创建会话
                self.session_token = secrets.token_urlsafe(32)
                self.session_expires = datetime.now() + timedelta(hours=8)
                
                # 记录审计日志
                self._log_audit("authentication", None, user_id, True, "成功登录")
                
                print("✅ 身份认证成功")
                return True
            else:
                # 认证失败
                self.failed_attempts += 1
                
                if self.failed_attempts >= self.max_failed_attempts:
                    self.lockout_until = datetime.now() + self.lockout_duration
                    print(f"❌ 认证失败次数过多，账户锁定 {self.lockout_duration.total_seconds()/60:.0f} 分钟")
                else:
                    remaining_attempts = self.max_failed_attempts - self.failed_attempts
                    print(f"❌ 认证失败，剩余尝试次数: {remaining_attempts}")
                
                # 记录审计日志
                self._log_audit("authentication", None, user_id, False, f"认证失败，尝试次数: {self.failed_attempts}")
                
                return False
                
        except Exception as e:
            print(f"❌ 身份认证异常: {e}")
            return False
    
    def _check_session(self) -> bool:
        """检查会话有效性"""
        if not self.session_token or not self.session_expires:
            return False
        
        if datetime.now() > self.session_expires:
            self.session_token = None
            self.session_expires = None
            print("⚠️ 会话已过期，请重新认证")
            return False
        
        return True
    
    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        try:
            # 简化的加密实现（实际应该使用更强的加密）
            key = hashlib.sha256(self.master_key.encode()).digest()
            
            # 使用简单的XOR加密（仅用于演示）
            encrypted = bytearray()
            for i, byte in enumerate(data.encode()):
                encrypted.append(byte ^ key[i % len(key)])
            
            return base64.b64encode(encrypted).decode()
            
        except Exception as e:
            print(f"❌ 数据加密失败: {e}")
            return ""
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            key = hashlib.sha256(self.master_key.encode()).digest()
            
            # 解密
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = bytearray()
            for i, byte in enumerate(encrypted_bytes):
                decrypted.append(byte ^ key[i % len(key)])
            
            return decrypted.decode()
            
        except Exception as e:
            print(f"❌ 数据解密失败: {e}")
            return ""
    
    def store_key(self, key_metadata: KeyMetadata, api_key: str, 
                  api_secret: str, passphrase: str = "") -> bool:
        """存储密钥"""
        try:
            if not self._check_session():
                print("❌ 会话无效，请先认证")
                return False
            
            # 加密敏感数据
            encrypted_api_key = self._encrypt_data(api_key)
            encrypted_api_secret = self._encrypt_data(api_secret)
            encrypted_passphrase = self._encrypt_data(passphrase) if passphrase else ""
            
            # 存储到数据库
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO secure_keys 
                (key_id, exchange_id, encrypted_api_key, encrypted_api_secret, 
                 encrypted_passphrase, metadata, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                key_metadata.key_id,
                key_metadata.exchange_id,
                encrypted_api_key,
                encrypted_api_secret,
                encrypted_passphrase,
                json.dumps(key_metadata.to_dict()),
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            # 记录审计日志
            self._log_audit("store_key", key_metadata.key_id, "system", True, 
                          f"存储密钥: {key_metadata.exchange_name}")
            
            print(f"✅ 密钥已安全存储: {key_metadata.exchange_name}")
            return True
            
        except Exception as e:
            print(f"❌ 存储密钥失败: {e}")
            self._log_audit("store_key", key_metadata.key_id, "system", False, str(e))
            return False
    
    def retrieve_key(self, key_id: str, user_id: str = "system") -> Optional[Dict]:
        """检索密钥"""
        try:
            if not self._check_session():
                print("❌ 会话无效，请先认证")
                return None
            
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT encrypted_api_key, encrypted_api_secret, encrypted_passphrase, metadata
                FROM secure_keys WHERE key_id = ?
            ''', (key_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                print(f"❌ 未找到密钥: {key_id}")
                return None
            
            # 解密数据
            api_key = self._decrypt_data(result[0])
            api_secret = self._decrypt_data(result[1])
            passphrase = self._decrypt_data(result[2]) if result[2] else ""
            metadata = json.loads(result[3])
            
            # 更新使用记录
            self._update_key_usage(key_id)
            
            # 记录审计日志
            self._log_audit("retrieve_key", key_id, user_id, True, "密钥检索成功")
            
            return {
                'api_key': api_key,
                'api_secret': api_secret,
                'passphrase': passphrase,
                'metadata': metadata
            }
            
        except Exception as e:
            print(f"❌ 检索密钥失败: {e}")
            self._log_audit("retrieve_key", key_id, user_id, False, str(e))
            return None
    
    def _update_key_usage(self, key_id: str):
        """更新密钥使用记录"""
        try:
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            # 获取当前元数据
            cursor.execute('SELECT metadata FROM secure_keys WHERE key_id = ?', (key_id,))
            result = cursor.fetchone()
            
            if result:
                metadata = json.loads(result[0])
                metadata['last_used'] = datetime.now().isoformat()
                metadata['usage_count'] = metadata.get('usage_count', 0) + 1
                
                # 更新数据库
                cursor.execute('''
                    UPDATE secure_keys SET metadata = ?, updated_at = ?
                    WHERE key_id = ?
                ''', (json.dumps(metadata), datetime.now().isoformat(), key_id))
                
                conn.commit()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 更新密钥使用记录失败: {e}")
    
    def rotate_key(self, key_id: str, new_api_key: str, new_api_secret: str, 
                   new_passphrase: str = "") -> bool:
        """轮换密钥"""
        try:
            if not self._check_session():
                print("❌ 会话无效，请先认证")
                return False
            
            # 获取现有密钥元数据
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT metadata FROM secure_keys WHERE key_id = ?', (key_id,))
            result = cursor.fetchone()
            
            if not result:
                print(f"❌ 未找到要轮换的密钥: {key_id}")
                return False
            
            metadata = json.loads(result[0])
            
            # 加密新密钥
            encrypted_api_key = self._encrypt_data(new_api_key)
            encrypted_api_secret = self._encrypt_data(new_api_secret)
            encrypted_passphrase = self._encrypt_data(new_passphrase) if new_passphrase else ""
            
            # 更新元数据
            metadata['last_rotated'] = datetime.now().isoformat()
            metadata['rotation_count'] = metadata.get('rotation_count', 0) + 1
            
            # 更新数据库
            cursor.execute('''
                UPDATE secure_keys 
                SET encrypted_api_key = ?, encrypted_api_secret = ?, 
                    encrypted_passphrase = ?, metadata = ?, updated_at = ?
                WHERE key_id = ?
            ''', (
                encrypted_api_key, encrypted_api_secret, encrypted_passphrase,
                json.dumps(metadata), datetime.now().isoformat(), key_id
            ))
            
            conn.commit()
            conn.close()
            
            # 记录审计日志
            self._log_audit("rotate_key", key_id, "system", True, "密钥轮换成功")
            
            print(f"✅ 密钥轮换成功: {key_id}")
            return True
            
        except Exception as e:
            print(f"❌ 密钥轮换失败: {e}")
            self._log_audit("rotate_key", key_id, "system", False, str(e))
            return False
    
    def revoke_key(self, key_id: str, reason: str = "") -> bool:
        """撤销密钥"""
        try:
            if not self._check_session():
                print("❌ 会话无效，请先认证")
                return False
            
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            # 获取现有元数据
            cursor.execute('SELECT metadata FROM secure_keys WHERE key_id = ?', (key_id,))
            result = cursor.fetchone()
            
            if not result:
                print(f"❌ 未找到要撤销的密钥: {key_id}")
                return False
            
            metadata = json.loads(result[0])
            metadata['status'] = KeyStatus.REVOKED.value
            metadata['revoked_at'] = datetime.now().isoformat()
            metadata['revocation_reason'] = reason
            
            # 更新数据库
            cursor.execute('''
                UPDATE secure_keys SET metadata = ?, updated_at = ?
                WHERE key_id = ?
            ''', (json.dumps(metadata), datetime.now().isoformat(), key_id))
            
            conn.commit()
            conn.close()
            
            # 记录审计日志
            self._log_audit("revoke_key", key_id, "system", True, f"密钥撤销: {reason}")
            
            print(f"✅ 密钥已撤销: {key_id}")
            return True
            
        except Exception as e:
            print(f"❌ 撤销密钥失败: {e}")
            self._log_audit("revoke_key", key_id, "system", False, str(e))
            return False
    
    def _log_audit(self, action: str, key_id: Optional[str], user_id: str, 
                   success: bool, details: str):
        """记录审计日志"""
        try:
            audit_entry = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'key_id': key_id,
                'user_id': user_id,
                'success': success,
                'details': details
            }
            
            self.audit_log.append(audit_entry)
            
            # 保存到数据库
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO audit_log 
                (timestamp, action, key_id, user_id, ip_address, success, details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                audit_entry['timestamp'],
                action,
                key_id,
                user_id,
                "127.0.0.1",  # 简化的IP地址
                int(success),
                details
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 记录审计日志失败: {e}")
    
    def get_audit_log(self, days: int = 30) -> List[Dict]:
        """获取审计日志"""
        try:
            if not self._check_session():
                print("❌ 会话无效，请先认证")
                return []
            
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            since_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor.execute('''
                SELECT timestamp, action, key_id, user_id, ip_address, success, details
                FROM audit_log WHERE timestamp > ?
                ORDER BY timestamp DESC
            ''', (since_date,))
            
            results = cursor.fetchall()
            conn.close()
            
            audit_logs = []
            for row in results:
                audit_logs.append({
                    'timestamp': row[0],
                    'action': row[1],
                    'key_id': row[2],
                    'user_id': row[3],
                    'ip_address': row[4],
                    'success': bool(row[5]),
                    'details': row[6]
                })
            
            return audit_logs
            
        except Exception as e:
            print(f"❌ 获取审计日志失败: {e}")
            return []
    
    def get_security_status(self) -> Dict:
        """获取安全状态"""
        try:
            conn = sqlite3.connect(self.vault_path)
            cursor = conn.cursor()
            
            # 统计密钥数量
            cursor.execute('SELECT COUNT(*) FROM secure_keys')
            total_keys = cursor.fetchone()[0]
            
            # 统计活跃密钥
            cursor.execute('''
                SELECT COUNT(*) FROM secure_keys 
                WHERE json_extract(metadata, '$.status') = 'active'
            ''')
            active_keys = cursor.fetchone()[0]
            
            # 统计最近使用的密钥
            recent_date = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute('''
                SELECT COUNT(*) FROM secure_keys 
                WHERE json_extract(metadata, '$.last_used') > ?
            ''', (recent_date,))
            recently_used = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_keys': total_keys,
                'active_keys': active_keys,
                'recently_used_keys': recently_used,
                'session_active': self._check_session(),
                'failed_attempts': self.failed_attempts,
                'locked_until': self.lockout_until.isoformat() if self.lockout_until else None,
                'vault_file_exists': os.path.exists(self.vault_path)
            }
            
        except Exception as e:
            print(f"❌ 获取安全状态失败: {e}")
            return {}


# 全局安全密钥保险库
secure_vault = None

def get_secure_vault(master_key: str = None) -> SecureKeyVault:
    """获取安全密钥保险库实例"""
    global secure_vault
    if secure_vault is None:
        secure_vault = SecureKeyVault(master_key=master_key)
    return secure_vault

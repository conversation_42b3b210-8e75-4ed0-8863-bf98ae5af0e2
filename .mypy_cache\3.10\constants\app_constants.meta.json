{"data_mtime": 1748495294, "dep_lines": [11, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30, 30], "dependencies": ["typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc"], "hash": "db9f3e640fdc8f39e5e7566afbdd83434cb77aef", "id": "constants.app_constants", "ignore_all": false, "interface_hash": "925479759a5a9eabae8d5cb365fc3f77b233d6c7", "mtime": 1748495293, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\constants\\app_constants.py", "plugin_data": null, "size": 7111, "suppressed": [], "version_id": "1.15.0"}
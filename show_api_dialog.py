#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接显示API登录对话框
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_api_login():
    """显示API登录对话框"""
    try:
        print("🚀 正在启动API登录对话框...")
        
        # 导入专业GUI
        from core.professional_trading_gui import ProfessionalTradingGUI
        
        # 创建GUI实例（但不显示主窗口）
        gui = ProfessionalTradingGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        print("✅ GUI实例创建成功")
        print("🔐 正在显示API登录对话框...")
        
        # 直接调用API登录对话框
        gui.show_api_login_dialog()
        
        print("✅ API登录对话框已显示")
        print("📱 请在弹出的窗口中配置您的Gate.io API凭证")
        
        # 运行GUI事件循环
        gui.root.mainloop()
        
    except Exception as e:
        print(f"❌ 显示API登录对话框失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔐 Gate.io API登录配置工具")
    print("=" * 50)
    show_api_login()

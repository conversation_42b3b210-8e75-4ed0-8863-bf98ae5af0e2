{"data_mtime": 1748490240, "dep_lines": [8, 11, 12, 13, 1, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "websockets.datastructures", "websockets.exceptions", "websockets.version", "__future__", "dataclasses", "os", "re", "sys", "warnings", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "99d3cf5d4308503035c9ff581d0fdb18e861974c", "id": "websockets.http11", "ignore_all": true, "interface_hash": "4cd8ea520c723f70fe1ec812f495401d5619a43e", "mtime": 1748487555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\http11.py", "plugin_data": null, "size": 15352, "suppressed": [], "version_id": "1.15.0"}
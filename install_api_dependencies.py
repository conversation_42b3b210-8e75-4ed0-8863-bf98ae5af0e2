#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安装API依赖
Install API Dependencies

安装GATE.IO API连接所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 GATE.IO API依赖安装程序")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "ccxt",           # 加密货币交易所API库
        "asyncio",        # 异步IO (Python 3.7+内置)
        "aiohttp",        # 异步HTTP客户端
        "websockets",     # WebSocket支持
        "cryptography",   # 加密库
        "requests"        # HTTP请求库
    ]
    
    print("📋 需要安装的依赖包:")
    for pkg in packages:
        print(f"  • {pkg}")
    
    print("\n🔍 检查已安装的包...")
    
    # 检查已安装的包
    installed = []
    to_install = []
    
    for package in packages:
        # 特殊处理asyncio (Python内置)
        if package == "asyncio":
            try:
                import asyncio
                installed.append(package)
                print(f"✅ {package} (内置)")
            except ImportError:
                to_install.append(package)
                print(f"❌ {package} (需要安装)")
        else:
            if check_package(package):
                installed.append(package)
                print(f"✅ {package} (已安装)")
            else:
                to_install.append(package)
                print(f"❌ {package} (需要安装)")
    
    if not to_install:
        print("\n🎉 所有依赖包都已安装！")
        return True
    
    print(f"\n📦 需要安装 {len(to_install)} 个包:")
    for pkg in to_install:
        print(f"  • {pkg}")
    
    # 询问用户是否继续
    response = input("\n是否继续安装? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("❌ 安装已取消")
        return False
    
    print("\n🚀 开始安装依赖包...")
    
    # 安装包
    success_count = 0
    for package in to_install:
        if package == "asyncio":
            # asyncio是内置的，跳过
            continue
            
        if install_package(package):
            success_count += 1
        else:
            print(f"⚠️ {package} 安装失败，可能需要手动安装")
    
    print("\n" + "=" * 50)
    print("📊 安装结果:")
    print(f"✅ 成功安装: {success_count} 个包")
    print(f"❌ 安装失败: {len(to_install) - success_count} 个包")
    
    if success_count == len([p for p in to_install if p != "asyncio"]):
        print("\n🎉 所有依赖包安装完成！")
        print("\n💡 现在您可以:")
        print("  1. 启动GUI: python core/ultimate_spot_trading_gui.py")
        print("  2. 点击'连接GATE交易所'")
        print("  3. 输入您的API凭证")
        print("  4. 开始使用真实数据进行实战演练")
        
        print("\n🔑 获取API Key:")
        print("  1. 访问 https://www.gate.io/myaccount/apiv4keys")
        print("  2. 创建新的API Key")
        print("  3. 权限设置: 只勾选'现货交易'和'查看'")
        print("  4. 建议使用测试环境进行模拟")
        
        return True
    else:
        print("\n⚠️ 部分依赖包安装失败")
        print("请手动安装失败的包:")
        for package in to_install:
            if package != "asyncio":
                print(f"  pip install {package}")
        return False

def create_requirements_file():
    """创建requirements.txt文件"""
    requirements = """# GATE.IO API 依赖包
ccxt>=4.0.0
aiohttp>=3.8.0
websockets>=11.0.0
cryptography>=3.4.0
requests>=2.28.0

# GUI 依赖 (通常已内置)
tkinter

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 可选: 更好的加密支持
pycryptodome>=3.15.0
"""
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements)
        print("📝 已创建 requirements.txt 文件")
        print("💡 您也可以使用: pip install -r requirements.txt")
        return True
    except Exception as e:
        print(f"❌ 创建requirements.txt失败: {e}")
        return False

if __name__ == "__main__":
    try:
        # 创建requirements文件
        create_requirements_file()
        
        print()
        
        # 安装依赖
        success = main()
        
        if success:
            print("\n🎯 安装完成！现在可以使用真实API功能了！")
        else:
            print("\n⚠️ 安装未完全成功，请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n\n👋 安装中断")
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
    
    input("\n按回车键退出...")
    sys.exit(0)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略优化引擎
Strategy Optimization Engine - 企业级现货交易系统优化模块 Phase 5
"""

import asyncio
import json
import logging
import math
import statistics
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class StrategyParameter:
    """策略参数"""

    name: str
    current_value: float
    min_value: float
    max_value: float
    step: float
    description: str


@dataclass
class OptimizationResult:
    """优化结果"""

    parameters: Dict[str, float]
    performance_score: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    trade_count: int
    optimization_timestamp: datetime


@dataclass
class BacktestResult:
    """回测结果"""

    strategy_name: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    trade_count: int
    avg_trade_duration: float
    trades: List[Dict]


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.benchmark_returns = []

    def calculate_metrics(
        self, returns: List[float], trades: List[Dict]
    ) -> Dict[str, float]:
        """计算性能指标"""
        if not returns:
            return self._empty_metrics()

        returns_array = np.array(returns)

        # 基础指标
        total_return = np.prod(1 + returns_array) - 1
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1
        volatility = np.std(returns_array) * np.sqrt(252)

        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率2%
        excess_returns = returns_array - risk_free_rate / 252
        sharpe_ratio = (
            np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            if np.std(excess_returns) > 0
            else 0
        )

        # 最大回撤
        cumulative_returns = np.cumprod(1 + returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)

        # 交易相关指标
        winning_trades = [t for t in trades if t.get("pnl", 0) > 0]
        win_rate = len(winning_trades) / len(trades) if trades else 0

        gross_profits = sum(t["pnl"] for t in trades if t.get("pnl", 0) > 0)
        gross_losses = abs(
            sum(t["pnl"] for t in trades if t.get("pnl", 0) < 0)
        )
        profit_factor = (
            gross_profits / gross_losses if gross_losses > 0 else float("inf")
        )

        return {
            "total_return": total_return,
            "annual_return": annual_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": abs(max_drawdown),
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "trade_count": len(trades),
            "avg_trade_duration": self._calculate_avg_duration(trades),
        }

    def _empty_metrics(self) -> Dict[str, float]:
        """空指标"""
        return {
            "total_return": 0.0,
            "annual_return": 0.0,
            "volatility": 0.0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "trade_count": 0,
            "avg_trade_duration": 0.0,
        }

    def _calculate_avg_duration(self, trades: List[Dict]) -> float:
        """计算平均交易持续时间"""
        if not trades:
            return 0.0

        durations = []
        for trade in trades:
            if "entry_time" in trade and "exit_time" in trade:
                duration = (
                    trade["exit_time"] - trade["entry_time"]
                ).total_seconds() / 3600  # 小时
                durations.append(duration)

        return statistics.mean(durations) if durations else 0.0


class BacktestEngine:
    """回测引擎"""

    def __init__(self):
        self.performance_analyzer = PerformanceAnalyzer()

    def run_backtest(
        self,
        strategy_name: str,
        parameters: Dict[str, float],
        data: pd.DataFrame,
        initial_capital: float = 10000,
    ) -> BacktestResult:
        """运行回测"""
        logger.info(f"开始回测策略: {strategy_name}")

        # 模拟交易执行
        trades = self._simulate_trades(
            strategy_name, parameters, data, initial_capital
        )

        # 计算收益序列
        returns = self._calculate_returns(trades, data)

        # 分析性能
        metrics = self.performance_analyzer.calculate_metrics(returns, trades)

        # 构建回测结果
        final_capital = initial_capital * (1 + metrics["total_return"])

        return BacktestResult(
            strategy_name=strategy_name,
            start_date=data.index[0] if not data.empty else datetime.now(),
            end_date=data.index[-1] if not data.empty else datetime.now(),
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=metrics["total_return"],
            annual_return=metrics["annual_return"],
            volatility=metrics["volatility"],
            sharpe_ratio=metrics["sharpe_ratio"],
            max_drawdown=metrics["max_drawdown"],
            win_rate=metrics["win_rate"],
            profit_factor=metrics["profit_factor"],
            trade_count=metrics["trade_count"],
            avg_trade_duration=metrics["avg_trade_duration"],
            trades=trades,
        )

    def _simulate_trades(
        self,
        strategy_name: str,
        parameters: Dict[str, float],
        data: pd.DataFrame,
        capital: float,
    ) -> List[Dict]:
        """模拟交易执行"""
        trades = []

        # 根据策略类型生成不同的交易
        if strategy_name == "breakout_strategy":
            trades.extend(
                self._simulate_breakout_trades(parameters, data, capital)
            )
        elif strategy_name == "mean_reversion":
            trades.extend(
                self._simulate_mean_reversion_trades(parameters, data, capital)
            )
        elif strategy_name == "momentum_strategy":
            trades.extend(
                self._simulate_momentum_trades(parameters, data, capital)
            )
        else:
            # 默认策略
            trades.extend(
                self._simulate_default_trades(parameters, data, capital)
            )

        return trades

    def _simulate_breakout_trades(
        self, params: Dict[str, float], data: pd.DataFrame, capital: float
    ) -> List[Dict]:
        """模拟突破策略交易"""
        trades = []
        lookback = int(params.get("lookback_period", 20))
        threshold = params.get("breakout_threshold", 0.02)

        for i in range(lookback, len(data)):
            current_price = (
                data.iloc[i]["close"]
                if "close" in data.columns
                else 100 + i * 0.1
            )
            high_period = max(
                [100 + j * 0.1 for j in range(i - lookback, i)]
            )  # 模拟数据

            if current_price > high_period * (1 + threshold):
                # 突破信号
                trade = {
                    "entry_time": (
                        data.index[i] if not data.empty else datetime.now()
                    ),
                    "exit_time": (
                        data.index[min(i + 5, len(data) - 1)]
                        if not data.empty
                        else datetime.now()
                    ),
                    "entry_price": current_price,
                    "exit_price": current_price
                    * (1 + np.random.normal(0.005, 0.02)),
                    "size": capital * 0.1,  # 10%仓位
                    "pnl": 0,
                }
                trade["pnl"] = (
                    (trade["exit_price"] - trade["entry_price"])
                    * trade["size"]
                    / trade["entry_price"]
                )
                trades.append(trade)

        return trades

    def _simulate_mean_reversion_trades(
        self, params: Dict[str, float], data: pd.DataFrame, capital: float
    ) -> List[Dict]:
        """模拟均值回归策略交易"""
        trades = []
        period = int(params.get("ma_period", 20))
        std_multiplier = params.get("std_multiplier", 2.0)

        for i in range(period, len(data), 5):  # 每5个周期检查一次
            current_price = 100 + i * 0.1  # 模拟价格
            ma = 100 + (i - period / 2) * 0.1  # 模拟均线
            std = 2.0  # 模拟标准差

            if current_price < ma - std * std_multiplier:
                # 均值回归买入信号
                trade = {
                    "entry_time": datetime.now() - timedelta(hours=i),
                    "exit_time": datetime.now() - timedelta(hours=i - 3),
                    "entry_price": current_price,
                    "exit_price": current_price
                    * (1 + np.random.normal(0.01, 0.015)),
                    "size": capital * 0.05,  # 5%仓位
                    "pnl": 0,
                }
                trade["pnl"] = (
                    (trade["exit_price"] - trade["entry_price"])
                    * trade["size"]
                    / trade["entry_price"]
                )
                trades.append(trade)

        return trades

    def _simulate_momentum_trades(
        self, params: Dict[str, float], data: pd.DataFrame, capital: float
    ) -> List[Dict]:
        """模拟动量策略交易"""
        trades = []
        momentum_period = int(params.get("momentum_period", 10))
        threshold = params.get("momentum_threshold", 0.01)

        for i in range(momentum_period, len(data), 3):  # 每3个周期检查一次
            current_price = 100 + i * 0.1
            past_price = 100 + (i - momentum_period) * 0.1
            momentum = (current_price - past_price) / past_price

            if momentum > threshold:
                # 动量买入信号
                trade = {
                    "entry_time": datetime.now() - timedelta(hours=i),
                    "exit_time": datetime.now() - timedelta(hours=i - 2),
                    "entry_price": current_price,
                    "exit_price": current_price
                    * (1 + np.random.normal(0.008, 0.012)),
                    "size": capital * 0.08,  # 8%仓位
                    "pnl": 0,
                }
                trade["pnl"] = (
                    (trade["exit_price"] - trade["entry_price"])
                    * trade["size"]
                    / trade["entry_price"]
                )
                trades.append(trade)

        return trades

    def _simulate_default_trades(
        self, params: Dict[str, float], data: pd.DataFrame, capital: float
    ) -> List[Dict]:
        """模拟默认策略交易"""
        trades = []

        # 生成一些随机交易
        for i in range(10):
            trade = {
                "entry_time": datetime.now() - timedelta(hours=i * 2),
                "exit_time": datetime.now() - timedelta(hours=i * 2 - 1),
                "entry_price": 100 + i,
                "exit_price": 100 + i + np.random.normal(0, 2),
                "size": capital * 0.05,
                "pnl": 0,
            }
            trade["pnl"] = (
                (trade["exit_price"] - trade["entry_price"])
                * trade["size"]
                / trade["entry_price"]
            )
            trades.append(trade)

        return trades

    def _calculate_returns(
        self, trades: List[Dict], data: pd.DataFrame
    ) -> List[float]:
        """计算收益序列"""
        if not trades:
            return []

        # 基于交易生成日收益率
        daily_returns = []
        for trade in trades:
            trade_return = (
                trade["pnl"] / trade["size"] if trade["size"] > 0 else 0
            )
            daily_returns.append(trade_return)

        return daily_returns


class ParameterOptimizer:
    """参数优化器"""

    def __init__(self):
        self.backtest_engine = BacktestEngine()

    def optimize_strategy(
        self,
        strategy_name: str,
        parameters: List[StrategyParameter],
        data: pd.DataFrame,
        optimization_method: str = "grid_search",
    ) -> OptimizationResult:
        """优化策略参数"""
        logger.info(
            f"开始优化策略: {strategy_name}, 方法: {optimization_method}"
        )

        if optimization_method == "grid_search":
            return self._grid_search_optimization(
                strategy_name, parameters, data
            )
        elif optimization_method == "random_search":
            return self._random_search_optimization(
                strategy_name, parameters, data
            )
        elif optimization_method == "genetic_algorithm":
            return self._genetic_algorithm_optimization(
                strategy_name, parameters, data
            )
        else:
            raise ValueError(f"不支持的优化方法: {optimization_method}")

    def _grid_search_optimization(
        self,
        strategy_name: str,
        parameters: List[StrategyParameter],
        data: pd.DataFrame,
    ) -> OptimizationResult:
        """网格搜索优化"""
        best_result = None
        best_score = float("-inf")

        # 生成参数组合
        param_combinations = self._generate_grid_combinations(parameters)

        logger.info(f"网格搜索: 将测试 {len(param_combinations)} 个参数组合")

        for i, param_dict in enumerate(param_combinations):
            try:
                backtest_result = self.backtest_engine.run_backtest(
                    strategy_name, param_dict, data
                )
                score = self._calculate_optimization_score(backtest_result)

                if score > best_score:
                    best_score = score
                    best_result = OptimizationResult(
                        parameters=param_dict,
                        performance_score=score,
                        total_return=backtest_result.total_return,
                        sharpe_ratio=backtest_result.sharpe_ratio,
                        max_drawdown=backtest_result.max_drawdown,
                        win_rate=backtest_result.win_rate,
                        trade_count=backtest_result.trade_count,
                        optimization_timestamp=datetime.now(),
                    )

                if (i + 1) % 10 == 0:
                    logger.info(
                        f"已完成 {i + 1}/{len(param_combinations)} 个组合测试"
                    )

            except Exception as e:
                logger.warning(f"参数组合 {param_dict} 测试失败: {e}")

        return best_result or self._default_optimization_result(parameters)

    def _random_search_optimization(
        self,
        strategy_name: str,
        parameters: List[StrategyParameter],
        data: pd.DataFrame,
        n_iterations: int = 100,
    ) -> OptimizationResult:
        """随机搜索优化"""
        best_result = None
        best_score = float("-inf")

        logger.info(f"随机搜索: 将测试 {n_iterations} 个随机参数组合")

        for i in range(n_iterations):
            try:
                param_dict = self._generate_random_parameters(parameters)
                backtest_result = self.backtest_engine.run_backtest(
                    strategy_name, param_dict, data
                )
                score = self._calculate_optimization_score(backtest_result)

                if score > best_score:
                    best_score = score
                    best_result = OptimizationResult(
                        parameters=param_dict,
                        performance_score=score,
                        total_return=backtest_result.total_return,
                        sharpe_ratio=backtest_result.sharpe_ratio,
                        max_drawdown=backtest_result.max_drawdown,
                        win_rate=backtest_result.win_rate,
                        trade_count=backtest_result.trade_count,
                        optimization_timestamp=datetime.now(),
                    )

                if (i + 1) % 20 == 0:
                    logger.info(f"已完成 {i + 1}/{n_iterations} 次随机搜索")

            except Exception as e:
                logger.warning(f"随机参数组合测试失败: {e}")

        return best_result or self._default_optimization_result(parameters)

    def _genetic_algorithm_optimization(
        self,
        strategy_name: str,
        parameters: List[StrategyParameter],
        data: pd.DataFrame,
    ) -> OptimizationResult:
        """遗传算法优化"""
        population_size = 20
        generations = 10
        mutation_rate = 0.1

        logger.info(
            f"遗传算法: 种群大小={population_size}, 代数={generations}"
        )

        # 初始种群
        population = [
            self._generate_random_parameters(parameters)
            for _ in range(population_size)
        ]

        for generation in range(generations):
            # 评估种群
            fitness_scores = []
            for individual in population:
                try:
                    backtest_result = self.backtest_engine.run_backtest(
                        strategy_name, individual, data
                    )
                    score = self._calculate_optimization_score(backtest_result)
                    fitness_scores.append((individual, score, backtest_result))
                except:
                    fitness_scores.append((individual, float("-inf"), None))

            # 按适应度排序
            fitness_scores.sort(key=lambda x: x[1], reverse=True)

            # 选择最优个体
            best_individuals = [
                item[0] for item in fitness_scores[: population_size // 2]
            ]

            # 生成下一代
            new_population = []
            new_population.extend(best_individuals)  # 保留最优个体

            # 交叉和变异
            while len(new_population) < population_size:
                parent1 = np.random.choice(best_individuals)
                parent2 = np.random.choice(best_individuals)
                child = self._crossover(parent1, parent2, parameters)
                if np.random.random() < mutation_rate:
                    child = self._mutate(child, parameters)
                new_population.append(child)

            population = new_population
            logger.info(
                f"第 {generation + 1} 代完成, 最佳适应度: {fitness_scores[0][1]:.4f}"
            )

        # 返回最优结果
        final_fitness = []
        for individual in population:
            try:
                backtest_result = self.backtest_engine.run_backtest(
                    strategy_name, individual, data
                )
                score = self._calculate_optimization_score(backtest_result)
                final_fitness.append((individual, score, backtest_result))
            except:
                continue

        if final_fitness:
            best_individual, best_score, best_backtest = max(
                final_fitness, key=lambda x: x[1]
            )
            return OptimizationResult(
                parameters=best_individual,
                performance_score=best_score,
                total_return=best_backtest.total_return,
                sharpe_ratio=best_backtest.sharpe_ratio,
                max_drawdown=best_backtest.max_drawdown,
                win_rate=best_backtest.win_rate,
                trade_count=best_backtest.trade_count,
                optimization_timestamp=datetime.now(),
            )

        return self._default_optimization_result(parameters)

    def _generate_grid_combinations(
        self, parameters: List[StrategyParameter]
    ) -> List[Dict[str, float]]:
        """生成网格搜索参数组合"""
        combinations = []

        # 为每个参数生成取值列表
        param_values = {}
        for param in parameters:
            values = []
            current = param.min_value
            while current <= param.max_value:
                values.append(current)
                current += param.step
            param_values[param.name] = values

        # 生成所有组合（限制组合数量避免过度计算）
        import itertools

        param_names = list(param_values.keys())

        # 如果组合数量太大，采样部分组合
        total_combinations = 1
        for values in param_values.values():
            total_combinations *= len(values)

        if total_combinations > 1000:  # 限制最大组合数
            # 随机采样
            for _ in range(1000):
                combo = {}
                for name in param_names:
                    combo[name] = np.random.choice(param_values[name])
                combinations.append(combo)
        else:
            # 完整网格搜索
            for combo_values in itertools.product(
                *[param_values[name] for name in param_names]
            ):
                combo = dict(zip(param_names, combo_values))
                combinations.append(combo)

        return combinations

    def _generate_random_parameters(
        self, parameters: List[StrategyParameter]
    ) -> Dict[str, float]:
        """生成随机参数"""
        param_dict = {}
        for param in parameters:
            value = np.random.uniform(param.min_value, param.max_value)
            # 如果有步长，调整到最近的有效值
            if param.step > 0:
                steps = round((value - param.min_value) / param.step)
                value = param.min_value + steps * param.step
            param_dict[param.name] = value
        return param_dict

    def _crossover(
        self,
        parent1: Dict[str, float],
        parent2: Dict[str, float],
        parameters: List[StrategyParameter],
    ) -> Dict[str, float]:
        """交叉操作"""
        child = {}
        for param in parameters:
            if np.random.random() < 0.5:
                child[param.name] = parent1[param.name]
            else:
                child[param.name] = parent2[param.name]
        return child

    def _mutate(
        self, individual: Dict[str, float], parameters: List[StrategyParameter]
    ) -> Dict[str, float]:
        """变异操作"""
        mutated = individual.copy()
        for param in parameters:
            if np.random.random() < 0.3:  # 30%概率变异
                # 添加小幅随机变动
                noise = np.random.normal(
                    0, (param.max_value - param.min_value) * 0.1
                )
                new_value = individual[param.name] + noise
                new_value = max(
                    param.min_value, min(param.max_value, new_value)
                )
                mutated[param.name] = new_value
        return mutated

    def _calculate_optimization_score(
        self, backtest_result: BacktestResult
    ) -> float:
        """计算优化评分"""
        # 综合评分公式
        return_score = backtest_result.total_return * 0.3
        sharpe_score = backtest_result.sharpe_ratio * 0.3
        drawdown_score = (1 - backtest_result.max_drawdown) * 0.2
        win_rate_score = backtest_result.win_rate * 0.2

        return return_score + sharpe_score + drawdown_score + win_rate_score

    def _default_optimization_result(
        self, parameters: List[StrategyParameter]
    ) -> OptimizationResult:
        """默认优化结果"""
        default_params = {
            param.name: param.current_value for param in parameters
        }
        return OptimizationResult(
            parameters=default_params,
            performance_score=0.0,
            total_return=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            trade_count=0,
            optimization_timestamp=datetime.now(),
        )


class StrategyOptimizer:
    """策略优化引擎主类"""

    def __init__(self, data_path: str = "data"):
        self.parameter_optimizer = ParameterOptimizer()
        self.data_path = data_path
        self.optimization_history = []

    def get_strategy_parameters(
        self, strategy_name: str
    ) -> List[StrategyParameter]:
        """获取策略参数定义"""
        strategy_params = {
            "breakout_strategy": [
                StrategyParameter(
                    "lookback_period", 20, 5, 50, 5, "突破回看周期"
                ),
                StrategyParameter(
                    "breakout_threshold", 0.02, 0.005, 0.05, 0.005, "突破阈值"
                ),
                StrategyParameter(
                    "stop_loss", 0.03, 0.01, 0.1, 0.005, "止损比例"
                ),
                StrategyParameter(
                    "take_profit", 0.06, 0.02, 0.15, 0.01, "止盈比例"
                ),
            ],
            "mean_reversion": [
                StrategyParameter("ma_period", 20, 10, 50, 5, "移动平均周期"),
                StrategyParameter(
                    "std_multiplier", 2.0, 1.0, 3.0, 0.2, "标准差倍数"
                ),
                StrategyParameter("holding_period", 5, 1, 20, 1, "持仓周期"),
                StrategyParameter(
                    "position_size", 0.1, 0.05, 0.3, 0.05, "仓位大小"
                ),
            ],
            "momentum_strategy": [
                StrategyParameter(
                    "momentum_period", 10, 5, 30, 5, "动量计算周期"
                ),
                StrategyParameter(
                    "momentum_threshold", 0.01, 0.005, 0.03, 0.005, "动量阈值"
                ),
                StrategyParameter(
                    "exit_threshold", 0.005, 0.001, 0.02, 0.002, "退出阈值"
                ),
                StrategyParameter(
                    "max_holding_days", 3, 1, 10, 1, "最大持仓天数"
                ),
            ],
        }

        return strategy_params.get(
            strategy_name,
            [
                StrategyParameter(
                    "default_param", 1.0, 0.1, 10.0, 0.1, "默认参数"
                )
            ],
        )

    def optimize_single_strategy(
        self, strategy_name: str, optimization_method: str = "grid_search"
    ) -> OptimizationResult:
        """优化单个策略"""
        logger.info(f"开始优化策略: {strategy_name}")

        # 获取策略参数
        parameters = self.get_strategy_parameters(strategy_name)

        # 生成模拟数据（实际使用中应该加载真实历史数据）
        data = self._generate_sample_data()

        # 执行优化
        result = self.parameter_optimizer.optimize_strategy(
            strategy_name, parameters, data, optimization_method
        )

        # 保存优化结果
        self.optimization_history.append(result)
        self._save_optimization_result(strategy_name, result)

        logger.info(
            f"策略 {strategy_name} 优化完成, 评分: {result.performance_score:.4f}"
        )
        return result

    def optimize_multiple_strategies(
        self, strategy_names: List[str]
    ) -> Dict[str, OptimizationResult]:
        """批量优化多个策略"""
        logger.info(f"开始批量优化 {len(strategy_names)} 个策略")

        results = {}
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_strategy = {
                executor.submit(self.optimize_single_strategy, name): name
                for name in strategy_names
            }

            for future in future_to_strategy:
                strategy_name = future_to_strategy[future]
                try:
                    result = future.result()
                    results[strategy_name] = result
                except Exception as e:
                    logger.error(f"策略 {strategy_name} 优化失败: {e}")

        return results

    def get_optimization_recommendations(
        self, strategy_name: str
    ) -> Dict[str, Any]:
        """获取优化建议"""
        parameters = self.get_strategy_parameters(strategy_name)

        recommendations = {
            "strategy_name": strategy_name,
            "parameter_analysis": [],
            "optimization_suggestions": [],
            "risk_warnings": [],
        }

        # 参数分析
        for param in parameters:
            analysis = {
                "name": param.name,
                "current_value": param.current_value,
                "recommended_range": f"{param.min_value} - {param.max_value}",
                "sensitivity": (
                    "high"
                    if param.step < (param.max_value - param.min_value) / 20
                    else "medium"
                ),
                "description": param.description,
            }
            recommendations["parameter_analysis"].append(analysis)

        # 优化建议
        recommendations["optimization_suggestions"] = [
            "建议首先使用网格搜索进行粗调",
            "对重要参数使用更细粒度的搜索",
            "考虑参数之间的相关性",
            "在不同市场环境下测试参数稳定性",
            "定期重新优化以适应市场变化",
        ]

        # 风险警告
        recommendations["risk_warnings"] = [
            "过度优化可能导致策略过拟合",
            "历史表现不代表未来收益",
            "应在样本外数据上验证优化结果",
            "考虑交易成本对策略表现的影响",
            "监控策略在实盘中的表现偏差",
        ]

        return recommendations

    def _generate_sample_data(self) -> pd.DataFrame:
        """生成样本数据用于测试"""
        dates = pd.date_range(start="2023-01-01", end="2024-01-01", freq="H")

        # 生成模拟价格数据
        np.random.seed(42)
        prices = []
        initial_price = 100

        for i in range(len(dates)):
            # 简单的随机游走模型
            change = np.random.normal(0, 0.01)
            initial_price *= 1 + change
            prices.append(initial_price)

        data = pd.DataFrame(
            {
                "timestamp": dates,
                "open": prices,
                "high": [
                    p * (1 + abs(np.random.normal(0, 0.005))) for p in prices
                ],
                "low": [
                    p * (1 - abs(np.random.normal(0, 0.005))) for p in prices
                ],
                "close": prices,
                "volume": [np.random.uniform(1000, 10000) for _ in prices],
            }
        )

        data.set_index("timestamp", inplace=True)
        return data

    def _save_optimization_result(
        self, strategy_name: str, result: OptimizationResult
    ):
        """保存优化结果"""
        try:
            import os

            os.makedirs("optimization_results", exist_ok=True)

            filename = f"optimization_results/{strategy_name}_{result.optimization_timestamp.strftime('%Y%m%d_%H%M%S')}.json"

            result_dict = asdict(result)
            result_dict["optimization_timestamp"] = (
                result.optimization_timestamp.isoformat()
            )

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"优化结果已保存到: {filename}")

        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")

    def get_strategy_performance_report(
        self, strategy_name: str
    ) -> Dict[str, Any]:
        """生成策略性能报告"""
        # 获取最新的优化结果
        latest_result = None
        for result in reversed(self.optimization_history):
            if strategy_name in str(result.parameters):  # 简单的策略名匹配
                latest_result = result
                break

        if not latest_result:
            return {"error": f"未找到策略 {strategy_name} 的优化结果"}

        report = {
            "strategy_name": strategy_name,
            "optimization_date": latest_result.optimization_timestamp.isoformat(),
            "performance_metrics": {
                "total_return": f"{latest_result.total_return:.2%}",
                "sharpe_ratio": f"{latest_result.sharpe_ratio:.3f}",
                "max_drawdown": f"{latest_result.max_drawdown:.2%}",
                "win_rate": f"{latest_result.win_rate:.1%}",
                "trade_count": latest_result.trade_count,
                "performance_score": f"{latest_result.performance_score:.4f}",
            },
            "optimized_parameters": latest_result.parameters,
            "recommendations": self.get_optimization_recommendations(
                strategy_name
            ),
            "next_optimization_date": (
                latest_result.optimization_timestamp + timedelta(days=30)
            ).isoformat(),
        }

        return report


# 测试和演示功能
def demo_strategy_optimization():
    """策略优化演示"""
    print("🎯 策略优化引擎演示")
    print("=" * 50)

    optimizer = StrategyOptimizer()

    # 演示单策略优化
    print("\n1. 单策略优化演示...")
    strategies = ["breakout_strategy", "mean_reversion", "momentum_strategy"]

    for strategy in strategies:
        print(f"\n优化策略: {strategy}")
        result = optimizer.optimize_single_strategy(strategy, "random_search")

        print(f"  优化完成:")
        print(f"    总收益: {result.total_return:.2%}")
        print(f"    夏普比率: {result.sharpe_ratio:.3f}")
        print(f"    最大回撤: {result.max_drawdown:.2%}")
        print(f"    胜率: {result.win_rate:.1%}")
        print(f"    性能评分: {result.performance_score:.4f}")

    # 演示性能报告
    print(f"\n2. 生成性能报告...")
    for strategy in strategies:
        report = optimizer.get_strategy_performance_report(strategy)
        if "error" not in report:
            print(f"\n{strategy} 性能报告:")
            metrics = report["performance_metrics"]
            print(f"  总收益: {metrics['total_return']}")
            print(f"  夏普比率: {metrics['sharpe_ratio']}")
            print(f"  最大回撤: {metrics['max_drawdown']}")

    print(f"\n3. 优化建议...")
    recommendations = optimizer.get_optimization_recommendations(
        "breakout_strategy"
    )
    print("突破策略优化建议:")
    for suggestion in recommendations["optimization_suggestions"][:3]:
        print(f"  • {suggestion}")

    print("\n🎊 策略优化引擎演示完成!")


if __name__ == "__main__":
    demo_strategy_optimization()

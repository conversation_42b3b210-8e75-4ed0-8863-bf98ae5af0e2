#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强型WebSocket客户端
Enhanced WebSocket Client with Reconnection and Data Management
"""

import json
import time
import threading
import websocket
from typing import Dict, List, Callable, Optional
from datetime import datetime
import queue
import logging


class EnhancedWebSocketClient:
    """增强型WebSocket客户端"""
    
    def __init__(self, url: str = "wss://api.gateio.ws/ws/v4/"):
        """初始化WebSocket客户端"""
        self.url = url
        self.ws = None
        self.running = False
        self.connected = False
        
        # 连接管理
        self.reconnect_interval = 5  # 重连间隔(秒)
        self.max_reconnect_attempts = 10
        self.reconnect_count = 0
        
        # 数据管理
        self.subscriptions = {}  # 订阅管理
        self.callbacks = {}      # 回调函数
        self.message_queue = queue.Queue(maxsize=1000)
        
        # 线程管理
        self.connection_thread = None
        self.message_thread = None
        
        # 心跳管理
        self.last_ping_time = 0
        self.ping_interval = 30  # 30秒心跳
        self.pong_received = True
        
        # 数据缓存
        self.ticker_data = {}
        self.orderbook_data = {}
        self.trade_data = {}
        
        # 日志设置
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """连接WebSocket"""
        try:
            if self.running:
                return True
            
            self.logger.info("🔌 启动WebSocket连接...")
            self.running = True
            
            # 启动连接线程
            self.connection_thread = threading.Thread(target=self._connection_loop, daemon=True)
            self.connection_thread.start()
            
            # 启动消息处理线程
            self.message_thread = threading.Thread(target=self._message_processing_loop, daemon=True)
            self.message_thread.start()
            
            # 等待连接建立
            for i in range(10):
                if self.connected:
                    self.logger.info("✅ WebSocket连接成功")
                    return True
                time.sleep(1)
            
            self.logger.warning("⚠️ WebSocket连接超时")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开WebSocket连接"""
        self.logger.info("🔌 断开WebSocket连接...")
        self.running = False
        self.connected = False
        
        if self.ws:
            self.ws.close()
        
        # 等待线程结束
        if self.connection_thread:
            self.connection_thread.join(timeout=5)
        if self.message_thread:
            self.message_thread.join(timeout=5)
        
        self.logger.info("✅ WebSocket已断开")
    
    def _connection_loop(self):
        """连接循环"""
        while self.running:
            try:
                if not self.connected:
                    self._establish_connection()
                
                # 心跳检查
                self._heartbeat_check()
                
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ 连接循环异常: {e}")
                self.connected = False
                time.sleep(self.reconnect_interval)
    
    def _establish_connection(self):
        """建立WebSocket连接"""
        try:
            self.logger.info(f"🔄 尝试连接WebSocket: {self.url}")
            
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_ping=self._on_ping,
                on_pong=self._on_pong
            )
            
            # 运行WebSocket
            self.ws.run_forever(
                ping_interval=self.ping_interval,
                ping_timeout=10,
                ping_payload="ping"
            )
            
        except Exception as e:
            self.logger.error(f"❌ 建立连接异常: {e}")
            self._handle_reconnection()
    
    def _handle_reconnection(self):
        """处理重连"""
        if not self.running:
            return
        
        self.connected = False
        self.reconnect_count += 1
        
        if self.reconnect_count <= self.max_reconnect_attempts:
            self.logger.info(f"🔄 准备重连 ({self.reconnect_count}/{self.max_reconnect_attempts})")
            time.sleep(self.reconnect_interval)
        else:
            self.logger.error("❌ 达到最大重连次数，停止重连")
            self.running = False
    
    def _heartbeat_check(self):
        """心跳检查"""
        current_time = time.time()
        
        # 发送ping
        if current_time - self.last_ping_time > self.ping_interval:
            if self.connected and self.ws:
                try:
                    self.ws.ping("ping")
                    self.last_ping_time = current_time
                    self.pong_received = False
                except Exception as e:
                    self.logger.error(f"❌ 发送ping失败: {e}")
                    self.connected = False
        
        # 检查pong响应
        if not self.pong_received and current_time - self.last_ping_time > 15:
            self.logger.warning("⚠️ 心跳超时，重新连接")
            self.connected = False
    
    def _on_open(self, ws):
        """WebSocket连接打开"""
        self.logger.info("🔗 WebSocket连接已建立")
        self.connected = True
        self.reconnect_count = 0
        
        # 重新订阅之前的频道
        self._resubscribe_all()
    
    def _on_message(self, ws, message):
        """接收WebSocket消息"""
        try:
            # 将消息放入队列
            if not self.message_queue.full():
                self.message_queue.put(message)
            else:
                self.logger.warning("⚠️ 消息队列已满，丢弃消息")
                
        except Exception as e:
            self.logger.error(f"❌ 处理消息异常: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket错误处理"""
        self.logger.error(f"❌ WebSocket错误: {error}")
        self.connected = False
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭"""
        self.logger.info(f"🔌 WebSocket连接已关闭: {close_status_code} - {close_msg}")
        self.connected = False
    
    def _on_ping(self, ws, message):
        """接收ping"""
        self.logger.debug("📡 收到ping")
    
    def _on_pong(self, ws, message):
        """接收pong"""
        self.logger.debug("📡 收到pong")
        self.pong_received = True
    
    def _message_processing_loop(self):
        """消息处理循环"""
        while self.running:
            try:
                # 从队列获取消息
                message = self.message_queue.get(timeout=1)
                self._process_message(message)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"❌ 消息处理异常: {e}")
    
    def _process_message(self, message: str):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            
            # 处理不同类型的消息
            if 'channel' in data:
                channel = data['channel']
                
                if channel == 'spot.tickers':
                    self._process_ticker_data(data)
                elif channel == 'spot.order_book':
                    self._process_orderbook_data(data)
                elif channel == 'spot.trades':
                    self._process_trade_data(data)
                
                # 调用回调函数
                if channel in self.callbacks:
                    for callback in self.callbacks[channel]:
                        try:
                            callback(data)
                        except Exception as e:
                            self.logger.error(f"❌ 回调函数异常: {e}")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON解析失败: {e}")
        except Exception as e:
            self.logger.error(f"❌ 处理消息异常: {e}")
    
    def _process_ticker_data(self, data: Dict):
        """处理行情数据"""
        if 'result' in data:
            result = data['result']
            currency_pair = result.get('currency_pair')
            if currency_pair:
                self.ticker_data[currency_pair] = {
                    'price': float(result.get('last', 0)),
                    'change': float(result.get('change_percentage', 0)),
                    'volume': float(result.get('base_volume', 0)),
                    'high': float(result.get('high_24h', 0)),
                    'low': float(result.get('low_24h', 0)),
                    'timestamp': time.time()
                }
    
    def _process_orderbook_data(self, data: Dict):
        """处理订单簿数据"""
        if 'result' in data:
            result = data['result']
            currency_pair = result.get('s')  # symbol
            if currency_pair:
                self.orderbook_data[currency_pair] = {
                    'asks': result.get('asks', []),
                    'bids': result.get('bids', []),
                    'timestamp': time.time()
                }
    
    def _process_trade_data(self, data: Dict):
        """处理交易数据"""
        if 'result' in data:
            result = data['result']
            currency_pair = result.get('currency_pair')
            if currency_pair:
                if currency_pair not in self.trade_data:
                    self.trade_data[currency_pair] = []
                
                trade_info = {
                    'price': float(result.get('price', 0)),
                    'amount': float(result.get('amount', 0)),
                    'side': result.get('side', ''),
                    'timestamp': time.time()
                }
                
                self.trade_data[currency_pair].append(trade_info)
                
                # 保持最近100条记录
                if len(self.trade_data[currency_pair]) > 100:
                    self.trade_data[currency_pair] = self.trade_data[currency_pair][-100:]
    
    def subscribe(self, channel: str, payload: List[str], callback: Callable = None):
        """订阅频道"""
        try:
            if not self.connected:
                self.logger.warning("⚠️ WebSocket未连接，订阅将在连接后执行")
            
            # 保存订阅信息
            self.subscriptions[channel] = payload
            
            # 添加回调函数
            if callback:
                if channel not in self.callbacks:
                    self.callbacks[channel] = []
                self.callbacks[channel].append(callback)
            
            # 发送订阅消息
            if self.connected and self.ws:
                self._send_subscribe_message(channel, payload)
            
        except Exception as e:
            self.logger.error(f"❌ 订阅失败: {e}")
    
    def _send_subscribe_message(self, channel: str, payload: List[str]):
        """发送订阅消息"""
        try:
            message = {
                "time": int(time.time()),
                "channel": channel,
                "event": "subscribe",
                "payload": payload
            }
            
            self.ws.send(json.dumps(message))
            self.logger.info(f"📡 已订阅频道: {channel} - {payload}")
            
        except Exception as e:
            self.logger.error(f"❌ 发送订阅消息失败: {e}")
    
    def _resubscribe_all(self):
        """重新订阅所有频道"""
        for channel, payload in self.subscriptions.items():
            self._send_subscribe_message(channel, payload)
    
    def unsubscribe(self, channel: str, payload: List[str]):
        """取消订阅"""
        try:
            if self.connected and self.ws:
                message = {
                    "time": int(time.time()),
                    "channel": channel,
                    "event": "unsubscribe",
                    "payload": payload
                }
                
                self.ws.send(json.dumps(message))
                self.logger.info(f"📡 已取消订阅: {channel} - {payload}")
            
            # 移除订阅信息
            if channel in self.subscriptions:
                del self.subscriptions[channel]
            
            # 移除回调函数
            if channel in self.callbacks:
                del self.callbacks[channel]
                
        except Exception as e:
            self.logger.error(f"❌ 取消订阅失败: {e}")
    
    def get_ticker_data(self, currency_pair: str) -> Optional[Dict]:
        """获取行情数据"""
        return self.ticker_data.get(currency_pair)
    
    def get_orderbook_data(self, currency_pair: str) -> Optional[Dict]:
        """获取订单簿数据"""
        return self.orderbook_data.get(currency_pair)
    
    def get_trade_data(self, currency_pair: str, limit: int = 10) -> List[Dict]:
        """获取交易数据"""
        trades = self.trade_data.get(currency_pair, [])
        return trades[-limit:] if trades else []
    
    def get_connection_status(self) -> Dict:
        """获取连接状态"""
        return {
            'connected': self.connected,
            'running': self.running,
            'reconnect_count': self.reconnect_count,
            'subscriptions': len(self.subscriptions),
            'message_queue_size': self.message_queue.qsize()
        }


class RealTimeDataManager:
    """实时数据管理器"""
    
    def __init__(self):
        """初始化实时数据管理器"""
        self.ws_client = EnhancedWebSocketClient()
        self.data_callbacks = []
        
        # 监控的交易对
        self.watched_pairs = [
            "BTC_USDT", "ETH_USDT", "SOL_USDT", "BNB_USDT",
            "ADA_USDT", "DOT_USDT", "AVAX_USDT"
        ]
    
    def start(self) -> bool:
        """启动实时数据管理器"""
        try:
            print("🚀 启动实时数据管理器...")
            
            # 连接WebSocket
            if not self.ws_client.connect():
                print("❌ WebSocket连接失败")
                return False
            
            # 订阅数据
            self._subscribe_data()
            
            print("✅ 实时数据管理器启动成功")
            return True
            
        except Exception as e:
            print(f"❌ 启动实时数据管理器失败: {e}")
            return False
    
    def stop(self):
        """停止实时数据管理器"""
        print("🛑 停止实时数据管理器...")
        self.ws_client.disconnect()
        print("✅ 实时数据管理器已停止")
    
    def _subscribe_data(self):
        """订阅数据"""
        # 订阅行情数据
        self.ws_client.subscribe("spot.tickers", self.watched_pairs, self._on_ticker_update)
        
        # 订阅交易数据
        self.ws_client.subscribe("spot.trades", self.watched_pairs, self._on_trade_update)
    
    def _on_ticker_update(self, data: Dict):
        """行情数据更新回调"""
        for callback in self.data_callbacks:
            try:
                callback('ticker', data)
            except Exception as e:
                print(f"❌ 行情回调异常: {e}")
    
    def _on_trade_update(self, data: Dict):
        """交易数据更新回调"""
        for callback in self.data_callbacks:
            try:
                callback('trade', data)
            except Exception as e:
                print(f"❌ 交易回调异常: {e}")
    
    def add_data_callback(self, callback: Callable):
        """添加数据回调函数"""
        self.data_callbacks.append(callback)
    
    def get_real_time_price(self, currency_pair: str) -> Optional[float]:
        """获取实时价格"""
        ticker = self.ws_client.get_ticker_data(currency_pair)
        return ticker['price'] if ticker else None
    
    def get_real_time_orderbook(self, currency_pair: str) -> Optional[Dict]:
        """获取实时订单簿"""
        return self.ws_client.get_orderbook_data(currency_pair)
    
    def get_connection_status(self) -> Dict:
        """获取连接状态"""
        return self.ws_client.get_connection_status()


# 全局实时数据管理器
real_time_manager = None

def get_real_time_manager() -> RealTimeDataManager:
    """获取实时数据管理器实例"""
    global real_time_manager
    if real_time_manager is None:
        real_time_manager = RealTimeDataManager()
    return real_time_manager

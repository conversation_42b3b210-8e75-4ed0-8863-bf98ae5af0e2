#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API登录对话框 - 验证Gate.io选项是否显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_api_dialog():
    """调试API登录对话框"""
    print("🔍 开始调试API登录对话框...")
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 专业颜色配置
        PROFESSIONAL_COLORS = {
            "bg_primary": "#1e1e1e",
            "bg_secondary": "#2d2d2d", 
            "bg_panel": "#3c3c3c",
            "text_primary": "#ffffff",
            "text_secondary": "#cccccc",
            "text_accent": "#4fc3f7",
            "accent": "#2196f3",
            "profit": "#4caf50",
            "loss": "#f44336",
            "warning": "#ff9800",
            "info": "#2196f3"
        }
        
        PROFESSIONAL_FONTS = {
            "heading": ("Arial", 14, "bold"),
            "body": ("Arial", 10),
            "small": ("Arial", 9),
            "data": ("Consolas", 9)
        }
        
        # 创建对话框
        dialog = tk.Toplevel(root)
        dialog.title("🔍 调试 - Gate.io API登录配置")
        dialog.geometry("600x800")
        dialog.configure(bg=PROFESSIONAL_COLORS["bg_primary"])
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (800 // 2)
        dialog.geometry(f"600x800+{x}+{y}")

        # 调试信息标题
        debug_title = tk.Label(
            dialog,
            text="🔍 调试模式 - API登录对话框",
            font=("Arial", 16, "bold"),
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["warning"]
        )
        debug_title.pack(pady=10)

        # 调试信息
        debug_info = tk.Label(
            dialog,
            text="正在验证Gate.io交易所选项是否正确显示...",
            font=PROFESSIONAL_FONTS["small"],
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["text_secondary"]
        )
        debug_info.pack(pady=5)

        # 交易所选择 - 更明显的界面
        exchange_frame = tk.Frame(dialog, bg=PROFESSIONAL_COLORS["bg_secondary"], relief="raised", bd=3)
        exchange_frame.pack(fill="x", padx=20, pady=15)

        # 交易所选择标题
        exchange_title = tk.Label(
            exchange_frame,
            text="🏦 选择交易所 (调试模式)",
            font=("Arial", 14, "bold"),
            bg=PROFESSIONAL_COLORS["bg_secondary"],
            fg=PROFESSIONAL_COLORS["text_accent"]
        )
        exchange_title.pack(pady=(15, 10))

        # 交易所选择区域
        exchange_select_frame = tk.Frame(exchange_frame, bg=PROFESSIONAL_COLORS["bg_secondary"])
        exchange_select_frame.pack(fill="x", padx=20, pady=10)

        exchange_var = tk.StringVar(value="Gate.io")
        
        # 使用单选按钮代替下拉菜单，更明显
        exchanges = [
            ("Gate.io", "✅ 完全支持"),
            ("Binance", "⚠️ 开发中"),
            ("OKX", "⚠️ 开发中"),
            ("Huobi", "⚠️ 开发中")
        ]
        
        print(f"📋 准备显示 {len(exchanges)} 个交易所选项:")
        for i, (exchange, status) in enumerate(exchanges):
            print(f"  {i+1}. {exchange} {status}")
        
        for exchange, status in exchanges:
            radio_frame = tk.Frame(exchange_select_frame, bg=PROFESSIONAL_COLORS["bg_secondary"])
            radio_frame.pack(fill="x", pady=3)
            
            radio_btn = tk.Radiobutton(
                radio_frame,
                text=f"{exchange} {status}",
                variable=exchange_var,
                value=exchange,
                font=("Arial", 12, "bold"),  # 更大的字体
                bg=PROFESSIONAL_COLORS["bg_secondary"],
                fg=PROFESSIONAL_COLORS["text_primary"],
                selectcolor=PROFESSIONAL_COLORS["bg_panel"],
                activebackground=PROFESSIONAL_COLORS["bg_secondary"],
                activeforeground=PROFESSIONAL_COLORS["text_accent"],
                command=lambda: update_exchange_info()
            )
            radio_btn.pack(anchor="w", padx=10)
            
            print(f"✅ 已创建单选按钮: {exchange} {status}")

        # 交易所信息显示
        exchange_info_label = tk.Label(
            exchange_frame,
            text="",
            font=("Arial", 10),
            bg=PROFESSIONAL_COLORS["bg_secondary"],
            fg=PROFESSIONAL_COLORS["info"],
            justify="left"
        )
        exchange_info_label.pack(anchor="w", pady=(10, 15), padx=20)

        def update_exchange_info():
            """更新交易所信息"""
            exchange = exchange_var.get()
            print(f"🔄 更新交易所信息: {exchange}")
            
            if exchange == "Gate.io":
                info = """✅ Gate.io 交易所 (调试模式)
• 支持现货交易
• 沙盒环境: testnet.gate.io
• 需要: API Key + Secret Key
• 官网: gate.io
• 状态: 完全支持 ✅"""
            elif exchange == "Binance":
                info = """⚠️ Binance 交易所 (调试模式)
• 支持现货交易
• 沙盒环境: testnet.binance.vision
• 需要: API Key + Secret Key
• 官网: binance.com
• 状态: 开发中 ⚠️"""
            elif exchange == "OKX":
                info = """⚠️ OKX 交易所 (调试模式)
• 支持现货交易
• 沙盒环境: aws.okx.com
• 需要: API Key + Secret Key + Passphrase
• 官网: okx.com
• 状态: 开发中 ⚠️"""
            elif exchange == "Huobi":
                info = """⚠️ Huobi 交易所 (调试模式)
• 支持现货交易
• 沙盒环境: api.testnet.huobi.pro
• 需要: API Key + Secret Key
• 官网: huobi.com
• 状态: 开发中 ⚠️"""
            else:
                info = "❌ 未知交易所"

            exchange_info_label.config(text=info)
            print(f"📝 已更新交易所信息显示")

        # 初始化显示Gate.io信息
        update_exchange_info()

        # 状态显示
        status_label = tk.Label(
            dialog,
            text="🎯 调试模式: 请检查Gate.io选项是否清楚可见",
            font=("Arial", 11, "bold"),
            bg=PROFESSIONAL_COLORS["bg_primary"],
            fg=PROFESSIONAL_COLORS["warning"]
        )
        status_label.pack(pady=20)

        # 测试按钮
        test_frame = tk.Frame(dialog, bg=PROFESSIONAL_COLORS["bg_primary"])
        test_frame.pack(fill="x", padx=30, pady=20)

        def test_selection():
            """测试选择"""
            selected = exchange_var.get()
            print(f"🎯 当前选择的交易所: {selected}")
            messagebox.showinfo("调试结果", f"当前选择: {selected}\n\nGate.io选项显示正常！")

        test_btn = tk.Button(
            test_frame,
            text="🧪 测试当前选择",
            command=test_selection,
            bg=PROFESSIONAL_COLORS["info"],
            fg="white",
            font=("Arial", 12, "bold"),
            relief="flat",
            padx=20,
            pady=10
        )
        test_btn.pack(side="left", padx=(0, 10))

        close_btn = tk.Button(
            test_frame,
            text="✅ 关闭调试",
            command=lambda: (dialog.destroy(), root.quit()),
            bg=PROFESSIONAL_COLORS["profit"],
            fg="white",
            font=("Arial", 12, "bold"),
            relief="flat",
            padx=20,
            pady=10
        )
        close_btn.pack(side="right")

        print("✅ API登录对话框调试界面已创建")
        print("🎯 请检查Gate.io选项是否在界面中清楚可见")
        
        # 显示对话框
        dialog.deiconify()
        root.mainloop()

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 启动API登录对话框调试工具")
    print("=" * 60)
    debug_api_dialog()

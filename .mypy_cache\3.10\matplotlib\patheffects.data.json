{".class": "MypyFile", "_fullname": "matplotlib.patheffects", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractPathEffect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.AbstractPathEffect", "name": "AbstractPathEffect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.AbstractPathEffect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.AbstractPathEffect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "offset"], "arg_types": ["matplotlib.patheffects.AbstractPathEffect", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractPathEffect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.AbstractPathEffect.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.AbstractPathEffect", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of AbstractPathEffect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.AbstractPathEffect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.AbstractPathEffect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GraphicsContextBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.GraphicsContextBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Normal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.Normal", "name": "Normal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.Normal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.Normal", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.Normal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.Normal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Patch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.Patch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PathEffectRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_bases.RendererBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.PathEffectRenderer", "name": "PathEffect<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.PathEffectRenderer", "matplotlib.backend_bases.RendererBase", "builtins.object"], "names": {".class": "SymbolTable", "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.__getattribute__", "name": "__getattribute__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattribute__ of PathEffectRenderer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path_effects", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "path_effects", "renderer"], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", {".class": "Instance", "args": ["matplotlib.patheffects.AbstractPathEffect"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PathEffectRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_with_path_effect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path_effects"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.copy_with_path_effect", "name": "copy_with_path_effect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path_effects"], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", {".class": "Instance", "args": ["matplotlib.patheffects.AbstractPathEffect"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_with_path_effect of PathEffectRenderer", "ret_type": "matplotlib.patheffects.PathEffectRenderer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_markers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 2, 4], "arg_names": ["self", "gc", "marker_path", "marker_trans", "path", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.draw_markers", "name": "draw_markers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 2, 4], "arg_names": ["self", "gc", "marker_path", "marker_trans", "path", "args", "kwargs"], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", "matplotlib.path.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_markers of PathEffectRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of PathEffectRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["self", "gc", "master_transform", "paths", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathEffectRenderer.draw_path_collection", "name": "draw_path_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["self", "gc", "master_transform", "paths", "args", "kwargs"], "arg_types": ["matplotlib.patheffects.PathEffectRenderer", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.transforms.Transform", {".class": "Instance", "args": ["matplotlib.path.Path"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path_collection of PathEffectRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.PathEffectRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.PathEffectRenderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PathPatchEffect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.PathPatchEffect", "name": "PathPatchEffect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathPatchEffect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.PathPatchEffect", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "offset", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathPatchEffect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "offset", "kwargs"], "arg_types": ["matplotlib.patheffects.PathPatchEffect", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PathPatchEffect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.PathPatchEffect.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.PathPatchEffect", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of PathPatchEffect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.patheffects.PathPatchEffect.patch", "name": "patch", "type": "matplotlib.patches.Patch"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.PathPatchEffect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.PathPatchEffect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendererBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.RendererBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SimpleLineShadow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.SimpleLineShadow", "name": "SimpleLineShadow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimpleLineShadow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.SimpleLineShadow", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "shadow_color", "alpha", "rho", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimpleLineShadow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "shadow_color", "alpha", "rho", "kwargs"], "arg_types": ["matplotlib.patheffects.SimpleLineShadow", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleLineShadow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimpleLineShadow.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.SimpleLineShadow", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of SimpleLineShadow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.SimpleLineShadow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.SimpleLineShadow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimplePatchShadow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.SimplePatchShadow", "name": "SimplePatchShadow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimplePatchShadow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.SimplePatchShadow", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "shadow_rgbFace", "alpha", "rho", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimplePatchShadow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "shadow_rgbFace", "alpha", "rho", "kwargs"], "arg_types": ["matplotlib.patheffects.SimplePatchShadow", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimplePatchShadow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.SimplePatchShadow.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.SimplePatchShadow", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of SimplePatchShadow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.SimplePatchShadow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.SimplePatchShadow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Stroke": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.Stroke", "name": "Stroke", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.Stroke", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.Stroke", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "offset", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.Stroke.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "offset", "kwargs"], "arg_types": ["matplotlib.patheffects.Stroke", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Stroke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.Stroke.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.Stroke", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of Stroke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.Stroke.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.Stroke", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TickedStroke": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.AbstractPathEffect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.TickedStroke", "name": "TickedStroke", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.TickedStroke", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.TickedStroke", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "spacing", "angle", "length", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.TickedStroke.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "offset", "spacing", "angle", "length", "kwargs"], "arg_types": ["matplotlib.patheffects.TickedStroke", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TickedStroke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.TickedStroke.draw_path", "name": "draw_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "renderer", "gc", "tpath", "affine", "rgbFace"], "arg_types": ["matplotlib.patheffects.TickedStroke", "matplotlib.backend_bases.RendererBase", "matplotlib.backend_bases.GraphicsContextBase", "matplotlib.path.Path", "matplotlib.transforms.Transform", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_path of TickedStroke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.TickedStroke.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.TickedStroke", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.patheffects.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "withSimplePatchShadow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.SimplePatchShadow"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.withSimplePatchShadow", "name": "withSimplePatchShadow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.withSimplePatchShadow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.withSimplePatchShadow", "matplotlib.patheffects.SimplePatchShadow", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.withSimplePatchShadow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.withSimplePatchShadow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "withStroke": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.Stroke"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.withStroke", "name": "withStroke", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.withStroke", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.withStroke", "matplotlib.patheffects.Stroke", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.withStroke.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.withStroke", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "withTickedStroke": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.patheffects.TickedStroke"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.patheffects.withTickedStroke", "name": "withTickedStroke", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.patheffects.withTickedStroke", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.patheffects", "mro": ["matplotlib.patheffects.withTickedStroke", "matplotlib.patheffects.TickedStroke", "matplotlib.patheffects.AbstractPathEffect", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.patheffects.withTickedStroke.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.patheffects.withTickedStroke", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\patheffects.pyi"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专业交易系统GUI
Professional Trading System GUI

专业级现货交易终端界面
Professional Spot Trading Terminal Interface
"""

import threading
import time
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, ttk

# 导入常量
try:
    from .constants import (
        AppConstants, StyleConstants, TradingConstants,
        MessageConstants, PathConstants
    )
    from .constants.chinese_ui_constants import ChineseUIConstants
except ImportError:
    # 如果常量不可用，使用默认值
    class AppConstants:
        DEFAULT_WINDOW_WIDTH = 1600
        DEFAULT_WINDOW_HEIGHT = 1000
        APP_NAME = "ULTIMATE SPOT TRADING TERMINAL"
    class StyleConstants:
        FONT_FAMILY_SANS = "Arial"
        FONT_SIZE_NORMAL = 10
    class TradingConstants:
        ALL_SUPPORTED_SYMBOLS = ["BTC/USDT", "ETH/USDT"]
    class MessageConstants:
        MSG_CONNECTION_SUCCESS = "connection.success"
    class PathConstants:
        APP_ICON = "assets/trading_icon.ico"
    class ChineseUIConstants:
        APP_TITLE = "🏦 终极现货交易终端 - 专业版"
        RISK_WARNING = "⚠️ 风险警告: 交易存在重大亏损风险"

# 导入配置和主题管理
try:
    from .config import get_config_manager, get_config, set_config
    from .ui import get_theme_manager, get_color, get_font
    from .i18n import get_language_manager, _, set_language
except ImportError:
    # 如果模块不可用，使用默认值
    def get_config(key, default=None): return default
    def set_config(key, value): return True
    def get_color(name): return StyleConstants.COLOR_GRAY_700
    def get_font(name): return (StyleConstants.FONT_FAMILY_SANS, StyleConstants.FONT_SIZE_NORMAL)
    def _(text): return text

# 导入交易系统核心
try:
    from .trading_system_core import get_trading_system_core
    TRADING_CORE_AVAILABLE = True
except ImportError:
    TRADING_CORE_AVAILABLE = False
    def get_trading_system_core(*args, **kwargs): return None

# 导入API连接器
try:
    from .api.gate_api_connector import gate_api, show_api_login_dialog
    API_AVAILABLE = True
except ImportError:
    API_AVAILABLE = False
    gate_api = None
    def show_api_login_dialog(parent): return False

# 导入图表组件
try:
    from .ui.charts.candlestick_chart import CandlestickChart
    CHART_SYSTEM_AVAILABLE = True
except ImportError:
    CHART_SYSTEM_AVAILABLE = False
    CandlestickChart = None


class ProfessionalTradingGUI:
    """专业交易系统GUI"""

    # 专业配色方案 - 优化可见性
    PROFESSIONAL_COLORS = {
        "bg_primary": "#1e1e1e",      # 深灰背景 (更清晰)
        "bg_secondary": "#2d2d2d",    # 次级背景 (更清晰)
        "bg_panel": "#3a3a3a",        # 面板背景 (更清晰)
        "text_primary": "#ffffff",     # 主要文字 (纯白)
        "text_secondary": "#e0e0e0",   # 次要文字 (更亮)
        "text_accent": "#00ff88",      # 强调文字 (更亮的绿)
        "profit": "#00ff88",          # 盈利绿 (更亮)
        "loss": "#ff4444",            # 亏损红 (更亮)
        "warning": "#ffcc00",         # 警告黄 (更亮)
        "info": "#44aaff",            # 信息蓝 (更亮)
        "connected": "#00ff88",       # 已连接 (更亮的绿)
        "disconnected": "#ff4444",    # 未连接 (更亮的红)
        "trading": "#44aaff",         # 交易中 (更亮的蓝)
        "paused": "#ffcc00",          # 暂停 (更亮的黄)
    }

    # 专业字体 - 优化可读性
    PROFESSIONAL_FONTS = {
        "title": ("Microsoft YaHei UI", 20, "bold"),      # 更大更清晰
        "heading": ("Microsoft YaHei UI", 16, "bold"),    # 更大更清晰
        "body": ("Microsoft YaHei UI", 12),               # 更大更清晰
        "monospace": ("Consolas", 11),                    # 更大更清晰
        "data": ("Courier New", 11),                      # 更大更清晰
        "small": ("Microsoft YaHei UI", 10),              # 更大更清晰
        "button": ("Microsoft YaHei UI", 11, "bold"),     # 按钮专用字体
        "label": ("Microsoft YaHei UI", 11),              # 标签专用字体
    }

    def __init__(self):
        """初始化专业交易GUI"""
        self.root = tk.Tk()
        self.setup_default_constants()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.init_trading_system()

    def setup_default_constants(self):
        """设置默认常量，防止缺失常量导致的错误"""
        self.default_constants = {
            'TAB_POSITIONS': '📊 持仓管理',
            'TAB_ORDERS': '📋 订单管理',
            'TAB_HISTORY': '📈 交易历史',
            'TAB_MARKET_DATA': '📈 市场数据',
            'POSITION_COLUMNS': ["交易对", "方向", "数量", "开仓价", "当前价", "盈亏", "盈亏率", "保证金"],
            'ORDER_COLUMNS': ["订单号", "交易对", "方向", "类型", "数量", "价格", "状态", "时间"],
            'HISTORY_COLUMNS': ["时间", "交易对", "方向", "数量", "价格", "手续费", "盈亏", "余额"],
            'ORDER_TYPE_MARKET': '市价单',
            'ORDER_TYPE_LIMIT': '限价单',
            'ALL_ORDER_TYPES': ['市价单', '限价单', '止损单', '止损限价单'],
            'MSG_PLACING_BUY_ORDER': '正在下买单',
            'MSG_PLACING_SELL_ORDER': '正在下卖单',
            'BUY_BUTTON': '🟢 买入',
            'SELL_BUTTON': '🔴 卖出',
            'SYMBOL_LABEL': '交易对:',
            'ORDER_TYPE_LABEL': '订单类型:',
            'QUANTITY_LABEL': '数量:',
            'PRICE_LABEL': '价格:',
            'QUICK_TRADING_PANEL': '⚡ 快速交易',
            'ACCOUNT_BALANCE': '账户余额',
            'ACCOUNT_EQUITY': '账户净值',
            'ACCOUNT_MARGIN': '已用保证金',
            'ACCOUNT_FREE_MARGIN': '可用保证金',
            'ACCOUNT_MARGIN_LEVEL': '保证金比例',
            'ACCOUNT_UNREALIZED_PL': '未实现盈亏',
            'ACCOUNT_REALIZED_PL': '已实现盈亏',
            'STATUS_READY': '● 系统就绪',
            'DIALOG_ORDER_PLACED': '订单已下达',
            'PAPER_MODE': '模拟交易',
            'LIVE_MODE': '实盘交易',
            'DIALOG_ERROR': '错误',
            'DIALOG_SUCCESS': '成功',
            'DIALOG_WARNING': '警告',
            'DIALOG_INFO': '信息',
            'STATUS_EMERGENCY_STOP': '🔴 紧急停止',
            'DIALOG_EMERGENCY_STOP': '紧急停止确认',
            'MSG_EMERGENCY_STOP': '所有交易活动已紧急停止',
            'STATUS_CONNECTED': '● 已连接',
            'DIALOG_CONFIRM_LIVE_TRADING': '确认实盘交易',
            'STATUS_TRADING': '● 交易中',
            'TRADING_MODE_PAUSED': '交易已暂停',
            'TRADING_MODE_STOPPED': '交易已停止',
            'TRADING_MODE_STARTED': '交易模式已启动',
            'MSG_BUY_ORDER_SUCCESS': '买单提交成功',
            'MSG_SELL_ORDER_SUCCESS': '卖单提交成功',
            'MSG_REFRESHING_MARKET': '🔄 正在刷新市场数据...',
            'MSG_MARKET_REFRESHED': '✅ 市场数据已刷新',
            'DIALOG_CONFIRM_CLOSE_ALL': '确认平仓',
            'MSG_CONFIRM_CLOSE_ALL_POSITIONS': '确认平仓所有持仓？\n此操作无法撤销！',
            'DIALOG_WARNING_SELECT_ORDER': '选择订单',
            'MSG_SELECT_ORDER_TO_CANCEL': '请选择要取消的订单',
            'DIALOG_CONFIRM_CANCEL': '确认取消',
            'MSG_CONFIRM_CANCEL_ORDER': '确认取消订单',
            'DIALOG_POSITION_ANALYSIS': '持仓分析'
        }

    def get_constant(self, name, default=None):
        """获取常量，如果不存在则使用默认值"""
        return getattr(ChineseUIConstants, name,
                      self.default_constants.get(name, default))

    def setup_window(self):
        """设置主窗口"""
        # 使用中文标题
        title = ChineseUIConstants.APP_TITLE
        geometry = f"{AppConstants.DEFAULT_WINDOW_WIDTH}x{AppConstants.DEFAULT_WINDOW_HEIGHT}"

        self.root.title(title)
        self.root.geometry(geometry)
        self.root.configure(bg=self.PROFESSIONAL_COLORS["bg_primary"])
        self.root.resizable(True, True)

        # 设置窗口图标和属性
        try:
            self.root.iconbitmap(str(PathConstants.APP_ICON))
        except Exception:
            pass

        # 设置最小窗口大小
        try:
            min_size = AppConstants.get_min_window_size()
            self.root.minsize(min_size[0], min_size[1])
        except AttributeError:
            # 如果方法不存在，使用默认值
            self.root.minsize(1200, 800)

    def setup_variables(self):
        """设置变量"""
        # 连接状态
        self.is_connected = False
        self.is_trading = False
        self.trading_mode = "PAPER"  # PAPER, LIVE

        # 交易数据
        self.account_data = {
            "balance": 0.0,
            "equity": 0.0,
            "margin": 0.0,
            "free_margin": 0.0,
            "margin_level": 0.0,
            "unrealized_pnl": 0.0,
            "realized_pnl": 0.0,
        }

        # 持仓数据
        self.positions = []

        # 订单数据
        self.orders = []

        # 市场数据
        self.market_data = {}

        # 交易系统
        self.trading_core = None
        self.api_connector = None

    def setup_ui(self):
        """设置用户界面"""
        # 风险警告横幅
        self.create_risk_warning_banner()

        # 主标题栏
        self.create_title_bar()

        # 主要内容区域
        self.create_main_content()

        # 状态栏
        self.create_status_bar()

    def create_risk_warning_banner(self):
        """创建风险警告横幅"""
        warning_frame = tk.Frame(
            self.root,
            bg=self.PROFESSIONAL_COLORS["loss"],
            height=35
        )
        warning_frame.pack(fill="x")
        warning_frame.pack_propagate(False)

        warning_label = tk.Label(
            warning_frame,
            text=ChineseUIConstants.RISK_WARNING,
            font=self.PROFESSIONAL_FONTS["body"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            bg=self.PROFESSIONAL_COLORS["loss"],
        )
        warning_label.pack(pady=8)

    def create_title_bar(self):
        """创建标题栏"""
        title_frame = tk.Frame(
            self.root,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            height=80
        )
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)

        # 主标题
        title_label = tk.Label(
            title_frame,
            text="🏦 终极现货交易终端",
            font=self.PROFESSIONAL_FONTS["title"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
        )
        title_label.pack(side="left", padx=20, pady=15)

        # 副标题
        subtitle_text = getattr(ChineseUIConstants, 'APP_SUBTITLE', '💎 实时交易 • 风险管理')
        subtitle_label = tk.Label(
            title_frame,
            text=subtitle_text,
            font=self.PROFESSIONAL_FONTS["heading"],
            fg=self.PROFESSIONAL_COLORS["text_secondary"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
        )
        subtitle_label.pack(side="left", padx=(0, 20), pady=15)

        # 控制按钮
        self.create_control_buttons(title_frame)

        # 连接状态指示器
        status_text = getattr(ChineseUIConstants, 'STATUS_DISCONNECTED', '● 未连接')
        self.status_indicator = tk.Label(
            title_frame,
            text=status_text,
            font=self.PROFESSIONAL_FONTS["heading"],
            fg=self.PROFESSIONAL_COLORS["disconnected"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
        )
        self.status_indicator.pack(side="right", padx=20, pady=15)

    def create_control_buttons(self, parent):
        """创建控制按钮"""
        buttons_frame = tk.Frame(parent, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        buttons_frame.pack(side="right", padx=20, pady=10)

        # 紧急停止按钮
        emergency_text = getattr(ChineseUIConstants, 'EMERGENCY_STOP', '🚨 紧急停止')
        emergency_btn = tk.Button(
            buttons_frame,
            text=emergency_text,
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            command=self.emergency_stop,
            relief="raised",
            bd=3
        )
        emergency_btn.pack(side="right", padx=5)

        # 设置按钮
        settings_text = getattr(ChineseUIConstants, 'SETTINGS', '⚙️ 设置')
        settings_btn = tk.Button(
            buttons_frame,
            text=settings_text,
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_panel"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            command=self.show_settings,
            relief="flat",
            bd=1
        )
        settings_btn.pack(side="right", padx=5)

        # 监控按钮
        monitor_text = getattr(ChineseUIConstants, 'MONITOR', '📊 监控')
        monitor_btn = tk.Button(
            buttons_frame,
            text=monitor_text,
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            command=self.show_monitor,
            relief="flat",
            bd=1
        )
        monitor_btn.pack(side="right", padx=5)

    def create_main_content(self):
        """创建主要内容区域 - 全新现代化布局"""
        # 主要内容框架
        main_frame = tk.Frame(self.root, bg=self.PROFESSIONAL_COLORS["bg_primary"])
        main_frame.pack(fill="both", expand=True, padx=3, pady=3)

        # 创建三栏布局：左侧控制 + 中央数据 + 右侧交易
        self.create_three_column_layout(main_frame)

    def create_three_column_layout(self, parent):
        """创建三栏现代化布局"""
        # 左侧控制面板 (25%)
        self.left_panel = tk.Frame(
            parent,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            width=350
        )
        self.left_panel.pack(side="left", fill="y", padx=(0, 2))
        self.left_panel.pack_propagate(False)

        # 中央数据面板 (50%)
        self.center_panel = tk.Frame(
            parent,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.center_panel.pack(side="left", fill="both", expand=True, padx=2)

        # 右侧交易面板 (25%)
        self.right_panel = tk.Frame(
            parent,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            width=350
        )
        self.right_panel.pack(side="right", fill="y", padx=(2, 0))
        self.right_panel.pack_propagate(False)

        # 设置各面板内容
        self.setup_left_control_panel()
        self.setup_center_data_panel()
        self.setup_right_trading_panel()

    def setup_left_control_panel(self):
        """设置左侧控制面板 - 系统控制和连接"""
        # 创建滚动区域
        canvas = tk.Canvas(self.left_panel, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        scrollbar = ttk.Scrollbar(self.left_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.PROFESSIONAL_COLORS["bg_secondary"])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 系统状态面板
        self.create_system_status_panel(scrollable_frame)

        # 连接控制面板
        self.create_connection_control_panel(scrollable_frame)

        # 交易模式控制
        self.create_trading_mode_panel(scrollable_frame)

        # 系统监控面板
        self.create_system_monitor_panel(scrollable_frame)

        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_system_status_panel(self, parent):
        """创建系统状态面板"""
        status_frame = tk.LabelFrame(
            parent,
            text="🖥️ 系统状态",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        status_frame.pack(fill="x", padx=10, pady=5)

        # 连接状态指示器
        status_row = tk.Frame(status_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        status_row.pack(fill="x", pady=3)

        tk.Label(
            status_row,
            text="连接状态:",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        self.connection_status_label = tk.Label(
            status_row,
            text="● 未连接",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["loss"]
        )
        self.connection_status_label.pack(side="right")

        # 交易状态指示器
        trading_row = tk.Frame(status_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        trading_row.pack(fill="x", pady=3)

        tk.Label(
            trading_row,
            text="交易状态:",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        self.trading_status_label = tk.Label(
            trading_row,
            text="● 停止",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_secondary"]
        )
        self.trading_status_label.pack(side="right")

    def create_connection_control_panel(self, parent):
        """创建连接控制面板"""
        conn_frame = tk.LabelFrame(
            parent,
            text="🔗 连接控制",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        conn_frame.pack(fill="x", padx=10, pady=5)

        # 连接按钮
        self.connect_btn = tk.Button(
            conn_frame,
            text="🔌 连接交易所",
            command=self.connect_exchange,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["button"],
            relief="flat",
            padx=20,
            pady=8,
        )
        self.connect_btn.pack(fill="x", pady=5)

        # 断开连接按钮
        self.disconnect_btn = tk.Button(
            conn_frame,
            text="🔌 断开连接",
            command=self.disconnect_exchange,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["button"],
            relief="flat",
            padx=20,
            pady=8,
            state="disabled"
        )
        self.disconnect_btn.pack(fill="x", pady=5)

    def disconnect_exchange(self):
        """断开交易所连接"""
        try:
            if self.is_connected:
                self.is_connected = False
                self.api_connector = None

                # 更新状态显示
                if hasattr(self, 'connection_status_label'):
                    self.connection_status_label.configure(
                        text="● 未连接",
                        fg=self.PROFESSIONAL_COLORS["loss"]
                    )

                # 更新按钮状态
                self.connect_btn.configure(state="normal")
                self.disconnect_btn.configure(state="disabled")

                self.log_message("🔌 已断开交易所连接")
            else:
                self.log_message("⚠️ 当前未连接到交易所")

        except Exception as e:
            self.log_message(f"❌ 断开连接失败: {e}")

    def create_trading_mode_panel(self, parent):
        """创建交易模式面板"""
        mode_frame = tk.LabelFrame(
            parent,
            text="⚙️ 交易模式",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        mode_frame.pack(fill="x", padx=10, pady=5)

        # 模式选择
        mode_select_frame = tk.Frame(mode_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        mode_select_frame.pack(fill="x", pady=5)

        tk.Label(
            mode_select_frame,
            text="模式:",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        paper_mode = getattr(ChineseUIConstants, 'PAPER_MODE', '模拟交易')
        live_mode = getattr(ChineseUIConstants, 'LIVE_MODE', '实盘交易')

        self.mode_var = tk.StringVar(value=paper_mode)
        mode_combo = ttk.Combobox(
            mode_select_frame,
            textvariable=self.mode_var,
            values=[paper_mode, live_mode],
            state="readonly",
            width=12
        )
        mode_combo.pack(side="right")

        # 交易控制按钮
        self.start_trading_btn = tk.Button(
            mode_frame,
            text="▶️ 开始交易",
            command=self.start_trading,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["button"],
            relief="flat",
            padx=20,
            pady=6,
        )
        self.start_trading_btn.pack(fill="x", pady=2)

        self.pause_trading_btn = tk.Button(
            mode_frame,
            text="⏸️ 暂停交易",
            command=self.pause_trading,
            bg=self.PROFESSIONAL_COLORS["warning"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["button"],
            relief="flat",
            padx=20,
            pady=6,
            state="disabled"
        )
        self.pause_trading_btn.pack(fill="x", pady=2)

        self.stop_trading_btn = tk.Button(
            mode_frame,
            text="⏹️ 停止交易",
            command=self.stop_trading,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["button"],
            relief="flat",
            padx=20,
            pady=6,
            state="disabled"
        )
        self.stop_trading_btn.pack(fill="x", pady=2)

    def create_system_monitor_panel(self, parent):
        """创建系统监控面板"""
        monitor_frame = tk.LabelFrame(
            parent,
            text="📊 系统监控",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        monitor_frame.pack(fill="x", padx=10, pady=5)

        # 系统指标
        metrics = [
            ("CPU使用率:", "0%"),
            ("内存使用:", "0MB"),
            ("网络延迟:", "0ms"),
            ("API调用:", "0/min"),
        ]

        self.system_metrics = {}
        for label_text, default_value in metrics:
            metric_row = tk.Frame(monitor_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
            metric_row.pack(fill="x", pady=2)

            tk.Label(
                metric_row,
                text=label_text,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"],
                width=10,
                anchor="w"
            ).pack(side="left")

            metric_label = tk.Label(
                metric_row,
                text=default_value,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["info"],
                anchor="e"
            )
            metric_label.pack(side="right")
            self.system_metrics[label_text] = metric_label

    def setup_center_data_panel(self):
        """设置中央数据面板 - 主要数据显示"""
        # 创建上下分割的布局
        # 上半部分：图表和市场数据 (70%)
        self.upper_data_frame = tk.Frame(
            self.center_panel,
            bg=self.PROFESSIONAL_COLORS["bg_primary"],
            height=500
        )
        self.upper_data_frame.pack(fill="both", expand=True, padx=2, pady=(0, 1))

        # 下半部分：持仓和订单 (30%)
        self.lower_data_frame = tk.Frame(
            self.center_panel,
            bg=self.PROFESSIONAL_COLORS["bg_primary"],
            height=200
        )
        self.lower_data_frame.pack(fill="x", padx=2, pady=(1, 0))
        self.lower_data_frame.pack_propagate(False)

        # 设置上下面板内容
        self.setup_upper_data_panel()
        self.setup_lower_data_panel()

    def setup_right_trading_panel(self):
        """设置右侧交易面板 - 交易操作和账户信息"""
        # 创建滚动区域
        canvas = tk.Canvas(self.right_panel, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        scrollbar = ttk.Scrollbar(self.right_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.PROFESSIONAL_COLORS["bg_secondary"])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 账户信息面板
        self.create_account_summary_panel(scrollable_frame)

        # 快速交易面板
        self.create_enhanced_trading_panel(scrollable_frame)

        # 风险管理面板
        self.create_enhanced_risk_panel(scrollable_frame)

        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def setup_upper_data_panel(self):
        """设置上部数据面板 - 图表和市场数据"""
        # 创建左右分割：图表(70%) + 市场数据(30%)
        chart_frame = tk.Frame(
            self.upper_data_frame,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            width=500
        )
        chart_frame.pack(side="left", fill="both", expand=True, padx=(0, 1))

        market_frame = tk.Frame(
            self.upper_data_frame,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            width=200
        )
        market_frame.pack(side="right", fill="y", padx=(1, 0))
        market_frame.pack_propagate(False)

        # 设置图表区域
        self.setup_chart_area(chart_frame)

        # 设置市场数据区域
        self.setup_market_data_area(market_frame)

    def setup_lower_data_panel(self):
        """设置下部数据面板 - 持仓和订单"""
        # 创建标签页
        self.data_notebook = ttk.Notebook(self.lower_data_frame)
        self.data_notebook.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建标签页
        self.create_positions_tab()
        self.create_orders_tab()
        self.create_history_tab()

    def setup_chart_area(self, parent):
        """设置图表区域"""
        chart_label_frame = tk.LabelFrame(
            parent,
            text="📈 价格图表",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=5,
            pady=5,
        )
        chart_label_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 图表显示区域
        self.chart_display_frame = tk.Frame(
            chart_label_frame,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.chart_display_frame.pack(fill="both", expand=True)

        # 如果图表系统可用，创建图表
        if CHART_SYSTEM_AVAILABLE:
            try:
                # 创建图表容器
                chart_container = tk.Frame(
                    self.chart_display_frame,
                    bg=self.PROFESSIONAL_COLORS["bg_primary"]
                )
                chart_container.pack(fill="both", expand=True)

                # 创建图表实例
                self.chart = CandlestickChart(chart_container)

                # 如果图表有frame属性，则pack它
                if hasattr(self.chart, 'frame'):
                    self.chart.frame.pack(fill="both", expand=True)
                elif hasattr(self.chart, 'canvas'):
                    self.chart.canvas.pack(fill="both", expand=True)
                else:
                    # 如果图表对象本身不能pack，创建占位符
                    placeholder_label = tk.Label(
                        chart_container,
                        text="📈 图表组件已加载\n等待数据更新",
                        font=self.PROFESSIONAL_FONTS["heading"],
                        bg=self.PROFESSIONAL_COLORS["bg_primary"],
                        fg=self.PROFESSIONAL_COLORS["text_secondary"]
                    )
                    placeholder_label.pack(expand=True)

            except Exception as e:
                self.log_message(f"❌ Chart creation failed: {e}")
                self.chart = None
                # 显示错误占位符
                error_label = tk.Label(
                    self.chart_display_frame,
                    text="📈 图表创建失败\n请检查图表组件",
                    font=self.PROFESSIONAL_FONTS["heading"],
                    bg=self.PROFESSIONAL_COLORS["bg_primary"],
                    fg=self.PROFESSIONAL_COLORS["loss"]
                )
                error_label.pack(expand=True)
        else:
            # 显示占位符
            placeholder_label = tk.Label(
                self.chart_display_frame,
                text="📈 图表系统不可用\n请安装相关依赖",
                font=self.PROFESSIONAL_FONTS["heading"],
                bg=self.PROFESSIONAL_COLORS["bg_primary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"]
            )
            placeholder_label.pack(expand=True)

    def setup_market_data_area(self, parent):
        """设置市场数据区域"""
        market_label_frame = tk.LabelFrame(
            parent,
            text="📊 市场数据",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=5,
            pady=5,
        )
        market_label_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 市场数据表格
        columns = getattr(ChineseUIConstants, 'MARKET_COLUMNS',
                         ["交易对", "最新价", "涨跌", "涨跌幅", "成交量", "最高价", "最低价", "均价"])
        self.market_tree = ttk.Treeview(
            market_label_frame,
            columns=columns,
            show="headings",
            height=8
        )

        # 设置列标题
        for col in columns:
            self.market_tree.heading(col, text=col)
            self.market_tree.column(col, width=80, anchor="center")

        # 添加滚动条
        market_scrollbar = ttk.Scrollbar(
            market_label_frame,
            orient="vertical",
            command=self.market_tree.yview
        )
        self.market_tree.configure(yscrollcommand=market_scrollbar.set)

        # 布局
        self.market_tree.pack(side="left", fill="both", expand=True)
        market_scrollbar.pack(side="right", fill="y")

    def create_account_summary_panel(self, parent):
        """创建账户摘要面板"""
        account_frame = tk.LabelFrame(
            parent,
            text="💰 账户摘要",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        account_frame.pack(fill="x", padx=10, pady=5)

        # 关键账户指标
        key_metrics = [
            ("总资产:", "balance", self.PROFESSIONAL_COLORS["text_primary"]),
            ("可用余额:", "equity", self.PROFESSIONAL_COLORS["profit"]),
            ("未实现盈亏:", "unrealized_pnl", self.PROFESSIONAL_COLORS["info"]),
        ]

        self.account_summary_labels = {}
        for label_text, key, color in key_metrics:
            metric_row = tk.Frame(account_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
            metric_row.pack(fill="x", pady=3)

            tk.Label(
                metric_row,
                text=label_text,
                font=self.PROFESSIONAL_FONTS["body"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"],
                width=12,
                anchor="w"
            ).pack(side="left")

            value_label = tk.Label(
                metric_row,
                text="$0.00",
                font=self.PROFESSIONAL_FONTS["body"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=color,
                anchor="e"
            )
            value_label.pack(side="right")
            self.account_summary_labels[key] = value_label

    def create_enhanced_trading_panel(self, parent):
        """创建增强交易面板"""
        trading_frame = tk.LabelFrame(
            parent,
            text="⚡ 快速交易",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        trading_frame.pack(fill="x", padx=10, pady=5)

        # 这里可以复用原有的快速交易逻辑
        self.create_quick_trading(trading_frame)

    def create_enhanced_risk_panel(self, parent):
        """创建增强风险面板"""
        risk_frame = tk.LabelFrame(
            parent,
            text="⚠️ 风险控制",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=10,
            pady=10,
        )
        risk_frame.pack(fill="x", padx=10, pady=5)

        # 风险指标
        risk_metrics = [
            ("风险等级:", "中等"),
            ("最大回撤:", "5.2%"),
            ("胜率:", "68%"),
        ]

        for label_text, value in risk_metrics:
            risk_row = tk.Frame(risk_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
            risk_row.pack(fill="x", pady=2)

            tk.Label(
                risk_row,
                text=label_text,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"],
                width=10,
                anchor="w"
            ).pack(side="left")

            tk.Label(
                risk_row,
                text=value,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["warning"],
                anchor="e"
            ).pack(side="right")

    def create_trading_control_center(self, parent):
        """创建交易控制中心"""
        control_frame = tk.LabelFrame(
            parent,
            text=ChineseUIConstants.TRADING_CONTROL_CENTER,
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=15,
            pady=15,
        )
        control_frame.pack(fill="x", padx=15, pady=15)

        # 连接按钮
        connect_btn = tk.Button(
            control_frame,
            text=ChineseUIConstants.CONNECT_EXCHANGE,
            command=self.connect_exchange,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=25,
            pady=8,
        )
        connect_btn.pack(fill="x", pady=3)

        # 交易模式选择
        mode_frame = tk.Frame(control_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        mode_frame.pack(fill="x", pady=10)

        tk.Label(
            mode_frame,
            text=ChineseUIConstants.MODE_LABEL,
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        self.mode_var = tk.StringVar(value=ChineseUIConstants.PAPER_MODE)
        mode_combo = ttk.Combobox(
            mode_frame,
            textvariable=self.mode_var,
            values=[ChineseUIConstants.PAPER_MODE, ChineseUIConstants.LIVE_MODE],
            state="readonly",
            width=10
        )
        mode_combo.pack(side="right")

        # 交易控制按钮
        self.create_trading_buttons(control_frame)

    def create_trading_buttons(self, parent):
        """创建交易控制按钮"""
        buttons_frame = tk.Frame(parent, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        buttons_frame.pack(fill="x", pady=10)

        # 开始交易
        self.start_trading_btn = tk.Button(
            buttons_frame,
            text=ChineseUIConstants.START_TRADING,
            command=self.start_trading,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=5,
        )
        self.start_trading_btn.pack(fill="x", pady=2)

        # 暂停交易
        self.pause_trading_btn = tk.Button(
            buttons_frame,
            text=ChineseUIConstants.PAUSE_TRADING,
            command=self.pause_trading,
            bg=self.PROFESSIONAL_COLORS["warning"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=5,
            state="disabled"
        )
        self.pause_trading_btn.pack(fill="x", pady=2)

        # 停止交易
        self.stop_trading_btn = tk.Button(
            buttons_frame,
            text=ChineseUIConstants.STOP_TRADING,
            command=self.stop_trading,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            padx=20,
            pady=5,
            state="disabled"
        )
        self.stop_trading_btn.pack(fill="x", pady=2)

    def create_account_info(self, parent):
        """创建账户信息面板"""
        account_frame = tk.LabelFrame(
            parent,
            text=ChineseUIConstants.ACCOUNT_INFO_PANEL,
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=15,
            pady=15,
        )
        account_frame.pack(fill="x", padx=15, pady=15)

        # 账户指标
        self.account_labels = {}
        account_items = [
            (f"{self.get_constant('ACCOUNT_BALANCE')}:", "balance", self.PROFESSIONAL_COLORS["text_primary"]),
            (f"{self.get_constant('ACCOUNT_EQUITY')}:", "equity", self.PROFESSIONAL_COLORS["info"]),
            (f"{self.get_constant('ACCOUNT_MARGIN')}:", "margin", self.PROFESSIONAL_COLORS["warning"]),
            (f"{self.get_constant('ACCOUNT_FREE_MARGIN')}:", "free_margin", self.PROFESSIONAL_COLORS["profit"]),
            (f"{self.get_constant('ACCOUNT_MARGIN_LEVEL')}:", "margin_level", self.PROFESSIONAL_COLORS["text_secondary"]),
            (f"{self.get_constant('ACCOUNT_UNREALIZED_PL')}:", "unrealized_pnl", self.PROFESSIONAL_COLORS["profit"]),
            (f"{self.get_constant('ACCOUNT_REALIZED_PL')}:", "realized_pnl", self.PROFESSIONAL_COLORS["profit"]),
        ]

        for i, (label_text, key, color) in enumerate(account_items):
            item_frame = tk.Frame(account_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
            item_frame.pack(fill="x", pady=2)

            tk.Label(
                item_frame,
                text=label_text,
                font=self.PROFESSIONAL_FONTS["body"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"],
                width=15,
                anchor="w"
            ).pack(side="left")

            self.account_labels[key] = tk.Label(
                item_frame,
                text="$0.00",
                font=self.PROFESSIONAL_FONTS["data"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=color,
                anchor="e"
            )
            self.account_labels[key].pack(side="right")

    def create_quick_trading(self, parent):
        """创建快速交易面板"""
        trading_frame = tk.LabelFrame(
            parent,
            text=self.get_constant('QUICK_TRADING_PANEL'),
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            padx=15,
            pady=15,
        )
        trading_frame.pack(fill="x", padx=15, pady=15)

        # 交易对选择
        symbol_frame = tk.Frame(trading_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        symbol_frame.pack(fill="x", pady=5)

        tk.Label(
            symbol_frame,
            text=self.get_constant('SYMBOL_LABEL'),
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        # 使用常量定义的交易对
        default_symbol = (TradingConstants.ALL_SUPPORTED_SYMBOLS[0]
                         if TradingConstants.ALL_SUPPORTED_SYMBOLS
                         else "BTC/USDT")
        self.symbol_var = tk.StringVar(value=default_symbol)
        symbol_combo = ttk.Combobox(
            symbol_frame,
            textvariable=self.symbol_var,
            values=TradingConstants.ALL_SUPPORTED_SYMBOLS,
            state="readonly",
            width=12
        )
        symbol_combo.pack(side="right")

        # 订单类型
        order_type_frame = tk.Frame(trading_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        order_type_frame.pack(fill="x", pady=5)

        tk.Label(
            order_type_frame,
            text=self.get_constant('ORDER_TYPE_LABEL'),
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        # 使用中文订单类型
        self.order_type_var = tk.StringVar(value=self.get_constant('ORDER_TYPE_MARKET'))
        order_type_combo = ttk.Combobox(
            order_type_frame,
            textvariable=self.order_type_var,
            values=self.get_constant('ALL_ORDER_TYPES'),
            state="readonly",
            width=12
        )
        order_type_combo.pack(side="right")

        # 数量输入
        quantity_frame = tk.Frame(trading_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        quantity_frame.pack(fill="x", pady=5)

        tk.Label(
            quantity_frame,
            text=self.get_constant('QUANTITY_LABEL'),
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        self.quantity_var = tk.StringVar(value="0.001")
        quantity_entry = tk.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            font=self.PROFESSIONAL_FONTS["data"],
            bg=self.PROFESSIONAL_COLORS["bg_panel"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            width=12
        )
        quantity_entry.pack(side="right")

        # 价格输入
        price_frame = tk.Frame(trading_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        price_frame.pack(fill="x", pady=5)

        tk.Label(
            price_frame,
            text=self.get_constant('PRICE_LABEL'),
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left")

        self.price_var = tk.StringVar(value="0.00")
        price_entry = tk.Entry(
            price_frame,
            textvariable=self.price_var,
            font=self.PROFESSIONAL_FONTS["data"],
            bg=self.PROFESSIONAL_COLORS["bg_panel"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            width=12
        )
        price_entry.pack(side="right")

        # 买卖按钮
        buttons_frame = tk.Frame(trading_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
        buttons_frame.pack(fill="x", pady=10)

        buy_btn = tk.Button(
            buttons_frame,
            text=self.get_constant('BUY_BUTTON'),
            command=self.place_buy_order,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            width=8
        )
        buy_btn.pack(side="left", padx=(0, 5))

        sell_btn = tk.Button(
            buttons_frame,
            text=self.get_constant('SELL_BUTTON'),
            command=self.place_sell_order,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"],
            relief="flat",
            width=8
        )
        sell_btn.pack(side="right", padx=(5, 0))

    def create_risk_management(self, parent):
        """创建风险管理面板"""
        risk_frame = tk.LabelFrame(
            parent,
            text="⚠️ RISK MANAGEMENT",
            font=self.PROFESSIONAL_FONTS["heading"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["warning"],
            padx=15,
            pady=15,
        )
        risk_frame.pack(fill="x", padx=15, pady=15)

        # 风险指标
        risk_items = [
            ("Max Position Size:", "5%"),
            ("Daily Loss Limit:", "2%"),
            ("Portfolio VaR:", "1.5%"),
            ("Margin Utilization:", "45%"),
        ]

        for label_text, value in risk_items:
            item_frame = tk.Frame(risk_frame, bg=self.PROFESSIONAL_COLORS["bg_secondary"])
            item_frame.pack(fill="x", pady=2)

            tk.Label(
                item_frame,
                text=label_text,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"],
                width=18,
                anchor="w"
            ).pack(side="left")

            tk.Label(
                item_frame,
                text=value,
                font=self.PROFESSIONAL_FONTS["small"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["warning"],
                anchor="e"
            ).pack(side="right")

    def setup_right_panel(self, parent):
        """设置右侧显示面板"""
        # 创建标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # 市场数据标签页
        self.create_market_data_tab()

        # 持仓管理标签页
        self.create_positions_tab()

        # 订单管理标签页
        self.create_orders_tab()

        # 交易历史标签页
        self.create_history_tab()

        # 图表分析标签页
        self.create_chart_tab()

        # 系统监控标签页
        self.create_monitor_tab()

    def create_market_data_tab(self):
        """创建市场数据标签页"""
        market_frame = tk.Frame(self.notebook, bg=self.PROFESSIONAL_COLORS["bg_primary"])
        self.notebook.add(market_frame, text=ChineseUIConstants.TAB_MARKET_DATA)

        # 市场数据表格
        columns = ChineseUIConstants.MARKET_COLUMNS

        self.market_tree = ttk.Treeview(
            market_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # 设置列标题
        for col in columns:
            self.market_tree.heading(col, text=col)
            self.market_tree.column(col, width=100, anchor="center")

        # 滚动条
        market_scroll = ttk.Scrollbar(
            market_frame,
            orient="vertical",
            command=self.market_tree.yview
        )
        self.market_tree.configure(yscrollcommand=market_scroll.set)

        # 布局
        self.market_tree.pack(side="left", fill="both", expand=True)
        market_scroll.pack(side="right", fill="y")

        # 刷新按钮
        refresh_btn = tk.Button(
            market_frame,
            text="🔄 REFRESH",
            command=self.refresh_market_data,
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        )
        refresh_btn.pack(side="bottom", pady=10)

    def create_positions_tab(self):
        """创建持仓管理标签页"""
        positions_frame = tk.Frame(
            self.data_notebook,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.data_notebook.add(positions_frame, text=self.get_constant('TAB_POSITIONS'))

        # 持仓表格
        pos_columns = self.get_constant('POSITION_COLUMNS')

        self.positions_tree = ttk.Treeview(
            positions_frame,
            columns=pos_columns,
            show="headings",
            height=12
        )

        for col in pos_columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=120, anchor="center")

        pos_scroll = ttk.Scrollbar(
            positions_frame,
            orient="vertical",
            command=self.positions_tree.yview
        )
        self.positions_tree.configure(yscrollcommand=pos_scroll.set)

        self.positions_tree.pack(side="left", fill="both", expand=True)
        pos_scroll.pack(side="right", fill="y")

        # 持仓操作按钮
        pos_buttons_frame = tk.Frame(
            positions_frame,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        pos_buttons_frame.pack(side="bottom", fill="x", pady=10)

        tk.Button(
            pos_buttons_frame,
            text="🔄 REFRESH",
            command=self.refresh_positions,
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            pos_buttons_frame,
            text="📊 ANALYZE",
            command=self.analyze_positions,
            bg=self.PROFESSIONAL_COLORS["warning"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            pos_buttons_frame,
            text="🔴 CLOSE ALL",
            command=self.close_all_positions,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="right", padx=5)

    def create_orders_tab(self):
        """创建订单管理标签页"""
        orders_frame = tk.Frame(
            self.data_notebook,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.data_notebook.add(orders_frame, text=self.get_constant('TAB_ORDERS'))

        # 订单表格
        order_columns = self.get_constant('ORDER_COLUMNS')

        self.orders_tree = ttk.Treeview(
            orders_frame,
            columns=order_columns,
            show="headings",
            height=12
        )

        for col in order_columns:
            self.orders_tree.heading(col, text=col)
            self.orders_tree.column(col, width=100, anchor="center")

        order_scroll = ttk.Scrollbar(
            orders_frame,
            orient="vertical",
            command=self.orders_tree.yview
        )
        self.orders_tree.configure(yscrollcommand=order_scroll.set)

        self.orders_tree.pack(side="left", fill="both", expand=True)
        order_scroll.pack(side="right", fill="y")

        # 订单操作按钮
        order_buttons_frame = tk.Frame(
            orders_frame,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        order_buttons_frame.pack(side="bottom", fill="x", pady=10)

        tk.Button(
            order_buttons_frame,
            text="🔄 REFRESH",
            command=self.refresh_orders,
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            order_buttons_frame,
            text="❌ CANCEL SELECTED",
            command=self.cancel_selected_order,
            bg=self.PROFESSIONAL_COLORS["warning"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            order_buttons_frame,
            text="🔴 CANCEL ALL",
            command=self.cancel_all_orders,
            bg=self.PROFESSIONAL_COLORS["loss"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="right", padx=5)

    def create_history_tab(self):
        """创建交易历史标签页"""
        history_frame = tk.Frame(
            self.data_notebook,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.data_notebook.add(history_frame, text=self.get_constant('TAB_HISTORY'))

        # 历史表格
        history_columns = self.get_constant('HISTORY_COLUMNS')

        self.history_tree = ttk.Treeview(
            history_frame,
            columns=history_columns,
            show="headings",
            height=12
        )

        for col in history_columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=110, anchor="center")

        history_scroll = ttk.Scrollbar(
            history_frame,
            orient="vertical",
            command=self.history_tree.yview
        )
        self.history_tree.configure(yscrollcommand=history_scroll.set)

        self.history_tree.pack(side="left", fill="both", expand=True)
        history_scroll.pack(side="right", fill="y")

        # 历史操作按钮
        history_buttons_frame = tk.Frame(
            history_frame,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        history_buttons_frame.pack(side="bottom", fill="x", pady=10)

        tk.Button(
            history_buttons_frame,
            text="🔄 REFRESH",
            command=self.refresh_history,
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            history_buttons_frame,
            text="📊 REPORT",
            command=self.generate_report,
            bg=self.PROFESSIONAL_COLORS["warning"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="left", padx=5)

        tk.Button(
            history_buttons_frame,
            text="💾 EXPORT",
            command=self.export_history,
            bg=self.PROFESSIONAL_COLORS["profit"],
            fg=self.PROFESSIONAL_COLORS["bg_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="right", padx=5)

    def create_chart_tab(self):
        """创建图表分析标签页"""
        chart_frame = tk.Frame(
            self.notebook,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.notebook.add(chart_frame, text=ChineseUIConstants.TAB_CHARTS)

        # 图表控制面板
        chart_control_frame = tk.Frame(
            chart_frame,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            height=50
        )
        chart_control_frame.pack(fill="x", padx=10, pady=5)
        chart_control_frame.pack_propagate(False)

        # 交易对选择
        tk.Label(
            chart_control_frame,
            text="Symbol:",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left", padx=10, pady=10)

        self.chart_symbol_var = tk.StringVar(value="BTC/USDT")
        chart_symbol_combo = ttk.Combobox(
            chart_control_frame,
            textvariable=self.chart_symbol_var,
            values=["BTC/USDT", "ETH/USDT", "SOL/USDT"],
            state="readonly",
            width=12
        )
        chart_symbol_combo.pack(side="left", padx=5, pady=10)

        # 时间周期选择
        tk.Label(
            chart_control_frame,
            text="Timeframe:",
            font=self.PROFESSIONAL_FONTS["body"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_primary"]
        ).pack(side="left", padx=10, pady=10)

        self.chart_timeframe_var = tk.StringVar(value="1h")
        chart_timeframe_combo = ttk.Combobox(
            chart_control_frame,
            textvariable=self.chart_timeframe_var,
            values=["1m", "5m", "15m", "1h", "4h", "1d"],
            state="readonly",
            width=8
        )
        chart_timeframe_combo.pack(side="left", padx=5, pady=10)

        # 更新按钮
        tk.Button(
            chart_control_frame,
            text="🔄 UPDATE",
            command=self.update_chart,
            bg=self.PROFESSIONAL_COLORS["info"],
            fg=self.PROFESSIONAL_COLORS["text_primary"],
            font=self.PROFESSIONAL_FONTS["body"]
        ).pack(side="right", padx=10, pady=10)

        # 图表显示区域
        self.chart_display_frame = tk.Frame(
            chart_frame,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.chart_display_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 如果图表系统可用，创建图表
        if CHART_SYSTEM_AVAILABLE:
            try:
                self.chart = CandlestickChart(self.chart_display_frame)
            except Exception as e:
                self.log_message(f"图表初始化失败: {e}")
                self.chart = None
        else:
            # 显示占位符
            placeholder_label = tk.Label(
                self.chart_display_frame,
                text="📈 Chart System Not Available\nInstall required dependencies",
                font=self.PROFESSIONAL_FONTS["heading"],
                bg=self.PROFESSIONAL_COLORS["bg_primary"],
                fg=self.PROFESSIONAL_COLORS["text_secondary"]
            )
            placeholder_label.pack(expand=True)

    def create_monitor_tab(self):
        """创建系统监控标签页"""
        monitor_frame = tk.Frame(
            self.notebook,
            bg=self.PROFESSIONAL_COLORS["bg_primary"]
        )
        self.notebook.add(monitor_frame, text=ChineseUIConstants.TAB_MONITOR)

        # 系统状态文本
        self.monitor_text = tk.Text(
            monitor_frame,
            height=20,
            font=self.PROFESSIONAL_FONTS["monospace"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_accent"],
            insertbackground=self.PROFESSIONAL_COLORS["text_primary"],
        )

        monitor_scroll = tk.Scrollbar(
            monitor_frame,
            orient="vertical",
            command=self.monitor_text.yview
        )
        self.monitor_text.configure(yscrollcommand=monitor_scroll.set)

        self.monitor_text.pack(side="left", fill="both", expand=True)
        monitor_scroll.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(
            self.root,
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            height=30
        )
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)

        # 状态信息
        self.status_label = tk.Label(
            status_frame,
            text=self.get_constant('STATUS_READY'),
            font=self.PROFESSIONAL_FONTS["small"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_secondary"],
        )
        self.status_label.pack(side="left", padx=10, pady=5)

        # 时间显示
        self.time_label = tk.Label(
            status_frame,
            text="",
            font=self.PROFESSIONAL_FONTS["small"],
            bg=self.PROFESSIONAL_COLORS["bg_secondary"],
            fg=self.PROFESSIONAL_COLORS["text_secondary"],
        )
        self.time_label.pack(side="right", padx=10, pady=5)

        # 启动时间更新
        self.update_time()

    def init_trading_system(self):
        """初始化交易系统"""
        try:
            # 延迟初始化交易核心，等待API连接器准备就绪
            if TRADING_CORE_AVAILABLE:
                self.trading_core = None  # 延迟初始化
                self.log_message("⚠️ Trading core will be initialized after API connection")
            else:
                self.log_message("⚠️ Trading core not available")

            # 初始化API连接器
            if API_AVAILABLE:
                self.api_connector = gate_api
                self.log_message("✅ API connector initialized")
            else:
                self.log_message("⚠️ API connector not available")

        except Exception as e:
            self.log_message(f"❌ Trading system initialization failed: {e}")

    def initialize_trading_core_with_api(self):
        """使用API连接器初始化交易核心"""
        try:
            if TRADING_CORE_AVAILABLE and self.api_connector:
                # 配置交易系统
                trading_config = {
                    'database_path': 'database/trading.db',
                    'risk_config': {
                        'max_position_size': 0.25,
                        'max_total_exposure': 0.80,
                        'max_daily_loss': 0.05,
                        'stop_loss_pct': 0.02,
                        'take_profit_pct': 0.06
                    },
                    'max_concurrent_executions': 3
                }

                self.trading_core = get_trading_system_core(self.api_connector, trading_config)
                self.log_message("✅ Trading system initialized with API connector")
                return True
            else:
                self.log_message("⚠️ Cannot initialize trading core: missing API connector")
                return False
        except Exception as e:
            self.log_message(f"❌ Trading core initialization failed: {e}")
            return False

    # ==================== 交易控制方法 ====================

    def emergency_stop(self):
        """紧急停止所有交易"""
        try:
            self.log_message("🔴 EMERGENCY STOP ACTIVATED")

            # 停止所有交易
            self.is_trading = False

            # 取消所有订单
            self.cancel_all_orders()

            # 平仓所有持仓
            self.close_all_positions()

            # 更新按钮状态
            self.update_trading_buttons()

            # 更新状态指示器
            self.status_indicator.configure(
                text=self.get_constant('STATUS_EMERGENCY_STOP'),
                fg=self.PROFESSIONAL_COLORS["loss"]
            )

            messagebox.showwarning(
                self.get_constant('DIALOG_EMERGENCY_STOP'),
                self.get_constant('MSG_EMERGENCY_STOP')
            )

        except Exception as e:
            self.log_message(f"❌ Emergency stop failed: {e}")

    def connect_exchange(self):
        """连接交易所"""
        try:
            if not API_AVAILABLE:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    "API连接器不可用"
                )
                return

            # 显示API登录对话框
            if show_api_login_dialog(self.root):
                self.is_connected = True

                # 更新连接状态显示
                if hasattr(self, 'connection_status_label'):
                    self.connection_status_label.configure(
                        text="● 已连接",
                        fg=self.PROFESSIONAL_COLORS["connected"]
                    )

                # 更新主状态指示器
                self.status_indicator.configure(
                    text=self.get_constant('STATUS_CONNECTED'),
                    fg=self.PROFESSIONAL_COLORS["connected"]
                )

                # 更新按钮状态
                self.connect_btn.configure(state="disabled")
                self.disconnect_btn.configure(state="normal")

                self.log_message("✅ Connected to exchange")

                # 初始化交易核心
                if self.initialize_trading_core_with_api():
                    self.log_message("✅ Trading system ready")

                self.update_account_data()
            else:
                self.log_message("❌ Connection failed")

        except Exception as e:
            self.log_message(f"❌ Connection error: {e}")

    def start_trading(self):
        """开始交易"""
        try:
            if not self.is_connected:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    "请先连接到交易所"
                )
                return

            # 确认交易模式
            mode = self.mode_var.get()
            if mode == self.get_constant('LIVE_MODE'):
                result = messagebox.askyesno(
                    self.get_constant('DIALOG_CONFIRM_LIVE_TRADING'),
                    "确认开始实盘交易？\n这将使用真实资金进行交易！"
                )
                if not result:
                    return

            self.is_trading = True
            self.trading_mode = mode

            self.log_message(f"🟢 {self.get_constant('TRADING_MODE_STARTED')} ({mode})")

            # 更新按钮状态
            self.update_trading_buttons()

            # 更新状态指示器
            self.status_indicator.configure(
                text=f"{self.get_constant('STATUS_TRADING')} ({mode})",
                fg=self.PROFESSIONAL_COLORS["trading"]
            )

            # 启动交易线程
            self.start_trading_thread()

        except Exception as e:
            self.log_message(f"❌ Start trading failed: {e}")

    def pause_trading(self):
        """暂停交易"""
        try:
            self.is_trading = False
            self.log_message(f"🟡 {self.get_constant('TRADING_MODE_PAUSED')}")

            self.update_trading_buttons()
            self.status_indicator.configure(
                text="● 已暂停",
                fg=self.PROFESSIONAL_COLORS["paused"]
            )

        except Exception as e:
            self.log_message(f"❌ Pause trading failed: {e}")

    def stop_trading(self):
        """停止交易"""
        try:
            self.is_trading = False
            self.log_message(f"🔴 {self.get_constant('TRADING_MODE_STOPPED')}")

            self.update_trading_buttons()
            self.status_indicator.configure(
                text="● 已停止",
                fg=self.PROFESSIONAL_COLORS["loss"]
            )

        except Exception as e:
            self.log_message(f"❌ Stop trading failed: {e}")

    def update_trading_buttons(self):
        """更新交易按钮状态"""
        if self.is_trading:
            self.start_trading_btn.configure(state="disabled")
            self.pause_trading_btn.configure(state="normal")
            self.stop_trading_btn.configure(state="normal")
        else:
            self.start_trading_btn.configure(state="normal")
            self.pause_trading_btn.configure(state="disabled")
            self.stop_trading_btn.configure(state="disabled")

    def start_trading_thread(self):
        """启动交易线程"""
        def trading_loop():
            while self.is_trading:
                try:
                    # 更新市场数据
                    self.update_market_data()

                    # 更新账户数据
                    self.update_account_data()

                    # 执行交易逻辑
                    if self.trading_core:
                        self.trading_core.process_trading_signals()

                    time.sleep(1)  # 1秒更新一次

                except Exception as e:
                    self.log_message(f"❌ Trading loop error: {e}")
                    time.sleep(5)

        trading_thread = threading.Thread(target=trading_loop, daemon=True)
        trading_thread.start()

    # ==================== 交易操作方法 ====================

    def place_buy_order(self):
        """下买单"""
        try:
            symbol = self.symbol_var.get()
            order_type = self.order_type_var.get()
            quantity = float(self.quantity_var.get())
            price = (float(self.price_var.get())
                    if order_type != self.get_constant('ORDER_TYPE_MARKET')
                    else 0)

            self.log_message(
                f"🟢 {self.get_constant('MSG_PLACING_BUY_ORDER')}: {quantity} {symbol} "
                f"@ {price if price > 0 else '市价'}"
            )

            # 执行风险检查
            if not self._validate_order_risk(symbol, 'BUY', quantity, price):
                return

            # 执行订单
            order_result = self._execute_order(symbol, 'BUY', order_type, quantity, price)

            if order_result['success']:
                messagebox.showinfo(
                    self.get_constant('DIALOG_ORDER_PLACED'),
                    f"{self.get_constant('MSG_BUY_ORDER_SUCCESS')}\n"
                    f"订单号: {order_result.get('order_id', 'N/A')}\n"
                    f"交易对: {symbol}\n"
                    f"数量: {quantity}\n"
                    f"类型: {order_type}"
                )

                # 刷新订单和持仓数据
                self.refresh_orders()
                self.refresh_positions()
                self.update_account_data()
            else:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    f"{self.get_constant('MSG_ORDER_FAILED')}: {order_result.get('message', '未知错误')}"
                )

        except ValueError:
            messagebox.showerror(
                self.get_constant('DIALOG_ERROR'),
                "无效的数量或价格输入"
            )
        except Exception as e:
            self.log_message(f"❌ 买单失败: {e}")
            messagebox.showerror(
                self.get_constant('DIALOG_ERROR'),
                f"下单失败: {e}"
            )

    def place_sell_order(self):
        """下卖单"""
        try:
            symbol = self.symbol_var.get()
            order_type = self.order_type_var.get()
            quantity = float(self.quantity_var.get())
            price = (float(self.price_var.get())
                    if order_type != self.get_constant('ORDER_TYPE_MARKET')
                    else 0)

            self.log_message(
                f"🔴 {self.get_constant('MSG_PLACING_SELL_ORDER')}: {quantity} {symbol} "
                f"@ {price if price > 0 else '市价'}"
            )

            # 模拟订单确认
            messagebox.showinfo(
                self.get_constant('DIALOG_ORDER_PLACED'),
                f"{self.get_constant('MSG_SELL_ORDER_SUCCESS')}\n"
                f"交易对: {symbol}\n"
                f"数量: {quantity}\n"
                f"类型: {order_type}"
            )

        except ValueError:
            messagebox.showerror(
                self.get_constant('DIALOG_ERROR'),
                "无效的数量或价格输入"
            )
        except Exception as e:
            self.log_message(f"❌ 卖单失败: {e}")

    # ==================== 订单执行和风险管理方法 ====================

    def _validate_order_risk(self, symbol, side, quantity, price):
        """验证订单风险"""
        try:
            # 基本输入验证
            if not symbol or symbol not in TradingConstants.ALL_SUPPORTED_SYMBOLS:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    "无效的交易对"
                )
                return False

            if quantity <= 0:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    "数量必须大于0"
                )
                return False

            if price < 0:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    "价格不能为负数"
                )
                return False

            # 检查账户余额
            if hasattr(self, 'account_data') and self.account_data:
                available_balance = self.account_data.get('free_margin', 0)
                estimated_cost = quantity * (price if price > 0 else 50000)  # 估算成本

                if estimated_cost > available_balance:
                    messagebox.showerror(
                        self.get_constant('DIALOG_ERROR'),
                        f"余额不足。需要: ${estimated_cost:.2f}, 可用: ${available_balance:.2f}"
                    )
                    return False

            # 检查最大持仓限制
            max_position_value = 50000  # 最大持仓价值
            if quantity * (price if price > 0 else 50000) > max_position_value:
                messagebox.showerror(
                    self.get_constant('DIALOG_ERROR'),
                    f"订单金额超过最大限制 ${max_position_value:,.0f}"
                )
                return False

            return True

        except Exception as e:
            self.log_message(f"❌ 风险检查失败: {e}")
            return False

    def _execute_order(self, symbol, side, order_type, quantity, price):
        """执行订单"""
        try:
            # 如果有API连接器，尝试真实下单
            if self.api_connector and hasattr(self.api_connector, 'create_order'):
                return self._execute_real_order(symbol, side, order_type, quantity, price)
            else:
                # 模拟订单执行
                return self._execute_simulated_order(symbol, side, order_type, quantity, price)

        except Exception as e:
            self.log_message(f"❌ 订单执行失败: {e}")
            return {
                'success': False,
                'message': f"订单执行失败: {e}",
                'order_id': None
            }

    def _execute_real_order(self, symbol, side, order_type, quantity, price):
        """执行真实订单"""
        try:
            # 构建订单参数
            order_params = {
                'symbol': symbol,
                'side': side.lower(),
                'type': 'market' if order_type == self.get_constant('ORDER_TYPE_MARKET') else 'limit',
                'amount': quantity
            }

            # 添加价格参数（限价单）
            if order_type != self.get_constant('ORDER_TYPE_MARKET') and price > 0:
                order_params['price'] = price

            # 调用API下单
            result = self.api_connector.create_order(**order_params)

            if result and result.get('id'):
                self.log_message(f"✅ 订单提交成功: {result['id']}")
                return {
                    'success': True,
                    'message': '订单提交成功',
                    'order_id': result['id'],
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'message': '订单提交失败',
                    'order_id': None
                }

        except Exception as e:
            self.log_message(f"❌ 真实订单执行失败: {e}")
            return {
                'success': False,
                'message': f"订单执行失败: {e}",
                'order_id': None
            }

    def _execute_simulated_order(self, symbol, side, order_type, quantity, price):
        """执行模拟订单"""
        try:
            import uuid
            import random

            # 生成模拟订单ID
            order_id = f"SIM_{uuid.uuid4().hex[:8].upper()}"

            # 模拟订单处理延迟
            import time
            time.sleep(0.1)

            # 90%成功率的模拟
            if random.random() < 0.9:
                self.log_message(f"✅ 模拟订单执行成功: {order_id}")
                return {
                    'success': True,
                    'message': '模拟订单执行成功',
                    'order_id': order_id,
                    'simulated': True
                }
            else:
                return {
                    'success': False,
                    'message': '模拟订单执行失败（随机失败）',
                    'order_id': None
                }

        except Exception as e:
            return {
                'success': False,
                'message': f"模拟订单执行失败: {e}",
                'order_id': None
            }

    # ==================== 数据更新方法 ====================

    def refresh_market_data(self):
        """刷新市场数据"""
        try:
            self.log_message(self.get_constant('MSG_REFRESHING_MARKET'))

            # 清空现有数据
            for item in self.market_tree.get_children():
                self.market_tree.delete(item)

            # 模拟市场数据 - 使用常量定义的交易对
            import random
            symbols = TradingConstants.ALL_SUPPORTED_SYMBOLS[:5]  # 取前5个

            for symbol in symbols:
                last_price = random.uniform(100, 50000)
                change = random.uniform(-5, 5)
                change_pct = change / last_price * 100
                volume = random.uniform(1000000, 10000000)
                high = last_price * random.uniform(1.01, 1.05)
                low = last_price * random.uniform(0.95, 0.99)
                vwap = (high + low + last_price) / 3

                self.market_tree.insert("", "end", values=(
                    symbol,
                    f"${last_price:.2f}",
                    f"${change:.2f}",
                    f"{change_pct:.2f}%",
                    f"${volume:,.0f}",
                    f"${high:.2f}",
                    f"${low:.2f}",
                    f"${vwap:.2f}"
                ))

            self.log_message(self.get_constant('MSG_MARKET_REFRESHED'))

        except Exception as e:
            self.log_message(f"❌ 市场数据刷新失败: {e}")

    def refresh_positions(self):
        """刷新持仓数据"""
        try:
            self.log_message("🔄 Refreshing positions...")

            # 清空现有数据
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)

            # 模拟持仓数据
            import random
            if random.random() > 0.5:  # 50%概率有持仓
                # 使用常量定义的交易对
                symbol1 = TradingConstants.ALL_SUPPORTED_SYMBOLS[0]
                symbol2 = TradingConstants.ALL_SUPPORTED_SYMBOLS[1] if len(TradingConstants.ALL_SUPPORTED_SYMBOLS) > 1 else symbol1
                positions = [
                    (symbol1, "LONG", "0.1", "45000", "46000", "100", "2.22%", "4500"),
                    (symbol2, "SHORT", "2.0", "2800", "2750", "100", "1.79%", "5600"),
                ]

                for pos in positions:
                    self.positions_tree.insert("", "end", values=pos)

            self.log_message("✅ Positions refreshed")

        except Exception as e:
            self.log_message(f"❌ Positions refresh failed: {e}")

    def refresh_orders(self):
        """刷新订单数据"""
        try:
            self.log_message("🔄 Refreshing orders...")

            # 清空现有数据
            for item in self.orders_tree.get_children():
                self.orders_tree.delete(item)

            # 模拟订单数据
            import random
            if random.random() > 0.3:  # 70%概率有订单
                # 使用常量定义的交易对和订单类型
                symbol1 = TradingConstants.ALL_SUPPORTED_SYMBOLS[0]
                symbol2 = (TradingConstants.ALL_SUPPORTED_SYMBOLS[1]
                          if len(TradingConstants.ALL_SUPPORTED_SYMBOLS) > 1
                          else symbol1)
                orders = [
                    ("ORD001", symbol1, TradingConstants.ORDER_SIDE_BUY,
                     TradingConstants.ORDER_TYPE_LIMIT, "0.05", "44000",
                     TradingConstants.ORDER_STATUS_PENDING, "10:30:15"),
                    ("ORD002", symbol2, TradingConstants.ORDER_SIDE_SELL,
                     TradingConstants.ORDER_TYPE_STOP, "1.0", "2900",
                     TradingConstants.ORDER_STATUS_PENDING, "10:25:30"),
                ]

                for order in orders:
                    self.orders_tree.insert("", "end", values=order)

            self.log_message("✅ Orders refreshed")

        except Exception as e:
            self.log_message(f"❌ Orders refresh failed: {e}")

    def refresh_history(self):
        """刷新交易历史"""
        try:
            self.log_message("🔄 Refreshing history...")

            # 清空现有数据
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # 模拟历史数据 - 使用常量定义的交易对
            symbols = TradingConstants.ALL_SUPPORTED_SYMBOLS[:3]  # 取前3个
            history = [
                ("10:15:30", symbols[0], TradingConstants.ORDER_SIDE_BUY,
                 "0.1", "45000", "4.5", "50", "10050"),
                ("09:45:15", symbols[1] if len(symbols) > 1 else symbols[0],
                 TradingConstants.ORDER_SIDE_SELL, "2.0", "2800", "2.8", "-30", "10000"),
                ("09:30:00", symbols[2] if len(symbols) > 2 else symbols[0],
                 TradingConstants.ORDER_SIDE_BUY, "10", "120", "1.2", "80", "10030"),
            ]

            for trade in history:
                self.history_tree.insert("", "end", values=trade)

            self.log_message("✅ History refreshed")

        except Exception as e:
            self.log_message(f"❌ History refresh failed: {e}")

    def update_account_data(self):
        """更新账户数据"""
        try:
            if not self.api_connector:
                # 如果没有API连接器，使用模拟数据
                self._update_simulated_account_data()
                return

            # 获取实际账户数据
            account_data = self._fetch_real_account_data()

            if account_data:
                self.account_data = account_data
                self._update_account_display()
                self.log_message("✅ 账户数据已更新")
            else:
                self.log_message("⚠️ 未获取到账户数据")
                # 使用模拟数据作为备用
                self._update_simulated_account_data()

        except Exception as e:
            self.log_message(f"❌ Account data update failed: {e}")
            # 发生错误时使用模拟数据
            self._update_simulated_account_data()

    def _fetch_real_account_data(self):
        """获取真实账户数据"""
        try:
            # 调用API获取账户信息
            account_info = self.api_connector.get_account_info()

            if account_info:
                # 解析账户数据
                balance = float(account_info.get('available', {}).get('USDT', 0))

                # 获取持仓信息计算权益
                positions = self.api_connector.get_positions() or []
                unrealized_pnl = sum(float(pos.get('unrealized_pnl', 0)) for pos in positions)

                equity = balance + unrealized_pnl

                # 计算已用保证金
                margin_used = sum(float(pos.get('margin', 0)) for pos in positions)
                free_margin = balance - margin_used

                # 计算保证金比例
                margin_level = (equity / margin_used * 100) if margin_used > 0 else 0

                # 获取今日已实现盈亏（这里简化处理）
                realized_pnl = 0  # 需要从交易历史计算

                return {
                    "balance": balance,
                    "equity": equity,
                    "margin": margin_used,
                    "free_margin": free_margin,
                    "margin_level": margin_level,
                    "unrealized_pnl": unrealized_pnl,
                    "realized_pnl": realized_pnl,
                }

            return None

        except Exception as e:
            self.log_message(f"❌ 获取账户数据失败: {e}")
            return None

    def _update_simulated_account_data(self):
        """更新模拟账户数据"""
        try:
            import random

            # 生成合理的模拟数据
            base_balance = 10000
            balance = base_balance + random.uniform(-2000, 5000)
            unrealized_pnl = random.uniform(-500, 800)
            equity = balance + unrealized_pnl
            margin = random.uniform(500, 2000)
            free_margin = balance - margin
            margin_level = (equity / margin * 100) if margin > 0 else 0
            realized_pnl = random.uniform(-200, 600)

            self.account_data = {
                "balance": balance,
                "equity": equity,
                "margin": margin,
                "free_margin": free_margin,
                "margin_level": margin_level,
                "unrealized_pnl": unrealized_pnl,
                "realized_pnl": realized_pnl,
            }

            self._update_account_display()
            self.log_message("✅ 模拟账户数据已更新")

        except Exception as e:
            self.log_message(f"❌ 模拟账户数据更新失败: {e}")

    def _update_account_display(self):
        """更新账户数据显示"""
        try:
            # 更新主要账户摘要
            if hasattr(self, 'account_summary_labels'):
                for key, value in self.account_data.items():
                    if key in self.account_summary_labels:
                        if key == "unrealized_pnl":
                            color = (self.PROFESSIONAL_COLORS["profit"]
                                   if value >= 0
                                   else self.PROFESSIONAL_COLORS["loss"])
                            self.account_summary_labels[key].configure(
                                text=f"${value:+.2f}",
                                fg=color
                            )
                        else:
                            self.account_summary_labels[key].configure(
                                text=f"${value:.2f}"
                            )

            # 更新详细账户标签
            if hasattr(self, 'account_labels'):
                for key, value in self.account_data.items():
                    if key in self.account_labels:
                        if key in ["unrealized_pnl", "realized_pnl"]:
                            color = (self.PROFESSIONAL_COLORS["profit"]
                                    if value >= 0
                                    else self.PROFESSIONAL_COLORS["loss"])
                            self.account_labels[key].configure(
                                text=f"${value:+.2f}",
                                fg=color
                            )
                        elif key == "margin_level":
                            # 保证金比例用百分比显示
                            color = self.PROFESSIONAL_COLORS["profit"] if value > 200 else self.PROFESSIONAL_COLORS["warning"]
                            self.account_labels[key].configure(
                                text=f"{value:.1f}%",
                                fg=color
                            )
                        else:
                            self.account_labels[key].configure(text=f"${value:.2f}")

        except Exception as e:
            self.log_message(f"❌ 账户显示更新失败: {e}")

    def update_market_data(self):
        """更新市场数据"""
        try:
            if not self.api_connector:
                # 如果没有API连接器，使用模拟数据
                self._update_simulated_market_data()
                return

            # 获取实际市场数据
            market_data = self._fetch_real_market_data()

            if market_data:
                self._update_market_display(market_data)
                self.log_message("✅ 市场数据已更新")
            else:
                self.log_message("⚠️ 未获取到市场数据")

        except Exception as e:
            self.log_message(f"❌ Market data update failed: {e}")
            # 发生错误时使用模拟数据
            self._update_simulated_market_data()

    def _fetch_real_market_data(self):
        """获取真实市场数据"""
        try:
            market_data = {}

            # 获取支持的交易对数据
            for symbol in TradingConstants.ALL_SUPPORTED_SYMBOLS[:10]:  # 限制数量避免过载
                try:
                    # 调用API获取单个交易对数据
                    ticker_data = self.api_connector.get_ticker(symbol)

                    if ticker_data:
                        market_data[symbol] = {
                            'last_price': ticker_data.get('last', 0),
                            'change': ticker_data.get('change_percentage', 0),
                            'volume': ticker_data.get('base_volume', 0),
                            'high': ticker_data.get('high_24h', 0),
                            'low': ticker_data.get('low_24h', 0),
                            'timestamp': datetime.now()
                        }

                except Exception as e:
                    self.log_message(f"⚠️ 获取 {symbol} 数据失败: {e}")
                    continue

            return market_data

        except Exception as e:
            self.log_message(f"❌ 获取市场数据失败: {e}")
            return None

    def _update_simulated_market_data(self):
        """更新模拟市场数据"""
        try:
            import random

            # 清空现有数据
            for item in self.market_tree.get_children():
                self.market_tree.delete(item)

            # 生成模拟数据
            symbols = TradingConstants.ALL_SUPPORTED_SYMBOLS[:8]  # 取前8个

            for symbol in symbols:
                # 生成随机但合理的价格数据
                base_price = random.uniform(100, 50000)
                change_pct = random.uniform(-8, 8)
                change_amount = base_price * change_pct / 100
                last_price = base_price + change_amount

                volume = random.uniform(1000000, 50000000)
                high = last_price * random.uniform(1.01, 1.08)
                low = last_price * random.uniform(0.92, 0.99)
                vwap = (high + low + last_price) / 3

                # 格式化数据
                price_str = f"${last_price:.2f}" if last_price < 1000 else f"${last_price:,.0f}"
                change_str = f"${change_amount:+.2f}"
                change_pct_str = f"{change_pct:+.2f}%"
                volume_str = f"${volume:,.0f}"
                high_str = f"${high:.2f}" if high < 1000 else f"${high:,.0f}"
                low_str = f"${low:.2f}" if low < 1000 else f"${low:,.0f}"
                vwap_str = f"${vwap:.2f}" if vwap < 1000 else f"${vwap:,.0f}"

                # 插入到表格
                self.market_tree.insert("", "end", values=(
                    symbol,
                    price_str,
                    change_str,
                    change_pct_str,
                    volume_str,
                    high_str,
                    low_str,
                    vwap_str
                ))

            self.log_message("✅ 模拟市场数据已更新")

        except Exception as e:
            self.log_message(f"❌ 模拟数据更新失败: {e}")

    def _update_market_display(self, market_data):
        """更新市场数据显示"""
        try:
            # 清空现有数据
            for item in self.market_tree.get_children():
                self.market_tree.delete(item)

            # 更新真实数据
            for symbol, data in market_data.items():
                last_price = data['last_price']
                change = data['change']
                volume = data['volume']
                high = data['high']
                low = data['low']

                # 计算VWAP
                vwap = (high + low + last_price) / 3

                # 格式化显示
                price_str = f"${last_price:.2f}" if last_price < 1000 else f"${last_price:,.0f}"
                change_str = f"{change:+.2f}%"
                volume_str = f"${volume:,.0f}"
                high_str = f"${high:.2f}" if high < 1000 else f"${high:,.0f}"
                low_str = f"${low:.2f}" if low < 1000 else f"${low:,.0f}"
                vwap_str = f"${vwap:.2f}" if vwap < 1000 else f"${vwap:,.0f}"

                # 插入数据
                self.market_tree.insert("", "end", values=(
                    symbol,
                    price_str,
                    "N/A",  # 变化金额
                    change_str,
                    volume_str,
                    high_str,
                    low_str,
                    vwap_str
                ))

        except Exception as e:
            self.log_message(f"❌ 市场数据显示更新失败: {e}")

    # ==================== 操作方法 ====================

    def analyze_positions(self):
        """分析持仓"""
        try:
            self.log_message("📊 Analyzing positions...")

            analysis_text = """
📊 POSITION ANALYSIS REPORT
========================================

Portfolio Summary:
• Total Positions: 2
• Total Exposure: $15,100
• Net P&L: +$200 (+1.32%)
• Risk Level: MEDIUM

Individual Positions:
1. BTC/USDT LONG
   - Size: 0.1 BTC
   - Entry: $45,000
   - Current: $46,000
   - P&L: +$100 (*****%)
   - Risk: LOW

2. ETH/USDT SHORT
   - Size: 2.0 ETH
   - Entry: $2,800
   - Current: $2,750
   - P&L: +$100 (*****%)
   - Risk: MEDIUM

Recommendations:
• Consider taking partial profits on BTC position
• Monitor ETH position closely
• Maintain current risk levels
"""

            messagebox.showinfo(self.get_constant('DIALOG_POSITION_ANALYSIS'), analysis_text)
            self.log_message("✅ Position analysis completed")

        except Exception as e:
            self.log_message(f"❌ Position analysis failed: {e}")

    def close_all_positions(self):
        """平仓所有持仓"""
        try:
            result = messagebox.askyesno(
                self.get_constant('DIALOG_CONFIRM_CLOSE_ALL'),
                self.get_constant('MSG_CONFIRM_CLOSE_ALL_POSITIONS')
            )

            if result:
                self.log_message("🔴 Closing all positions...")

                # 这里应该调用实际的平仓API
                # for position in self.positions:
                #     self.api_connector.close_position(position)

                # 清空持仓显示
                for item in self.positions_tree.get_children():
                    self.positions_tree.delete(item)

                self.log_message("✅ All positions closed")

        except Exception as e:
            self.log_message(f"❌ Close all positions failed: {e}")

    def cancel_selected_order(self):
        """取消选中的订单"""
        try:
            selected = self.orders_tree.selection()
            if not selected:
                messagebox.showwarning(
                    self.get_constant('DIALOG_WARNING_SELECT_ORDER'),
                    self.get_constant('MSG_SELECT_ORDER_TO_CANCEL')
                )
                return

            order_id = self.orders_tree.item(selected[0])['values'][0]

            result = messagebox.askyesno(
                self.get_constant('DIALOG_CONFIRM_CANCEL'),
                f"{self.get_constant('MSG_CONFIRM_CANCEL_ORDER')} {order_id}?"
            )

            if result:
                self.log_message(f"❌ Cancelling order {order_id}...")

                # 这里应该调用实际的取消订单API
                # self.api_connector.cancel_order(order_id)

                # 从显示中移除
                self.orders_tree.delete(selected[0])

                self.log_message(f"✅ Order {order_id} cancelled")

        except Exception as e:
            self.log_message(f"❌ Cancel order failed: {e}")

    def cancel_all_orders(self):
        """取消所有订单"""
        try:
            if not self.orders_tree.get_children():
                return

            result = messagebox.askyesno(
                "Confirm Cancel All",
                "Cancel ALL pending orders?"
            )

            if result:
                self.log_message("❌ Cancelling all orders...")

                # 这里应该调用实际的取消所有订单API
                # self.api_connector.cancel_all_orders()

                # 清空订单显示
                for item in self.orders_tree.get_children():
                    self.orders_tree.delete(item)

                self.log_message("✅ All orders cancelled")

        except Exception as e:
            self.log_message(f"❌ Cancel all orders failed: {e}")

    def generate_report(self):
        """生成交易报告"""
        try:
            self.log_message("📊 Generating trading report...")

            report_text = """
📊 TRADING PERFORMANCE REPORT
========================================

Period: Today
Generated: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """

Account Summary:
• Starting Balance: $10,000.00
• Current Balance: $10,150.00
• Total P&L: +$150.00 (*****%)
• Win Rate: 66.67%

Trading Statistics:
• Total Trades: 3
• Winning Trades: 2
• Losing Trades: 1
• Average Win: $90.00
• Average Loss: -$30.00
• Profit Factor: 2.0

Risk Metrics:
• Maximum Drawdown: -0.5%
• Sharpe Ratio: 1.85
• Current Risk Level: MEDIUM

Top Performing Symbols:
1. BTC/USDT: +$100 (*****%)
2. ETH/USDT: +$100 (*****%)
3. SOL/USDT: +$80 (+0.80%)
"""

            messagebox.showinfo("Trading Report", report_text)
            self.log_message("✅ Trading report generated")

        except Exception as e:
            self.log_message(f"❌ Report generation failed: {e}")

    def export_history(self):
        """导出交易历史"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                self.log_message(f"💾 Exporting history to {filename}...")

                # 这里应该实现实际的导出逻辑
                # export_data_to_csv(self.history_data, filename)

                self.log_message("✅ History exported successfully")

        except Exception as e:
            self.log_message(f"❌ Export failed: {e}")

    def update_chart(self):
        """更新图表"""
        try:
            symbol = self.chart_symbol_var.get()
            timeframe = self.chart_timeframe_var.get()

            self.log_message(f"📈 Updating chart: {symbol} {timeframe}")

            if self.chart:
                # 这里应该获取实际的K线数据
                # kline_data = self.api_connector.get_kline_data(symbol, timeframe)
                # self.chart.update_data(kline_data)
                pass

            self.log_message("✅ Chart updated")

        except Exception as e:
            self.log_message(f"❌ Chart update failed: {e}")

    # ==================== 辅助方法 ====================

    def show_settings(self):
        """显示设置对话框"""
        try:
            settings_dialog = tk.Toplevel(self.root)
            settings_dialog.title("SYSTEM SETTINGS")
            settings_dialog.geometry("500x400")
            settings_dialog.configure(bg=self.PROFESSIONAL_COLORS["bg_primary"])
            settings_dialog.transient(self.root)
            settings_dialog.grab_set()

            # 居中显示
            settings_dialog.update_idletasks()
            x = (settings_dialog.winfo_screenwidth() // 2) - (500 // 2)
            y = (settings_dialog.winfo_screenheight() // 2) - (400 // 2)
            settings_dialog.geometry(f"500x400+{x}+{y}")

            # 设置内容
            tk.Label(
                settings_dialog,
                text="⚙️ SYSTEM SETTINGS",
                font=self.PROFESSIONAL_FONTS["heading"],
                fg=self.PROFESSIONAL_COLORS["text_accent"],
                bg=self.PROFESSIONAL_COLORS["bg_primary"],
            ).pack(pady=20)

            settings_text = tk.Text(
                settings_dialog,
                height=15,
                font=self.PROFESSIONAL_FONTS["monospace"],
                bg=self.PROFESSIONAL_COLORS["bg_secondary"],
                fg=self.PROFESSIONAL_COLORS["text_primary"],
            )
            settings_text.pack(fill="both", expand=True, padx=20, pady=10)

            settings_content = """
TRADING SYSTEM CONFIGURATION
========================================

API Settings:
• Exchange: GATE.IO
• Connection: WebSocket + REST
• Rate Limit: 100 requests/minute
• Timeout: 10 seconds

Risk Management:
• Max Position Size: 5%
• Daily Loss Limit: 2%
• Stop Loss: 5%
• Take Profit: 10%

System Settings:
• Update Interval: 1 second
• Auto Refresh: Enabled
• Logging Level: INFO
• Theme: Professional Dark

Performance:
• CPU Usage: < 30%
• Memory Usage: < 500MB
• Network Latency: < 100ms
• Data Cache: 1000 records
"""

            settings_text.insert(tk.END, settings_content)
            settings_text.configure(state="disabled")

            # 关闭按钮
            tk.Button(
                settings_dialog,
                text="CLOSE",
                command=settings_dialog.destroy,
                bg=self.PROFESSIONAL_COLORS["bg_panel"],
                fg=self.PROFESSIONAL_COLORS["text_primary"],
                font=self.PROFESSIONAL_FONTS["body"]
            ).pack(pady=10)

        except Exception as e:
            self.log_message(f"❌ Settings dialog failed: {e}")

    def show_monitor(self):
        """显示系统监控"""
        try:
            self.log_message("📊 Opening system monitor...")

            # 切换到监控标签页
            for i in range(self.notebook.index("end")):
                if "MONITOR" in self.notebook.tab(i, "text"):
                    self.notebook.select(i)
                    break

            # 更新监控信息
            self.update_monitor_display()

        except Exception as e:
            self.log_message(f"❌ Monitor display failed: {e}")

    def update_monitor_display(self):
        """更新监控显示"""
        try:
            if hasattr(self, 'monitor_text'):
                self.monitor_text.delete(1.0, tk.END)

                import psutil
                import platform

                monitor_info = f"""
🖥️ SYSTEM MONITOR - {datetime.now().strftime('%H:%M:%S')}
{'='*60}

💻 System Information:
• OS: {platform.system()} {platform.release()}
• CPU: {psutil.cpu_count()} cores @ {psutil.cpu_percent():.1f}%
• Memory: {psutil.virtual_memory().percent:.1f}% used
• Disk: {psutil.disk_usage('/').percent:.1f}% used

🌐 Network Status:
• Connection: {'✅ CONNECTED' if self.is_connected else '❌ DISCONNECTED'}
• Trading: {'✅ ACTIVE' if self.is_trading else '❌ INACTIVE'}
• Mode: {self.trading_mode}
• Latency: ~50ms

📊 Trading Statistics:
• Active Positions: {len(self.positions)}
• Pending Orders: {len(self.orders)}
• Account Balance: ${self.account_data.get('balance', 0):.2f}
• Unrealized P&L: ${self.account_data.get('unrealized_pnl', 0):.2f}

⚡ Performance Metrics:
• Update Rate: 1 Hz
• Data Processing: Normal
• Memory Usage: {psutil.virtual_memory().percent:.1f}%
• CPU Usage: {psutil.cpu_percent():.1f}%

🔄 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

                self.monitor_text.insert(tk.END, monitor_info)

        except Exception as e:
            self.log_message(f"❌ Monitor update failed: {e}")

    def update_time(self):
        """更新时间显示"""
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.time_label.configure(text=current_time)

            # 每秒更新一次
            self.root.after(1000, self.update_time)

        except Exception as e:
            self.log_message(f"❌ Time update failed: {e}")

    def log_message(self, message):
        """记录日志消息"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message}"

            # 输出到控制台
            print(log_entry)

            # 更新状态栏
            if hasattr(self, 'status_label'):
                self.status_label.configure(text=message)

        except Exception as e:
            print(f"❌ Logging failed: {e}")

    def run(self):
        """运行GUI"""
        try:
            self.log_message("🚀 Starting Professional Trading Terminal...")
            self.root.mainloop()
        except Exception as e:
            self.log_message(f"❌ GUI runtime error: {e}")


def show_api_login_dialog(parent):
    """显示API登录对话框"""
    try:
        dialog = tk.Toplevel(parent)
        dialog.title("API连接设置")
        dialog.geometry("400x300")
        dialog.configure(bg="#1e1e1e")
        dialog.transient(parent)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"400x300+{x}+{y}")

        result = {"success": False}

        # 标题
        title_label = tk.Label(
            dialog,
            text="🔗 API连接配置",
            font=("Microsoft YaHei", 14, "bold"),
            fg="#00d4aa",
            bg="#1e1e1e"
        )
        title_label.pack(pady=20)

        # API Key输入
        tk.Label(dialog, text="API Key:", fg="white", bg="#1e1e1e").pack(pady=5)
        api_key_entry = tk.Entry(dialog, width=40, show="*")
        api_key_entry.pack(pady=5)

        # Secret输入
        tk.Label(dialog, text="Secret:", fg="white", bg="#1e1e1e").pack(pady=5)
        secret_entry = tk.Entry(dialog, width=40, show="*")
        secret_entry.pack(pady=5)

        # 交易所选择
        tk.Label(dialog, text="交易所:", fg="white", bg="#1e1e1e").pack(pady=5)
        exchange_var = tk.StringVar(value="Binance")
        exchange_combo = ttk.Combobox(
            dialog,
            textvariable=exchange_var,
            values=["Binance", "OKX", "Huobi", "KuCoin"],
            state="readonly"
        )
        exchange_combo.pack(pady=5)

        def on_connect():
            # 这里应该验证API连接
            # 暂时模拟成功
            result["success"] = True
            dialog.destroy()

        def on_cancel():
            result["success"] = False
            dialog.destroy()

        # 按钮框架
        button_frame = tk.Frame(dialog, bg="#1e1e1e")
        button_frame.pack(pady=20)

        connect_btn = tk.Button(
            button_frame,
            text="连接",
            command=on_connect,
            bg="#00d4aa",
            fg="white",
            font=("Microsoft YaHei", 10, "bold"),
            width=10
        )
        connect_btn.pack(side="left", padx=10)

        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            command=on_cancel,
            bg="#ff6b6b",
            fg="white",
            font=("Microsoft YaHei", 10, "bold"),
            width=10
        )
        cancel_btn.pack(side="left", padx=10)

        # 等待对话框关闭
        dialog.wait_window()

        return result["success"]

    except Exception as e:
        print(f"API login dialog error: {e}")
        return False


def main():
    """主函数"""
    try:
        # 创建并运行专业交易GUI
        app = ProfessionalTradingGUI()
        app.run()

    except Exception as e:
        print(f"❌ Application startup failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

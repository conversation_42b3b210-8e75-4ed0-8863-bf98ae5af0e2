{".class": "MypyFile", "_fullname": "websockets.asyncio.router", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.router.Map", "name": "Map", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.router.Map", "source_any": null, "type_of_any": 3}}}, "NotFound": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.router.NotFound", "name": "NotFound", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.router.NotFound", "source_any": null, "type_of_any": 3}}}, "Request": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Request", "kind": "Gdef", "module_public": false}, "RequestRedirect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.router.RequestRedirect", "name": "RequestRedirect", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.router.RequestRedirect", "source_any": null, "type_of_any": 3}}}, "Response": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Response", "kind": "Gdef", "module_public": false}, "Router": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.asyncio.router.Router", "name": "Router", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.asyncio.router", "mro": ["websockets.asyncio.router.Router", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url_map", "server_name", "url_scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url_map", "server_name", "url_scheme"], "arg_types": ["websockets.asyncio.router.Router", {".class": "AnyType", "missing_import_name": "websockets.asyncio.router.Map", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Router", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_server_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router.get_server_name", "name": "get_server_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "request"], "arg_types": ["websockets.asyncio.router.Router", "websockets.asyncio.server.ServerConnection", "websockets.http11.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_server_name of Router", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.router.Router.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["websockets.asyncio.router.Router", "websockets.asyncio.server.ServerConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of Router", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_found": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router.not_found", "name": "not_found", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["websockets.asyncio.router.Router", "websockets.asyncio.server.ServerConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_found of Router", "ret_type": "websockets.http11.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router.redirect", "name": "redirect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "url"], "arg_types": ["websockets.asyncio.router.Router", "websockets.asyncio.server.ServerConnection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect of Router", "ret_type": "websockets.http11.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "route_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.Router.route_request", "name": "route_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "request"], "arg_types": ["websockets.asyncio.router.Router", "websockets.asyncio.server.ServerConnection", "websockets.http11.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "route_request of Router", "ret_type": {".class": "UnionType", "items": ["websockets.http11.Response", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.router.Router.server_name", "name": "server_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "url_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.router.Router.url_map", "name": "url_map", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.router.Map", "source_any": null, "type_of_any": 3}}}, "url_scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.router.Router.url_scheme", "name": "url_scheme", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.asyncio.router.Router.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.asyncio.router.Router", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Server": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.Server", "kind": "Gdef", "module_public": false}, "ServerConnection": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.ServerConnection", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.router.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.router.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef", "module_public": false}, "route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["url_map", "args", "server_name", "ssl", "create_router", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.route", "name": "route", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["url_map", "args", "server_name", "ssl", "create_router", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": "websockets.asyncio.router.Map", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "websockets.asyncio.router.Router"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "route", "ret_type": {".class": "Instance", "args": ["websockets.asyncio.server.Server"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serve": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.server.serve", "kind": "Gdef", "module_public": false}, "ssl_module": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "unix_route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["url_map", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.asyncio.router.unix_route", "name": "unix_route", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["url_map", "path", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": "websockets.asyncio.router.Map", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unix_route", "ret_type": {".class": "Instance", "args": ["websockets.asyncio.server.Server"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py"}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易启动器
Trading Starter

简化的交易启动和配置脚本
"""

import json
import os
import time
from datetime import datetime

from live_trading_executor import LiveTradingExecutor


def create_config_file():
    """创建配置文件"""
    config = {
        "exchange": {
            "name": "binance",
            "api_key": "",  # 在这里填入您的API Key
            "secret": "",  # 在这里填入您的Secret
            "sandbox": True,  # True=沙盒模式(测试), False=实盘模式
            "testnet": True,  # 币安测试网
        },
        "trading": {
            "initial_capital": 10000,  # 初始资金 USDT
            "max_positions": 3,  # 最大持仓数
            "daily_loss_limit": 300,  # 日亏损限制 USDT
            "total_loss_limit": 1500,  # 总亏损限制 USDT
            "trading_interval": 60,  # 交易周期间隔(秒)
        },
        "strategies": {
            "CryptoBreakout": {
                "enabled": True,
                "allocation": 0.40,
                "pairs": ["BTC/USDT", "ETH/USDT"],
                "timeframe": "15m",
            },
            "CryptoGrid": {
                "enabled": True,
                "allocation": 0.35,
                "pairs": ["BTC/USDT", "ETH/USDT"],
                "grid_spacing": 0.015,
            },
            "CryptoMomentum": {
                "enabled": True,
                "allocation": 0.25,
                "pairs": ["BTC/USDT", "ETH/USDT"],
                "timeframe": "1h",
            },
        },
        "risk_management": {
            "stop_loss": 0.05,  # 5% 止损
            "take_profit": 0.12,  # 12% 止盈
            "max_position_size": 0.15,  # 最大单仓位15%
            "emergency_stop": True,  # 紧急停止功能
        },
    }

    with open("trading_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

    print("✅ 配置文件已创建: trading_config.json")
    return config


def load_config():
    """加载配置文件"""
    if not os.path.exists("trading_config.json"):
        print("📝 配置文件不存在，正在创建...")
        return create_config_file()

    try:
        with open("trading_config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        print("✅ 配置文件加载成功")
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None


def validate_config(config):
    """验证配置"""
    errors = []

    # 检查API配置
    if not config["exchange"]["api_key"]:
        errors.append("❌ API Key 未配置")

    if not config["exchange"]["secret"]:
        errors.append("❌ Secret 未配置")

    # 检查资金配置
    if config["trading"]["initial_capital"] < 100:
        errors.append("❌ 初始资金过少，建议至少100 USDT")

    # 检查策略配置
    total_allocation = sum(
        strategy["allocation"]
        for strategy in config["strategies"].values()
        if strategy["enabled"]
    )

    if abs(total_allocation - 1.0) > 0.01:
        errors.append(f"❌ 策略分配比例不正确: {total_allocation:.2%}")

    return errors


def display_config(config):
    """显示配置信息"""
    print("\n📊 当前配置:")
    print(f"交易所: {config['exchange']['name']}")
    print(
        f"模式: {'沙盒测试' if config['exchange']['sandbox'] else '实盘交易'}"
    )
    print(f"初始资金: {config['trading']['initial_capital']:,} USDT")
    print(f"交易间隔: {config['trading']['trading_interval']} 秒")

    print(f"\n💰 策略分配:")
    for name, strategy in config["strategies"].items():
        if strategy["enabled"]:
            allocation = strategy["allocation"]
            amount = config["trading"]["initial_capital"] * allocation
            print(f"  {name}: {allocation:.0%} ({amount:,.0f} USDT)")

    print(f"\n🛡️ 风险控制:")
    print(f"日亏损限制: {config['trading']['daily_loss_limit']} USDT")
    print(f"总亏损限制: {config['trading']['total_loss_limit']} USDT")
    print(f"止损: {config['risk_management']['stop_loss']:.1%}")
    print(f"止盈: {config['risk_management']['take_profit']:.1%}")


def setup_api_keys():
    """设置API密钥"""
    print("\n🔑 API密钥设置")
    print("请访问币安官网获取API密钥:")
    print("1. 登录币安账户")
    print("2. 进入 账户 -> API管理")
    print("3. 创建新的API密钥")
    print("4. 启用现货交易权限")
    print("5. 复制API Key和Secret")

    print("\n⚠️ 安全提醒:")
    print("- 不要启用提现权限")
    print("- 设置IP白名单")
    print("- 妥善保管密钥")

    api_key = input("\n请输入API Key: ").strip()
    secret = input("请输入Secret: ").strip()

    if api_key and secret:
        # 更新配置文件
        config = load_config()
        config["exchange"]["api_key"] = api_key
        config["exchange"]["secret"] = secret

        with open("trading_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print("✅ API密钥已保存")
        return True
    else:
        print("❌ API密钥不能为空")
        return False


def start_trading_system():
    """启动交易系统"""
    print("\n🚀 启动交易系统")

    # 加载配置
    config = load_config()
    if not config:
        return

    # 验证配置
    errors = validate_config(config)
    if errors:
        print("\n❌ 配置验证失败:")
        for error in errors:
            print(f"  {error}")

        if not config["exchange"]["api_key"]:
            if input("\n是否现在设置API密钥? (y/n): ").lower() == "y":
                if not setup_api_keys():
                    return
                config = load_config()  # 重新加载配置
            else:
                return

    # 显示配置
    display_config(config)

    # 确认启动
    mode = "沙盒测试" if config["exchange"]["sandbox"] else "实盘交易"
    confirm = input(f"\n确认启动 {mode} 模式? (y/n): ").lower()

    if confirm != "y":
        print("❌ 用户取消启动")
        return

    try:
        # 创建交易执行器
        executor = LiveTradingExecutor(config["exchange"])

        # 连接交易所
        print("\n📡 连接交易所...")
        if not executor.connect_exchange():
            print("❌ 连接失败，请检查API配置")
            return

        # 设置策略组合
        print("⚙️ 设置策略组合...")
        executor.setup_portfolio()

        # 显示初始状态
        status = executor.get_status()
        print(f"\n💰 账户状态:")
        print(f"USDT余额: {status['balance']:.2f}")
        print(f"持仓数量: {len(status['positions'])}")

        # 开始交易
        print(
            f"\n🎯 开始自动交易 (间隔: {config['trading']['trading_interval']}秒)"
        )
        print("按 Ctrl+C 停止交易")

        executor.start_trading(interval=config["trading"]["trading_interval"])

    except KeyboardInterrupt:
        print("\n⏹️ 用户停止交易")
    except Exception as e:
        print(f"\n❌ 交易系统错误: {e}")


def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🎯 加密货币量化交易系统")
    print("=" * 50)
    print("1. 查看配置")
    print("2. 设置API密钥")
    print("3. 开始交易")
    print("4. 创建新配置")
    print("5. 退出")
    print("=" * 50)


def main():
    """主函数"""
    print("🚀 欢迎使用加密货币量化交易系统!")

    while True:
        show_menu()
        choice = input("请选择操作 (1-5): ").strip()

        if choice == "1":
            config = load_config()
            if config:
                display_config(config)

        elif choice == "2":
            setup_api_keys()

        elif choice == "3":
            start_trading_system()

        elif choice == "4":
            create_config_file()

        elif choice == "5":
            print("👋 感谢使用，再见!")
            break

        else:
            print("❌ 无效选择，请重试")

        input("\n按回车键继续...")


if __name__ == "__main__":
    main()

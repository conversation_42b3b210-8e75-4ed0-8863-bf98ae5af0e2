#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
消息常量
Message Constants

定义系统消息的常量键
"""


class MessageConstants:
    """消息常量键"""
    
    # ==================== 通用消息 ====================
    # 基础操作
    MSG_SUCCESS = "msg.success"
    MSG_ERROR = "msg.error"
    MSG_WARNING = "msg.warning"
    MSG_INFO = "msg.info"
    MSG_CONFIRM = "msg.confirm"
    MSG_CANCEL = "msg.cancel"
    MSG_OK = "msg.ok"
    MSG_YES = "msg.yes"
    MSG_NO = "msg.no"
    
    # 状态消息
    MSG_LOADING = "msg.loading"
    MSG_SAVING = "msg.saving"
    MSG_CONNECTING = "msg.connecting"
    MSG_CONNECTED = "msg.connected"
    MSG_DISCONNECTED = "msg.disconnected"
    MSG_READY = "msg.ready"
    MSG_BUSY = "msg.busy"
    MSG_COMPLETED = "msg.completed"
    MSG_FAILED = "msg.failed"
    
    # ==================== 连接相关消息 ====================
    # 连接成功
    MSG_CONNECTION_SUCCESS = "connection.success"
    MSG_CONNECTION_ESTABLISHED = "connection.established"
    MSG_API_CONNECTED = "connection.api_connected"
    MSG_WEBSOCKET_CONNECTED = "connection.websocket_connected"
    
    # 连接失败
    MSG_CONNECTION_FAILED = "connection.failed"
    MSG_CONNECTION_TIMEOUT = "connection.timeout"
    MSG_CONNECTION_REFUSED = "connection.refused"
    MSG_CONNECTION_LOST = "connection.lost"
    MSG_API_ERROR = "connection.api_error"
    MSG_NETWORK_ERROR = "connection.network_error"
    
    # 连接状态
    MSG_CONNECTION_CHECKING = "connection.checking"
    MSG_CONNECTION_RETRYING = "connection.retrying"
    MSG_CONNECTION_STABLE = "connection.stable"
    MSG_CONNECTION_UNSTABLE = "connection.unstable"
    
    # ==================== 交易相关消息 ====================
    # 订单操作
    MSG_ORDER_PLACED = "order.placed"
    MSG_ORDER_FILLED = "order.filled"
    MSG_ORDER_CANCELLED = "order.cancelled"
    MSG_ORDER_REJECTED = "order.rejected"
    MSG_ORDER_EXPIRED = "order.expired"
    MSG_ORDER_MODIFIED = "order.modified"
    
    # 订单错误
    MSG_ORDER_INVALID_AMOUNT = "order.invalid_amount"
    MSG_ORDER_INVALID_PRICE = "order.invalid_price"
    MSG_ORDER_INSUFFICIENT_BALANCE = "order.insufficient_balance"
    MSG_ORDER_MARKET_CLOSED = "order.market_closed"
    MSG_ORDER_SYMBOL_NOT_SUPPORTED = "order.symbol_not_supported"
    
    # 持仓操作
    MSG_POSITION_OPENED = "position.opened"
    MSG_POSITION_CLOSED = "position.closed"
    MSG_POSITION_MODIFIED = "position.modified"
    MSG_POSITION_LIQUIDATED = "position.liquidated"
    
    # 交易状态
    MSG_TRADING_STARTED = "trading.started"
    MSG_TRADING_STOPPED = "trading.stopped"
    MSG_TRADING_PAUSED = "trading.paused"
    MSG_TRADING_RESUMED = "trading.resumed"
    MSG_EMERGENCY_STOP = "trading.emergency_stop"
    
    # ==================== 账户相关消息 ====================
    # 余额信息
    MSG_BALANCE_UPDATED = "account.balance_updated"
    MSG_BALANCE_LOW = "account.balance_low"
    MSG_BALANCE_INSUFFICIENT = "account.balance_insufficient"
    MSG_MARGIN_CALL = "account.margin_call"
    MSG_MARGIN_LIQUIDATION = "account.margin_liquidation"
    
    # 账户状态
    MSG_ACCOUNT_VERIFIED = "account.verified"
    MSG_ACCOUNT_SUSPENDED = "account.suspended"
    MSG_ACCOUNT_RESTRICTED = "account.restricted"
    MSG_ACCOUNT_LOCKED = "account.locked"
    
    # ==================== 风险管理消息 ====================
    # 风险警告
    MSG_RISK_HIGH = "risk.high"
    MSG_RISK_CRITICAL = "risk.critical"
    MSG_RISK_LIMIT_EXCEEDED = "risk.limit_exceeded"
    MSG_DRAWDOWN_WARNING = "risk.drawdown_warning"
    MSG_CORRELATION_HIGH = "risk.correlation_high"
    MSG_VOLATILITY_HIGH = "risk.volatility_high"
    
    # 风险控制
    MSG_RISK_CHECK_PASSED = "risk.check_passed"
    MSG_RISK_CHECK_FAILED = "risk.check_failed"
    MSG_POSITION_SIZE_LIMITED = "risk.position_size_limited"
    MSG_DAILY_LIMIT_REACHED = "risk.daily_limit_reached"
    
    # ==================== 数据相关消息 ====================
    # 数据更新
    MSG_DATA_UPDATED = "data.updated"
    MSG_DATA_REFRESHED = "data.refreshed"
    MSG_DATA_SYNCHRONIZED = "data.synchronized"
    MSG_MARKET_DATA_RECEIVED = "data.market_data_received"
    
    # 数据错误
    MSG_DATA_ERROR = "data.error"
    MSG_DATA_CORRUPTED = "data.corrupted"
    MSG_DATA_MISSING = "data.missing"
    MSG_DATA_OUTDATED = "data.outdated"
    MSG_DATA_INVALID = "data.invalid"
    
    # 数据操作
    MSG_DATA_SAVED = "data.saved"
    MSG_DATA_LOADED = "data.loaded"
    MSG_DATA_EXPORTED = "data.exported"
    MSG_DATA_IMPORTED = "data.imported"
    MSG_DATA_BACKUP_CREATED = "data.backup_created"
    
    # ==================== 系统相关消息 ====================
    # 系统状态
    MSG_SYSTEM_STARTED = "system.started"
    MSG_SYSTEM_STOPPED = "system.stopped"
    MSG_SYSTEM_RESTARTED = "system.restarted"
    MSG_SYSTEM_SHUTDOWN = "system.shutdown"
    MSG_SYSTEM_ERROR = "system.error"
    
    # 性能监控
    MSG_PERFORMANCE_NORMAL = "system.performance_normal"
    MSG_PERFORMANCE_DEGRADED = "system.performance_degraded"
    MSG_CPU_HIGH = "system.cpu_high"
    MSG_MEMORY_HIGH = "system.memory_high"
    MSG_DISK_FULL = "system.disk_full"
    
    # 更新和维护
    MSG_UPDATE_AVAILABLE = "system.update_available"
    MSG_UPDATE_INSTALLED = "system.update_installed"
    MSG_MAINTENANCE_MODE = "system.maintenance_mode"
    MSG_SERVICE_UNAVAILABLE = "system.service_unavailable"
    
    # ==================== 用户界面消息 ====================
    # 界面操作
    MSG_UI_LOADED = "ui.loaded"
    MSG_UI_REFRESHED = "ui.refreshed"
    MSG_UI_THEME_CHANGED = "ui.theme_changed"
    MSG_UI_LANGUAGE_CHANGED = "ui.language_changed"
    MSG_UI_SETTINGS_SAVED = "ui.settings_saved"
    
    # 用户输入
    MSG_INPUT_INVALID = "ui.input_invalid"
    MSG_INPUT_REQUIRED = "ui.input_required"
    MSG_INPUT_TOO_LONG = "ui.input_too_long"
    MSG_INPUT_TOO_SHORT = "ui.input_too_short"
    MSG_INPUT_FORMAT_ERROR = "ui.input_format_error"
    
    # ==================== 安全相关消息 ====================
    # 认证
    MSG_LOGIN_SUCCESS = "security.login_success"
    MSG_LOGIN_FAILED = "security.login_failed"
    MSG_LOGOUT_SUCCESS = "security.logout_success"
    MSG_SESSION_EXPIRED = "security.session_expired"
    MSG_PASSWORD_INCORRECT = "security.password_incorrect"
    
    # 授权
    MSG_ACCESS_GRANTED = "security.access_granted"
    MSG_ACCESS_DENIED = "security.access_denied"
    MSG_PERMISSION_REQUIRED = "security.permission_required"
    MSG_UNAUTHORIZED = "security.unauthorized"
    
    # 安全警告
    MSG_SECURITY_BREACH = "security.breach"
    MSG_SUSPICIOUS_ACTIVITY = "security.suspicious_activity"
    MSG_MULTIPLE_LOGIN_ATTEMPTS = "security.multiple_login_attempts"
    MSG_IP_BLOCKED = "security.ip_blocked"
    
    # ==================== 配置相关消息 ====================
    # 配置操作
    MSG_CONFIG_LOADED = "config.loaded"
    MSG_CONFIG_SAVED = "config.saved"
    MSG_CONFIG_RESET = "config.reset"
    MSG_CONFIG_EXPORTED = "config.exported"
    MSG_CONFIG_IMPORTED = "config.imported"
    
    # 配置错误
    MSG_CONFIG_ERROR = "config.error"
    MSG_CONFIG_INVALID = "config.invalid"
    MSG_CONFIG_MISSING = "config.missing"
    MSG_CONFIG_CORRUPTED = "config.corrupted"
    
    # ==================== 报告相关消息 ====================
    # 报告生成
    MSG_REPORT_GENERATED = "report.generated"
    MSG_REPORT_EXPORTED = "report.exported"
    MSG_REPORT_SENT = "report.sent"
    MSG_REPORT_SCHEDULED = "report.scheduled"
    
    # 报告错误
    MSG_REPORT_ERROR = "report.error"
    MSG_REPORT_EMPTY = "report.empty"
    MSG_REPORT_TOO_LARGE = "report.too_large"
    
    # ==================== 确认对话框消息 ====================
    # 操作确认
    MSG_CONFIRM_DELETE = "confirm.delete"
    MSG_CONFIRM_RESET = "confirm.reset"
    MSG_CONFIRM_EXIT = "confirm.exit"
    MSG_CONFIRM_SAVE = "confirm.save"
    MSG_CONFIRM_CANCEL = "confirm.cancel"
    
    # 交易确认
    MSG_CONFIRM_ORDER = "confirm.order"
    MSG_CONFIRM_CLOSE_POSITION = "confirm.close_position"
    MSG_CONFIRM_EMERGENCY_STOP = "confirm.emergency_stop"
    MSG_CONFIRM_LIVE_TRADING = "confirm.live_trading"
    
    # ==================== 帮助和提示消息 ====================
    # 帮助信息
    MSG_HELP_AVAILABLE = "help.available"
    MSG_HELP_TOOLTIP = "help.tooltip"
    MSG_HELP_GUIDE = "help.guide"
    MSG_HELP_FAQ = "help.faq"
    
    # 提示信息
    MSG_TIP_OF_DAY = "tip.of_day"
    MSG_TIP_TRADING = "tip.trading"
    MSG_TIP_RISK = "tip.risk"
    MSG_TIP_PERFORMANCE = "tip.performance"
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def get_all_message_keys(cls) -> list:
        """获取所有消息键"""
        return [
            value for name, value in cls.__dict__.items()
            if name.startswith('MSG_') and isinstance(value, str)
        ]
    
    @classmethod
    def get_message_categories(cls) -> dict:
        """获取消息分类"""
        categories = {}
        for name, value in cls.__dict__.items():
            if name.startswith('MSG_') and isinstance(value, str):
                category = value.split('.')[0]
                if category not in categories:
                    categories[category] = []
                categories[category].append(value)
        return categories
    
    @classmethod
    def is_error_message(cls, message_key: str) -> bool:
        """检查是否为错误消息"""
        error_keywords = ['error', 'failed', 'invalid', 'denied', 'rejected']
        return any(keyword in message_key.lower() for keyword in error_keywords)
    
    @classmethod
    def is_success_message(cls, message_key: str) -> bool:
        """检查是否为成功消息"""
        success_keywords = ['success', 'completed', 'placed', 'connected', 'saved']
        return any(keyword in message_key.lower() for keyword in success_keywords)
    
    @classmethod
    def is_warning_message(cls, message_key: str) -> bool:
        """检查是否为警告消息"""
        warning_keywords = ['warning', 'high', 'low', 'limit', 'risk']
        return any(keyword in message_key.lower() for keyword in warning_keywords)


# 创建全局消息常量实例
MESSAGE_CONSTANTS = MessageConstants()


if __name__ == "__main__":
    # 测试消息常量
    print("📝 消息常量测试")
    
    all_keys = MessageConstants.get_all_message_keys()
    print(f"消息键总数: {len(all_keys)}")
    
    categories = MessageConstants.get_message_categories()
    print(f"消息分类: {list(categories.keys())}")
    
    # 测试消息类型检查
    test_messages = [
        MessageConstants.MSG_CONNECTION_SUCCESS,
        MessageConstants.MSG_CONNECTION_FAILED,
        MessageConstants.MSG_RISK_HIGH
    ]
    
    for msg in test_messages:
        print(f"{msg}: 成功={MessageConstants.is_success_message(msg)}, "
              f"错误={MessageConstants.is_error_message(msg)}, "
              f"警告={MessageConstants.is_warning_message(msg)}")
    
    print("✅ 消息常量测试完成")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置模块
Configuration Module

提供系统配置管理功能
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导出主要类和函数
try:
    from .config_manager import Config<PERSON>ana<PERSON>, get_config_manager, get_config, set_config
    
    __all__ = [
        'ConfigManager',
        'get_config_manager', 
        'get_config',
        'set_config'
    ]
    
except ImportError as e:
    print(f"配置模块导入警告: {e}")
    __all__ = []

#!/usr/bin/env python3
"""
代码质量修复工具
Code Quality Fix Tool

自动修复发现的代码质量问题
"""

import os
import re
import shutil
from pathlib import Path
from datetime import datetime

class CodeQualityFixer:
    """代码质量修复器"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.backup_dir = Path("backups") / f"before_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.fixes_applied = []
        
    def apply_all_fixes(self):
        """应用所有修复"""
        print("🔧 开始代码质量修复...")
        
        # 创建备份
        self._create_backup()
        
        # 1. 清理调试print语句
        self._fix_debug_prints()
        
        # 2. 重命名中文文件
        self._rename_chinese_files()
        
        # 3. 整理重复启动器
        self._organize_launchers()
        
        # 4. 优化import语句
        self._optimize_imports()
        
        # 5. 修复长行问题
        self._fix_long_lines()
        
        # 6. 生成修复报告
        self._generate_fix_report()
        
        print("✅ 代码质量修复完成！")
        
    def _create_backup(self):
        """创建备份"""
        print("📦 创建代码备份...")
        
        if not self.backup_dir.exists():
            self.backup_dir.mkdir(parents=True)
            
        # 备份主要的Python文件
        for py_file in self.project_root.rglob("*.py"):
            if "backups" not in str(py_file) and "__pycache__" not in str(py_file):
                relative_path = py_file.relative_to(self.project_root)
                backup_path = self.backup_dir / relative_path
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(py_file, backup_path)
                
        print(f"✅ 备份创建完成: {self.backup_dir}")
        
    def _fix_debug_prints(self):
        """修复调试print语句"""
        print("🖨️ 修复调试print语句...")
        
        python_files = list(self.project_root.rglob("*.py"))
        fixed_files = []
        
        for py_file in python_files:
            if "backups" in str(py_file) or "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                original_content = content
                
                # 替换调试print为logging
                debug_patterns = [
                    (r'print\s*\(\s*f?"?测试[^)]*\)', '# 调试输出已移除'),
                    (r'print\s*\(\s*f?"?debug[^)]*\)', '# 调试输出已移除'),
                    (r'print\s*\(\s*f?"?Debug[^)]*\)', '# 调试输出已移除'),
                    (r'print\s*\(\s*f?"?DEBUG[^)]*\)', '# 调试输出已移除'),
                ]
                
                for pattern, replacement in debug_patterns:
                    content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                
                # 如果有修改，保存文件
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(py_file))
                    
            except Exception as e:
                print(f"⚠️ 无法处理文件 {py_file}: {e}")
        
        if fixed_files:
            self.fixes_applied.append({
                'type': 'Debug Print Cleanup',
                'files_modified': len(fixed_files),
                'details': fixed_files[:5]  # 只显示前5个
            })
            print(f"✅ 已修复 {len(fixed_files)} 个文件的调试输出")
        else:
            print("ℹ️ 未发现需要修复的调试输出")
            
    def _rename_chinese_files(self):
        """重命名中文文件"""
        print("🈶 重命名中文文件...")
        
        # 中文文件名映射
        rename_map = {
            '安全API集成指南.py': 'secure_api_integration_guide.py',
            '启动现货滚雪球策略.py': 'launch_spot_snowball_strategy.py',
            '启动终极版现货交易系统.py': 'launch_ultimate_spot_trading.py',
            '启动GATE模拟交易.py': 'launch_gate_simulation.py',
            '修复后API测试.py': 'fixed_api_test.py',
            '修复利润再投资一致性.py': 'fix_profit_reinvestment_consistency.py',
            '演示专业倍增策略.py': 'demo_professional_doubling_strategy.py',
            '演示GATE模拟交易.py': 'demo_gate_simulation.py',
            '演示1000USDT配置.py': 'demo_1000usdt_config.py',
            '测试倍增效果.py': 'test_doubling_effect.py',
            '测试1000USDT配置.py': 'test_1000usdt_config.py',
            '检查交易对格式.py': 'check_trading_pair_format.py',
            '最终倍增效果演示.py': 'final_doubling_effect_demo.py',
            '最终API功能测试.py': 'final_api_function_test.py',
            '简化API测试.py': 'simplified_api_test.py',
            '快速验证倍增效果.py': 'quick_verify_doubling_effect.py',
            '快速盈利实战演示.py': 'quick_profit_demo.py',
            '立即修复虚假信号.py': 'fix_false_signals.py',
            '登录功能演示.py': 'login_function_demo.py',
            'API功能完成演示.py': 'api_function_completion_demo.py',
            '系统重命名和深度审查.py': 'system_rename_and_audit.py'
        }
        
        renamed_files = []
        
        for old_name, new_name in rename_map.items():
            old_path = self.project_root / old_name
            new_path = self.project_root / new_name
            
            if old_path.exists() and not new_path.exists():
                try:
                    old_path.rename(new_path)
                    renamed_files.append(f"{old_name} -> {new_name}")
                    print(f"  ✅ {old_name} -> {new_name}")
                except Exception as e:
                    print(f"  ⚠️ 无法重命名 {old_name}: {e}")
        
        if renamed_files:
            self.fixes_applied.append({
                'type': 'Chinese File Rename',
                'files_renamed': len(renamed_files),
                'details': renamed_files
            })
            print(f"✅ 已重命名 {len(renamed_files)} 个中文文件")
        else:
            print("ℹ️ 未发现需要重命名的中文文件")
            
    def _organize_launchers(self):
        """整理重复启动器"""
        print("🚀 整理启动器文件...")
        
        # 创建launchers目录
        launchers_dir = self.project_root / "launchers"
        launchers_dir.mkdir(exist_ok=True)
        
        # 要移动的启动器（保留终极版在根目录）
        launcher_files = [
            'launch_complete_optimized_system.py',
            'launch_final_optimized_system.py',
            'launch_optimized_v2.py',
            'launch_optimized_system.py',
            'launch_trading_gui.py'
        ]
        
        moved_files = []
        
        for launcher in launcher_files:
            src_path = self.project_root / launcher
            dst_path = launchers_dir / launcher
            
            if src_path.exists() and not dst_path.exists():
                try:
                    shutil.move(str(src_path), str(dst_path))
                    moved_files.append(launcher)
                    print(f"  ✅ {launcher} -> launchers/")
                except Exception as e:
                    print(f"  ⚠️ 无法移动 {launcher}: {e}")
        
        # 创建说明文件
        if moved_files:
            readme_content = """# 启动器目录

这个目录包含了各种版本的系统启动器。

## 推荐使用
- 主目录的 `launch_ultimate_optimized_system.py` - 最新的终极优化版

## 备选启动器
- `launch_complete_optimized_system.py` - 完整优化版
- `launch_final_optimized_system.py` - 最终版
- `launch_optimized_v2.py` - V2版本
- `launch_optimized_system.py` - 基础版
- `launch_trading_gui.py` - 基础GUI版

## 使用说明
选择适合的启动器运行即可。如有问题，建议使用主目录的终极版启动器。
"""
            
            with open(launchers_dir / "README.md", 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            self.fixes_applied.append({
                'type': 'Launcher Organization',
                'files_moved': len(moved_files),
                'details': moved_files
            })
            print(f"✅ 已整理 {len(moved_files)} 个启动器到 launchers/ 目录")
        else:
            print("ℹ️ 启动器已经整理完毕")
            
    def _optimize_imports(self):
        """优化import语句"""
        print("📦 优化import语句...")
        
        python_files = list(self.project_root.rglob("*.py"))
        optimized_files = []
        
        for py_file in python_files:
            if "backups" in str(py_file) or "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                modified = False
                new_lines = []
                
                for line in lines:
                    # 移除未使用的import（简单检测）
                    if line.strip().startswith('import ') or line.strip().startswith('from '):
                        # 保留所有import，但可以添加注释
                        new_lines.append(line)
                    else:
                        new_lines.append(line)
                
                # 这里可以添加更复杂的import优化逻辑
                
            except Exception as e:
                print(f"⚠️ 无法优化 {py_file}: {e}")
        
        print("ℹ️ Import优化完成（保持现有结构）")
        
    def _fix_long_lines(self):
        """修复长行"""
        print("📏 检查长行问题...")
        
        python_files = list(self.project_root.rglob("*.py"))
        long_line_files = []
        
        for py_file in python_files:
            if "backups" in str(py_file) or "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                long_lines = []
                for i, line in enumerate(lines, 1):
                    if len(line) > 120:
                        long_lines.append(i)
                
                if long_lines:
                    long_line_files.append({
                        'file': str(py_file),
                        'long_lines': len(long_lines),
                        'lines': long_lines[:5]  # 只显示前5行
                    })
                    
            except Exception as e:
                print(f"⚠️ 无法检查 {py_file}: {e}")
        
        if long_line_files:
            self.fixes_applied.append({
                'type': 'Long Lines Detection',
                'files_with_long_lines': len(long_line_files),
                'details': long_line_files[:3]  # 只显示前3个文件
            })
            print(f"ℹ️ 发现 {len(long_line_files)} 个文件包含长行，建议手动调整")
        else:
            print("✅ 未发现长行问题")
            
    def _generate_fix_report(self):
        """生成修复报告"""
        print("📊 生成修复报告...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"code_quality_fix_report_{timestamp}.md"
        
        report_content = f"""# 代码质量修复报告

**修复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**备份位置**: {self.backup_dir}

## 修复摘要

总共应用了 {len(self.fixes_applied)} 类修复：

"""
        
        for fix in self.fixes_applied:
            report_content += f"""
### {fix['type']}

- 修改数量: {fix.get('files_modified', fix.get('files_moved', fix.get('files_renamed', fix.get('files_with_long_lines', 0))))}
- 详细信息:
"""
            for detail in fix['details']:
                report_content += f"  - {detail}\n"
        
        report_content += f"""

## 下一步建议

1. **测试系统**: 运行 `python launch_ultimate_optimized_system.py` 验证修复效果
2. **检查功能**: 确保所有功能正常工作
3. **继续优化**: 根据长行检测结果手动调整代码格式
4. **备份管理**: 如果一切正常，可以清理旧备份

## 备份恢复

如果修复导致问题，可以从备份恢复：

```bash
# 恢复单个文件
cp {self.backup_dir}/filename.py ./filename.py

# 恢复所有文件
cp -r {self.backup_dir}/* ./
```

---
*自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 修复报告已生成: {report_file}")
        return report_file

def main():
    """主函数"""
    print("🔧 代码质量自动修复工具")
    print("=" * 50)
    
    fixer = CodeQualityFixer()
    
    # 确认执行
    response = input("是否开始修复？这将创建备份并修改代码文件 (y/N): ")
    if response.lower() != 'y':
        print("❌ 修复已取消")
        return
    
    try:
        fixer.apply_all_fixes()
        print("\n" + "=" * 50)
        print("✅ 代码质量修复完成！")
        print("💡 建议: 运行系统测试确保一切正常")
        print("=" * 50)
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        print("💡 建议: 从备份恢复并手动修复")

if __name__ == "__main__":
    main()

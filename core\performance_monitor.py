#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业级性能监控系统
Enterprise Performance Monitoring System
"""

import asyncio
import json
import logging
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import psutil


@dataclass
class SystemMetrics:
    """系统性能指标"""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    trading_active: bool = False

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result["timestamp"] = self.timestamp.isoformat()
        return result


@dataclass
class TradingMetrics:
    """交易性能指标"""

    timestamp: datetime
    execution_time_ms: float
    api_response_time_ms: float
    orders_processed: int
    errors_count: int
    success_rate: float
    performance_grade: str

    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result["timestamp"] = self.timestamp.isoformat()
        return result


class PerformanceMonitor:
    """系统性能监控器"""

    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._default_config()
        self.metrics_history: List[SystemMetrics] = []
        self.trading_history: List[TradingMetrics] = []
        self.alert_callbacks: List[Callable] = []
        self.is_monitoring = False
        self.monitor_thread = None

        # 性能阈值
        self.alert_thresholds = {
            "cpu_usage": 80.0,
            "memory_usage": 85.0,
            "api_response_time": 2000,  # ms
            "order_execution_time": 500,  # ms
            "error_rate": 0.05,  # 5%
        }

        # 设置日志
        self.logger = self._setup_logger()

        # 创建监控目录
        self.metrics_dir = Path("logs/performance")
        self.metrics_dir.mkdir(parents=True, exist_ok=True)

    def _default_config(self) -> Dict[str, Any]:
        """默认配置"""
        return {
            "monitoring_interval": 30,  # 30秒监控间隔
            "history_retention_hours": 24,  # 保留24小时历史
            "auto_cleanup": True,
            "alert_enabled": True,
            "export_interval_minutes": 60,  # 每小时导出一次数据
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger("PerformanceMonitor")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # 创建文件处理器
            log_file = Path("logs/performance_monitor.log")
            log_file.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.INFO)

            # 创建格式器
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def start_monitoring(self):
        """启动性能监控"""
        if self.is_monitoring:
            self.logger.warning("性能监控已在运行中")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitor_thread.start()

        self.logger.info("🚀 性能监控系统已启动")
        print("🚀 性能监控系统已启动")

    def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        self.logger.info("⏹️ 性能监控系统已停止")
        print("⏹️ 性能监控系统已停止")

    def _monitoring_loop(self):
        """监控主循环"""
        last_export = datetime.now()

        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = self.collect_system_metrics()
                self.metrics_history.append(metrics)

                # 检查警报条件
                if self.config.get("alert_enabled", True):
                    self._check_alerts(metrics)

                # 清理历史数据
                if self.config.get("auto_cleanup", True):
                    self._cleanup_old_data()

                # 定期导出数据
                now = datetime.now()
                if (now - last_export).total_seconds() >= self.config.get(
                    "export_interval_minutes", 60
                ) * 60:
                    self._export_metrics()
                    last_export = now

                # 等待下一次检查
                time.sleep(self.config.get("monitoring_interval", 30))

            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再重试

    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统性能指标"""
        try:
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 获取内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 获取磁盘使用率
            try:
                disk = psutil.disk_usage("/")
                disk_usage = (disk.used / disk.total) * 100
            except:
                disk_usage = 0.0

            # 获取网络IO
            try:
                network_io = psutil.net_io_counters()._asdict()
            except:
                network_io = {}

            # 获取进程数量
            process_count = len(psutil.pids())

            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
            )

            return metrics

        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            # 返回默认指标
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_usage=0.0,
                network_io={},
                process_count=0,
            )

    def monitor_trading_performance(
        self,
        operation_start: float,
        api_response_time: float = 0,
        orders_count: int = 1,
        errors_count: int = 0,
    ) -> TradingMetrics:
        """监控交易操作性能"""
        execution_time = (time.time() - operation_start) * 1000
        success_rate = (
            (orders_count - errors_count) / orders_count
            if orders_count > 0
            else 0
        )

        metrics = TradingMetrics(
            timestamp=datetime.now(),
            execution_time_ms=execution_time,
            api_response_time_ms=api_response_time,
            orders_processed=orders_count,
            errors_count=errors_count,
            success_rate=success_rate,
            performance_grade=self._grade_performance(execution_time),
        )

        self.trading_history.append(metrics)

        # 检查交易性能警报
        self._check_trading_alerts(metrics)

        return metrics

    def _grade_performance(self, execution_time: float) -> str:
        """性能等级评估"""
        if execution_time < 100:
            return "优秀"
        elif execution_time < 300:
            return "良好"
        elif execution_time < 500:
            return "一般"
        else:
            return "需优化"

    def _check_alerts(self, metrics: SystemMetrics):
        """检查系统性能警报"""
        alerts = []

        # 检查CPU使用率
        if metrics.cpu_percent > self.alert_thresholds["cpu_usage"]:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "HIGH_CPU",
                    "message": f"CPU使用率过高: {metrics.cpu_percent:.1f}%",
                    "value": metrics.cpu_percent,
                    "threshold": self.alert_thresholds["cpu_usage"],
                }
            )

        # 检查内存使用率
        if metrics.memory_percent > self.alert_thresholds["memory_usage"]:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "HIGH_MEMORY",
                    "message": f"内存使用率过高: {metrics.memory_percent:.1f}%",
                    "value": metrics.memory_percent,
                    "threshold": self.alert_thresholds["memory_usage"],
                }
            )

        # 触发警报回调
        for alert in alerts:
            self._trigger_alert(alert)

    def _check_trading_alerts(self, metrics: TradingMetrics):
        """检查交易性能警报"""
        alerts = []

        # 检查执行时间
        if (
            metrics.execution_time_ms
            > self.alert_thresholds["order_execution_time"]
        ):
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "SLOW_EXECUTION",
                    "message": f"订单执行缓慢: {metrics.execution_time_ms:.1f}ms",
                    "value": metrics.execution_time_ms,
                    "threshold": self.alert_thresholds["order_execution_time"],
                }
            )

        # 检查API响应时间
        if (
            metrics.api_response_time_ms
            > self.alert_thresholds["api_response_time"]
        ):
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "SLOW_API",
                    "message": f"API响应缓慢: {metrics.api_response_time_ms:.1f}ms",
                    "value": metrics.api_response_time_ms,
                    "threshold": self.alert_thresholds["api_response_time"],
                }
            )

        # 检查错误率
        if metrics.success_rate < (1 - self.alert_thresholds["error_rate"]):
            alerts.append(
                {
                    "level": "CRITICAL",
                    "type": "HIGH_ERROR_RATE",
                    "message": f"错误率过高: {(1-metrics.success_rate)*100:.1f}%",
                    "value": 1 - metrics.success_rate,
                    "threshold": self.alert_thresholds["error_rate"],
                }
            )

        # 触发警报回调
        for alert in alerts:
            self._trigger_alert(alert)

    def _trigger_alert(self, alert: Dict[str, Any]):
        """触发警报"""
        self.logger.warning(f"⚠️ 性能警报: {alert['message']}")

        # 调用注册的警报回调
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"警报回调执行失败: {e}")

    def add_alert_callback(self, callback: Callable):
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)

    def _cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(
            hours=self.config.get("history_retention_hours", 24)
        )

        # 清理系统指标历史
        self.metrics_history = [
            m for m in self.metrics_history if m.timestamp > cutoff_time
        ]

        # 清理交易指标历史
        self.trading_history = [
            m for m in self.trading_history if m.timestamp > cutoff_time
        ]

    def _export_metrics(self):
        """导出性能指标到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 导出系统指标
            if self.metrics_history:
                system_file = (
                    self.metrics_dir / f"system_metrics_{timestamp}.json"
                )
                with open(system_file, "w", encoding="utf-8") as f:
                    data = [
                        m.to_dict() for m in self.metrics_history[-100:]
                    ]  # 只保存最近100条
                    json.dump(data, f, indent=2, ensure_ascii=False)

            # 导出交易指标
            if self.trading_history:
                trading_file = (
                    self.metrics_dir / f"trading_metrics_{timestamp}.json"
                )
                with open(trading_file, "w", encoding="utf-8") as f:
                    data = [
                        m.to_dict() for m in self.trading_history[-100:]
                    ]  # 只保存最近100条
                    json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"✅ 性能指标已导出: {timestamp}")

        except Exception as e:
            self.logger.error(f"导出性能指标失败: {e}")

    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "暂无监控数据"}

        latest = self.metrics_history[-1]

        # 计算平均值（最近10个数据点）
        recent_metrics = self.metrics_history[-10:]
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(
            recent_metrics
        )
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(
            recent_metrics
        )

        return {
            "status": (
                "healthy" if avg_cpu < 70 and avg_memory < 80 else "warning"
            ),
            "timestamp": latest.timestamp.isoformat(),
            "cpu_percent": latest.cpu_percent,
            "memory_percent": latest.memory_percent,
            "disk_usage": latest.disk_usage,
            "process_count": latest.process_count,
            "avg_cpu_10min": avg_cpu,
            "avg_memory_10min": avg_memory,
            "monitoring_active": self.is_monitoring,
            "data_points": len(self.metrics_history),
        }

    def get_trading_summary(self) -> Dict[str, Any]:
        """获取交易性能摘要"""
        if not self.trading_history:
            return {"status": "no_data", "message": "暂无交易数据"}

        recent_trades = self.trading_history[-50:]  # 最近50个交易

        avg_execution_time = sum(
            t.execution_time_ms for t in recent_trades
        ) / len(recent_trades)
        avg_success_rate = sum(t.success_rate for t in recent_trades) / len(
            recent_trades
        )
        total_orders = sum(t.orders_processed for t in recent_trades)
        total_errors = sum(t.errors_count for t in recent_trades)

        return {
            "status": (
                "healthy"
                if avg_execution_time < 300 and avg_success_rate > 0.95
                else "warning"
            ),
            "avg_execution_time_ms": avg_execution_time,
            "avg_success_rate": avg_success_rate,
            "total_orders_processed": total_orders,
            "total_errors": total_errors,
            "recent_trades_count": len(recent_trades),
            "performance_distribution": {
                "优秀": len(
                    [t for t in recent_trades if t.performance_grade == "优秀"]
                ),
                "良好": len(
                    [t for t in recent_trades if t.performance_grade == "良好"]
                ),
                "一般": len(
                    [t for t in recent_trades if t.performance_grade == "一般"]
                ),
                "需优化": len(
                    [
                        t
                        for t in recent_trades
                        if t.performance_grade == "需优化"
                    ]
                ),
            },
        }


class ResourceOptimizer:
    """资源使用优化器"""

    def __init__(self):
        self.connection_pool = {}
        self.cache_size = 1000
        self._data_cache = {}
        self._cache_timestamps = {}
        self.cache_ttl = 300  # 5分钟缓存TTL

        self.logger = logging.getLogger("ResourceOptimizer")

    def cached_api_call(
        self, cache_key: str, api_func: Callable, *args, **kwargs
    ) -> Any:
        """带缓存的API调用"""
        now = time.time()

        # 检查缓存是否有效
        if (
            cache_key in self._data_cache
            and cache_key in self._cache_timestamps
            and now - self._cache_timestamps[cache_key] < self.cache_ttl
        ):

            self.logger.debug(f"缓存命中: {cache_key}")
            return self._data_cache[cache_key]

        # 执行API调用
        try:
            result = api_func(*args, **kwargs)

            # 更新缓存
            self._data_cache[cache_key] = result
            self._cache_timestamps[cache_key] = now

            # 清理过期缓存
            self._cleanup_cache()

            self.logger.debug(f"缓存更新: {cache_key}")
            return result

        except Exception as e:
            self.logger.error(f"API调用失败: {cache_key}, 错误: {e}")

            # 如果有过期缓存，返回过期数据
            if cache_key in self._data_cache:
                self.logger.warning(f"使用过期缓存: {cache_key}")
                return self._data_cache[cache_key]

            raise

    def _cleanup_cache(self):
        """清理过期缓存"""
        now = time.time()
        expired_keys = [
            key
            for key, timestamp in self._cache_timestamps.items()
            if now - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            self._data_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        # 限制缓存大小
        if len(self._data_cache) > self.cache_size:
            # 删除最旧的数据
            sorted_keys = sorted(
                self._cache_timestamps.items(), key=lambda x: x[1]
            )

            keys_to_remove = [
                k for k, _ in sorted_keys[: len(sorted_keys) - self.cache_size]
            ]
            for key in keys_to_remove:
                self._data_cache.pop(key, None)
                self._cache_timestamps.pop(key, None)

    async def batch_api_requests(
        self, requests: List[Callable], max_concurrent: int = 5
    ) -> List[Any]:
        """批量处理API请求以提高效率"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def limited_request(request_func):
            async with semaphore:
                return await self._execute_async_request(request_func)

        try:
            results = await asyncio.gather(
                *[limited_request(req) for req in requests]
            )
            return results
        except Exception as e:
            self.logger.error(f"批量请求失败: {e}")
            return []

    async def _execute_async_request(self, request_func: Callable) -> Any:
        """执行异步请求"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, request_func)

    def optimize_memory_usage(self):
        """内存使用优化"""
        import gc

        # 清理缓存
        self._data_cache.clear()
        self._cache_timestamps.clear()

        # 强制垃圾回收
        collected = gc.collect()

        self.logger.info(f"🧹 内存优化完成，清理了 {collected} 个对象")
        return collected


# 全局性能监控实例
performance_monitor = None


def get_performance_monitor(
    config: Optional[Dict] = None,
) -> PerformanceMonitor:
    """获取性能监控实例"""
    global performance_monitor

    if performance_monitor is None:
        performance_monitor = PerformanceMonitor(config)

    return performance_monitor


def start_system_monitoring(config: Optional[Dict] = None):
    """启动系统性能监控"""
    monitor = get_performance_monitor(config)
    monitor.start_monitoring()
    return monitor


if __name__ == "__main__":
    # 演示性能监控系统
    print("🚀 启动性能监控系统演示")

    # 启动监控
    monitor = start_system_monitoring(
        {"monitoring_interval": 10, "alert_enabled": True}  # 10秒间隔用于演示
    )

    # 添加警报回调
    def alert_handler(alert):
        print(f"🚨 警报: {alert['message']}")

    monitor.add_alert_callback(alert_handler)

    try:
        # 模拟一些交易操作
        for i in range(5):
            start_time = time.time()
            time.sleep(0.1)  # 模拟API调用

            # 记录交易性能
            trading_metrics = monitor.monitor_trading_performance(
                operation_start=start_time,
                api_response_time=100,
                orders_count=1,
                errors_count=0,
            )

            print(
                f"交易 {i+1}: {trading_metrics.performance_grade} ({trading_metrics.execution_time_ms:.1f}ms)"
            )
            time.sleep(2)

        # 显示当前状态
        print("\n📊 系统状态:")
        status = monitor.get_current_status()
        for key, value in status.items():
            print(f"  {key}: {value}")

        print("\n📈 交易摘要:")
        summary = monitor.get_trading_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")

        # 运行30秒后停止
        print("\n⏳ 监控运行中，30秒后自动停止...")
        time.sleep(30)

    finally:
        monitor.stop_monitoring()
        print("✅ 演示完成")

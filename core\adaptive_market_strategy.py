#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自适应市场策略
Adaptive Market Strategy

根据市场条件自动调整交易参数的智能策略系统
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class MarketCondition:
    """市场状态"""

    trend: str  # 'bull', 'bear', 'sideways'
    volatility: float  # 波动率
    volume_ratio: float  # 成交量比率
    sentiment: str  # 'bullish', 'bearish', 'neutral'
    strength: float  # 趋势强度 0-1


@dataclass
class AdaptiveParams:
    """自适应参数"""

    risk_per_trade: float
    max_position_size: float
    min_profit_margin: float
    stop_loss_pct: float
    take_profit_pct: float
    min_win_rate: float
    min_signal_strength: float
    hold_time_target: int  # 目标持仓时间(分钟)


class AdaptiveMarketStrategy:
    """自适应市场策略"""

    def __init__(self, initial_capital: float = 1000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital

        # 基础参数配置 (1000 USDT 稳健型)
        self.base_params = AdaptiveParams(
            risk_per_trade=1.5,
            max_position_size=20.0,
            min_profit_margin=0.6,
            stop_loss_pct=2.5,
            take_profit_pct=5.0,
            min_win_rate=70.0,
            min_signal_strength=75.0,
            hold_time_target=120,  # 2小时
        )

        # 当前参数
        self.current_params = self.base_params

        # 市场状态历史
        self.market_history = []
        self.performance_history = []

        # 自适应配置
        self.adaptation_enabled = True
        self.min_history_length = 10  # 最少需要10个数据点才开始自适应

    def analyze_market_condition(self, market_data: Dict) -> MarketCondition:
        """分析市场状态"""
        try:
            # 获取价格数据
            prices = []
            volumes = []

            for symbol, data in market_data.items():
                if "ohlcv" in data:
                    df = data["ohlcv"]
                    prices.extend(df["close"].tolist()[-20:])  # 最近20个价格点
                    volumes.extend(df["volume"].tolist()[-20:])

            if len(prices) < 10:
                # 数据不足，返回中性状态
                return MarketCondition(
                    trend="sideways",
                    volatility=0.02,
                    volume_ratio=1.0,
                    sentiment="neutral",
                    strength=0.5,
                )

            # 计算趋势
            trend, strength = self._calculate_trend(prices)

            # 计算波动率
            volatility = self._calculate_volatility(prices)

            # 计算成交量比率
            volume_ratio = self._calculate_volume_ratio(volumes)

            # 判断市场情绪
            sentiment = self._determine_sentiment(
                trend, volatility, volume_ratio
            )

            return MarketCondition(
                trend=trend,
                volatility=volatility,
                volume_ratio=volume_ratio,
                sentiment=sentiment,
                strength=strength,
            )

        except Exception as e:
            logger.error(f"分析市场状态失败: {e}")
            # 返回默认中性状态
            return MarketCondition(
                trend="sideways",
                volatility=0.02,
                volume_ratio=1.0,
                sentiment="neutral",
                strength=0.5,
            )

    def _calculate_trend(self, prices: List[float]) -> Tuple[str, float]:
        """计算趋势方向和强度"""
        if len(prices) < 5:
            return "sideways", 0.5

        # 计算短期和长期移动平均
        short_ma = np.mean(prices[-5:])
        long_ma = (
            np.mean(prices[-10:]) if len(prices) >= 10 else np.mean(prices)
        )

        # 计算趋势强度
        price_change = (prices[-1] - prices[0]) / prices[0]
        strength = min(abs(price_change) * 10, 1.0)  # 标准化到0-1

        # 判断趋势方向
        if short_ma > long_ma * 1.02:  # 上涨超过2%
            return "bull", strength
        elif short_ma < long_ma * 0.98:  # 下跌超过2%
            return "bear", strength
        else:
            return "sideways", strength * 0.5  # 横盘时强度减半

    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算波动率"""
        if len(prices) < 2:
            return 0.02

        # 计算价格变化率
        returns = []
        for i in range(1, len(prices)):
            returns.append((prices[i] - prices[i - 1]) / prices[i - 1])

        # 计算标准差作为波动率
        volatility = np.std(returns) if returns else 0.02
        return min(volatility, 0.1)  # 限制最大波动率为10%

    def _calculate_volume_ratio(self, volumes: List[float]) -> float:
        """计算成交量比率"""
        if len(volumes) < 5:
            return 1.0

        recent_volume = np.mean(volumes[-3:])  # 最近3个周期
        avg_volume = np.mean(volumes[:-3])  # 之前的平均

        if avg_volume > 0:
            return recent_volume / avg_volume
        else:
            return 1.0

    def _determine_sentiment(
        self, trend: str, volatility: float, volume_ratio: float
    ) -> str:
        """判断市场情绪"""
        # 综合考虑趋势、波动率和成交量
        if trend == "bull" and volume_ratio > 1.2:
            return "bullish"
        elif trend == "bear" and volume_ratio > 1.2:
            return "bearish"
        elif volatility > 0.05:  # 高波动
            return "volatile"
        else:
            return "neutral"

    def adapt_parameters(
        self, market_condition: MarketCondition, recent_performance: Dict
    ) -> AdaptiveParams:
        """根据市场条件自适应调整参数"""
        if not self.adaptation_enabled:
            return self.current_params

        # 从基础参数开始
        adapted = AdaptiveParams(
            risk_per_trade=self.base_params.risk_per_trade,
            max_position_size=self.base_params.max_position_size,
            min_profit_margin=self.base_params.min_profit_margin,
            stop_loss_pct=self.base_params.stop_loss_pct,
            take_profit_pct=self.base_params.take_profit_pct,
            min_win_rate=self.base_params.min_win_rate,
            min_signal_strength=self.base_params.min_signal_strength,
            hold_time_target=self.base_params.hold_time_target,
        )

        # 根据市场趋势调整
        if market_condition.trend == "bull":
            adapted = self._adapt_for_bull_market(adapted, market_condition)
        elif market_condition.trend == "bear":
            adapted = self._adapt_for_bear_market(adapted, market_condition)
        else:  # sideways
            adapted = self._adapt_for_sideways_market(
                adapted, market_condition
            )

        # 根据波动率调整
        adapted = self._adapt_for_volatility(
            adapted, market_condition.volatility
        )

        # 根据历史表现调整
        adapted = self._adapt_for_performance(adapted, recent_performance)

        # 确保参数在合理范围内
        adapted = self._validate_parameters(adapted)

        return adapted

    def _adapt_for_bull_market(
        self, params: AdaptiveParams, condition: MarketCondition
    ) -> AdaptiveParams:
        """牛市适应"""
        # 牛市策略：适当增加仓位，延长持仓时间
        params.max_position_size *= 1.2  # 增加20%仓位
        params.take_profit_pct *= 1.3  # 提高止盈目标
        params.hold_time_target = int(
            params.hold_time_target * 1.5
        )  # 延长持仓
        params.min_signal_strength *= 0.9  # 稍微降低信号要求

        # 强势牛市进一步调整
        if condition.strength > 0.7:
            params.risk_per_trade *= 1.1
            params.min_win_rate *= 0.95

        return params

    def _adapt_for_bear_market(
        self, params: AdaptiveParams, condition: MarketCondition
    ) -> AdaptiveParams:
        """熊市适应"""
        # 熊市策略：降低仓位，快进快出
        params.max_position_size *= 0.7  # 减少30%仓位
        params.stop_loss_pct *= 0.8  # 收紧止损
        params.take_profit_pct *= 0.8  # 降低止盈目标
        params.hold_time_target = int(
            params.hold_time_target * 0.6
        )  # 缩短持仓
        params.min_signal_strength *= 1.1  # 提高信号要求

        # 强势熊市进一步调整
        if condition.strength > 0.7:
            params.risk_per_trade *= 0.8
            params.min_win_rate *= 1.05

        return params

    def _adapt_for_sideways_market(
        self, params: AdaptiveParams, condition: MarketCondition
    ) -> AdaptiveParams:
        """横盘市场适应"""
        # 横盘策略：区间交易，快速止盈止损
        params.take_profit_pct *= 0.7  # 降低止盈目标
        params.stop_loss_pct *= 0.9  # 稍微收紧止损
        params.hold_time_target = int(
            params.hold_time_target * 0.8
        )  # 缩短持仓
        params.min_profit_margin *= 0.8  # 降低利润要求

        return params

    def _adapt_for_volatility(
        self, params: AdaptiveParams, volatility: float
    ) -> AdaptiveParams:
        """根据波动率调整"""
        if volatility > 0.05:  # 高波动
            # 高波动：降低仓位，收紧止损
            params.max_position_size *= 0.8
            params.risk_per_trade *= 0.9
            params.stop_loss_pct *= 0.8
            params.min_signal_strength *= 1.1
        elif volatility < 0.01:  # 低波动
            # 低波动：可以适当增加仓位
            params.max_position_size *= 1.1
            params.risk_per_trade *= 1.05

        return params

    def _adapt_for_performance(
        self, params: AdaptiveParams, performance: Dict
    ) -> AdaptiveParams:
        """根据历史表现调整"""
        if not performance:
            return params

        win_rate = performance.get("win_rate", 0.7)
        profit_factor = performance.get("profit_factor", 1.5)
        drawdown = performance.get("max_drawdown", 0.0)

        # 表现良好时适当放宽
        if win_rate > 0.8 and profit_factor > 2.0:
            params.risk_per_trade *= 1.05
            params.max_position_size *= 1.1
            params.min_signal_strength *= 0.95

        # 表现不佳时收紧
        elif win_rate < 0.6 or profit_factor < 1.2:
            params.risk_per_trade *= 0.9
            params.max_position_size *= 0.9
            params.min_signal_strength *= 1.1

        # 回撤过大时大幅收紧
        if drawdown > 0.08:  # 回撤超过8%
            params.risk_per_trade *= 0.7
            params.max_position_size *= 0.7
            params.min_win_rate *= 1.1

        return params

    def _validate_parameters(self, params: AdaptiveParams) -> AdaptiveParams:
        """验证并修正参数范围"""
        # 风险控制限制
        params.risk_per_trade = max(0.5, min(params.risk_per_trade, 3.0))
        params.max_position_size = max(
            5.0, min(params.max_position_size, 35.0)
        )
        params.min_profit_margin = max(0.3, min(params.min_profit_margin, 2.0))
        params.stop_loss_pct = max(1.0, min(params.stop_loss_pct, 5.0))
        params.take_profit_pct = max(2.0, min(params.take_profit_pct, 10.0))
        params.min_win_rate = max(50.0, min(params.min_win_rate, 85.0))
        params.min_signal_strength = max(
            60.0, min(params.min_signal_strength, 90.0)
        )
        params.hold_time_target = max(
            30, min(params.hold_time_target, 480)
        )  # 30分钟-8小时

        return params

    def get_current_strategy_mode(
        self, market_condition: MarketCondition
    ) -> str:
        """获取当前策略模式"""
        if (
            market_condition.trend == "bull"
            and market_condition.strength > 0.6
        ):
            return "🚀 激进牛市模式"
        elif (
            market_condition.trend == "bear"
            and market_condition.strength > 0.6
        ):
            return "🛡️ 防守熊市模式"
        elif market_condition.volatility > 0.05:
            return "⚡ 高波动模式"
        elif market_condition.trend == "sideways":
            return "🌊 区间震荡模式"
        else:
            return "⚖️ 平衡模式"

    def update_performance_history(self, performance_data: Dict):
        """更新表现历史"""
        performance_data["timestamp"] = datetime.now()
        self.performance_history.append(performance_data)

        # 保持历史长度
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-50:]

    def get_adaptation_summary(self) -> Dict:
        """获取自适应摘要"""
        if not self.market_history:
            return {"status": "等待市场数据"}

        latest_condition = self.market_history[-1]

        return {
            "自适应状态": "启用" if self.adaptation_enabled else "禁用",
            "当前市场": latest_condition.trend,
            "市场强度": f"{latest_condition.strength:.1%}",
            "波动率": f"{latest_condition.volatility:.2%}",
            "策略模式": self.get_current_strategy_mode(latest_condition),
            "参数调整": {
                "风险比例": f"{self.current_params.risk_per_trade:.1f}%",
                "最大仓位": f"{self.current_params.max_position_size:.1f}%",
                "止损": f"{self.current_params.stop_loss_pct:.1f}%",
                "止盈": f"{self.current_params.take_profit_pct:.1f}%",
            },
        }

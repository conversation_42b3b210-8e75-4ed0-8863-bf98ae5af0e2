{".class": "MypyFile", "_fullname": "matplotlib.tri", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CubicTriInterpolator": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triinterpolate.CubicTriInterpolator", "kind": "Gdef"}, "LinearTriInterpolator": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triinterpolate.LinearTriInterpolator", "kind": "Gdef"}, "TrapezoidMapTriFinder": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._trifinder.TrapezoidMapTriFinder", "kind": "Gdef"}, "TriAnalyzer": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._tritools.TriAnalyzer", "kind": "Gdef"}, "TriContourSet": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._tricontour.TriContourSet", "kind": "Gdef"}, "TriFinder": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._trifinder.TriFinder", "kind": "Gdef"}, "TriInterpolator": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triinterpolate.TriInterpolator", "kind": "Gdef"}, "TriRefiner": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._trirefine.TriRefiner", "kind": "Gdef"}, "Triangulation": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triangulation.Triangulation", "kind": "Gdef"}, "UniformTriRefiner": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._trirefine.UniformTriRefiner", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.tri.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "tricontour": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._tricontour.tricontour", "kind": "Gdef"}, "tricontourf": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._tricontour.tricontourf", "kind": "Gdef"}, "tripcolor": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._tripcolor.tripcolor", "kind": "Gdef"}, "triplot": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triplot.triplot", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\tri\\__init__.py"}
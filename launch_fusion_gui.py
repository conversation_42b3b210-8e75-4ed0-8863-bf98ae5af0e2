#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动融合交易GUI
Launch Fusion Trading GUI

启动新创建的融合交易界面系统
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    required_files = [
        'core/fusion_trading_gui.py',
        'core/config_manager.py',
        'core/ux_enhancement.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有依赖文件检查通过")
    return True

def show_fusion_info():
    """显示融合GUI信息"""
    print("=" * 60)
    print("🚀 融合交易GUI系统")
    print("=" * 60)
    print("📋 功能特点:")
    print("• 模块化设计 - 可插拔组件架构")
    print("• 自适应布局 - 基础/专业/高级三种模式")
    print("• 实时监控 - 账户余额、盈亏、订单状态")
    print("• 智能策略 - 多种交易策略支持")
    print("• 数据分析 - 详细的交易分析报告")
    print("• UX增强 - 智能提示和用户引导")
    print()
    print("🎯 集成的GUI界面优点:")
    print("• simple_spot_gui.py - 简洁的交易操作")
    print("• optimized_trading_gui.py - 优化的用户体验")
    print("• ultimate_spot_trading_gui.py - 完整的功能集")
    print("• multi_pairs_gui.py - 多交易对支持")
    print("• spot_snowball_gui.py - 滚雪球策略")
    print("• 其他4个GUI界面的最佳特性")
    print()

def check_configuration():
    """检查配置状态"""
    print("🔧 检查系统配置...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️ 缺少配置文件 (.env)")
        print("💡 使用演示配置启动...")
        return True
    
    print("✅ 配置文件检查完成")
    return True

def launch_fusion_gui():
    """启动融合GUI"""
    print("🚀 启动融合交易GUI...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path.cwd() / 'core')
        
        # 启动GUI
        cmd = [sys.executable, 'core/fusion_trading_gui.py']
        print(f"📱 执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一下看是否有立即错误
        try:
            stdout, stderr = process.communicate(timeout=3)
            if process.returncode != 0:
                print(f"❌ 启动失败:")
                print(f"标准输出: {stdout}")
                print(f"错误输出: {stderr}")
                return False
        except subprocess.TimeoutExpired:
            # 超时说明程序正在运行，这是好事
            print("✅ 融合交易GUI已成功启动")
            print("📱 GUI界面应该已经打开")
            return True
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 故障排除建议:")
        print("1. 检查Python环境是否正确安装")
        print("2. 确保所有依赖文件存在")
        print("3. 查看core/fusion_trading_gui.py是否有语法错误")
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n📖 使用提示:")
    print("-" * 30)
    print("🎯 布局模式:")
    print("• 基础模式 - 菜单 > 视图 > 基础模式")
    print("• 专业模式 - 菜单 > 视图 > 专业模式 (默认)")
    print("• 高级模式 - 菜单 > 视图 > 高级模式")
    print()
    print("🔧 主要面板:")
    print("• 左侧：交易面板、实时监控、策略管理")
    print("• 右侧：数据分析、交易历史、系统设置")
    print()
    print("⚡ 快捷操作:")
    print("• F1 - 帮助文档")
    print("• Ctrl+N - 新建配置")
    print("• Ctrl+S - 保存设置")

def main():
    """主函数"""
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示系统信息
    show_fusion_info()
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 解决方案:")
        print("1. 确保运行在正确的项目目录中")
        print("2. 检查是否有文件被意外删除")
        print("3. 重新下载缺失的文件")
        input("\n按回车键退出...")
        return False
    
    # 检查配置
    if not check_configuration():
        print("\n💡 配置问题解决方案:")
        print("1. 复制 .env.example 到 .env")
        print("2. 编辑 .env 文件配置API密钥")
        print("3. 或使用演示模式运行")
        input("\n按回车键退出...")
        return False
    
    # 启动GUI
    print()
    success = launch_fusion_gui()
    
    if success:
        show_usage_tips()
        print("\n🎉 融合交易GUI启动成功！")
        print("如果GUI窗口没有显示，请检查任务栏或重新运行此脚本。")
    else:
        print("\n❌ 启动失败，请检查错误信息并重试。")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n📞 如需技术支持，请提供:")
            print("- 错误信息截图")
            print("- 系统环境信息")
            print("- 操作步骤描述")
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
    
    input("\n按回车键退出...")

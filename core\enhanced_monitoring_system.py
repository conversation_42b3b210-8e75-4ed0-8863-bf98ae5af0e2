#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强监控告警系统
Enhanced Monitoring and Alert System with Real-time Intelligence
"""

import time
import threading
import json
import sqlite3
from typing import Dict, List, Callable, Optional, Any
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import statistics
import numpy as np


class AlertSeverity(Enum):
    """告警严重程度"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


class MonitoringMetric(Enum):
    """监控指标类型"""
    PRICE_VOLATILITY = "price_volatility"
    VOLUME_ANOMALY = "volume_anomaly"
    SPREAD_ANALYSIS = "spread_analysis"
    LATENCY_MONITORING = "latency_monitoring"
    ERROR_RATE = "error_rate"
    BALANCE_THRESHOLD = "balance_threshold"
    TRADE_PERFORMANCE = "trade_performance"
    SYSTEM_HEALTH = "system_health"


@dataclass
class MetricData:
    """监控指标数据"""
    metric_type: MonitoringMetric
    value: float
    timestamp: datetime
    source: str
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['metric_type'] = self.metric_type.value
        return data


class IntelligentAlert:
    """智能告警"""
    
    def __init__(self, alert_id: str, metric_type: MonitoringMetric, 
                 severity: AlertSeverity, title: str, message: str,
                 threshold_value: float, actual_value: float,
                 confidence: float = 1.0, metadata: Dict = None):
        """初始化智能告警"""
        self.alert_id = alert_id
        self.metric_type = metric_type
        self.severity = severity
        self.title = title
        self.message = message
        self.threshold_value = threshold_value
        self.actual_value = actual_value
        self.confidence = confidence
        self.metadata = metadata or {}
        
        self.timestamp = datetime.now()
        self.acknowledged = False
        self.resolved = False
        self.escalated = False
        self.response_time = None
        self.resolution_time = None
        
        # 智能分析
        self.trend_analysis = None
        self.impact_assessment = None
        self.recommended_actions = []
    
    def acknowledge(self, user: str = "system"):
        """确认告警"""
        self.acknowledged = True
        self.response_time = datetime.now()
        self.metadata['acknowledged_by'] = user
        self.metadata['acknowledge_time'] = self.response_time.isoformat()
    
    def resolve(self, user: str = "system", resolution_note: str = ""):
        """解决告警"""
        self.resolved = True
        self.resolution_time = datetime.now()
        self.metadata['resolved_by'] = user
        self.metadata['resolution_time'] = self.resolution_time.isoformat()
        self.metadata['resolution_note'] = resolution_note
    
    def escalate(self, reason: str = ""):
        """升级告警"""
        self.escalated = True
        self.metadata['escalated'] = True
        self.metadata['escalation_reason'] = reason
        self.metadata['escalation_time'] = datetime.now().isoformat()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'alert_id': self.alert_id,
            'metric_type': self.metric_type.value,
            'severity': self.severity.value,
            'title': self.title,
            'message': self.message,
            'threshold_value': self.threshold_value,
            'actual_value': self.actual_value,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'acknowledged': self.acknowledged,
            'resolved': self.resolved,
            'escalated': self.escalated,
            'response_time': self.response_time.isoformat() if self.response_time else None,
            'resolution_time': self.resolution_time.isoformat() if self.resolution_time else None,
            'metadata': self.metadata
        }


class MetricAnalyzer:
    """指标分析器"""
    
    def __init__(self, window_size: int = 100):
        """初始化分析器"""
        self.window_size = window_size
        self.metric_history = {}
        
    def add_metric(self, metric: MetricData):
        """添加指标数据"""
        metric_key = f"{metric.metric_type.value}_{metric.source}"
        
        if metric_key not in self.metric_history:
            self.metric_history[metric_key] = []
        
        self.metric_history[metric_key].append(metric)
        
        # 保持窗口大小
        if len(self.metric_history[metric_key]) > self.window_size:
            self.metric_history[metric_key] = self.metric_history[metric_key][-self.window_size:]
    
    def analyze_trend(self, metric_type: MonitoringMetric, source: str) -> Dict:
        """分析趋势"""
        metric_key = f"{metric_type.value}_{source}"
        
        if metric_key not in self.metric_history or len(self.metric_history[metric_key]) < 10:
            return {'trend': 'insufficient_data', 'confidence': 0.0}
        
        values = [m.value for m in self.metric_history[metric_key][-20:]]  # 最近20个数据点
        
        # 计算趋势
        x = list(range(len(values)))
        trend_slope = np.polyfit(x, values, 1)[0] if len(values) > 1 else 0
        
        # 计算变异系数
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values) if len(values) > 1 else 0
        cv = std_val / mean_val if mean_val != 0 else 0
        
        # 趋势判断
        if abs(trend_slope) < mean_val * 0.01:  # 变化小于1%
            trend = 'stable'
        elif trend_slope > 0:
            trend = 'increasing'
        else:
            trend = 'decreasing'
        
        return {
            'trend': trend,
            'slope': trend_slope,
            'volatility': cv,
            'confidence': min(1.0, len(values) / 20.0),
            'mean': mean_val,
            'std': std_val
        }
    
    def detect_anomaly(self, metric_type: MonitoringMetric, source: str, 
                      current_value: float, threshold_multiplier: float = 2.0) -> Dict:
        """异常检测"""
        metric_key = f"{metric_type.value}_{source}"
        
        if metric_key not in self.metric_history or len(self.metric_history[metric_key]) < 10:
            return {'is_anomaly': False, 'confidence': 0.0}
        
        values = [m.value for m in self.metric_history[metric_key]]
        
        # 计算统计指标
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values) if len(values) > 1 else 0
        
        # Z-score异常检测
        z_score = abs(current_value - mean_val) / std_val if std_val > 0 else 0
        is_anomaly = z_score > threshold_multiplier
        
        # 计算置信度
        confidence = min(1.0, z_score / threshold_multiplier) if is_anomaly else 0.0
        
        return {
            'is_anomaly': is_anomaly,
            'z_score': z_score,
            'confidence': confidence,
            'threshold': threshold_multiplier,
            'mean': mean_val,
            'std': std_val
        }


class EnhancedMonitoringSystem:
    """增强监控系统"""
    
    def __init__(self, db_path: str = "monitoring.db"):
        """初始化增强监控系统"""
        self.db_path = db_path
        self.analyzer = MetricAnalyzer()
        self.alerts = []
        self.active_monitors = {}
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        
        # 数据源和回调
        self.data_sources = {}
        self.alert_callbacks = []
        
        # 智能规则
        self.intelligent_rules = {}
        
        # 初始化数据库
        self._init_database()
        
        # 设置默认监控规则
        self._setup_intelligent_rules()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建指标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_type TEXT NOT NULL,
                    value REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    source TEXT NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # 创建告警表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_id TEXT UNIQUE NOT NULL,
                    metric_type TEXT NOT NULL,
                    severity INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    threshold_value REAL,
                    actual_value REAL,
                    confidence REAL,
                    timestamp TEXT NOT NULL,
                    acknowledged INTEGER DEFAULT 0,
                    resolved INTEGER DEFAULT 0,
                    metadata TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print("✅ 监控数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    def _setup_intelligent_rules(self):
        """设置智能监控规则"""
        
        # 价格波动率监控
        def price_volatility_rule(metrics: List[MetricData]) -> Optional[IntelligentAlert]:
            for metric in metrics:
                if metric.metric_type == MonitoringMetric.PRICE_VOLATILITY:
                    trend = self.analyzer.analyze_trend(metric.metric_type, metric.source)
                    anomaly = self.analyzer.detect_anomaly(metric.metric_type, metric.source, metric.value)
                    
                    if anomaly['is_anomaly'] and metric.value > 0.1:  # 10%以上波动
                        severity = AlertSeverity.HIGH if metric.value > 0.2 else AlertSeverity.MEDIUM
                        
                        alert = IntelligentAlert(
                            alert_id=f"volatility_{metric.source}_{int(time.time())}",
                            metric_type=metric.metric_type,
                            severity=severity,
                            title=f"{metric.source} 价格异常波动",
                            message=f"{metric.source} 价格波动率达到 {metric.value:.2%}，超出正常范围",
                            threshold_value=0.1,
                            actual_value=metric.value,
                            confidence=anomaly['confidence']
                        )
                        
                        alert.trend_analysis = trend
                        alert.impact_assessment = self._assess_impact(metric)
                        alert.recommended_actions = self._get_recommendations(metric)
                        
                        return alert
            return None
        
        # 交易延迟监控
        def latency_rule(metrics: List[MetricData]) -> Optional[IntelligentAlert]:
            for metric in metrics:
                if metric.metric_type == MonitoringMetric.LATENCY_MONITORING:
                    if metric.value > 5000:  # 5秒延迟
                        severity = AlertSeverity.CRITICAL if metric.value > 10000 else AlertSeverity.HIGH
                        
                        return IntelligentAlert(
                            alert_id=f"latency_{metric.source}_{int(time.time())}",
                            metric_type=metric.metric_type,
                            severity=severity,
                            title=f"{metric.source} 交易延迟过高",
                            message=f"{metric.source} 响应延迟 {metric.value:.0f}ms，可能影响交易执行",
                            threshold_value=5000,
                            actual_value=metric.value,
                            confidence=1.0
                        )
            return None
        
        # 错误率监控
        def error_rate_rule(metrics: List[MetricData]) -> Optional[IntelligentAlert]:
            for metric in metrics:
                if metric.metric_type == MonitoringMetric.ERROR_RATE:
                    if metric.value > 0.05:  # 5%错误率
                        severity = AlertSeverity.CRITICAL if metric.value > 0.2 else AlertSeverity.HIGH
                        
                        return IntelligentAlert(
                            alert_id=f"error_rate_{metric.source}_{int(time.time())}",
                            metric_type=metric.metric_type,
                            severity=severity,
                            title=f"{metric.source} 错误率过高",
                            message=f"{metric.source} 错误率达到 {metric.value:.2%}，系统可能存在问题",
                            threshold_value=0.05,
                            actual_value=metric.value,
                            confidence=1.0
                        )
            return None
        
        self.intelligent_rules = {
            'price_volatility': price_volatility_rule,
            'latency_monitoring': latency_rule,
            'error_rate': error_rate_rule
        }
    
    def _assess_impact(self, metric: MetricData) -> Dict:
        """评估影响"""
        impact = {
            'severity': 'medium',
            'affected_systems': [],
            'estimated_loss': 0.0,
            'recovery_time': '5-10 minutes'
        }
        
        if metric.metric_type == MonitoringMetric.PRICE_VOLATILITY:
            if metric.value > 0.2:
                impact['severity'] = 'high'
                impact['affected_systems'] = ['trading', 'risk_management']
                impact['estimated_loss'] = metric.value * 1000  # 估算损失
        
        return impact
    
    def _get_recommendations(self, metric: MetricData) -> List[str]:
        """获取建议操作"""
        recommendations = []
        
        if metric.metric_type == MonitoringMetric.PRICE_VOLATILITY:
            recommendations.extend([
                "暂停自动交易",
                "检查市场新闻和事件",
                "调整风险参数",
                "增加监控频率"
            ])
        elif metric.metric_type == MonitoringMetric.LATENCY_MONITORING:
            recommendations.extend([
                "检查网络连接",
                "重启交易连接",
                "切换到备用服务器",
                "联系技术支持"
            ])
        
        return recommendations
    
    def add_metric(self, metric: MetricData):
        """添加监控指标"""
        try:
            # 添加到分析器
            self.analyzer.add_metric(metric)
            
            # 保存到数据库
            self._save_metric_to_db(metric)
            
            # 检查智能规则
            self._check_intelligent_rules([metric])
            
        except Exception as e:
            print(f"❌ 添加指标失败: {e}")
    
    def _save_metric_to_db(self, metric: MetricData):
        """保存指标到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO metrics (metric_type, value, timestamp, source, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                metric.metric_type.value,
                metric.value,
                metric.timestamp.isoformat(),
                metric.source,
                json.dumps(metric.metadata) if metric.metadata else None
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存指标到数据库失败: {e}")
    
    def _check_intelligent_rules(self, metrics: List[MetricData]):
        """检查智能规则"""
        for rule_name, rule_func in self.intelligent_rules.items():
            try:
                alert = rule_func(metrics)
                if alert:
                    self._handle_intelligent_alert(alert)
            except Exception as e:
                print(f"❌ 智能规则 {rule_name} 执行失败: {e}")
    
    def _handle_intelligent_alert(self, alert: IntelligentAlert):
        """处理智能告警"""
        try:
            # 添加到告警列表
            self.alerts.append(alert)
            
            # 保存到数据库
            self._save_alert_to_db(alert)
            
            # 调用回调函数
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    print(f"❌ 告警回调失败: {e}")
            
            # 智能通知
            self._send_intelligent_notification(alert)
            
        except Exception as e:
            print(f"❌ 处理智能告警失败: {e}")
    
    def _save_alert_to_db(self, alert: IntelligentAlert):
        """保存告警到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO alerts 
                (alert_id, metric_type, severity, title, message, threshold_value, 
                 actual_value, confidence, timestamp, acknowledged, resolved, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.alert_id,
                alert.metric_type.value,
                alert.severity.value,
                alert.title,
                alert.message,
                alert.threshold_value,
                alert.actual_value,
                alert.confidence,
                alert.timestamp.isoformat(),
                int(alert.acknowledged),
                int(alert.resolved),
                json.dumps(alert.metadata)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存告警到数据库失败: {e}")
    
    def _send_intelligent_notification(self, alert: IntelligentAlert):
        """发送智能通知"""
        try:
            severity_emoji = {
                AlertSeverity.LOW: "ℹ️",
                AlertSeverity.MEDIUM: "⚠️",
                AlertSeverity.HIGH: "❌",
                AlertSeverity.CRITICAL: "🚨",
                AlertSeverity.EMERGENCY: "🆘"
            }
            
            emoji = severity_emoji.get(alert.severity, "📢")
            
            print(f"\n{emoji} 【智能告警】 {alert.title}")
            print(f"🎯 严重程度: {alert.severity.name}")
            print(f"📊 置信度: {alert.confidence:.2%}")
            print(f"📈 阈值: {alert.threshold_value}, 实际值: {alert.actual_value}")
            print(f"📝 消息: {alert.message}")
            
            if alert.trend_analysis:
                print(f"📈 趋势分析: {alert.trend_analysis}")
            
            if alert.impact_assessment:
                print(f"💥 影响评估: {alert.impact_assessment}")
            
            if alert.recommended_actions:
                print(f"💡 建议操作: {', '.join(alert.recommended_actions)}")
            
            print("-" * 60)
            
        except Exception as e:
            print(f"❌ 发送智能通知失败: {e}")
    
    def get_alert_statistics(self, days: int = 7) -> Dict:
        """获取告警统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            since_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 总告警数
            cursor.execute('SELECT COUNT(*) FROM alerts WHERE timestamp > ?', (since_date,))
            total_alerts = cursor.fetchone()[0]
            
            # 按严重程度统计
            cursor.execute('''
                SELECT severity, COUNT(*) FROM alerts 
                WHERE timestamp > ? GROUP BY severity
            ''', (since_date,))
            severity_stats = dict(cursor.fetchall())
            
            # 按类型统计
            cursor.execute('''
                SELECT metric_type, COUNT(*) FROM alerts 
                WHERE timestamp > ? GROUP BY metric_type
            ''', (since_date,))
            type_stats = dict(cursor.fetchall())
            
            # 响应时间统计
            cursor.execute('''
                SELECT AVG(julianday(metadata) - julianday(timestamp)) * 24 * 60
                FROM alerts WHERE acknowledged = 1 AND timestamp > ?
            ''', (since_date,))
            avg_response_time = cursor.fetchone()[0] or 0
            
            conn.close()
            
            return {
                'total_alerts': total_alerts,
                'severity_distribution': severity_stats,
                'type_distribution': type_stats,
                'avg_response_time_minutes': avg_response_time,
                'period_days': days
            }
            
        except Exception as e:
            print(f"❌ 获取告警统计失败: {e}")
            return {}
    
    def start_monitoring(self):
        """启动增强监控"""
        if self.monitoring_active:
            return
        
        print("🚀 启动增强监控系统...")
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("✅ 增强监控系统已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring_active:
            return
        
        print("🛑 停止增强监控系统...")
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("✅ 增强监控系统已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集指标数据
                current_time = datetime.now()
                
                # 模拟一些指标数据
                test_metrics = [
                    MetricData(
                        MonitoringMetric.PRICE_VOLATILITY,
                        np.random.normal(0.05, 0.02),  # 5%平均波动率
                        current_time,
                        "BTC_USDT"
                    ),
                    MetricData(
                        MonitoringMetric.LATENCY_MONITORING,
                        np.random.normal(2000, 500),  # 2秒平均延迟
                        current_time,
                        "trading_engine"
                    )
                ]
                
                for metric in test_metrics:
                    self.add_metric(metric)
                
                time.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                time.sleep(60)


# 全局增强监控系统
enhanced_monitoring = None

def get_enhanced_monitoring() -> EnhancedMonitoringSystem:
    """获取增强监控系统实例"""
    global enhanced_monitoring
    if enhanced_monitoring is None:
        enhanced_monitoring = EnhancedMonitoringSystem()
    return enhanced_monitoring

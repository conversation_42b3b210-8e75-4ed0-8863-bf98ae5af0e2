{"data_mtime": 1748490882, "dep_lines": [1, 2, 4, 5, 6, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 36, 37, 40, 30, 35, 36, 38, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 20, 10, 20, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.axes._base", "matplotlib.axes._secondary_axes", "matplotlib.artist", "matplotlib.backend_bases", "matplotlib.collections", "matplotlib.colorizer", "matplotlib.colors", "matplotlib.container", "matplotlib.contour", "matplotlib.image", "matplotlib.inset", "matplotlib.legend", "matplotlib.legend_handler", "matplotlib.lines", "matplotlib.mlab", "matplotlib.patches", "matplotlib.quiver", "matplotlib.text", "matplotlib.transforms", "matplotlib.typing", "matplotlib.tri", "matplotlib.table", "matplotlib.stackplot", "matplotlib.streamplot", "PIL.Image", "collections.abc", "numpy.typing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datetime", "PIL", "typing", "numpy", "builtins", "re", "pprint", "copy", "inspect", "os", "warnings", "operator", "html", "sys", "collections", "string", "itertools", "contextlib", "types", "traceback", "_frozen_importlib", "abc", "matplotlib.markers", "matplotlib.path", "matplotlib.tri._triangulation", "matplotlib.tri._tricontour", "matplotlib.tri._tripcolor", "matplotlib.tri._triplot", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence"], "hash": "b2e69b6ef21e6bea53afb96d148623c6fe7991c5", "id": "matplotlib.axes._axes", "ignore_all": true, "interface_hash": "d6cf6007b60cf0e7838ced2f7778f6f485023370", "mtime": 1748487523, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\axes\\_axes.pyi", "plugin_data": null, "size": 26116, "suppressed": [], "version_id": "1.15.0"}
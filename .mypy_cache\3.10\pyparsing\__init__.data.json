{".class": "MypyFile", "_fullname": "pyparsing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "And": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.And", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AtLineStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.AtLineStart", "kind": "Gdef"}, "AtStringStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.AtStringStart", "kind": "Gdef"}, "C": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.C", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CaselessKeyword": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.CaselessKeyword", "kind": "Gdef"}, "CaselessLiteral": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.CaselessLiteral", "kind": "Gdef"}, "Char": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Char", "kind": "Gdef"}, "CharsNotIn": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.CharsNotIn", "kind": "Gdef"}, "CloseMatch": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.CloseMatch", "kind": "Gdef"}, "Combine": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Combine", "kind": "Gdef"}, "DebugExceptionAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.DebugExceptionAction", "kind": "Gdef", "module_public": false}, "DebugStartAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.DebugStartAction", "kind": "Gdef", "module_public": false}, "DebugSuccessAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.DebugSuccessAction", "kind": "Gdef", "module_public": false}, "DelimitedList": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.DelimitedList", "kind": "Gdef"}, "Diagnostics": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Diagnostics", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Dict", "kind": "Gdef"}, "Each": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Each", "kind": "Gdef"}, "Empty": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Empty", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "FollowedBy": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.FollowedBy", "kind": "Gdef"}, "Forward": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Forward", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "GoToColumn": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.GoToColumn", "kind": "Gdef"}, "Group": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Group", "kind": "Gdef"}, "IndentedBlock": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.IndentedBlock", "kind": "Gdef"}, "InfixNotationOperatorArgType": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.InfixNotationOperatorArgType", "kind": "Gdef", "module_public": false}, "InfixNotationOperatorSpec": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.InfixNotationOperatorSpec", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Keyword": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Keyword", "kind": "Gdef"}, "LRUMemo": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.LRUMemo", "kind": "Gdef", "module_public": false}, "LineEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.LineEnd", "kind": "Gdef"}, "LineStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.LineStart", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Literal", "kind": "Gdef"}, "Located": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Located", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MatchFirst": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.MatchFirst", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "NoMatch": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.NoMatch", "kind": "Gdef"}, "NotAny": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.NotAny", "kind": "Gdef"}, "OneOrMore": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.OneOrMore", "kind": "Gdef"}, "OnlyOnce": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.OnlyOnce", "kind": "Gdef"}, "OpAssoc": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.OpAssoc", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Optional", "kind": "Gdef"}, "Or": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Or", "kind": "Gdef"}, "ParseAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.ParseAction", "kind": "Gdef", "module_public": false}, "ParseBaseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseBaseException", "kind": "Gdef"}, "ParseCondition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseCondition", "kind": "Gdef", "module_public": false}, "ParseElementEnhance": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseElementEnhance", "kind": "Gdef"}, "ParseException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseException", "kind": "Gdef"}, "ParseExpression": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseExpression", "kind": "Gdef"}, "ParseFailAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseFailAction", "kind": "Gdef", "module_public": false}, "ParseFatalException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseFatalException", "kind": "Gdef"}, "ParseImplReturnType": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParseImplReturnType", "kind": "Gdef", "module_public": false}, "ParseResults": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results.ParseResults", "kind": "Gdef"}, "ParseSyntaxException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.ParseSyntaxException", "kind": "Gdef"}, "ParserElement": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ParserElement", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PositionToken": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.PositionToken", "kind": "Gdef"}, "PostParseReturnType": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.PostParseReturnType", "kind": "Gdef", "module_public": false}, "PrecededBy": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.PrecededBy", "kind": "Gdef"}, "QuotedString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.QuotedString", "kind": "Gdef"}, "RLock": {".class": "SymbolTableNode", "cross_ref": "threading.RLock", "kind": "Gdef", "module_public": false}, "RecursiveGrammarException": {".class": "SymbolTableNode", "cross_ref": "pyparsing.exceptions.RecursiveGrammarException", "kind": "Gdef"}, "Regex": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Regex", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SkipTo": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.SkipTo", "kind": "Gdef"}, "StringEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.StringEnd", "kind": "Gdef"}, "StringStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.StringStart", "kind": "Gdef"}, "Suppress": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Suppress", "kind": "Gdef"}, "Tag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Tag", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_public": false}, "Token": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Token", "kind": "Gdef"}, "TokenConverter": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.TokenConverter", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "UnboundedMemo": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.UnboundedMemo", "kind": "Gdef", "module_public": false}, "UnicodeRangeList": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.UnicodeRangeList", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "White": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.White", "kind": "Gdef"}, "Word": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.Word", "kind": "Gdef"}, "WordEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.WordEnd", "kind": "Gdef"}, "WordStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.WordStart", "kind": "Gdef"}, "ZeroOrMore": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.ZeroOrMore", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.__author__", "name": "__author__", "type": "builtins.str"}}, "__compat__": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.__compat__", "kind": "Gdef"}, "__diag__": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.__diag__", "kind": "Gdef"}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__versionTime__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.__versionTime__", "name": "__versionTime__", "type": "builtins.str"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.__version__", "name": "__version__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__version_info__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pyparsing.__version_info__", "name": "__version_info__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": "pyparsing.version_info"}}}, "__version_time__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.__version_time__", "name": "__version_time__", "type": "builtins.str"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "alphanums": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.alphanums", "kind": "Gdef"}, "alphas": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.alphas", "kind": "Gdef"}, "alphas8bit": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.alphas8bit", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "anyCloseTag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.anyCloseTag", "kind": "Gdef"}, "anyOpenTag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.anyOpenTag", "kind": "Gdef"}, "any_close_tag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.any_close_tag", "kind": "Gdef"}, "any_open_tag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.any_open_tag", "kind": "Gdef"}, "autoname_elements": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.autoname_elements", "kind": "Gdef"}, "cStyleComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.cStyleComment", "kind": "Gdef"}, "c_style_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.c_style_comment", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "col": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.col", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "common": {".class": "SymbolTableNode", "cross_ref": "pyparsing.common.pyparsing_common", "kind": "Gdef"}, "commonHTMLEntity": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.commonHTMLEntity", "kind": "Gdef"}, "common_builtin_exprs": {".class": "SymbolTableNode", "cross_ref": "pyparsing.common._builtin_exprs", "kind": "Gdef", "module_public": false}, "common_html_entity": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.common_html_entity", "kind": "Gdef"}, "conditionAsParseAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.conditionAsParseAction", "kind": "Gdef"}, "condition_as_parse_action": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.condition_as_parse_action", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "core_builtin_exprs": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core._builtin_exprs", "kind": "Gdef", "module_public": false}, "countedArray": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.countedArray", "kind": "Gdef"}, "counted_array": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.counted_array", "kind": "Gdef"}, "cppStyleComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.cppStyleComment", "kind": "Gdef"}, "cpp_style_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.cpp_style_comment", "kind": "Gdef"}, "dblQuotedString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.dblQuotedString", "kind": "Gdef"}, "dblSlashComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.dblSlashComment", "kind": "Gdef"}, "dbl_quoted_string": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.dbl_quoted_string", "kind": "Gdef"}, "dbl_slash_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.dbl_slash_comment", "kind": "Gdef"}, "delimitedList": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.delimitedList", "kind": "Gdef"}, "delimited_list": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.delimited_list", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef", "module_public": false}, "dictOf": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.dictOf", "kind": "Gdef"}, "dict_of": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.dict_of", "kind": "Gdef"}, "disable_diag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.disable_diag", "kind": "Gdef", "module_public": false}, "empty": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.empty", "kind": "Gdef"}, "enable_all_warnings": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.enable_all_warnings", "kind": "Gdef", "module_public": false}, "enable_diag": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.enable_diag", "kind": "Gdef", "module_public": false}, "helper_builtin_exprs": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers._builtin_exprs", "kind": "Gdef", "module_public": false}, "hexnums": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.hexnums", "kind": "Gdef"}, "html": {".class": "SymbolTableNode", "cross_ref": "html", "kind": "Gdef", "module_public": false}, "htmlComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.htmlComment", "kind": "Gdef"}, "html_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.html_comment", "kind": "Gdef"}, "identbodychars": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.identbodychars", "kind": "Gdef"}, "identchars": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.identchars", "kind": "Gdef"}, "indentedBlock": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.indentedBlock", "kind": "Gdef"}, "infixNotation": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.infixNotation", "kind": "Gdef"}, "infix_notation": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.infix_notation", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "itemgetter": {".class": "SymbolTableNode", "cross_ref": "operator.itemgetter", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "javaStyleComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.javaStyleComment", "kind": "Gdef"}, "java_style_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.java_style_comment", "kind": "Gdef"}, "line": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.line", "kind": "Gdef"}, "lineEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.lineEnd", "kind": "Gdef"}, "lineStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.lineStart", "kind": "Gdef"}, "line_end": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.line_end", "kind": "Gdef"}, "line_start": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.line_start", "kind": "Gdef"}, "lineno": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.lineno", "kind": "Gdef"}, "locatedExpr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.locatedExpr", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef", "module_public": false}, "makeHTMLTags": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.makeHTMLTags", "kind": "Gdef"}, "makeXMLTags": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.makeXMLTags", "kind": "Gdef"}, "make_compressed_re": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.make_compressed_re", "kind": "Gdef", "module_public": false}, "make_html_tags": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.make_html_tags", "kind": "Gdef"}, "make_xml_tags": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.make_xml_tags", "kind": "Gdef"}, "matchOnlyAtCol": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.matchOnlyAtCol", "kind": "Gdef"}, "matchPreviousExpr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.matchPreviousExpr", "kind": "Gdef"}, "matchPreviousLiteral": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.matchPreviousLiteral", "kind": "Gdef"}, "match_only_at_col": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.match_only_at_col", "kind": "Gdef"}, "match_previous_expr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.match_previous_expr", "kind": "Gdef"}, "match_previous_literal": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.match_previous_literal", "kind": "Gdef"}, "nestedExpr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.nestedExpr", "kind": "Gdef"}, "nested_expr": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.nested_expr", "kind": "Gdef"}, "nullDebugAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.nullDebugAction", "kind": "Gdef"}, "null_debug_action": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.null_debug_action", "kind": "Gdef"}, "nums": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.nums", "kind": "Gdef"}, "oneOf": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.oneOf", "kind": "Gdef"}, "one_of": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.one_of", "kind": "Gdef"}, "opAssoc": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.opAssoc", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "originalTextFor": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.originalTextFor", "kind": "Gdef"}, "original_text_for": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.original_text_for", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pa_call_line_synth": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.pa_call_line_synth", "kind": "Gdef", "module_public": false}, "pprint": {".class": "SymbolTableNode", "cross_ref": "pprint", "kind": "Gdef", "module_public": false}, "ppu": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.pyparsing_unicode", "kind": "Gdef", "module_public": false}, "printables": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.printables", "kind": "Gdef"}, "punc8bit": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.punc8bit", "kind": "Gdef"}, "pyparsing_common": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pyparsing.pyparsing_common", "line": 150, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pyparsing.common.pyparsing_common"}}, "pyparsing_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pyparsing.pyparsing_test", "line": 152, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pyparsing.testing.pyparsing_test"}}, "pyparsing_unicode": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.pyparsing_unicode", "kind": "Gdef"}, "pythonStyleComment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.pythonStyleComment", "kind": "Gdef"}, "python_quoted_string": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.python_quoted_string", "kind": "Gdef", "module_public": false}, "python_style_comment": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.python_style_comment", "kind": "Gdef"}, "quotedString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.quotedString", "kind": "Gdef"}, "quoted_string": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.quoted_string", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "removeQuotes": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.removeQuotes", "kind": "Gdef"}, "remove_quotes": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.remove_quotes", "kind": "Gdef"}, "replaceHTMLEntity": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.replaceHTMLEntity", "kind": "Gdef"}, "replaceWith": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.replaceWith", "kind": "Gdef"}, "replace_html_entity": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.replace_html_entity", "kind": "Gdef"}, "replace_with": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.replace_with", "kind": "Gdef"}, "replaced_by_pep8": {".class": "SymbolTableNode", "cross_ref": "pyparsing.util.replaced_by_pep8", "kind": "Gdef", "module_public": false}, "restOfLine": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.restOfLine", "kind": "Gdef"}, "rest_of_line": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.rest_of_line", "kind": "Gdef"}, "sglQuotedString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.sglQuotedString", "kind": "Gdef"}, "sgl_quoted_string": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.sgl_quoted_string", "kind": "Gdef"}, "srange": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.srange", "kind": "Gdef"}, "str_type": {".class": "SymbolTableNode", "cross_ref": "pyparsing.results.str_type", "kind": "Gdef", "module_public": false}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef", "module_public": false}, "stringEnd": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.stringEnd", "kind": "Gdef"}, "stringStart": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.stringStart", "kind": "Gdef"}, "string_end": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.string_end", "kind": "Gdef"}, "string_start": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.string_start", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "testing": {".class": "SymbolTableNode", "cross_ref": "pyparsing.testing.pyparsing_test", "kind": "Gdef"}, "tokenMap": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.tokenMap", "kind": "Gdef"}, "token_map": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.token_map", "kind": "Gdef"}, "traceParseAction": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.traceParseAction", "kind": "Gdef"}, "trace_parse_action": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.trace_parse_action", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "ungroup": {".class": "SymbolTableNode", "cross_ref": "pyparsing.helpers.ungroup", "kind": "Gdef"}, "unicode": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.pyparsing_unicode", "kind": "Gdef"}, "unicodeString": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.unicodeString", "kind": "Gdef"}, "unicode_set": {".class": "SymbolTableNode", "cross_ref": "pyparsing.unicode.unicode_set", "kind": "Gdef"}, "unicode_string": {".class": "SymbolTableNode", "cross_ref": "pyparsing.core.unicode_string", "kind": "Gdef"}, "version_info": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.version_info", "name": "version_info", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "pyparsing.version_info", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["major", "minor", "micro", "releaselevel", "serial"]}}, "module_name": "pyparsing", "mro": ["pyparsing.version_info", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "major"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "micro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "releaselevel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serial"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "major", "minor", "micro", "releaselevel", "serial"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pyparsing.version_info.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "major", "minor", "micro", "releaselevel", "serial"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of version_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.version_info.__repr__", "name": "__repr__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.version_info.__str__", "name": "__str__", "type": null}}, "__version__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pyparsing.version_info.__version__", "name": "__version__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pyparsing.version_info.__version__", "name": "__version__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": "pyparsing.version_info"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__version__ of version_info", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.version_info._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of version_info", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pyparsing.version_info._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of version_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "pyparsing.version_info._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of version_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "major", "minor", "micro", "releaselevel", "serial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.version_info._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "major", "minor", "micro", "releaselevel", "serial"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of version_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info._NT", "id": -1, "name": "_NT", "namespace": "pyparsing.version_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pyparsing.version_info._source", "name": "_source", "type": "builtins.str"}}, "major": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.version_info.major", "name": "major", "type": "builtins.int"}}, "major-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.version_info.major", "kind": "<PERSON><PERSON><PERSON>"}, "micro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.version_info.micro", "name": "micro", "type": "builtins.int"}}, "micro-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.version_info.micro", "kind": "<PERSON><PERSON><PERSON>"}, "minor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.version_info.minor", "name": "minor", "type": "builtins.int"}}, "minor-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.version_info.minor", "kind": "<PERSON><PERSON><PERSON>"}, "releaselevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.version_info.releaselevel", "name": "releaselevel", "type": "builtins.str"}}, "releaselevel-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.version_info.releaselevel", "kind": "<PERSON><PERSON><PERSON>"}, "serial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pyparsing.version_info.serial", "name": "serial", "type": "builtins.int"}}, "serial-redefinition": {".class": "SymbolTableNode", "cross_ref": "pyparsing.version_info.serial", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.version_info.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": "pyparsing.version_info"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "withAttribute": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.withAttribute", "kind": "Gdef"}, "withClass": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.withClass", "kind": "Gdef"}, "with_attribute": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.with_attribute", "kind": "Gdef"}, "with_class": {".class": "SymbolTableNode", "cross_ref": "pyparsing.actions.with_class", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\pyparsing\\__init__.py"}
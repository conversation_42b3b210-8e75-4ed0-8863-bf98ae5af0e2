{"real_time_metrics": [{"metric": "current_pnl", "display_name": "当前盈亏", "format": "currency", "alert_threshold": -100, "update_frequency": "real_time"}, {"metric": "daily_pnl", "display_name": "当日盈亏", "format": "currency", "alert_threshold": -200, "update_frequency": "real_time"}, {"metric": "position_count", "display_name": "持仓数量", "format": "integer", "alert_threshold": 2, "update_frequency": "real_time"}, {"metric": "total_exposure", "display_name": "总敞口", "format": "currency", "alert_threshold": 2000, "update_frequency": "real_time"}], "daily_metrics": [{"metric": "daily_return", "display_name": "日收益率", "format": "percentage", "target": 0.005}, {"metric": "win_rate", "display_name": "胜率", "format": "percentage", "target": 0.6}, {"metric": "avg_trade_size", "display_name": "平均交易规模", "format": "currency", "target": 750}], "alerts": [{"type": "loss_limit", "threshold": -200, "action": "stop_trading", "notification": ["email", "sms"]}, {"type": "position_limit", "threshold": 2, "action": "block_new_trades", "notification": ["dashboard"]}, {"type": "system_error", "threshold": 1, "action": "emergency_stop", "notification": ["email", "sms", "dashboard"]}]}
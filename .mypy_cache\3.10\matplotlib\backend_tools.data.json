{".class": "MypyFile", "_fullname": "matplotlib.backend_tools", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AxisScaleBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolToggleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.AxisScaleBase", "name": "AxisScaleBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.AxisScaleBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.AxisScaleBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "disable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.AxisScaleBase.disable", "name": "disable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.AxisScaleBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable of AxisScaleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.AxisScaleBase.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.AxisScaleBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable of AxisScaleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.AxisScaleBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.AxisScaleBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConfigureSubplotsBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ConfigureSubplotsBase", "name": "ConfigureSubplotsBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ConfigureSubplotsBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ConfigureSubplotsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ConfigureSubplotsBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ConfigureSubplotsBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Cursors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.Cursors", "name": "Cursors", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "matplotlib.backend_tools.Cursors", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.Cursors", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "HAND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.HAND", "name": "HAND", "type": "builtins.int"}}, "MOVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.MOVE", "name": "MOVE", "type": "builtins.int"}}, "POINTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.POINTER", "name": "POINTER", "type": "builtins.int"}}, "RESIZE_HORIZONTAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.RESIZE_HORIZONTAL", "name": "RESIZE_HORIZONTAL", "type": "builtins.int"}}, "RESIZE_VERTICAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.RESIZE_VERTICAL", "name": "RESIZE_VERTICAL", "type": "builtins.int"}}, "SELECT_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.SELECT_REGION", "name": "SELECT_REGION", "type": "builtins.int"}}, "WAIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "matplotlib.backend_tools.Cursors.WAIT", "name": "WAIT", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.Cursors.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.Cursors", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Figure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.Figure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FigureCanvasBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.FigureCanvasBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RubberbandBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.RubberbandBase", "name": "RubberbandBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.RubberbandBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.RubberbandBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "draw_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.RubberbandBase.draw_rubberband", "name": "draw_rubberband", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "data"], "arg_types": ["matplotlib.backend_tools.RubberbandBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draw_rubberband of RubberbandBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_rubberband": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.RubberbandBase.remove_rubberband", "name": "remove_rubberband", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.RubberbandBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_rubberband of RubberbandBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.RubberbandBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.RubberbandBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaveFigureBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.SaveFigureBase", "name": "SaveFigureBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.SaveFigureBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.SaveFigureBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.SaveFigureBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.SaveFigureBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScaleBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.scale.ScaleBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolBack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ViewsPositionsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolBack", "name": "ToolBack", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolBack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolBack", "matplotlib.backend_tools.ViewsPositionsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolBack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolBack", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolBase", "name": "ToolBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "toolmanager", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "toolmanager", "name"], "arg_types": ["matplotlib.backend_tools.ToolBase", "matplotlib.backend_managers.ToolManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ToolBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canvas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.canvas", "name": "canvas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canvas of Too<PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolBase.canvas", "name": "canvas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canvas of Too<PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["matplotlib.backend_bases.FigureCanvasBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_keymap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.default_keymap", "name": "default_keymap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_keymap of ToolBase", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolBase.default_keymap", "name": "default_keymap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_keymap of ToolBase", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolBase.description", "name": "description", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.backend_tools.ToolBase.figure", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.figure", "name": "figure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of ToolBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolBase.figure", "name": "figure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of ToolBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.figure", "name": "figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolBase", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of ToolBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "figure", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of ToolBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolBase.image", "name": "image", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ToolBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolBase.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ToolBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolBase.set_figure", "name": "set_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolBase", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_figure of ToolBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolBase.toolmanager", "name": "toolmanager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toolmanager of ToolBase", "ret_type": "matplotlib.backend_managers.ToolManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolBase.toolmanager", "name": "toolmanager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toolmanager of ToolBase", "ret_type": "matplotlib.backend_managers.ToolManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "sender", "event", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolBase.trigger", "name": "trigger", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "sender", "event", "data"], "arg_types": ["matplotlib.backend_tools.ToolBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "matplotlib.backend_managers.ToolEvent", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trigger of ToolBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolContainerBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.ToolContainerBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolCopyToClipboardBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolCopyToClipboardBase", "name": "ToolCopyToClipboardBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolCopyToClipboardBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolCopyToClipboardBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolCopyToClipboardBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolCopyToClipboardBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolCursorPosition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolCursorPosition", "name": "ToolCursorPosition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolCursorPosition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolCursorPosition", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "send_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolCursorPosition.send_message", "name": "send_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ToolCursorPosition", "matplotlib.backend_managers.ToolEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_message of ToolCursorPosition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolCursorPosition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolCursorPosition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolEvent": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_managers.ToolEvent", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolForward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ViewsPositionsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolForward", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolForward", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolForward", "matplotlib.backend_tools.ViewsPositionsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolForward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolForward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolFullScreen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolFullScreen", "name": "ToolFullScreen", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolFullScreen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolFullScreen", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolFullScreen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolFullScreen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolGrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolGrid", "name": "ToolGrid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolGrid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolGrid", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolGrid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolGrid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolHelpBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolHelpBase", "name": "ToolHelpBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolHelpBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolHelpBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "format_shortcut": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key_sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolHelpBase.format_shortcut", "name": "format_shortcut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key_sequence"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_shortcut of ToolHelpBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolHelpBase.format_shortcut", "name": "format_shortcut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key_sequence"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_shortcut of ToolHelpBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolHelpBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolHelpBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolHome": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ViewsPositionsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolHome", "name": "ToolHome", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolHome", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolHome", "matplotlib.backend_tools.ViewsPositionsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolHome.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolHome", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolManager": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_managers.ToolManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolMinorGrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolMinorGrid", "name": "ToolMinorGrid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolMinorGrid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolMinorGrid", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolMinorGrid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolMinorGrid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolPan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ZoomPanBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolPan", "name": "ToolPan", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolPan", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolPan", "matplotlib.backend_tools.ZoomPanBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolPan.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolPan", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolQuit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolQuit", "name": "ToolQuit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolQuit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolQuit", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolQuit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolQuit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolQuitAll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolQuitAll", "name": "ToolQuitAll", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolQuitAll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolQuitAll", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolQuitAll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolQuitAll", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolSetCursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolSetCursor", "name": "ToolSetCursor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolSetCursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolSetCursor", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolSetCursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolSetCursor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolToggleBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolToggleBase", "name": "ToolToggleBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolToggleBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolToggleBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ToolToggleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolToggleBase.cursor", "name": "cursor", "type": {".class": "UnionType", "items": ["matplotlib.backend_tools.Cursors", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "default_toggled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolToggleBase.default_toggled", "name": "default_toggled", "type": "builtins.bool"}}, "disable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolToggleBase.disable", "name": "disable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable of ToolToggleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolToggleBase.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable of ToolToggleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radio_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolToggleBase.radio_group", "name": "radio_group", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolToggleBase.set_figure", "name": "set_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_figure of ToolToggleBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toggled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "matplotlib.backend_tools.ToolToggleBase.toggled", "name": "toggled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toggled of ToolToggleBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "matplotlib.backend_tools.ToolToggleBase.toggled", "name": "toggled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolToggleBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toggled of ToolToggleBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolToggleBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolToggleBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolViewsPositions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolViewsPositions", "name": "ToolViewsPositions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolViewsPositions", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "add_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.add_figure", "name": "add_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_figure of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "back": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.back", "name": "back", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions", "matplotlib.figure.Figure"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "home": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.home", "name": "home", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "home of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "home_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolViewsPositions.home_views", "name": "home_views", "type": {".class": "Instance", "args": ["matplotlib.figure.Figure", {".class": "Instance", "args": ["matplotlib.axes._axes.Axes", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolViewsPositions.positions", "name": "positions", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.figure.Figure", "matplotlib.axes._axes.Axes"], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "matplotlib.cbook._Stack"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "push_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.push_current", "name": "push_current", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_current of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_home_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.update_home_views", "name": "update_home_views", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "figure"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_home_views of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolViewsPositions.update_view", "name": "update_view", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.backend_tools.ToolViewsPositions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_view of ToolViewsPositions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ToolViewsPositions.views", "name": "views", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["matplotlib.figure.Figure", "matplotlib.axes._axes.Axes"], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "matplotlib.cbook._Stack"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolViewsPositions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolViewsPositions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolXScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.AxisScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolXScale", "name": "ToolXScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolXScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolXScale", "matplotlib.backend_tools.AxisScaleBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "set_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ax", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolXScale.set_scale", "name": "set_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ax", "scale"], "arg_types": ["matplotlib.backend_tools.ToolXScale", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.scale.ScaleBase"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_scale of ToolXScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolXScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolXScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolYScale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.AxisScaleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolYScale", "name": "ToolYScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolYScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolYScale", "matplotlib.backend_tools.AxisScaleBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "set_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ax", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolYScale.set_scale", "name": "set_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ax", "scale"], "arg_types": ["matplotlib.backend_tools.ToolYScale", "matplotlib.axes._axes.Axes", {".class": "UnionType", "items": ["builtins.str", "matplotlib.scale.ScaleBase"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_scale of ToolYScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolYScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolYScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ToolZoom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ZoomPanBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ToolZoom", "name": "ToolZoom", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ToolZoom", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ToolZoom", "matplotlib.backend_tools.ZoomPanBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ToolZoom.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ToolZoom", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ViewsPositionsBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ViewsPositionsBase", "name": "ViewsPositionsBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ViewsPositionsBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ViewsPositionsBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ViewsPositionsBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ViewsPositionsBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZoomPanBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.backend_tools.ToolToggleBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.backend_tools.ZoomPanBase", "name": "ZoomPanBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ZoomPanBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.backend_tools", "mro": ["matplotlib.backend_tools.ZoomPanBase", "matplotlib.backend_tools.ToolToggleBase", "matplotlib.backend_tools.ToolBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ZoomPanBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["matplotlib.backend_tools.ZoomPanBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ZoomPanBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ZoomPanBase.base_scale", "name": "base_scale", "type": "builtins.float"}}, "disable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ZoomPanBase.disable", "name": "disable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ZoomPanBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable of ZoomPanBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ZoomPanBase.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ZoomPanBase", {".class": "UnionType", "items": ["matplotlib.backend_managers.ToolEvent", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable of ZoomPanBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lastscroll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ZoomPanBase.lastscroll", "name": "lastscroll", "type": "builtins.float"}}, "scroll_zoom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.ZoomPanBase.scroll_zoom", "name": "scroll_zoom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["matplotlib.backend_tools.ZoomPanBase", "matplotlib.backend_managers.ToolEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scroll_zoom of ZoomPanBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scrollthresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.backend_tools.ZoomPanBase.scrollthresh", "name": "scrollthresh", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.backend_tools.ZoomPanBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.backend_tools.ZoomPanBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_tools_to_container": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["container", "tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.add_tools_to_container", "name": "add_tools_to_container", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["container", "tools"], "arg_types": ["matplotlib.backend_bases.ToolContainerBase", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tools_to_container", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tools_to_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["toolmanager", "tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.backend_tools.add_tools_to_manager", "name": "add_tools_to_manager", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["toolmanager", "tools"], "arg_types": ["matplotlib.backend_managers.ToolManager", {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "matplotlib.backend_tools.ToolBase"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tools_to_manager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cursors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "matplotlib.backend_tools.cursors", "line": 20, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "matplotlib.backend_tools.Cursors"}}, "default_toolbar_tools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.default_toolbar_tools", "name": "default_toolbar_tools", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "default_tools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.backend_tools.default_tools", "name": "default_tools", "type": {".class": "Instance", "args": ["builtins.str", "matplotlib.backend_tools.ToolBase"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\终极版现货交易\\.venv\\lib\\site-packages\\matplotlib\\backend_tools.pyi"}
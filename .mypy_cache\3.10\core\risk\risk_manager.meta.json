{"data_mtime": 1748493663, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio", "logging", "time", "datetime", "typing", "dataclasses", "enum", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "types", "typing_extensions"], "hash": "61e5ea94b7168aa86cc21c44cb0e56bd743ac6cb", "id": "core.risk.risk_manager", "ignore_all": true, "interface_hash": "f37a9a4f54ce193e2ce7048b07242ac52d08228e", "mtime": 1748490054, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\终极版现货交易\\core\\risk\\risk_manager.py", "plugin_data": null, "size": 16290, "suppressed": [], "version_id": "1.15.0"}
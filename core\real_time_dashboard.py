#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时监控仪表板
Real-time Monitoring Dashboard

为机构级量化交易框架提供实时监控和可视化界面
"""

import json
import logging
import os
import sys
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from institutional_manager import InstitutionalFrameworkManager
from performance_monitoring import PerformanceAlert, PerformanceMetrics

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DashboardMetrics:
    """仪表板指标"""

    timestamp: datetime
    total_strategies: int
    active_strategies: int
    total_alerts: int
    critical_alerts: int
    avg_validation_score: float
    avg_sharpe_ratio: float
    total_portfolio_value: float
    daily_pnl: float
    portfolio_count: int
    system_status: str


@dataclass
class StrategySnapshot:
    """策略快照"""

    name: str
    validation_score: float
    current_sharpe: float
    daily_return: float
    cumulative_return: float
    max_drawdown: float
    status: str
    last_update: datetime
    alert_count: int


class RealTimeDashboard:
    """
    实时监控仪表板

    提供策略和组合的实时监控界面
    """

    def __init__(self, framework_manager: InstitutionalFrameworkManager):
        """
        初始化仪表板

        Args:
            framework_manager: 机构级框架管理器
        """
        self.framework = framework_manager
        self.is_running = False
        self.monitoring_thread = None
        self.update_interval = 30  # 30秒更新一次

        # 数据存储
        self.dashboard_history = []
        self.strategy_snapshots = {}
        self.portfolio_snapshots = {}

        # 状态管理
        self.last_update = None
        self.system_health = "HEALTHY"

        logger.info("实时监控仪表板初始化完成")

    def start_monitoring(self):
        """启动实时监控"""
        if self.is_running:
            logger.warning("监控已在运行中")
            return

        self.is_running = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitoring_thread.start()
        logger.info("实时监控已启动")

    def stop_monitoring(self):
        """停止实时监控"""
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("实时监控已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 更新仪表板数据
                self._update_dashboard_metrics()
                self._update_strategy_snapshots()
                self._update_portfolio_snapshots()

                # 检查系统健康状态
                self._check_system_health()

                # 保存快照
                self._save_dashboard_snapshot()

                self.last_update = datetime.now()

                # 等待下次更新
                time.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(self.update_interval)

    def _update_dashboard_metrics(self):
        """更新仪表板指标"""
        # 获取框架状态
        dashboard_data = self.framework.get_framework_dashboard()

        # 计算额外指标
        total_alerts = len(self.framework.monitor.alerts)
        critical_alerts = len(
            [
                a
                for a in self.framework.monitor.alerts
                if a.severity == "CRITICAL"
            ]
        )

        # 模拟组合价值和PnL（实际应用中从交易系统获取）
        total_portfolio_value = 10_000_000  # 1000万基础资金
        daily_pnl = np.random.normal(50000, 20000)  # 模拟日收益

        metrics = DashboardMetrics(
            timestamp=datetime.now(),
            total_strategies=dashboard_data["total_strategies"],
            active_strategies=dashboard_data["deployed_strategies"],
            total_alerts=total_alerts,
            critical_alerts=critical_alerts,
            avg_validation_score=dashboard_data["avg_validation_score"],
            avg_sharpe_ratio=dashboard_data["avg_sharpe_ratio"],
            total_portfolio_value=total_portfolio_value,
            daily_pnl=daily_pnl,
            portfolio_count=len(
                getattr(self.framework, "portfolio_manager", {}).get(
                    "portfolios", {}
                )
            ),
            system_status=self.system_health,
        )

        self.dashboard_history.append(metrics)

        # 保持最近1000条记录
        if len(self.dashboard_history) > 1000:
            self.dashboard_history = self.dashboard_history[-1000:]

    def _update_strategy_snapshots(self):
        """更新策略快照"""
        for strategy_name in self.framework.strategies:
            try:
                # 获取策略状态
                status = self.framework.monitor.get_strategy_status(
                    strategy_name
                )

                if "latest_metrics" in status and status["latest_metrics"]:
                    metrics = status["latest_metrics"]

                    # 获取验证评分
                    validation_score = 0
                    if strategy_name in self.framework.validation_results:
                        validation_score = self.framework.validation_results[
                            strategy_name
                        ].validation_score

                    snapshot = StrategySnapshot(
                        name=strategy_name,
                        validation_score=validation_score,
                        current_sharpe=metrics.sharpe_ratio,
                        daily_return=metrics.daily_return,
                        cumulative_return=metrics.cumulative_return,
                        max_drawdown=metrics.max_drawdown,
                        status=(
                            "ACTIVE"
                            if metrics.sharpe_ratio > 0.5
                            else "WARNING"
                        ),
                        last_update=metrics.timestamp,
                        alert_count=status["total_alerts"],
                    )

                    self.strategy_snapshots[strategy_name] = snapshot

            except Exception as e:
                logger.warning(f"更新策略 {strategy_name} 快照失败: {e}")

    def _update_portfolio_snapshots(self):
        """更新组合快照"""
        # 这里应该从组合管理器获取实际数据
        # 为演示目的，创建模拟数据
        portfolio_names = [
            "Conservative_Portfolio",
            "Balanced_Portfolio",
            "Aggressive_Portfolio",
        ]

        for portfolio_name in portfolio_names:
            self.portfolio_snapshots[portfolio_name] = {
                "name": portfolio_name,
                "value": np.random.uniform(3_000_000, 4_000_000),
                "daily_return": np.random.normal(0.001, 0.015),
                "ytd_return": np.random.uniform(0.15, 0.25),
                "sharpe_ratio": np.random.uniform(1.5, 2.5),
                "max_drawdown": np.random.uniform(-0.15, -0.05),
                "last_update": datetime.now(),
            }

    def _check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查关键指标
            recent_alerts = [
                a
                for a in self.framework.monitor.alerts[-10:]
                if a.severity in ["HIGH", "CRITICAL"]
            ]

            if len(recent_alerts) > 5:
                self.system_health = "CRITICAL"
            elif len(recent_alerts) > 2:
                self.system_health = "WARNING"
            else:
                self.system_health = "HEALTHY"

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            self.system_health = "ERROR"

    def _save_dashboard_snapshot(self):
        """保存仪表板快照"""
        try:
            snapshot_data = {
                "timestamp": datetime.now().isoformat(),
                "dashboard_metrics": (
                    asdict(self.dashboard_history[-1])
                    if self.dashboard_history
                    else {}
                ),
                "strategy_snapshots": {
                    name: asdict(snapshot)
                    for name, snapshot in self.strategy_snapshots.items()
                },
                "portfolio_snapshots": self.portfolio_snapshots,
                "system_health": self.system_health,
            }

            # 保存到文件
            os.makedirs("dashboard_data", exist_ok=True)
            snapshot_file = f"dashboard_data/snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(snapshot_file, "w", encoding="utf-8") as f:
                json.dump(
                    snapshot_data, f, ensure_ascii=False, indent=2, default=str
                )

        except Exception as e:
            logger.error(f"保存快照失败: {e}")

    def get_current_dashboard(self) -> Dict[str, Any]:
        """获取当前仪表板数据"""
        return {
            "last_update": self.last_update,
            "system_health": self.system_health,
            "dashboard_metrics": (
                asdict(self.dashboard_history[-1])
                if self.dashboard_history
                else {}
            ),
            "strategy_snapshots": {
                name: asdict(snapshot)
                for name, snapshot in self.strategy_snapshots.items()
            },
            "portfolio_snapshots": self.portfolio_snapshots,
            "recent_alerts": [
                asdict(alert) for alert in self.framework.monitor.alerts[-10:]
            ],
        }

    def generate_dashboard_report(self) -> str:
        """生成仪表板报告"""
        if not self.dashboard_history:
            return "暂无仪表板数据"

        latest_metrics = self.dashboard_history[-1]

        # 计算趋势
        if len(self.dashboard_history) >= 2:
            prev_metrics = self.dashboard_history[-2]
            pnl_trend = (
                "📈"
                if latest_metrics.daily_pnl > prev_metrics.daily_pnl
                else "📉"
            )
            score_trend = (
                "📈"
                if latest_metrics.avg_validation_score
                > prev_metrics.avg_validation_score
                else "📉"
            )
        else:
            pnl_trend = score_trend = "➡️"

        report = f"""
=== 实时监控仪表板 ===
更新时间: {latest_metrics.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
系统状态: {self._get_status_emoji(latest_metrics.system_status)} {latest_metrics.system_status}

=== 核心指标 ===
策略总数: {latest_metrics.total_strategies}
活跃策略: {latest_metrics.active_strategies}
组合数量: {latest_metrics.portfolio_count}
平均评分: {latest_metrics.avg_validation_score:.1f}/100 {score_trend}
平均夏普: {latest_metrics.avg_sharpe_ratio:.3f}

=== 风险监控 ===
总警报数: {latest_metrics.total_alerts}
严重警报: {latest_metrics.critical_alerts}
系统健康: {self._get_status_emoji(self.system_health)} {self.system_health}

=== 投资组合 ===
总资产: ¥{latest_metrics.total_portfolio_value:,.0f}
日收益: ¥{latest_metrics.daily_pnl:,.0f} {pnl_trend}
日收益率: {latest_metrics.daily_pnl/latest_metrics.total_portfolio_value:.2%}

=== 策略表现TOP5 ==="""

        # 按夏普比率排序显示前5个策略
        sorted_strategies = sorted(
            self.strategy_snapshots.values(),
            key=lambda x: x.current_sharpe,
            reverse=True,
        )[:5]

        for i, strategy in enumerate(sorted_strategies, 1):
            status_emoji = "🟢" if strategy.status == "ACTIVE" else "🟡"
            report += f"""
{i}. {strategy.name} {status_emoji}
   评分: {strategy.validation_score:.1f} | 夏普: {strategy.current_sharpe:.3f}
   日收益: {strategy.daily_return:.2%} | 累计: {strategy.cumulative_return:.2%}"""

        # 最近警报
        recent_alerts = self.framework.monitor.alerts[-5:]
        if recent_alerts:
            report += "\n\n=== 最近警报 ==="
            for alert in recent_alerts:
                severity_emoji = self._get_alert_emoji(alert.severity)
                report += f"""
{severity_emoji} {alert.alert_type} - {alert.strategy_name}
   {alert.message}
   时间: {alert.timestamp.strftime('%H:%M:%S')}"""

        return report

    def _get_status_emoji(self, status: str) -> str:
        """获取状态表情符号"""
        emoji_map = {
            "HEALTHY": "🟢",
            "WARNING": "🟡",
            "CRITICAL": "🔴",
            "ERROR": "❌",
        }
        return emoji_map.get(status, "❓")

    def _get_alert_emoji(self, severity: str) -> str:
        """获取警报表情符号"""
        emoji_map = {
            "LOW": "🔵",
            "MEDIUM": "🟡",
            "HIGH": "🟠",
            "CRITICAL": "🔴",
        }
        return emoji_map.get(severity, "❓")

    def export_dashboard_data(self, output_dir: str = "dashboard_exports"):
        """导出仪表板数据"""
        os.makedirs(output_dir, exist_ok=True)

        # 导出历史指标
        if self.dashboard_history:
            metrics_df = pd.DataFrame(
                [asdict(m) for m in self.dashboard_history]
            )
            metrics_df.to_csv(
                os.path.join(output_dir, "dashboard_metrics.csv"), index=False
            )

        # 导出策略快照
        if self.strategy_snapshots:
            strategies_df = pd.DataFrame(
                [asdict(s) for s in self.strategy_snapshots.values()]
            )
            strategies_df.to_csv(
                os.path.join(output_dir, "strategy_snapshots.csv"), index=False
            )

        # 导出组合快照
        if self.portfolio_snapshots:
            portfolios_df = pd.DataFrame(
                list(self.portfolio_snapshots.values())
            )
            portfolios_df.to_csv(
                os.path.join(output_dir, "portfolio_snapshots.csv"),
                index=False,
            )

        logger.info(f"仪表板数据已导出到: {output_dir}")


def create_sample_dashboard_data(framework: InstitutionalFrameworkManager):
    """创建示例仪表板数据"""
    # 模拟一些策略数据
    sample_strategies = [
        "TrendStrategy",
        "MeanReversionStrategy",
        "ArbitrageStrategy",
    ]

    for strategy_name in sample_strategies:
        # 创建模拟收益数据
        dates = pd.date_range("2024-01-01", "2024-12-31", freq="D")
        returns = np.random.normal(0.0008, 0.015, len(dates))
        returns_series = pd.Series(returns, index=dates)

        # 注册到监控系统
        framework.monitor.register_strategy(strategy_name, returns_series)

        # 模拟一些新数据更新
        recent_returns = pd.Series(
            np.random.normal(0.001, 0.012, 5),
            index=pd.date_range("2025-01-01", periods=5),
        )
        framework.monitor.update_performance(strategy_name, recent_returns)


def main():
    """主函数 - 演示实时仪表板"""
    print("🖥️ 启动实时监控仪表板演示")
    print("=" * 60)

    # 创建框架和仪表板
    framework = InstitutionalFrameworkManager()
    dashboard = RealTimeDashboard(framework)

    # 创建示例数据
    print("创建示例数据...")
    create_sample_dashboard_data(framework)

    # 启动监控
    print("启动实时监控...")
    dashboard.start_monitoring()

    try:
        # 运行一段时间展示实时更新
        for i in range(10):
            time.sleep(3)  # 每3秒更新一次显示

            print(f"\n{'='*60}")
            print(f"实时更新 #{i+1}")
            print(f"{'='*60}")

            # 显示当前仪表板
            report = dashboard.generate_dashboard_report()
            print(report)

            # 模拟新数据
            for strategy_name in [
                "TrendStrategy",
                "MeanReversionStrategy",
                "ArbitrageStrategy",
            ]:
                new_return = pd.Series(
                    [np.random.normal(0.001, 0.015)], index=[datetime.now()]
                )
                framework.monitor.update_performance(strategy_name, new_return)

        # 导出数据
        print("\n导出仪表板数据...")
        dashboard.export_dashboard_data()

        print("\n✅ 仪表板演示完成！")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    finally:
        # 停止监控
        dashboard.stop_monitoring()
        print("监控已停止")


if __name__ == "__main__":
    main()
